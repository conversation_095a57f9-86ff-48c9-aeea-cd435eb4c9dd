import request from '@/config/axios'

export const SecondEncoderApi = {
  /** 获得编码器规则列表分页 */
  getEncoderRulePage: (params: any) => {
    return request.get({ url: '/project/second-encoder/page', params })
  },
  /** 获取编码器规则 */
  getEncoderRule: (id: number) => {
    return request.get({ url: '/project/second-encoder/get', params: { id } })
  },
  /** 删除编码器规则 */
  deleteEncoderRule: (id: number) => {
    return request.get({ url: '/project/second-encoder/delete', params: { id } })
  },
  /** 保存编码器规则 */
  saveEncoderRule: (params: any) => {
    return request.get({ url: '/project/second-encoder/save', params })
  },
  /** 保存编码器规则段 */
  saveEncoderRuleSegment: (data: any) => {
    return request.post({ url: '/project/second-encoder/save-segment', data })
  },
  /** 获取编码器规则段 */
  getEncoderRuleSegmentList: (ruleId: number) => {
    return request.get({ url: '/project/second-encoder/list-segment', params: { ruleId } })
  },
  /** 生成编码 */
  generateEncoder: (data: any) => {
    return request.post({ url: '/project/second-encoder/generate-encoder', data })
  },
  /** 获取编码分页 */
  getEncoderPage: (params: any) => {
    return request.get({ url: '/project/second-encoder/get-encoder-page', params })
  },
  /** 释放编码 */
  deleteEncoder: (id: number) => {
    return request.get({ url: '/project/second-encoder/delete-encoder', params: { id } })
  }
}
