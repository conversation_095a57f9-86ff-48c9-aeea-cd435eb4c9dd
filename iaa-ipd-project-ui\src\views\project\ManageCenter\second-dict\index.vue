<template>
  <ContentWrap>
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="100px"
    >
      <el-form-item label="字典名称" prop="name">
        <el-input
          v-model="queryParams.name"
          class="!w-240px"
          clearable
          placeholder="请输入字典名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="字典类型" prop="code">
        <el-input
          v-model="queryParams.code"
          class="!w-240px"
          clearable
          placeholder="请输入字典类型"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button> <Icon icon="ep:search" /> 查询 </el-button>
        <el-button> <Icon class="mr-5px" icon="ep:refresh" /> 重置</el-button>
        <el-button plain type="primary" @click="dictTypeFormRef?.openForm()">
          <Icon class="mr-5px" icon="ep:plus" />
          新增
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <ContentWrap>
    <el-table :loading="loading" :data="list" align="center">
      <el-table-column label="字典编号" prop="id" align="center" />
      <el-table-column label="字典名称" prop="name" show-overflow-tooltip align="center" />
      <el-table-column label="字典编码" prop="code" align="center" />
      <el-table-column align="center" label="操作">
        <template #default="scope">
          <el-button
            v-hasPermi="['system:dict:update']"
            link
            type="primary"
            @click="dictTypeFormRef?.openForm(scope.row.id)"
          >
            修改
          </el-button>
          <el-button link type="primary" @click="dictDataFormRef?.openForm(scope.row.id)">
            数据
          </el-button>
          <el-button
            v-hasPermi="['system:dict:delete']"
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
  <DictTypeForm ref="dictTypeFormRef" @success="handleQuery" />
  <DictDataForm ref="dictDataFormRef" />
</template>

<script lang="ts" setup>
import { SecondDictApi } from '@/api/project/seconddict'
import DictTypeForm from './DictTypeForm.vue'
import DictDataForm from './DictDataForm.vue'

const queryParams = ref({
  name: undefined,
  code: undefined,
  pageSize: 30,
  pageNo: 1
})
const list = ref<any[]>([])
const total = ref(0)
const loading = ref(false)
const dictTypeFormRef = ref()
const dictDataFormRef = ref()
const message = useMessage()

const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

const getList = async () => {
  loading.value = true
  try {
    const res = await SecondDictApi.pageDictType(queryParams.value)
    list.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const handleDelete = async (id: number) => {
  loading.value = true
  try {
    await message.delConfirm()
    await SecondDictApi.deleteDictType(id)
    message.success('删除成功')
    handleQuery()
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  handleQuery()
})
</script>
