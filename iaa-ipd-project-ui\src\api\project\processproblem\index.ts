import request from '@/config/axios'

export const ProcessProblemApi = {
  /** 创建项目过程问题 */
  create: (data: any) => {
    return request.post({ url: '/project/process-problem/create', data })
  },
  /** 更新项目过程问题 */
  update: (data: any) => {
    return request.post({ url: '/project/process-problem/update', data })
  },
  /** 批量更新项目过程问题 */
  updateBatch: (data: any) => {
    return request.post({ url: '/project/process-problem/update-batch', data })
  },
  /** 获得项目问题过程信息列表 */
  list: (params: any) => {
    return request.get({ url: '/project/process-problem/list', params })
  },
  /** 获取项目过程问题 */
  get: (id: number) => {
    return request.get({ url: '/project/process-problem/get/' + id })
  },
  /** 导出数据 */
  export: (data: any) => {
    return request.downloadPost({ url: '/project/process-problem/export', data })
  }
}
