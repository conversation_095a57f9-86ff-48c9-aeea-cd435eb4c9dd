<template>
  <Dialog title="字典类型" v-model="visible">
    <el-form label-width="120" :model="formData" :rules="formRules" ref="formRef">
      <el-form-item label="字典名称" prop="name">
        <el-input v-model="formData.name" />
      </el-form-item>
      <el-form-item label="字典编码" prop="code">
        <el-input v-model="formData.code" :disabled="formData.id" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" :loading="loading" @click="onSubmit"> 保存 </el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { SecondDictApi } from '@/api/project/seconddict'

const visible = ref(false)
const formData = ref({
  id: undefined,
  name: undefined,
  code: undefined
})
const formRules = reactive({
  name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
  code: [{ required: true, message: '编码不能为空', trigger: 'blur' }]
})
const formRef = ref()
const loading = ref(false)
const emits = defineEmits(['success'])
const message = useMessage()

const openForm = async (id?: number) => {
  if (!id) {
    visible.value = true
    refresh()
  } else {
    formData.value = await SecondDictApi.getDictType(id)
    visible.value = true
  }
}

const onSubmit = async () => {
  loading.value = true
  try {
    await formRef.value?.validate()
    await SecondDictApi.saveDictType(formData.value)
    visible.value = false
    message.success('保存成功')
    emits('success')
  } finally {
    loading.value = false
  }
}

const refresh = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    code: undefined
  }
}

defineExpose({
  openForm
})
</script>
