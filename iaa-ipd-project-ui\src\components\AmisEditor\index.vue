<template>
  <AmisEditor
    ref="amisEditorRef"
    id="editorName"
    theme="cxd"
    className="test-amis-editor"
    :preview="props.preview"
    :isMobile="mobileModel"
    :value="schema"
    :onChange="editorChanged"
    v-if="schema"
  />
</template>

<script lang="ts" setup>
// 引入一些样式依赖
import 'amis-ui/lib/themes/default.css'
import 'amis-ui/lib/themes/cxd.css'
import 'amis-editor-core/lib/style.css'
import { applyReactInVue } from 'veaury'
import { Editor } from 'amis-editor' //引入编辑器
import { ref } from 'vue'
import { propTypes } from '@/utils/propTypes'

const props = defineProps({
  // 当前选中的链接
  data: propTypes.any.def({}),
  preview: propTypes.bool.def(false)
})

const AmisEditor = applyReactInVue(Editor) //使用编辑器

const mobileModel = ref(false) //是否是手机模式

const schema = ref(props.data)
const amisEditorRef = ref()
const resultData = ref()

const editorChanged = (value) => {
  //编辑器内容变化后触发的方法
  resultData.value = value
  //todo  如果需要将数据保存，在这里可以操作
}

const getData = () => {
  return resultData.value
}

defineExpose({
  getData
})
</script>

<style scoped>
:deep(.test-amis-editor) {
  height: 100% !important;
  overflow-y: auto;
}
</style>
