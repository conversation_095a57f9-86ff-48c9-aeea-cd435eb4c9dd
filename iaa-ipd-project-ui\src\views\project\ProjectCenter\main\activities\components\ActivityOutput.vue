<template>
  <div class="mb-10px flex">
    <el-button
      type="primary"
      size="small"
      v-if="allowPermission && data.status !== 10 && data.progress !== 100 && allowTheOutput"
      class="!w-50%"
      plain
      @click="openActivitiesSelectdForm"
    >
      选取确认的工业设计活动
    </el-button>
    <el-button
      type="warning"
      size="small"
      class="!w-50%"
      plain
      v-if="allowPermission && data.status !== 10 && data.progress !== 100 && allowTheOutput"
      @click="emits('completed')"
    >
      不确认方案直接完成
    </el-button>
  </div>
  <vxe-table
    :data="dockingList"
    :header-cell-config="{ height: 30 }"
    :cell-config="{ height: 30 }"
    :cell-style="{ padding: '0 5px' }"
    :header-cell-style="{ padding: '0 5px' }"
    show-overflow
    border
    stripe
  >
    <vxe-column title="活动" field="name" />
    <vxe-column title="操作" field="opereation" width="200" align="center">
      <template #default="{ row }">
        <el-button type="primary" link size="small" @click="openActivitiesDetailsForm(row.code)"
          >查看详情</el-button
        >
        <el-button
          type="danger"
          link
          size="small"
          v-if="allowPermission && data.status !== 10 && data.progress !== 100 && allowTheOutput"
          @click="deleteDocking(row.id)"
        >
          删除
        </el-button>
      </template>
    </vxe-column>
  </vxe-table>

  <!-- 活动选择对话框 -->
  <Dialog title="选择确认的活动作为输出对象" v-model="activitiesSelectedVisble">
    <el-input
      v-model="activitiesQueryName"
      size="small"
      placeholder="输入内容发起活动查询"
      @keydown.enter="onListActivities"
    >
      <template #append>
        <el-button type="primary" :icon="Search" size="small" @click="onListActivities" />
      </template>
    </el-input>
    <vxe-table
      class="mt-10px"
      :data="activitiesList"
      :header-cell-config="{ height: 30 }"
      :cell-config="{ height: 30 }"
      :cell-style="{ padding: '0 5px' }"
      :header-cell-style="{ padding: '0 5px' }"
      height="250px"
      border
      stripe
      show-overflow
    >
      <vxe-column title="活动主题" field="name">
        <template #default="{ row }">
          {{ `${row.orderNo} ${row.name}` }}
        </template>
      </vxe-column>
      <vxe-column title="时间" field="date">
        <template #default="{ row }">
          {{
            `${formatToDate(row.startDate, 'YYYY.MM.DD')}-${formatToDate(row.endDate, 'YYYY.MM.DD')}`
          }}
        </template>
      </vxe-column>
      <vxe-column title="进度" field="progress" width="100px" align="center" />
      <vxe-column title="操作" align="center" width="100px">
        <template #default="{ row }">
          <el-button type="warning" link size="small" @click="openActivitiesDetailsForm(row.id)"
            >预览</el-button
          >
          <el-button
            type="primary"
            link
            size="small"
            @click="onActivitiesSelected(row)"
            v-if="row.progress == 100"
          >
            选择
          </el-button>
        </template>
      </vxe-column>
    </vxe-table>
    <el-divider> 已选活动 </el-divider>
    <vxe-table
      :data="activitiesSelectedList"
      :header-cell-config="{ height: 30 }"
      :cell-config="{ height: 30 }"
      :cell-style="{ padding: '0 5px' }"
      :header-cell-style="{ padding: '0 5px' }"
      height="250px"
      border
      stripe
    >
      <vxe-column title="活动主题" field="name">
        <template #default="{ row }">
          {{ `${row.orderNo} ${row.name}` }}
        </template>
      </vxe-column>
      <vxe-column title="时间" field="date">
        <template #default="{ row }">
          {{
            `${formatToDate(row.startDate, 'YYYY.MM.DD')}-${formatToDate(row.endDate, 'YYYY.MM.DD')}`
          }}
        </template>
      </vxe-column>
      <vxe-column title="进度" field="progress" width="100px" align="center" />
      <vxe-column title="操作" align="center" width="100px">
        <template #default="{ row }">
          <el-button type="danger" link size="small" @click="onActivitiesUnSelected(row)">
            取消选择
          </el-button>
        </template>
      </vxe-column>
    </vxe-table>
    <template #footer>
      <el-button type="primary" @click="onSubmitActivitiesList" :loading="activitiesSubmitLoading">
        保存
      </el-button>
    </template>
  </Dialog>
  <Dialog :title="activitiesData?.name" v-model="activitiesDetailVisible" width="70%">
    <el-tabs
      v-model="currentTab"
      class="position-sticky top-0 z-10 bg-#fff"
      @tab-change="onTabChange"
    >
      <el-tab-pane label="基础信息" name="info" />
      <el-tab-pane label="输出物" name="target" />
    </el-tabs>
    <template v-if="currentTab === 'info'">
      <el-form class="custom-form">
        <el-form-item label="活动名称">
          <el-input v-model="activitiesData.name" :disabled="true" />
        </el-form-item>
        <el-form-item label="活动内容">
          <el-input v-model="activitiesData.content" type="textarea" :rows="3" :disabled="true" />
        </el-form-item>
        <el-form-item label="活动描述">
          <el-input
            v-model="activitiesData.description"
            type="textarea"
            :rows="6"
            :disabled="true"
          />
        </el-form-item>
        <el-form-item label="负责人" prop="director">
          <user-avatar-list
            v-model="activitiesData.director!"
            :user-list="userList"
            :size="28"
            :limit="3"
            :add="false"
          />
        </el-form-item>
        <el-form-item label="执行人">
          <user-avatar-list
            v-model="activitiesData.coordinate!"
            :user-list="userList"
            :size="28"
            :limit="3"
            :add="false"
          />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startDate">
              <el-date-picker
                v-model="activitiesData.startDate"
                value-format="YYYY-MM-DD"
                class="!w-100%"
                :disabled="true"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endDate">
              <el-date-picker
                v-model="activitiesData.endDate"
                value-format="YYYY-MM-DD"
                class="!w-100%"
                :disabled="true"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="进度">
          <el-progress
            :percentage="activitiesData.progress"
            class="w-100% no-radius"
            :text-inside="true"
            :stroke-width="20"
            status="success"
          />
        </el-form-item>
        <el-form-item label="状态">
          <DictTag type="project_activities_status" :value="activitiesData.status!" />
        </el-form-item>
      </el-form>
      <Comment
        ref="commentRef"
        category="activities"
        :limit="5"
        bgColor="#fff"
        :disabled="false"
        :user-list="[]"
      />
    </template>
    <template v-else>
      <template v-if="[0, 4].includes(activitiesData.targetType!)">
        <el-collapse v-model="activeNames" ref="collapseRef" class="custom-collapse">
          <el-collapse-item
            title="输出参考"
            name="1"
            v-if="formData.mold == 0 && formData.targetType == 0"
          >
            <div class="flex">
              <div
                class="cursor-pointer w-70px h-120px p-5px hover:bg-#f8f8f8"
                v-for="file in fileTemplateList.filter((item) =>
                  formData.targetTemplateIds?.includes(item.id)
                )"
                :key="file.id"
                @click="officeEditorRef.open(file.infraFileId, file.name)"
              >
                <img :src="getImagePath(file.uri, file.hasFolder)" class="w-60px h-60px" />
                <div
                  class="line-clamp-2 overflow-hidden [display:-webkit-box] [-webkit-box-orient:vertical] [-webkit-line-clamp:2] h-40px text-.65vw word-break-normal"
                >
                  {{ file.name }}
                </div>
              </div>
            </div>
          </el-collapse-item>
          <el-collapse-item title="附件输出" name="2">
            <vxe-table
              class="w-100%"
              :header-cell-style="{ padding: '5px', backgroundColor: '#fff' }"
              :cell-style="{ padding: '5px', height: '30px' }"
              show-overflow
              :data="attachmentList"
              align="center"
              border
            >
              <vxe-column title="文件名" field="name" min-width="200" align="left">
                <template #default="{ row }">
                  <el-link
                    type="primary"
                    @click="attachmentPreviewRef?.openForm(row.name, row.processInstanceId)"
                  >
                    {{ row.name }}
                  </el-link>
                </template>
              </vxe-column>
              <vxe-column title="版本" field="currentVersion" width="60" />
              <vxe-column title="绑定参考" field="templateId" width="200">
                <template #default="{ row }">
                  <el-select v-model="row.templateId" :disabled="true">
                    <el-option
                      v-for="item in fileTemplateList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </template>
              </vxe-column>
              <vxe-column title="审签状态" field="approvalStatus" width="90">
                <template #default="{ row }">
                  <DictTag type="project_target_approval_status" :value="row.approvalStatus" />
                </template>
              </vxe-column>
              <vxe-column
                title="审核通过时间"
                field="approvalTime"
                :formatter="dateFormatter3"
                width="120"
              />
            </vxe-table>
          </el-collapse-item>
          <el-collapse-item title="工时确认" name="3" v-if="activitiesData.workHoursType != '99'">
            <span class="text-1rem font-bold"
              >已选工时：{{ activitiesData.workHoursTotal || 0 }}H</span
            >
            <vxe-table
              ref="workHoursRef"
              :header-cell-style="{ padding: '5px', backgroundColor: '#fff' }"
              :cell-style="{ padding: '5px' }"
              :header-cell-config="{ height: 24 }"
              :cell-config="{ height: 24 }"
              :row-config="{ keyField: 'id' }"
              :checkbox-config="{
                checkRowKeys: activitiesData.workHoursIds,
                highlight: true,
                visibleMethod: visibleMethod,
                checkMethod: checkMethod
              }"
              show-overflow
              border
              align="center"
              :data="workHoursList"
              size="mini"
              :max-height="500"
            >
              <vxe-column type="checkbox" width="60" />
              <vxe-column title="分类" width="150" field="secondaryType">
                <template #default="{ row }">
                  <DictTag type="project_testing_secondary_type" :value="row.secondaryType" />
                </template>
              </vxe-column>
              <vxe-column title="测试项" width="150" align="left" field="name" />
              <vxe-column title="测试要求" min-width="200" align="left" field="demand" />
              <vxe-column title="测试说明" width="200" align="left" field="accountFor" />
              <vxe-column title="工时" width="100" field="workHours" />
            </vxe-table>
          </el-collapse-item>
        </el-collapse>
      </template>
      <template v-else-if="activitiesData.targetType == 1">
        <template v-if="activitiesData.targetDockingId == 0">
          <vxe-table
            :data="targetDockingList"
            :header-cell-config="{ height: 30 }"
            :cell-config="{ height: 30 }"
            :cell-style="{ padding: '0 5px' }"
            :header-cell-style="{ padding: '0 5px' }"
            show-overflow
            border
            stripe
          >
            <vxe-column title="BOM" field="code" />
            <vxe-column title="操作" field="opereation" width="200" align="center">
              <template #default="{ row }">
                <el-button type="primary" link size="small" @click="openBomDetailsForm(row.code)">
                  查看详情
                </el-button>
              </template>
            </vxe-column>
          </vxe-table>
        </template>
        <template v-else>
          <el-empty description="开发中，暂不支持" />
        </template>
      </template>
      <template v-else-if="activitiesData.targetType == 5">
        <vxe-table
          :data="targetDockingList"
          :header-cell-config="{ height: 30 }"
          :cell-config="{ height: 30 }"
          :cell-style="{ padding: '0 5px' }"
          :header-cell-style="{ padding: '0 5px' }"
          show-overflow
          border
          stripe
        >
          <vxe-column title="规格书" field="name" />
          <vxe-column title="审批状态" field="approvalStatus" width="200" align="center">
            <template #default="{ row }">
              <DictTag type="specification_status" :value="row.approvalStatus" />
              <el-button
                type="primary"
                link
                v-if="row.approvalStatus != 0"
                @click="toBpm(row.processInstanceId)"
              >
                跳转流程
              </el-button>
            </template>
          </vxe-column>
          <vxe-column title="操作" field="opereation" width="200" align="center">
            <template #default="{ row }">
              <el-button type="primary" link size="small" @click="onPreviewSpec(row, true)">
                查看详情
              </el-button>
            </template>
          </vxe-column>
        </vxe-table>
      </template>

      <OfficeEditor ref="officeEditorRef" :download="true" />
      <AttachmentPreview ref="attachmentPreviewRef" />
      <Dialog :title="bomTreeTitle" v-model="bomTreeVisible" width="70%">
        <vxe-table
          :data="bomTreeList"
          :header-cell-config="{ height: 30 }"
          :cell-config="{ height: 30 }"
          :cell-style="{ padding: '0 5px' }"
          :header-cell-style="{ padding: '0 5px' }"
          :tree-config="{
            rowField: 'id',
            parentField: 'parentId',
            transform: true,
            expandAll: true
          }"
          border
          show-overflow
          align="center"
        >
          <vxe-column title="序号" type="seq" width="150" tree-node />
          <vxe-column title="品号" field="id" width="120" />
          <vxe-column title="版本" field="partVar" width="80" />
          <vxe-column title="品名" field="partName" align="left" width="250" />
          <vxe-column title="单位" field="unit" width="80" />
          <vxe-column title="规格" field="spec" align="left" />
          <vxe-column title="用量" field="counts" width="80" />
        </vxe-table>
      </Dialog>
      <Dialog
        title="规格书查看"
        v-model="specPreviewVisible"
        width="80%"
        class="file-viewer-dialog"
      >
        <template #title>
          <div class="flex justify-between items-center">
            <div class="text-white leading-40px">
              {{ specData?.name }}
            </div>
            <el-button
              link
              v-if="specData?.id"
              @click="specificationRef?.onExport()"
              class="ml-50px"
            >
              <Icon icon="ep:download" />
              <span class="text-white">下载为word文件</span>
            </el-button>
          </div>
        </template>
        <SpecificationForm ref="specificationRef" @cancel="specPreviewVisible = false" />
      </Dialog>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { Search } from '@element-plus/icons-vue'
import { ActivitiesVO, ActivitiesApi } from '@/api/project/activities'
import { DockingApi } from '@/api/project/activitiestargetdocking'
import { propTypes } from '@/utils/propTypes'
import { UserVO, getSimpleUserList } from '@/api/system/user'
import { FileTemplateApi } from '@/api/project/file/template'
import { getImagePath } from '@/utils/icon'
import { AttachmentApi } from '@/api/project/attachment'
import { TestingTemplateApi } from '@/api/project/testingtemplate'
import AttachmentPreview from '../../components/AttachmentPreview.vue'
import { dateFormatter3 } from '@/utils/formatTime'
import { BomApi } from '@/api/docking/plm/bom'
import { SpecificationApi } from '@/api/project/specification'
import SpecificationForm from '@/views/project/ArchiveCenter/specification/SpecificationForm.vue'
import { formatToDate } from '@/utils/dateUtil'

const props = defineProps({
  data: propTypes.oneOf<ActivitiesVO>([]).isRequired,
  formData: propTypes.oneOf<ActivitiesVO>([]).isRequired,
  allowPermission: propTypes.bool.def(false),
  allowTheOutput: propTypes.bool.def(false)
})

const emits = defineEmits(['completed', 'docking-updated', 'refresh'])

const message = useMessage()
const dockingList = ref<any[]>([])
const userList = ref<UserVO[]>([])
const commentRef = ref()

// 活动相关状态
const activitiesList = ref<any[]>([])
const activitiesQueryName = ref('')
const activitiesSelectedVisble = ref(false)
const activitiesSelectedList = ref<any[]>([])
const activitiesSubmitLoading = ref(false)

// 活动临时展示对象
const targetDockingList = ref<any[]>([])
const activitiesData = ref<ActivitiesVO>({})
const activitiesDetailVisible = ref(false)
const currentTab = ref('info')
const activeNames = ref(['1', '2', '3'])
const fileTemplateList = ref<any[]>([])
const officeEditorRef = ref()
const attachmentList = ref<any[]>([])
const workHoursList = ref<any[]>([])
const attachmentPreviewRef = ref()
const bomTreeList = ref<any[]>([])
const bomTreeVisible = ref(false)
const bomTreeTitle = ref('')
const specPreviewVisible = ref(false)
const specData = ref<any>({})
const specificationRef = ref()

const onListTargetDocking = async () => {
  const res = await DockingApi.getDockingList(activitiesData.value.id!)
  targetDockingList.value = res
}
const onPreviewSpec = async (row, edit: boolean) => {
  const res = await SpecificationApi.getSpecification(row.code)
  specData.value = res
  specPreviewVisible.value = true
  await nextTick()
  specificationRef.value?.openForm({}, edit, specData.value)
}

const openBomDetailsForm = async (bomName: string) => {
  const res = await BomApi.getBomTreeList(bomName)
  bomTreeList.value = res
  bomTreeVisible.value = true
  bomTreeTitle.value = bomName
}
const onListWorkHoursTemplate = async () => {
  if (!activitiesData.value.workHoursType) return
  if (activitiesData.value.workHoursType == '99') return
  const res = await TestingTemplateApi.getTestingTemplateList(activitiesData.value.workHoursType!)
  workHoursList.value = res
}

/** 获取附件列表 */
const onListAttachment = async () => {
  const res = await AttachmentApi.getAttachmentList({
    category: 'activities',
    dynamicId: activitiesData.value.id
  })
  attachmentList.value = res
}

const listFileTemplate = async () => {
  const res = await FileTemplateApi.getFileTemplatePage({})
  fileTemplateList.value = res
}

const onListDocking = async () => {
  const res = await DockingApi.getDockingList(props.formData.id!)
  console.log(res)
  dockingList.value = res
  emits('docking-updated', res)
}

const onListActivities = async () => {
  const res = await ActivitiesApi.getActivitiesList({
    basicsId: props.data.basicsId,
    name: activitiesQueryName.value
  })
  activitiesList.value = res?.filter((item) => item.id != props.data.id && item.targetType != 6)
}

const openActivitiesSelectdForm = () => {
  onListActivities()
  activitiesSelectedVisble.value = true
}

const onActivitiesSelected = (row) => {
  const exists = activitiesSelectedList.value.some((item) => item.id == row.id)
  if (exists) {
    message.error('已添加当前活动，请勿重复添加')
    return
  }
  activitiesSelectedList.value.push(row)
}

const onActivitiesUnSelected = (row) => {
  activitiesSelectedList.value = activitiesSelectedList.value.filter((item) => item.id != row.id)
}

const onSubmitActivitiesList = async () => {
  if (!activitiesSelectedList.value || activitiesSelectedList.value.length == 0) {
    message.error('未选择活动，无需提交')
    return
  }
  activitiesSubmitLoading.value = true
  try {
    const data = activitiesSelectedList.value.map((item) => {
      return {
        activitiesId: props.formData.id,
        targetType: props.formData.targetType,
        targetDockingId: props.formData.targetDockingId,
        code: item.id,
        name: `${item.orderNo} ${item.name}`,
        approvalStatus: item.status
      }
    })
    await DockingApi.createBatch(data)
    activitiesQueryName.value = ''
    activitiesSelectedList.value = []
    activitiesList.value = []
    activitiesSelectedVisble.value = false
    await onListDocking()
    message
      .confirm('保存成功，请确认是否发起活动完成流程')
      .then(() => {
        emits('completed')
      })
      .catch(() => {
        emits('refresh')
      })
  } finally {
    activitiesSubmitLoading.value = false
  }
}

const onTabChange = async () => {
  await nextTick()
  if (currentTab.value === 'info') {
    const res = await ActivitiesApi.getActivities(activitiesData.value.id!)
    activitiesData.value = res
    commentRef.value?.refreshComment()
  } else {
    listFileTemplate()
    if ([0, 4].includes(activitiesData.value.targetType!)) {
      onListAttachment()
      onListWorkHoursTemplate()
    } else if (activitiesData.value.targetType == 1) {
      if (activitiesData.value.targetDockingId == 0) {
        onListTargetDocking()
      }
    } else if (activitiesData.value.targetType == 5) {
      onListTargetDocking()
    }
  }
}

const openActivitiesDetailsForm = async (code: string) => {
  userList.value = await getSimpleUserList()
  activitiesData.value.id = Number(code)
  currentTab.value = 'info'
  onTabChange()
  activitiesDetailVisible.value = true
}

const deleteDocking = async (id: number) => {
  await message.delConfirm()
  await DockingApi.delDocking(id)
  onListDocking()
}

/** 选中方法 */
const visibleMethod = ({ row }: { row: any }) => {
  return activitiesData.value.workHoursIds?.includes(row.id)
}

const checkMethod = ({ row }) => {
  if (activitiesData.value.status !== 10 && activitiesData.value.progress !== 100) {
    return false
  } else {
    true
  }
}

const router = useRouter()
const toBpm = (id: string) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: id
    }
  })
}

const init = () => {
  onListDocking()
}

defineExpose({
  init,
  onListDocking
})
</script>
