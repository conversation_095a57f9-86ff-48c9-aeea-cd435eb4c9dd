<template>
  <Dialog v-model="dialogVisible" title="设计规则" width="60%">
    <el-button type="primary" size="small" plain :loading="loading" @click="onAddSegment">
      添加段
    </el-button>
    <vxe-table
      class="mt-10px"
      :loading="loading"
      :header-cell-config="{ height: 40 }"
      :cell-config="{ height: 40 }"
      align="center"
      :data="list"
      border
    >
      <vxe-column title="序号" field="segmentOrder" />
      <vxe-column title="名称" field="name">
        <template #default="{ row }">
          <el-input v-model="row.name" :prefix-icon="ChatDotSquare" />
        </template>
      </vxe-column>
      <vxe-column title="段类型" field="segment">
        <template #default="{ row }">
          <el-select v-model="row.segment">
            <el-option
              v-for="dict in getStrDictOptions('encoder_segment_type')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </template>
      </vxe-column>
      <vxe-column title="字典码/占位码内容/日期格式" field="value">
        <template #default="{ row }">
          <template v-if="row.segment == 'dict'">
            <el-select v-model="row.value" filterable>
              <el-option
                v-for="dict in dictList"
                :key="dict.code"
                :label="dict.name"
                :value="dict.code"
              />
            </el-select>
          </template>
          <template v-else>
            <el-input
              v-model="row.value"
              :prefix-icon="ChatDotSquare"
              :disabled="['custom'].includes(row.segment)"
            />
          </template>
        </template>
      </vxe-column>
      <vxe-column title="流水码长度" field="serialLength">
        <template #default="{ row }">
          <el-input
            type="number"
            v-model="row.serialLength"
            :disabled="row.segment !== 'serial'&&!row.isMultiple"
            :prefix-icon="Tickets"
          />
        </template>
      </vxe-column>
      <vxe-column title="是否多选" field="isMultiple" width="80">
        <template #default="{ row }">
          <el-switch v-model="row.isMultiple" :disabled="row.segment != 'dict'" />
        </template>
      </vxe-column>
      <vxe-column title="是否末级" field="isFinalLevel" width="80">
        <template #default="{ row }">
          <el-switch v-model="row.isFinalLevel" :disabled="row.segment != 'dict'" />
        </template>
      </vxe-column>
    </vxe-table>
    <template #footer>
      <el-button type="primary" :loading="loading" @click="onSave">保存</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { SecondEncoderApi } from '@/api/project/secondencoder'
import { SecondDictApi } from '@/api/project/seconddict'
import { getStrDictOptions } from '@/utils/dict'
import { ChatDotSquare, Tickets } from '@element-plus/icons-vue'
const dialogVisible = ref(false)

const formRuleId = ref<number | undefined>(undefined)
const list = ref<any[]>([])
const loading = ref(false)
const message = useMessage()

const dictList = ref<any[]>([])

const openForm = async (ruleId: number) => {
  loading.value = true
  try {
    dialogVisible.value = true
    formRuleId.value = ruleId
    const res = await SecondEncoderApi.getEncoderRuleSegmentList(ruleId)
    list.value = res
    onListDict()
  } finally {
    loading.value = false
  }
}

const onListDict = async () => {
  const res = await SecondDictApi.listDictType()
  dictList.value = res
}

const onAddSegment = () => {
  list.value.push({
    segment: undefined,
    value: undefined,
    serialLength: undefined,
    segmentOrder: list.value.length + 1
  })
}

const onSave = async () => {
  loading.value = true
  try {
    if (list.value.length === 0) {
      message.error('没有设置任何规则')
      return
    }
    for (const key in list.value) {
      if (!list.value[key].segment) {
        message.error(`请设置第${key + 1}行段类型`)
        return
      }
      switch (list.value[key].segment) {
        case 'dict':
        case 'placeholder':
        case 'date':
          if (!list.value[key].value) {
            message.error(`请设置第${key + 1}行段值`)
            return
          }
          break
        case 'serial':
          if (!list.value[key].value) {
            message.error(`请设置第${key + 1}行段值`)
            return
          }
          if (!list.value[key].serialLength) {
            message.error(`请设置第${key + 1}行流水码长度`)
          }
      }
    }
    await SecondEncoderApi.saveEncoderRuleSegment({ ruleId: formRuleId.value!, list: list.value })
    message.success('设置成功')
    dialogVisible.value = false
  } finally {
    loading.value = false
  }
}

defineExpose({
  openForm
})
</script>

<style lang="scss" scoped>
:deep(.el-select__wrapper),
:deep(.el-input__wrapper) {
  box-shadow: none;

  &:hover {
    box-shadow: none;
  }
}
</style>
