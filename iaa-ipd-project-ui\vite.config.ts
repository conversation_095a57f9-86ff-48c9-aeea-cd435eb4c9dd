import { resolve } from 'path'
import { loadEnv } from 'vite'
import type { UserConfig, ConfigEnv } from 'vite'
import { createVitePlugins } from './build/vite'
import { include, exclude } from "./build/vite/optimize"
// 当前执行node命令时文件夹的地址(工作目录)
const root = process.cwd()

// 路径查找
function pathResolve(dir: string) {
  return resolve(root, '.', dir)
}

// https://vitejs.dev/config/
export default ({ command, mode }: ConfigEnv): UserConfig => {
  let env = {} as any
  const isBuild = command === 'build'
  if (!isBuild) {
    env = loadEnv((process.argv[3] === '--mode' ? process.argv[4] : process.argv[3]), root)
  } else {
    env = loadEnv(mode, root)
  }
  return {
    base: env.VITE_BASE_PATH,
    root: root,
    // 服务端渲染
    server: {
      port: env.VITE_PORT, // 端口号
      host: "0.0.0.0",
      open: env.VITE_OPEN === 'true',
      // 本地跨域代理. 目前注释的原因：暂时没有用途，server 端已经支持跨域
      // proxy: {
      //   ['/admin-api']: {
      //     target: env.VITE_BASE_URL,
      //     ws: false,
      //     changeOrigin: true,
      //     rewrite: (path) => path.replace(new RegExp(`^/admin-api`), ''),
      //   },
      // },
    },
    // 项目使用的vite插件。 单独提取到build/vite/plugin中管理
    plugins: createVitePlugins(),
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@import "./src/styles/variables.scss";',
          javascriptEnabled: true
        }
      }
    },
    resolve: {
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.scss', '.css'],
      alias: [
        {
          find: 'vue-i18n',
          replacement: 'vue-i18n/dist/vue-i18n.cjs.js'
        },
        {
          find: /\@\//,
          replacement: `${pathResolve('src')}/`
        }
      ]
    },
    build: {
      minify: isBuild ? 'esbuild' : false, // 使用更快的 esbuild 替代 terser
      outDir: env.VITE_OUT_DIR || 'dist',
      sourcemap: false, // 禁用 sourcemap 以节省内存
      reportCompressedSize: false, // 禁用压缩大小报告以节省内存
      chunkSizeWarningLimit: 2000,
      rollupOptions: {
        // 优化内存使用的配置
        maxParallelFileOps: 2, // 限制并行文件操作数量
        output: {
          // 更细粒度的代码分割以减少单个chunk的大小
          manualChunks: (id) => {
            // 第三方库分组
            if (id.includes('node_modules')) {
              if (id.includes('echarts')) {
                return 'echarts'
              }
              if (id.includes('amis')) {
                return 'amis-vendor'
              }
              if (id.includes('element-plus')) {
                return 'element-plus'
              }
              if (id.includes('vue') || id.includes('@vue')) {
                return 'vue-vendor'
              }
              if (id.includes('lodash') || id.includes('axios') || id.includes('dayjs')) {
                return 'utils-vendor'
              }
              // 其他第三方库
              return 'vendor'
            }
            // 按功能模块分割应用代码
            if (id.includes('/src/views/')) {
              const pathParts = id.split('/src/views/')[1].split('/')
              if (pathParts.length > 0) {
                return `views-${pathParts[0]}`
              }
            }
          },
          // 限制chunk大小
          chunkFileNames: (chunkInfo) => {
            const facadeModuleId = chunkInfo.facadeModuleId ? chunkInfo.facadeModuleId.split('/').pop() : 'chunk'
            return `js/[name]-[hash].js`
          }
        },
      },
    },
    optimizeDeps: {
      include,
      exclude,
      // 减少预构建的并发数以节省内存
      esbuildOptions: {
        target: 'es2015',
        // 限制并发构建数量
        logLevel: 'error'
      }
    },
    // 额外的内存优化配置
    esbuild: {
      // 在生产环境中移除console和debugger
      drop: isBuild ? ['console', 'debugger'] : [],
      // 使用更少的内存
      target: 'es2015'
    }
  }
}
