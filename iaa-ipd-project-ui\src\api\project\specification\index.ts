import request from '@/config/axios'

export const SpecificationApi = {
  /** 创建规格书模板 */
  createDocTemplate: (data: any) => {
    return request.post({ url: '/project/spec-template/create', data })
  },
  /** 获取规格数模板分页 */
  pageDocTemplate: (params: any) => {
    return request.get({ url: '/project/spec-template/page', params })
  },
  /** 获取规格书模板列表 */
  listDocTemplate: (name?: string) => {
    return request.get({ url: '/project/spec-template/list', params: { name } })
  },
  /** 通过模板编码获取历史模板列表 */
  getTemplateListByCode: (templateCode: string) => {
    return request.get({
      url: '/project/spec-template/get-template-list-by-code/' + templateCode
    })
  },
  /** 通过模板编码获取最新模板 */
  getLastTemplateByCode: (templateCode: string) => {
    return request.get({
      url: '/project/spec-template/get-last-template-by-code',
      params: { templateCode }
    })
  },
  /** 获取规格书分页 */
  pageSpecification: (params: any) => {
    return request.get({ url: '/project/specification/page', params })
  },
  /** 获取规格书列表 */
  listSpecification: (name: any) => {
    return request.get({ url: '/project/specification/list', params: { name } })
  },
  /** 创建规格书 */
  createSpecification: (data: any) => {
    return request.post({ url: '/project/specification/create', data })
  },
  exportSpecification: (id: number) => {
    return request.download({ url: '/project/specification/export/' + id })
  },
  /** 获取规格书 */
  getSpecification: (id: number) => {
    return request.get({ url: '/project/specification/get/' + id })
  },
  /** 获取规格书历史版本 */
  listHistoryByCode: (code: string) => {
    return request.get({ url: '/project/specification/list-history-by-code', params: { code } })
  }
}
