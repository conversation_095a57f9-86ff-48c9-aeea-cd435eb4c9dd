import request from '@/config/axios'

export const SecondDictApi = {
  /** 保存字典类型 */
  saveDictType: (params: any) => {
    return request.get({ url: '/project/second-dict/save', params })
  },
  /** 分页获取字典类型 */
  pageDictType: (params: any) => {
    return request.get({ url: '/project/second-dict/page', params })
  },
  /** 获取字典类型列表 */
  listDictType: () => {
    return request.get({ url: '/project/second-dict/list' })
  },
  /** 根据ID获取字典类型 */
  getDictType: (id: number) => {
    return request.get({ url: '/project/second-dict/get', params: { id } })
  },
  /** 删除字典类型 */
  deleteDictType: (id: number) => {
    return request.get({ url: '/project/second-dict/delete', params: { id } })
  },
  /** 获取字典数据 */
  listDictDataByDictTypeId: (dictTypeId: number) => {
    return request.get({ url: '/project/second-dict/list-data', params: { dictTypeId } })
  },
  /** 获取字典数据 */
  listDictDataByTypeCode: (code: string) => {
    return request.get({ url: '/project/second-dict/list-data-by-type-code', params: { code } })
  },
  /** 保存字典数据 */
  saveDictData: (params: any) => {
    return request.get({ url: '/project/second-dict/save-data', params })
  },
  /** 删除子项数据 */
  deleteDictData: (dataId: number) => {
    return request.get({ url: '/project/second-dict/delete-data', params: { dataId } })
  }
}
