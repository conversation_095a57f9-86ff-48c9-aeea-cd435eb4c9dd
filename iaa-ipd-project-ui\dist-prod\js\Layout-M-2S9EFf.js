import{n as U,av as hl,_ as N,aw as Ce,ax as oe,F as Y,d as Q,N as fl,M as Ie,J as Ne,K as gt,ay as gl,b as bl,az as wl,aA as yl,aB as xl}from"./views-Home-ewBLhuw2.js";import{aq as _l,ar as Ol,as as Pl,H as ne,at as kl,a1 as Ye,a2 as Ze,a0 as Ge,P as jl,Z as Cl,_ as Il,h as Ml,e as bt,M as $l,a as Ll,b as Me,L as Tl,G as Sl,au as Vl,av as Rl}from"./element-plus-DgaixBsQ.js";import{l as A,m as O,J as W,M as I,u as a,r as M,S as o,F as G,e as v,aE as ie,y as wt,X as Je,d as xe,w as ge,K as w,p as B,a7 as $e,Q as X,R as j,G as zl,aT as yt,ah as Qe,q as f,P as me,a1 as Dl,n as Xe,E as El,aU as Al,O as Ul,B as Le,az as Fl,aA as Bl,aV as Wl,aW as ql,Y as Hl,L as ce,T as Te,U as xt,a5 as Nl,aX as Yl,$ as Zl,ap as Se}from"./vue-vendor-BbSoq9WN.js";import{e as Ve,f as Gl,_ as Jl,g as Ql,h as Xl}from"./views-Login-BCX8kkKD.js";import{a as te,p as Ke,d as Kl,u as ea,C as ta}from"./views-Error-Cx8xxY17.js";import{m as et}from"./utils-vendor-Vtb-rlR8.js";import{u as Re}from"./views-bpm-BO-XbtTX.js";import{g as la,a as aa}from"./views-system-BZfQ0tqU.js";import{a as ra}from"./views-Profile-4epX3JDT.js";import{bB as oa}from"./vendor-DLCNhz7G.js";import{_ as na}from"../assets/index-DRUx63iF.js";import{S as ia}from"./views-project-C9zQgjvz.js";import"./echarts-D356XqSJ.js";var ca=Object.defineProperty,sa=Object.defineProperties,ua=Object.getOwnPropertyDescriptors,_t=Object.getOwnPropertySymbols,pa=Object.prototype.hasOwnProperty,da=Object.prototype.propertyIsEnumerable,Ot=(l,t,e)=>t in l?ca(l,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):l[t]=e,va=(l,t)=>{for(var e in t||(t={}))pa.call(t,e)&&Ot(l,e,t[e]);if(_t)for(var e of _t(t))da.call(t,e)&&Ot(l,e,t[e]);return l},ma=(l,t)=>sa(l,ua(t));const ha=A(ma(va({},{name:"BackTop"}),{__name:"Backtop",setup(l){const{getPrefixCls:t,variables:e}=U(),n=t("backtop");return(r,u)=>(O(),W(a(_l),{class:I(`${a(n)}-backtop`),target:`.${a(e).namespace}-layout-content-scrollbar .${a(e).elNamespace}-scrollbar__wrap`},null,8,["class","target"]))}}));var fa=Object.defineProperty,ga=Object.defineProperties,ba=Object.getOwnPropertyDescriptors,Pt=Object.getOwnPropertySymbols,wa=Object.prototype.hasOwnProperty,ya=Object.prototype.propertyIsEnumerable,kt=(l,t,e)=>t in l?fa(l,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):l[t]=e,xa=(l,t)=>{for(var e in t||(t={}))wa.call(t,e)&&kt(l,e,t[e]);if(Pt)for(var e of Pt(t))ya.call(t,e)&&kt(l,e,t[e]);return l},_a=(l,t)=>ga(l,ba(t));const Oa=(l,t)=>(hl(l,e=>e.path===t)||[]).map(e=>e.path),Pa=(l=[],t)=>{const e=M(),n=l.filter(r=>{var u;return((u=r.meta)!=null?u:{}).hidden?!1:(e.value=r,!0)});return n.length===1?{oneShowingChild:!0,onlyOneChild:a(e)}:n.length?{oneShowingChild:!1,onlyOneChild:a(e)}:(e.value=_a(xa({},t),{path:"",noShowingChildren:!0}),{oneShowingChild:!0,onlyOneChild:a(e)})},ka=()=>({renderMenuTitle:l=>{const{t}=te(),{title:e="Please set title",icon:n}=l;return n?o(G,null,[o(N,{icon:l.icon},null),o("span",{class:"v-menu__title overflow-hidden overflow-ellipsis whitespace-nowrap"},[t(e)])]):o("span",{class:"v-menu__title overflow-hidden overflow-ellipsis whitespace-nowrap"},[t(e)])}}),{renderMenuTitle:jt}=ka(),ja=()=>{const l=(t,e="/")=>t.filter(n=>{var r;return!((r=n.meta)!=null&&r.hidden)}).map(n=>{const r=n.meta??{},{oneShowingChild:u,onlyOneChild:i}=Pa(n.children,n),c=Ce(n.path)?n.path:oe(e,n.path);return u&&(!(i!=null&&i.children)||i!=null&&i.noShowingChildren)&&!(r!=null&&r.alwaysShow)?o(Ol,{index:i?oe(c,i.path):c},{default:()=>jt(i?i==null?void 0:i.meta:r)}):o(Pl,{index:c},{title:()=>jt(r),default:()=>l(n.children,c)})});return{renderMenuItem:l}};function Ca(l){return typeof l=="function"||Object.prototype.toString.call(l)==="[object Object]"&&!wt(l)}const{getPrefixCls:Ia}=U(),_e=Ia("menu"),Ma=A({name:"Menu",props:{menuSelect:{type:Function,default:void 0}},setup(l){const t=Y(),e=v(()=>t.getLayout),{push:n,currentRoute:r}=ie(),u=Ve(),i=v(()=>["classic","topLeft","cutMenu"].includes(a(e))?"vertical":"horizontal"),c=v(()=>a(e)==="cutMenu"?u.getMenuTabRouters:u.getRouters),s=v(()=>t.getCollapse),p=v(()=>t.getUniqueOpened),h=v(()=>{const{meta:y,path:V}=a(r);return y.activeMenu?y.activeMenu:V}),g=y=>{l.menuSelect&&l.menuSelect(y),Ce(y)?window.open(y):n(y)},P=()=>{if(a(e)==="top")return k();{let y;return o(ne,null,Ca(y=k())?y:{default:()=>[y]})}},k=()=>o(kl,{defaultActive:a(h),mode:a(i),collapse:a(e)==="top"||a(e)==="cutMenu"?!1:a(s),uniqueOpened:a(e)==="top"?!1:a(p),backgroundColor:"var(--left-menu-bg-color)",textColor:"var(--left-menu-text-color)",activeTextColor:"var(--left-menu-text-active-color)",popperClass:a(i)==="vertical"?`${_e}-popper--vertical`:`${_e}-popper--horizontal`,onSelect:g},{default:()=>{const{renderMenuItem:y}=ja(a(i));return y(a(c))}});return()=>o("div",{id:_e,class:[`${_e} ${_e}__${a(i)}`,"h-[100%] overflow-hidden flex-col bg-[var(--left-menu-bg-color)]",{"w-[var(--left-menu-min-width)]":a(s)&&a(e)!=="cutMenu","w-[var(--left-menu-max-width)]":!a(s)&&a(e)!=="cutMenu"}]},[P()])}}),ze=Q(Ma,[["__scopeId","data-v-b8de0a58"]]),De=Je({}),$a=l=>{var t;for(const e of l){const n=(t=e.meta)!=null?t:{};n!=null&&n.hidden||(De[e.path]=[])}},Ct=(l,t)=>{var e;const n=[];for(const r of l){let u=null;const i=(e=r.meta)!=null?e:{};if(!i.hidden||i.canTo){const c=Oa(t,r.path),s=Ce(r.path)?r.path:c.join("/");u=et(r),u.path=s,r.children&&u&&(u.children=Ct(r.children,t)),u&&n.push(u),c.length&&Reflect.has(De,c[0])&&De[c[0]].push(s)}}return n},{getPrefixCls:La,variables:Ta}=U(),tt=La("tab-menu"),Sa=A({name:"TabMenu",setup(){const{push:l,currentRoute:t}=ie(),{t:e}=te(),n=Y(),r=v(()=>n.getCollapse),u=v(()=>n.getFixedMenu),i=Ve(),c=v(()=>i.getRouters),s=v(()=>a(c).filter(d=>{var m;return!((m=d==null?void 0:d.meta)!=null&&m.hidden)})),p=()=>{n.setCollapse(!a(r))};xe(()=>{var d;if(a(u)){const m=`/${a(t).path.split("/")[1]}`,C=(d=a(s).find(_=>{var R,$,L;return(((R=_.meta)==null?void 0:R.alwaysShow)||(($=_==null?void 0:_.children)==null?void 0:$.length)&&((L=_==null?void 0:_.children)==null?void 0:L.length)>1)&&_.path===m}))==null?void 0:d.children;P.value=m,C&&i.setMenuTabRouters(et(C).map(_=>(_.path=oe(a(P),_.path),_)))}}),ge(()=>c.value,d=>{$a(d),Ct(d,d)},{immediate:!0,deep:!0});const h=M(!0);ge(()=>r.value,d=>{d?h.value=!d:setTimeout(()=>{h.value=!d},200)});const g=M(!!a(u)),P=M(""),k=d=>{if(Ce(d.path)){window.open(d.path);return}const m=d.children?d.path:d.path.split("/")[0],C=a(P);P.value=d.children?d.path:d.path.split("/")[0],d.children?((m===C||!a(g))&&(g.value=a(u)?!0:!a(g)),a(g)&&i.setMenuTabRouters(et(d.children).map(_=>(_.path=oe(a(P),_.path),_)))):(l(d.path),i.setMenuTabRouters([]),g.value=!1)},y=d=>{const{path:m}=a(t);return!!De[d].includes(m)},V=()=>{!a(g)||a(u)||(g.value=!1)};return()=>o("div",{id:`${Ta.namespace}-menu`,class:[tt,"relative bg-[var(--left-menu-bg-color)] top-1px layout-border__right",{"w-[var(--tab-menu-max-width)]":!a(r),"w-[var(--tab-menu-min-width)]":a(r)}],onMouseleave:V},[o(ne,{class:"!h-[calc(100%-var(--tab-menu-collapse-height)-1px)]"},{default:()=>[o("div",null,{default:()=>a(s).map(d=>{var C,_,R,$,L,H;const m=(C=d.meta)!=null&&C.alwaysShow||(_=d==null?void 0:d.children)!=null&&_.length&&((R=d==null?void 0:d.children)==null?void 0:R.length)>1?d:{...(d==null?void 0:d.children)&&(d==null?void 0:d.children[0]),path:oe(d.path,($=(d==null?void 0:d.children)&&(d==null?void 0:d.children[0]))==null?void 0:$.path)};return o("div",{class:[`${tt}__item`,"text-center text-12px relative py-12px cursor-pointer",{"is-active":y(d.path)}],onClick:()=>{k(m)}},[o("div",null,[o(N,{icon:(L=m==null?void 0:m.meta)==null?void 0:L.icon},null)]),a(h)?o("p",{class:"mt-5px break-words px-2px text-1.4vh"},[e((H=m.meta)==null?void 0:H.title)]):void 0])})})]}),o("div",{class:[`${tt}--collapse`,"text-center h-[var(--tab-menu-collapse-height)] leading-[var(--tab-menu-collapse-height)] cursor-pointer"],onClick:p},[o(N,{icon:a(r)?"ep:d-arrow-right":"ep:d-arrow-left"},null)]),o(ze,{class:["!absolute top-0 z-11",{"!left-[var(--tab-menu-min-width)]":a(r),"!left-[var(--tab-menu-max-width)]":!a(r),"!w-[calc(var(--left-menu-max-width)+1px)]":a(g)||a(u),"!w-0":!a(g)&&!a(u)}],style:"transition: width var(--transition-time-02), left var(--transition-time-02);"},null)])}}),Va=Q(Sa,[["__scopeId","data-v-5698ef1d"]]);var Ra=Object.defineProperty,za=Object.defineProperties,Da=Object.getOwnPropertyDescriptors,It=Object.getOwnPropertySymbols,Ea=Object.prototype.hasOwnProperty,Aa=Object.prototype.propertyIsEnumerable,Mt=(l,t,e)=>t in l?Ra(l,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):l[t]=e,Ua=(l,t)=>{for(var e in t||(t={}))Ea.call(t,e)&&Mt(l,e,t[e]);if(It)for(var e of It(t))Aa.call(t,e)&&Mt(l,e,t[e]);return l},Fa=(l,t)=>za(l,Da(t));const $t=(l,t="")=>{let e=[];return l.forEach(n=>{const r=n.meta,u=oe(t,n.path);if(r!=null&&r.affix&&e.push(Fa(Ua({},n),{path:u,fullPath:u})),n.children){const i=$t(n.children,u);i.length>=1&&(e=[...e,...i])}}),e};var Ba=Object.defineProperty,Wa=Object.defineProperties,qa=Object.getOwnPropertyDescriptors,Lt=Object.getOwnPropertySymbols,Ha=Object.prototype.hasOwnProperty,Na=Object.prototype.propertyIsEnumerable,Tt=(l,t,e)=>t in l?Ba(l,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):l[t]=e,Ya=(l,t)=>{for(var e in t||(t={}))Ha.call(t,e)&&Tt(l,e,t[e]);if(Lt)for(var e of Lt(t))Na.call(t,e)&&Tt(l,e,t[e]);return l},Za=(l,t)=>Wa(l,qa(t));const St=A(Za(Ya({},{name:"ContextMenu"}),{__name:"ContextMenu",props:{schema:{type:Array,default:()=>[]},trigger:{type:String,default:"contextmenu"},tagItem:{type:Object,default:()=>({})}},emits:["visibleChange"],setup(l,{expose:t,emit:e}){const{getPrefixCls:n}=U(),r=n("context-menu"),{t:u}=te(),i=e,c=l,s=g=>{g.command&&g.command(g)},p=g=>{i("visibleChange",g,c.tagItem)},h=M();return t({elDropdownMenuRef:h,tagItem:c.tagItem}),(g,P)=>{const k=N,y=Ye,V=Ze,d=Ge;return O(),W(d,{ref_key:"elDropdownMenuRef",ref:h,class:I(a(r)),trigger:l.trigger,placement:"bottom-start","popper-class":"v-context-menu-popper",onCommand:s,onVisibleChange:p},{dropdown:w(()=>[o(V,null,{default:w(()=>[(O(!0),B(G,null,$e(l.schema,(m,C)=>(O(),W(y,{key:`dropdown${C}`,command:m,disabled:m.disabled,divided:m.divided},{default:w(()=>[o(k,{icon:m.icon},null,8,["icon"]),X(" "+j(a(u)(m.label)),1)]),_:2},1032,["command","disabled","divided"]))),128))]),_:1})]),default:w(()=>[zl(g.$slots,"default")]),_:3},8,["class","trigger"])}}})),Ga=(l,t,e,n)=>(l/=n/2,l<1?e/2*l*l+t:(l--,-e/2*(l*(l-2)-1)+t)),Ja=(l,t,e)=>{l[t]=e};function Oe({el:l,position:t="scrollLeft",to:e,duration:n=500,callback:r}){const u=M(!1),i=l[t],c=e-i,s=20;let p=0;function h(){if(!a(u))return;p+=s;const k=Ga(p,i,c,n);Ja(l,t,k),p<n&&a(u)?requestAnimationFrame(h):r&&r()}function g(){u.value=!0,h()}function P(){u.value=!1}return{start:g,stop:P}}var Qa=Object.defineProperty,Vt=Object.getOwnPropertySymbols,Xa=Object.prototype.hasOwnProperty,Ka=Object.prototype.propertyIsEnumerable,Rt=(l,t,e)=>t in l?Qa(l,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):l[t]=e,er=(l,t)=>{for(var e in t||(t={}))Xa.call(t,e)&&Rt(l,e,t[e]);if(Vt)for(var e of Vt(t))Ka.call(t,e)&&Rt(l,e,t[e]);return l},zt=(l,t,e)=>new Promise((n,r)=>{var u=s=>{try{c(e.next(s))}catch(p){r(p)}},i=s=>{try{c(e.throw(s))}catch(p){r(p)}},c=s=>s.done?n(s.value):Promise.resolve(s.value).then(u,i);c((e=e.apply(l,t)).next())});const tr=["id"],lr={class:"flex-1 overflow-hidden"},ar={class:"h-full flex"},rr=["onClick"],or=A({__name:"TagsView",setup(l){const{getPrefixCls:t}=U(),e=t("tags-view"),{t:n}=te(),{currentRoute:r,push:u,replace:i}=ie(),c=Ve(),s=v(()=>c.getRouters),p=Re(),h=v(()=>p.getVisitedViews),g=M([]),P=Y(),k=v(()=>P.getTagsViewImmerse),y=v(()=>P.getTagsViewIcon),V=v(()=>P.getIsDark),d=()=>{g.value=$t(a(s));for(const x of a(g))x.name&&p.addVisitedView(x)},m=M(),C=()=>{const{name:x}=a(r);return x&&(m.value=a(r),p.addView(a(r))),!1},_=x=>{var D;(D=x==null?void 0:x.meta)!=null&&D.affix||(p.delView(x),Z(x)&&T())},R=()=>{p.delAllViews(),T()},$=()=>{p.delOthersViews(a(m))},L=x=>zt(this,null,function*(){if(!x)return;p.delCachedView();const{path:D,query:E}=x;yield Xe(),i({path:"/redirect"+D,query:E})}),H=()=>{p.delLeftViews(a(m))},z=()=>{p.delRightViews(a(m))},T=()=>{const x=p.getVisitedViews.slice(-1)[0];if(x)u(x);else{if(a(r).path===c.getAddRouters[0].path||a(r).path===c.getAddRouters[0].redirect){C();return}u("/")}},be=()=>zt(this,null,function*(){yield Xe();for(const x of a(h))if(x.fullPath===a(r).path){de(x),x.fullPath!==a(r).fullPath&&p.updateVisitedView(a(r));break}}),fe=yt(),de=x=>{var D;const E=(D=a(He))==null?void 0:D.wrapRef;let q=null,ve=null;const ae=a(fe);if(ae.length>0&&(q=ae[0],ve=ae[ae.length-1]),(q==null?void 0:q.to).fullPath===x.fullPath){const{start:ee}=Oe({el:E,position:"scrollLeft",to:0,duration:500});ee()}else if((ve==null?void 0:ve.to).fullPath===x.fullPath){const{start:ee}=Oe({el:E,position:"scrollLeft",to:E.scrollWidth-E.offsetWidth,duration:500});ee()}else{const ee=ae.findIndex(J=>(J==null?void 0:J.to).fullPath===x.fullPath),we=document.getElementsByClassName(`${e}__item`),re=we[ee-1],je=we[ee+1],b=je.offsetLeft+je.offsetWidth+4,ye=re.offsetLeft-4;if(b>a(ke)+E.offsetWidth){const{start:J}=Oe({el:E,position:"scrollLeft",to:b-E.offsetWidth,duration:500});J()}else if(ye<a(ke)){const{start:J}=Oe({el:E,position:"scrollLeft",to:ye,duration:500});J()}}},Z=x=>x.path===a(r).path,rt=yt(),dl=(x,D)=>{if(x)for(const E of a(rt)){const q=E.elDropdownMenuRef;D.fullPath!==E.tagItem.fullPath&&(q==null||q.handleClose())}},He=M(),ke=M(0),vl=({scrollLeft:x})=>{ke.value=x},ot=x=>{var D;const E=(D=a(He))==null?void 0:D.wrapRef,{start:q}=Oe({el:E,position:"scrollLeft",to:a(ke)+x,duration:500});q()};return xe(()=>{d(),C()}),ge(()=>r.value,()=>{C(),be()}),(x,D)=>{var E,q,ve,ae,ee,we;const re=N,je=Qe("router-link");return O(),B("div",{id:a(e),class:I([a(e),"relative w-full flex bg-[#fff] dark:bg-[var(--el-bg-color)]"])},[f("span",{class:I([k.value?"":`${a(e)}__tool ${a(e)}__tool--first`,"h-[var(--tags-view-height)] w-[var(--tags-view-height)] flex cursor-pointer items-center justify-center"]),onClick:D[0]||(D[0]=b=>ot(-200))},[o(re,{icon:"ep:d-arrow-left",color:"var(--el-text-color-placeholder)","hover-color":V.value?"#fff":"var(--el-color-black)"},null,8,["hover-color"])],2),f("div",lr,[o(a(ne),{ref_key:"scrollbarRef",ref:He,class:"h-full",onScroll:vl},{default:w(()=>[f("div",ar,[(O(!0),B(G,null,$e(h.value,b=>{var ye,J,nt,it,ct,st,ut,pt,dt;return O(),W(a(St),{ref_for:!0,ref:a(rt).set,schema:[{icon:"ep:refresh",label:a(n)("common.reload"),disabled:((ye=m.value)==null?void 0:ye.fullPath)!==b.fullPath,command:()=>{L(b)}},{icon:"ep:close",label:a(n)("common.closeTab"),disabled:!!((J=h.value)!=null&&J.length)&&((nt=m.value)==null?void 0:nt.meta.affix),command:()=>{_(b)}},{divided:!0,icon:"ep:d-arrow-left",label:a(n)("common.closeTheLeftTab"),disabled:!!((it=h.value)!=null&&it.length)&&(b.fullPath===h.value[0].fullPath||((ct=m.value)==null?void 0:ct.fullPath)!==b.fullPath),command:()=>{H()}},{icon:"ep:d-arrow-right",label:a(n)("common.closeTheRightTab"),disabled:!!((st=h.value)!=null&&st.length)&&(b.fullPath===h.value[h.value.length-1].fullPath||((ut=m.value)==null?void 0:ut.fullPath)!==b.fullPath),command:()=>{z()}},{divided:!0,icon:"ep:discount",label:a(n)("common.closeOther"),disabled:((pt=m.value)==null?void 0:pt.fullPath)!==b.fullPath,command:()=>{$()}},{icon:"ep:minus",label:a(n)("common.closeAll"),command:()=>{R()}}],key:b.fullPath,"tag-item":b,class:I([`${a(e)}__item`,k.value?`${a(e)}__item--immerse`:"",y.value?`${a(e)}__item--icon`:"",k.value&&y.value?`${a(e)}__item--immerse--icon`:"",(dt=b==null?void 0:b.meta)!=null&&dt.affix?`${a(e)}__item--affix`:"",{"is-active":Z(b)}]),onVisibleChange:dl},{default:w(()=>[f("div",null,[o(je,{ref_for:!0,ref:a(fe).set,to:er({},b),custom:""},{default:w(({navigate:ml})=>{var vt,mt,ht,ft;return[f("div",{onClick:ml,class:I(`h-full flex items-center justify-center whitespace-nowrap pl-15px ${a(e)}__item--label`)},[y.value&&((vt=b==null?void 0:b.meta)!=null&&vt.icon||b!=null&&b.matched&&b.matched[0]&&(mt=b.matched[b.matched.length-1].meta)!=null&&mt.icon)?(O(),W(re,{key:0,icon:((ht=b==null?void 0:b.meta)==null?void 0:ht.icon)||b.matched[b.matched.length-1].meta.icon,size:12,class:"mr-5px"},null,8,["icon"])):me("",!0),X(" "+j(a(n)((ft=b==null?void 0:b.meta)==null?void 0:ft.title))+" ",1),o(re,{class:I(`${a(e)}__item--close`),color:"#333",icon:"ep:close",size:12,onClick:Dl(En=>_(b),["prevent","stop"])},null,8,["class","onClick"])],10,rr)]}),_:2},1032,["to"])])]),_:2},1032,["schema","tag-item","class"])}),128))])]),_:1},512)]),f("span",{class:I([k.value?"":`${a(e)}__tool`,"h-[var(--tags-view-height)] w-[var(--tags-view-height)] flex cursor-pointer items-center justify-center"]),onClick:D[1]||(D[1]=b=>ot(200))},[o(re,{icon:"ep:d-arrow-right",color:"var(--el-text-color-placeholder)","hover-color":V.value?"#fff":"var(--el-color-black)"},null,8,["hover-color"])],2),f("span",{class:I([k.value?"":`${a(e)}__tool`,"h-[var(--tags-view-height)] w-[var(--tags-view-height)] flex cursor-pointer items-center justify-center"]),onClick:D[2]||(D[2]=b=>L(m.value))},[o(re,{icon:"ep:refresh-right",color:"var(--el-text-color-placeholder)","hover-color":V.value?"#fff":"var(--el-color-black)"},null,8,["hover-color"])],2),o(a(St),{trigger:"click",schema:[{icon:"ep:refresh",label:a(n)("common.reload"),command:()=>{L(m.value)}},{icon:"ep:close",label:a(n)("common.closeTab"),disabled:!!((E=h.value)!=null&&E.length)&&((q=m.value)==null?void 0:q.meta.affix),command:()=>{_(m.value)}},{divided:!0,icon:"ep:d-arrow-left",label:a(n)("common.closeTheLeftTab"),disabled:!!((ve=h.value)!=null&&ve.length)&&((ae=m.value)==null?void 0:ae.fullPath)===h.value[0].fullPath,command:()=>{H()}},{icon:"ep:d-arrow-right",label:a(n)("common.closeTheRightTab"),disabled:!!((ee=h.value)!=null&&ee.length)&&((we=m.value)==null?void 0:we.fullPath)===h.value[h.value.length-1].fullPath,command:()=>{z()}},{divided:!0,icon:"ep:discount",label:a(n)("common.closeOther"),command:()=>{$()}},{icon:"ep:minus",label:a(n)("common.closeAll"),command:()=>{R()}}]},{default:w(()=>[f("span",{class:I([k.value?"":`${a(e)}__tool`,"block h-[var(--tags-view-height)] w-[var(--tags-view-height)] flex cursor-pointer items-center justify-center"])},[o(re,{icon:"ep:menu",color:"var(--el-text-color-placeholder)","hover-color":V.value?"#fff":"var(--el-color-black)"},null,8,["hover-color"])],2)]),_:1},8,["schema"])],10,tr)}}}),Ee=Q(or,[["__scopeId","data-v-ff2e1e54"]]);var nr=Object.defineProperty,ir=Object.defineProperties,cr=Object.getOwnPropertyDescriptors,Dt=Object.getOwnPropertySymbols,sr=Object.prototype.hasOwnProperty,ur=Object.prototype.propertyIsEnumerable,Et=(l,t,e)=>t in l?nr(l,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):l[t]=e,pr=(l,t)=>{for(var e in t||(t={}))sr.call(t,e)&&Et(l,e,t[e]);if(Dt)for(var e of Dt(t))ur.call(t,e)&&Et(l,e,t[e]);return l},dr=(l,t)=>ir(l,cr(t));const vr=f("img",{class:"h-[calc(var(--logo-height)-10px)] w-[calc(var(--logo-height)-10px)]",src:Gl,style:{"object-fit":"contain"}},null,-1),Ae=A(dr(pr({},{name:"Logo"}),{__name:"Logo",setup(l){const{getPrefixCls:t}=U(),e=t("logo"),n=Y(),r=M(!0),u=v(()=>n.getTitle),i=v(()=>n.getLayout),c=v(()=>n.getCollapse);return xe(()=>{a(c)&&(r.value=!1)}),ge(()=>c.value,s=>{if(a(i)==="topLeft"||a(i)==="cutMenu"){r.value=!0;return}s?r.value=!s:setTimeout(()=>{r.value=!s},400)}),ge(()=>i.value,s=>{s==="top"||s==="cutMenu"?r.value=!0:a(c)?r.value=!1:r.value=!0}),(s,p)=>{const h=Qe("router-link");return O(),B("div",null,[o(h,{class:I([a(e),i.value!=="classic"?`${a(e)}__Top`:"","flex !h-[var(--logo-height)] items-center cursor-pointer pl-8px relative decoration-none overflow-hidden"]),to:"/"},{default:w(()=>[vr,r.value?(O(),B("div",{key:0,class:I(["ml-10px text-16px font-700",{"text-[var(--logo-title-text-color)]":i.value==="classic","text-[var(--top-header-text-color)]":i.value==="topLeft"||i.value==="top"||i.value==="cutMenu"}])},j(u.value),3)):me("",!0)]),_:1},8,["class"])])}}}));var mr=Object.defineProperty,hr=Object.defineProperties,fr=Object.getOwnPropertyDescriptors,At=Object.getOwnPropertySymbols,gr=Object.prototype.hasOwnProperty,br=Object.prototype.propertyIsEnumerable,Ut=(l,t,e)=>t in l?mr(l,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):l[t]=e,wr=(l,t)=>{for(var e in t||(t={}))gr.call(t,e)&&Ut(l,e,t[e]);if(At)for(var e of At(t))br.call(t,e)&&Ut(l,e,t[e]);return l},yr=(l,t)=>hr(l,fr(t));const xr={class:"text-14px"},_r=A(yr(wr({},{name:"Footer"}),{__name:"Footer",setup(l){const{getPrefixCls:t}=U(),e=t("footer"),n=Y(),r=v(()=>n.getTitle);return(u,i)=>(O(),B("div",{class:I([a(e),"h-[var(--app-footer-height)] bg-[var(--app-content-bg-color)] text-center leading-[var(--app-footer-height)] text-[var(--el-text-color-placeholder)] dark:bg-[var(--el-bg-color)]"])},[f("span",xr,"Copyright \xA92022-"+j(a(r)),1)],2))}}));var Or=Object.defineProperty,Pr=Object.defineProperties,kr=Object.getOwnPropertyDescriptors,Ft=Object.getOwnPropertySymbols,jr=Object.prototype.hasOwnProperty,Cr=Object.prototype.propertyIsEnumerable,Bt=(l,t,e)=>t in l?Or(l,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):l[t]=e,Ir=(l,t)=>{for(var e in t||(t={}))jr.call(t,e)&&Bt(l,e,t[e]);if(Ft)for(var e of Ft(t))Cr.call(t,e)&&Bt(l,e,t[e]);return l},Mr=(l,t)=>Pr(l,kr(t));const Ue=A(Mr(Ir({},{name:"AppView"}),{__name:"AppView",setup(l){const t=Y(),e=v(()=>t.getLayout),n=v(()=>t.getFixedHeader),r=v(()=>t.getFooter),u=Re(),i=v(()=>u.getCachedViews),c=v(()=>t.getTagsView),s=M(!0);return El("reload",()=>{s.value=!1,Xe(()=>s.value=!0)}),(p,h)=>{const g=Qe("router-view");return O(),B(G,null,[f("section",{class:I(["p-[var(--app-content-padding)] w-[calc(100%-var(--app-content-padding)-var(--app-content-padding))] bg-[var(--app-content-bg-color)] dark:bg-[var(--el-bg-color)]",{"!min-h-[calc(100%-var(--app-content-padding)-var(--app-content-padding)-var(--app-footer-height))]":a(n)&&(a(e)==="classic"||a(e)==="topLeft"||a(e)==="top")&&a(r)||!a(c)&&a(e)==="top"&&a(r),"!min-h-[calc(100%-var(--app-content-padding)-var(--app-content-padding)-var(--app-footer-height)-var(--tags-view-height))]":a(c)&&a(e)==="top"&&a(r),"!min-h-[calc(100%-var(--tags-view-height)-var(--app-content-padding)-var(--app-content-padding)-var(--top-tool-height)-var(--app-footer-height))]":!a(n)&&a(e)==="classic"&&a(r),"!min-h-[calc(100%-var(--tags-view-height)-var(--app-content-padding)-var(--app-content-padding)-var(--app-footer-height))]":!a(n)&&a(e)==="topLeft"&&a(r),"!min-h-[calc(100%-var(--top-tool-height)-var(--app-content-padding)-var(--app-content-padding))]":a(n)&&a(e)==="cutMenu"&&a(r),"!min-h-[calc(100%-var(--top-tool-height)-var(--app-content-padding)-var(--app-content-padding)-var(--tags-view-height))]":!a(n)&&a(e)==="cutMenu"&&a(r)}])},[a(s)?(O(),W(g,{key:0},{default:w(({Component:P,route:k})=>[(O(),W(Al,{include:a(i)},[(O(),W(Ul(P),{key:k.fullPath}))],1032,["include"]))]),_:1})):me("",!0)],2),a(r)?(O(),W(a(_r),{key:0})):me("",!0)],64)}}}));var $r=Object.defineProperty,Lr=Object.defineProperties,Tr=Object.getOwnPropertyDescriptors,Wt=Object.getOwnPropertySymbols,Sr=Object.prototype.hasOwnProperty,Vr=Object.prototype.propertyIsEnumerable,qt=(l,t,e)=>t in l?$r(l,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):l[t]=e,Rr=(l,t)=>{for(var e in t||(t={}))Sr.call(t,e)&&qt(l,e,t[e]);if(Wt)for(var e of Wt(t))Vr.call(t,e)&&qt(l,e,t[e]);return l},zr=(l,t)=>Lr(l,Tr(t)),Ht=(l,t,e)=>new Promise((n,r)=>{var u=s=>{try{c(e.next(s))}catch(p){r(p)}},i=s=>{try{c(e.throw(s))}catch(p){r(p)}},c=s=>s.done?n(s.value):Promise.resolve(s.value).then(u,i);c((e=e.apply(l,t)).next())});const Dr=l=>(Fl("data-v-27613469"),l=l(),Bl(),l),Er={class:"message"},Ar=Dr(()=>f("img",{alt:"",class:"message-icon",src:Ie},null,-1)),Ur={class:"message-content"},Fr={class:"message-title"},Br={class:"message-date"},Wr={style:{"margin-top":"10px","text-align":"right"}},qr=A(zr(Rr({},{name:"Message"}),{__name:"Message",setup(l){const{push:t}=ie(),e=M("notice"),n=M(0),r=M([]),u=()=>Ht(this,null,function*(){r.value=yield la(),n.value=0}),i=()=>Ht(this,null,function*(){aa().then(s=>{n.value=s})}),c=()=>{t({name:"MyNotifyMessage"})};return xe(()=>{i(),setInterval(()=>{i()},1e3*60*5)}),(s,p)=>{const h=N,g=jl,P=ne,k=Cl,y=Il,V=Jl,d=Ml;return O(),B("div",Er,[o(d,{width:400,placement:"bottom",trigger:"click"},{reference:w(()=>[o(g,{"is-dot":a(n)>0,class:"item"},{default:w(()=>[o(h,{size:18,class:"cursor-pointer",icon:"ep:bell",onClick:u})]),_:1},8,["is-dot"])]),default:w(()=>[o(y,{modelValue:a(e),"onUpdate:modelValue":p[0]||(p[0]=m=>Le(e)?e.value=m:null)},{default:w(()=>[o(k,{label:"\u6211\u7684\u7AD9\u5185\u4FE1",name:"notice"},{default:w(()=>[o(P,{class:"message-list"},{default:w(()=>[(O(!0),B(G,null,$e(a(r),m=>(O(),B("div",{key:m.id,class:"message-item"},[Ar,f("div",Ur,[f("span",Fr,j(m.templateNickname)+"\uFF1A"+j(m.templateContent),1),f("span",Br,j(a(fl)(m.createTime)),1)])]))),128))]),_:1})]),_:1})]),_:1},8,["modelValue"]),f("div",Wr,[o(V,{preIcon:"ep:view",title:"\u67E5\u770B\u5168\u90E8",type:"primary",onClick:c})])]),_:1})])}}})),Hr=Q(qr,[["__scopeId","data-v-27613469"]]);var Nr=Object.defineProperty,Yr=Object.defineProperties,Zr=Object.getOwnPropertyDescriptors,Nt=Object.getOwnPropertySymbols,Gr=Object.prototype.hasOwnProperty,Jr=Object.prototype.propertyIsEnumerable,Yt=(l,t,e)=>t in l?Nr(l,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):l[t]=e,Qr=(l,t)=>{for(var e in t||(t={}))Gr.call(t,e)&&Yt(l,e,t[e]);if(Nt)for(var e of Nt(t))Jr.call(t,e)&&Yt(l,e,t[e]);return l},Xr=(l,t)=>Yr(l,Zr(t));const Kr=A(Xr(Qr({},{name:"Collapse"}),{__name:"Collapse",props:{color:Ke.string.def("")},setup(l){const{getPrefixCls:t}=U(),e=t("collapse"),n=Y(),r=v(()=>n.getCollapse),u=()=>{const i=a(r);n.setCollapse(!i)};return(i,c)=>{const s=N;return O(),B("div",{class:I(a(e)),onClick:u},[o(s,{color:l.color,icon:a(r)?"ep:expand":"ep:fold",size:18,class:"cursor-pointer"},null,8,["color","icon"])],2)}}})),lt=oa("lock",{state:()=>({lockInfo:{}}),getters:{getLockInfo(){return this.lockInfo}},actions:{setLockInfo(l){this.lockInfo=l},resetLockInfo(){this.lockInfo={}},unLock(l){var t;return((t=this.lockInfo)==null?void 0:t.password)===l?(this.resetLockInfo(),!0):!1}},persist:!0});var eo=Object.defineProperty,to=Object.defineProperties,lo=Object.getOwnPropertyDescriptors,Zt=Object.getOwnPropertySymbols,ao=Object.prototype.hasOwnProperty,ro=Object.prototype.propertyIsEnumerable,Gt=(l,t,e)=>t in l?eo(l,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):l[t]=e,oo=(l,t)=>{for(var e in t||(t={}))ao.call(t,e)&&Gt(l,e,t[e]);if(Zt)for(var e of Zt(t))ro.call(t,e)&&Gt(l,e,t[e]);return l},no=(l,t)=>to(l,lo(t)),io=(l,t,e)=>new Promise((n,r)=>{var u=s=>{try{c(e.next(s))}catch(p){r(p)}},i=s=>{try{c(e.throw(s))}catch(p){r(p)}},c=s=>s.done?n(s.value):Promise.resolve(s.value).then(u,i);c((e=e.apply(l,t)).next())});const co={class:"flex flex-col items-center"},so=["src"],uo={class:"text-14px my-10px text-[var(--top-header-text-color)]"},po=A({__name:"LockDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue"],setup(l,{emit:t}){const{getPrefixCls:e}=U(),n=e("lock-dialog"),{required:r}=Ql(),{t:u}=te(),i=lt(),c=l,s=Ne(),p=v(()=>{var C;return(C=s.user.avatar)!=null?C:Ie}),h=v(()=>{var C;return(C=s.user.nickname)!=null?C:"Admin"}),g=t,P=v({get:()=>c.modelValue,set:C=>{g("update:modelValue",C)}}),k=M(u("lock.lockScreen")),y=M({password:void 0}),V=Je({password:[r()]}),d=M(),m=()=>io(this,null,function*(){!d||!(yield d.value.validate())||(P.value=!1,i.setLockInfo(no(oo({},y.value),{isLock:!0})))});return(C,_)=>{const R=bt,$=$l,L=Ll,H=Me,z=gt;return O(),W(z,{modelValue:a(P),"onUpdate:modelValue":_[1]||(_[1]=T=>Le(P)?P.value=T:null),width:"500px","max-height":"170px",class:I(a(n)),title:a(k)},{footer:w(()=>[o(H,{type:"primary",onClick:m},{default:w(()=>[X(j(a(u)("lock.lock")),1)]),_:1})]),default:w(()=>[f("div",co,[f("img",{src:a(p),alt:"",class:"w-70px h-70px rounded-[50%]"},null,8,so),f("span",uo,j(a(h)),1)]),o(L,{ref_key:"formRef",ref:d,model:a(y),rules:a(V),"label-width":"80px"},{default:w(()=>[o($,{label:a(u)("lock.lockPassword"),prop:"password"},{default:w(()=>[o(R,{type:"password",modelValue:a(y).password,"onUpdate:modelValue":_[0]||(_[0]=T=>a(y).password=T),placeholder:"\u8BF7\u8F93\u5165"+a(u)("lock.lockPassword"),clearable:"","show-password":""},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),_:1},8,["modelValue","class","title"])}}}),vo=Q(po,[["__scopeId","data-v-6916a729"]]);var mo=Object.defineProperty,ho=Object.defineProperties,fo=Object.getOwnPropertyDescriptors,Jt=Object.getOwnPropertySymbols,go=Object.prototype.hasOwnProperty,bo=Object.prototype.propertyIsEnumerable,Qt=(l,t,e)=>t in l?mo(l,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):l[t]=e,wo=(l,t)=>{for(var e in t||(t={}))go.call(t,e)&&Qt(l,e,t[e]);if(Jt)for(var e of Jt(t))bo.call(t,e)&&Qt(l,e,t[e]);return l},yo=(l,t)=>ho(l,fo(t));const xo=(l=!0)=>{let t;const e=Je({year:0,month:0,week:"",day:0,hour:"",minute:"",second:0,meridiem:""}),n=()=>{const i=gl(),c=i.format("HH"),s=i.format("mm"),p=i.get("s");e.year=i.get("y"),e.month=i.get("M")+1,e.week="\u661F\u671F"+["\u65E5","\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D"][i.day()],e.day=i.get("date"),e.hour=c,e.minute=s,e.second=p,e.meridiem=i.format("A")};function r(){n(),clearInterval(t),t=setInterval(()=>n(),1e3)}function u(){clearInterval(t)}return Wl(()=>{l&&r()}),ql(()=>{u()}),yo(wo({},Hl(e)),{start:r,stop:u})};var Xt=(l,t,e)=>new Promise((n,r)=>{var u=s=>{try{c(e.next(s))}catch(p){r(p)}},i=s=>{try{c(e.throw(s))}catch(p){r(p)}},c=s=>s.done?n(s.value):Promise.resolve(s.value).then(u,i);c((e=e.apply(l,t)).next())});const _o={class:"flex w-screen h-screen justify-center items-center"},Oo={class:"flex flex-col items-center"},Po=["src"],ko={class:"text-14px my-10px text-[var(--logo-title-text-color)]"},jo={class:"absolute bottom-5 w-full text-gray-300 xl:text-xl 2xl:text-3xl text-center enter-y"},Co={class:"text-5xl mb-4 enter-x"},Io={class:"text-3xl"},Mo={class:"text-2xl"},$o=A({__name:"LockPage",setup(l){const t=Re(),{replace:e}=ie(),n=Ne(),r=M(""),u=M(!1),i=M(!1),c=M(!0),{getPrefixCls:s}=U(),p=s("lock-page"),h=v(()=>{var z;return(z=n.user.avatar)!=null?z:Ie}),g=v(()=>{var z;return(z=n.user.nickname)!=null?z:"Admin"}),P=lt(),{hour:k,month:y,minute:V,meridiem:d,year:m,day:C,week:_}=xo(!0),{t:R}=te();function $(){return Xt(this,null,function*(){if(!r.value)return;let z=r.value;try{u.value=!0;const T=yield P.unLock(z);i.value=!T}finally{u.value=!1}})}function L(){return Xt(this,null,function*(){yield n.loginOut().catch(()=>{}),Kl(),t.delAllViews(),P.resetLockInfo(),e("/login")})}function H(z=!1){c.value=z}return(z,T)=>{const be=N,fe=bt,de=Me;return O(),B("div",{class:I([a(p),"fixed inset-0 flex h-screen w-screen bg-black items-center justify-center"])},[ce(f("div",{class:I([`${a(p)}__unlock`,"absolute top-0 left-1/2 flex pt-5 h-16 items-center justify-center sm:text-md xl:text-xl text-white flex-col cursor-pointer transform translate-x-1/2"]),onClick:T[0]||(T[0]=Z=>H(!1))},[o(be,{icon:"ep:lock"}),f("span",null,j(a(R)("lock.unlock")),1)],2),[[Te,a(c)]]),f("div",_o,[f("div",{class:I([`${a(p)}__hour`,"relative mr-5 md:mr-20 w-2/5 h-2/5 md:h-4/5"])},[f("span",null,j(a(k)),1),ce(f("span",{class:"meridiem absolute left-5 top-5 text-md xl:text-xl"},j(a(d)),513),[[Te,a(c)]])],2),f("div",{class:I(`${a(p)}__minute w-2/5 h-2/5 md:h-4/5 `)},[f("span",null,j(a(V)),1)],2)]),o(xt,{name:"fade-slide"},{default:w(()=>[ce(f("div",{class:I(`${a(p)}-entry`)},[f("div",{class:I(`${a(p)}-entry-content`)},[f("div",Oo,[f("img",{src:a(h),alt:"",class:"w-70px h-70px rounded-[50%]"},null,8,Po),f("span",ko,j(a(g)),1)]),o(fe,{type:"password",placeholder:a(R)("lock.placeholder"),class:"enter-x",modelValue:a(r),"onUpdate:modelValue":T[1]||(T[1]=Z=>Le(r)?r.value=Z:null)},null,8,["placeholder","modelValue"]),a(i)?(O(),B("span",{key:0,class:I(`text-14px ${a(p)}-entry__err-msg enter-x`)},j(a(R)("lock.message")),3)):me("",!0),f("div",{class:I(`${a(p)}-entry__footer enter-x`)},[o(de,{type:"primary",size:"small",class:"mt-2 mr-2 enter-x",link:"",disabled:a(u),onClick:T[2]||(T[2]=Z=>H(!0))},{default:w(()=>[X(j(a(R)("common.back")),1)]),_:1},8,["disabled"]),o(de,{type:"primary",size:"small",class:"mt-2 mr-2 enter-x",link:"",disabled:a(u),onClick:L},{default:w(()=>[X(j(a(R)("lock.backToLogin")),1)]),_:1},8,["disabled"]),o(de,{type:"primary",class:"mt-2",size:"small",link:"",onClick:T[3]||(T[3]=Z=>$()),disabled:a(u)},{default:w(()=>[X(j(a(R)("lock.entrySystem")),1)]),_:1},8,["disabled"])],2)],2)],2),[[Te,!a(c)]])]),_:1}),f("div",jo,[ce(f("div",Co,[X(j(a(k))+":"+j(a(V))+" ",1),f("span",Io,j(a(d)),1)],512),[[Te,!a(c)]]),f("div",Mo,j(a(m))+"/"+j(a(y))+"/"+j(a(C))+" "+j(a(_)),1)])],2)}}}),Lo=Q($o,[["__scopeId","data-v-90e6443b"]]);var To=Object.defineProperty,So=Object.defineProperties,Vo=Object.getOwnPropertyDescriptors,Kt=Object.getOwnPropertySymbols,Ro=Object.prototype.hasOwnProperty,zo=Object.prototype.propertyIsEnumerable,el=(l,t,e)=>t in l?To(l,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):l[t]=e,Do=(l,t)=>{for(var e in t||(t={}))Ro.call(t,e)&&el(l,e,t[e]);if(Kt)for(var e of Kt(t))zo.call(t,e)&&el(l,e,t[e]);return l},Eo=(l,t)=>So(l,Vo(t)),at=(l,t,e)=>new Promise((n,r)=>{var u=s=>{try{c(e.next(s))}catch(p){r(p)}},i=s=>{try{c(e.throw(s))}catch(p){r(p)}},c=s=>s.done?n(s.value):Promise.resolve(s.value).then(u,i);c((e=e.apply(l,t)).next())});const Ao={class:"flex items-center"},Uo={class:"pl-[5px] text-14px text-[var(--top-header-text-color)] <lg:hidden"},Fo=A(Eo(Do({},{name:"UserInfo"}),{__name:"UserInfo",setup(l){const{t}=te(),{push:e,replace:n}=ie(),r=Ne(),u=bl(),{wsCache:i}=ea(),c=Re(),{getPrefixCls:s}=U(),p=s("user-info"),h=v(()=>{var $;return($=r.user.avatar)!=null?$:Ie}),g=v(()=>{var $;return($=r.user.nickname)!=null?$:"Admin"}),P=lt(),k=v(()=>{var $,L;return(L=($=P.getLockInfo)==null?void 0:$.isLock)!=null?L:!1}),y=M(!1),V=()=>{y.value=!0},d=()=>at(this,null,function*(){try{yield Sl.confirm(t("common.loginOutMessage"),t("common.reminder"),{confirmButtonText:t("common.ok"),cancelButtonText:t("common.cancel"),type:"warning"}),yield r.loginOut(),c.delAllViews(),n("/login?redirect=/index")}catch{}}),m=()=>at(this,null,function*(){e("/user/profile")}),C=M({visiable:!1}),_=$=>{u.alert("\u60A8\u5F53\u524D\u4E3A\u9996\u6B21\u767B\u5F55\uFF0C\u5FC5\u987B\u4FEE\u6539\u5BC6\u7801")},R=()=>at(this,null,function*(){r.user.loginCount+=1,C.value.visiable=!1;const $=yield wl();i.set(ta.USER,$)});return xe(()=>{r.user.loginCount===0&&(C.value.visiable=!0)}),($,L)=>{const H=Tl,z=N,T=Ye,be=Ze,fe=Ge,de=gt;return O(),B(G,null,[o(fe,{class:I(["custom-hover",a(p)]),trigger:"click"},{dropdown:w(()=>[o(be,null,{default:w(()=>[o(T,null,{default:w(()=>[o(z,{icon:"ep:tools"}),f("div",{onClick:m},j(a(t)("common.profile")),1)]),_:1}),o(T,{divided:""},{default:w(()=>[o(z,{icon:"ep:lock"}),f("div",{onClick:V},j(a(t)("lock.lockScreen")),1)]),_:1}),o(T,{divided:"",onClick:d},{default:w(()=>[o(z,{icon:"ep:switch-button"}),f("div",null,j(a(t)("common.loginOut")),1)]),_:1})]),_:1})]),default:w(()=>[f("div",Ao,[o(H,{src:a(h),alt:"",class:"w-[calc(var(--logo-height)-25px)] rounded-[50%]"},null,8,["src"]),f("span",Uo,j(a(g)),1)])]),_:1},8,["class"]),a(y)?(O(),W(vo,{key:0,modelValue:a(y),"onUpdate:modelValue":L[0]||(L[0]=Z=>Le(y)?y.value=Z:null)},null,8,["modelValue"])):me("",!0),(O(),W(Nl,{to:"body"},[o(xt,{name:"fade-bottom",mode:"out-in"},{default:w(()=>[a(k)?(O(),W(Lo,{key:0})):me("",!0)]),_:1})])),o(de,{modelValue:a(C).visiable,"onUpdate:modelValue":L[1]||(L[1]=Z=>a(C).visiable=Z),title:"\u9996\u6B21\u767B\u5F55\uFF0C\u8BF7\u4FEE\u6539\u4F60\u7684\u5BC6\u7801","before-close":_},{default:w(()=>[o(ra,{"old-pws":"admin123",onSuccess:R})]),_:1},8,["modelValue"])],64)}}})),Bo=Q(Fo,[["__scopeId","data-v-1dccd932"]]);var Wo=Object.defineProperty,qo=Object.defineProperties,Ho=Object.getOwnPropertyDescriptors,tl=Object.getOwnPropertySymbols,No=Object.prototype.hasOwnProperty,Yo=Object.prototype.propertyIsEnumerable,ll=(l,t,e)=>t in l?Wo(l,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):l[t]=e,Zo=(l,t)=>{for(var e in t||(t={}))No.call(t,e)&&ll(l,e,t[e]);if(tl)for(var e of tl(t))Yo.call(t,e)&&ll(l,e,t[e]);return l},Go=(l,t)=>qo(l,Ho(t));const Jo=A(Go(Zo({},{name:"ScreenFull"}),{__name:"Screenfull",props:{color:Ke.string.def("")},setup(l){const{getPrefixCls:t}=U(),e=t("screenfull"),{toggle:n,isFullscreen:r}=Yl(),u=()=>{n()};return(i,c)=>(O(),B("div",{class:I(a(e)),onClick:u},[o(a(N),{color:l.color,icon:a(r)?"zmdi:fullscreen-exit":"zmdi:fullscreen",size:18},null,8,["color","icon"])],2))}}));var Qo=Object.defineProperty,Xo=Object.defineProperties,Ko=Object.getOwnPropertyDescriptors,al=Object.getOwnPropertySymbols,en=Object.prototype.hasOwnProperty,tn=Object.prototype.propertyIsEnumerable,rl=(l,t,e)=>t in l?Qo(l,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):l[t]=e,ol=(l,t)=>{for(var e in t||(t={}))en.call(t,e)&&rl(l,e,t[e]);if(al)for(var e of al(t))tn.call(t,e)&&rl(l,e,t[e]);return l},ln=(l,t)=>Xo(l,Ko(t));const nl=(l,t="")=>{var e;const n=[];for(const r of l){const u=r==null?void 0:r.meta;if(u.hidden&&!u.canTo)continue;const i=!u.alwaysShow&&((e=r.children)==null?void 0:e.length)===1?ln(ol({},r.children[0]),{path:oe(r.path,r.children[0].path)}):ol({},r);i.path=oe(t,i.path),i.children&&(i.children=nl(i.children,i.path)),i&&n.push(i)}return n};function an(l){return typeof l=="function"||Object.prototype.toString.call(l)==="[object Object]"&&!wt(l)}const{getPrefixCls:rn}=U(),on=rn("breadcrumb"),nn=Y(),cn=v(()=>nn.getBreadcrumbIcon),sn=A({name:"Breadcrumb",setup(){const{currentRoute:l}=ie(),{t}=te(),e=M([]),n=Ve(),r=v(()=>{const c=n.getRouters;return nl(c)}),u=()=>{const c=l.value.matched.slice(-1)[0].path;e.value=yl(a(r),s=>s.path===c)},i=()=>xl(a(e)).map(c=>{const s=!c.redirect||c.redirect==="noredirect",p=c.meta;return o(Rl,{to:{path:s?"":c.path},key:c.name},{default:()=>{var h,g;return[p!=null&&p.icon&&cn.value?o("div",{class:"flex items-center"},[o(N,{icon:p.icon,class:"mr-[2px]",svgClass:"inline-block"},null),t((h=c==null?void 0:c.meta)==null?void 0:h.title)]):t((g=c==null?void 0:c.meta)==null?void 0:g.title)]}})});return ge(()=>l.value,c=>{c.path.startsWith("/redirect/")||u()},{immediate:!0}),()=>{let c;return o(Vl,{separator:"/",class:`${on} flex items-center h-full ml-[10px]`},{default:()=>[o(Zl,{appear:!0,"enter-active-class":"animate__animated animate__fadeInRight"},an(c=i())?c:{default:()=>[c]})]})}}}),un=Q(sn,[["__scopeId","data-v-4b03c1c3"]]);var pn=Object.defineProperty,dn=Object.defineProperties,vn=Object.getOwnPropertyDescriptors,il=Object.getOwnPropertySymbols,mn=Object.prototype.hasOwnProperty,hn=Object.prototype.propertyIsEnumerable,cl=(l,t,e)=>t in l?pn(l,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):l[t]=e,fn=(l,t)=>{for(var e in t||(t={}))mn.call(t,e)&&cl(l,e,t[e]);if(il)for(var e of il(t))hn.call(t,e)&&cl(l,e,t[e]);return l},gn=(l,t)=>dn(l,vn(t));const bn=A(gn(fn({},{name:"SizeDropdown"}),{__name:"SizeDropdown",props:{color:Ke.string.def("")},setup(l){const{getPrefixCls:t}=U(),e=t("size-dropdown"),{t:n}=te(),r=Y(),u=v(()=>r.sizeMap),i=c=>{r.setCurrentSize(c)};return(c,s)=>{const p=N,h=Ye,g=Ze,P=Ge;return O(),W(P,{class:I(a(e)),trigger:"click",onCommand:i},{dropdown:w(()=>[o(g,null,{default:w(()=>[(O(!0),B(G,null,$e(a(u),k=>(O(),W(h,{key:k,command:k},{default:w(()=>[X(j(a(n)(`size.${k}`)),1)]),_:2},1032,["command"]))),128))]),_:1})]),default:w(()=>[o(p,{color:l.color,size:18,class:"cursor-pointer",icon:"mdi:format-size"},null,8,["color"])]),_:1},8,["class"])}}})),{getPrefixCls:wn,variables:yn}=U(),xn=wn("tool-header"),se=Y(),_n=v(()=>se.getBreadcrumb),On=v(()=>se.getHamburger),Pn=v(()=>se.getScreenfull),kn=v(()=>se.search),jn=v(()=>se.getSize),sl=v(()=>se.getLayout),Cn=v(()=>se.getLocale),In=v(()=>se.getMessage),Mn=A({name:"ToolHeader",setup(){const{push:l}=ie(),t=M(),e=()=>{l({path:"/management-center/DrawingEncoder"})},n=()=>{var r;(r=t.value)==null||r.openForm()};return()=>o("div",{id:`${yn.namespace}-tool-header`,class:[xn,"h-[var(--top-tool-height)] relative px-[var(--top-tool-p-x)] flex items-center justify-between","dark:bg-[var(--el-bg-color)]"]},[sl.value!=="top"?o("div",{class:"h-full flex items-center"},[On.value&&sl.value!=="cutMenu"?o(Kr,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,_n.value?o(un,{class:"lt-md:hidden"},null):void 0]):void 0,o("div",{class:"h-full flex items-center"},[o(Me,{link:!0,type:"primary",onClick:n},{default:()=>[X("\u9879\u76EE\u7F16\u7801\u5217\u8868")]}),o(Me,{link:!0,type:"primary",onClick:e},{default:()=>[X("\u56FE\u53F7\u7533\u8BF7")]}),Pn.value?o(Jo,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,kn.value?o(na,{isModal:!1},null):void 0,jn.value?o(bn,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,Cn.value?o(Xl,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,In.value?o(Hr,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,o(Bo,null,null)]),o(ia,{ruleId:1,showEncoder:!1,ref:t},null)])}}),Fe=Q(Mn,[["__scopeId","data-v-ba26f0a8"]]),{getPrefixCls:$n}=U(),ue=$n("layout"),le=Y(),Be=v(()=>le.getPageLoading),pe=v(()=>le.getTagsView),F=v(()=>le.getCollapse),Pe=v(()=>le.logo),S=v(()=>le.getFixedHeader),K=v(()=>le.getMobile),he=v(()=>le.getFixedMenu),We=()=>({renderClassic:()=>o(G,null,[o("div",{class:["absolute top-0 left-0 h-full layout-border__right",{"!fixed z-3000":K.value}]},[Pe.value?o(Ae,{class:["bg-[var(--left-menu-bg-color)] relative",{"!pl-0":K.value&&F.value,"w-[var(--left-menu-min-width)]":le.getCollapse,"w-[var(--left-menu-max-width)]":!le.getCollapse}],style:"transition: all var(--transition-time-02);"},null):void 0,o(ze,{class:[{"!h-[calc(100%-var(--logo-height))]":Pe.value}]},null)]),o("div",{class:[`${ue}-content`,"absolute top-0 h-[100%]",{"w-[calc(100%-var(--left-menu-min-width))] left-[var(--left-menu-min-width)]":F.value&&!K.value&&!K.value,"w-[calc(100%-var(--left-menu-max-width))] left-[var(--left-menu-max-width)]":!F.value&&!K.value&&!K.value,"fixed !w-full !left-0":K.value}],style:"transition: all var(--transition-time-02);"},[ce(o(ne,{class:[`${ue}-content-scrollbar`,{"!h-[calc(100%-var(--top-tool-height)-var(--tags-view-height))] mt-[calc(var(--top-tool-height)+var(--tags-view-height))]":S.value}]},{default:()=>[o("div",{class:[{"fixed top-0 left-0 z-10":S.value,"w-[calc(100%-var(--left-menu-min-width))] !left-[var(--left-menu-min-width)]":F.value&&S.value&&!K.value,"w-[calc(100%-var(--left-menu-max-width))] !left-[var(--left-menu-max-width)]":!F.value&&S.value&&!K.value,"!w-full !left-0":K.value}],style:"transition: all var(--transition-time-02);"},[o(Fe,{class:["bg-[var(--top-header-bg-color)]",{"layout-border__bottom":!pe.value}]},null),pe.value?o(Ee,{class:"layout-border__top layout-border__bottom"},null):void 0]),o(Ue,null,null)]}),[[Se("loading"),Be.value]])])]),renderTopLeft:()=>o(G,null,[o("div",{class:"relative flex items-center bg-[var(--top-header-bg-color)] layout-border__bottom dark:bg-[var(--el-bg-color)]"},[Pe.value?o(Ae,{class:"custom-hover"},null):void 0,o(Fe,{class:"flex-1"},null)]),o("div",{class:"absolute left-0 top-[var(--logo-height)+1px] h-[calc(100%-1px-var(--logo-height))] w-full flex"},[o(ze,{class:"relative layout-border__right !h-full"},null),o("div",{class:[`${ue}-content`,"h-[100%]",{"w-[calc(100%-var(--left-menu-min-width))] left-[var(--left-menu-min-width)]":F.value,"w-[calc(100%-var(--left-menu-max-width))] left-[var(--left-menu-max-width)]":!F.value}],style:"transition: all var(--transition-time-02);"},[ce(o(ne,{class:[`${ue}-content-scrollbar`,{"!h-[calc(100%-var(--tags-view-height))] mt-[calc(var(--tags-view-height))]":S.value&&pe.value}]},{default:()=>[pe.value?o(Ee,{class:["layout-border__bottom absolute",{"!fixed top-0 left-0 z-10":S.value,"w-[calc(100%-var(--left-menu-min-width))] !left-[var(--left-menu-min-width)] mt-[calc(var(--logo-height)+1px)]":F.value&&S.value,"w-[calc(100%-var(--left-menu-max-width))] !left-[var(--left-menu-max-width)] mt-[calc(var(--logo-height)+1px)]":!F.value&&S.value}],style:"transition: width var(--transition-time-02), left var(--transition-time-02);"},null):void 0,o(Ue,null,null)]}),[[Se("loading"),Be.value]])])])]),renderTop:()=>o(G,null,[o("div",{class:["flex items-center justify-between bg-[var(--top-header-bg-color)] relative",{"layout-border__bottom":!pe.value}]},[Pe.value?o(Ae,{class:"custom-hover"},null):void 0,o(ze,{class:"h-[var(--top-tool-height)] flex-1 px-10px"},null),o(Fe,null,null)]),o("div",{class:[`${ue}-content`,"w-full",{"h-[calc(100%-var(--app-footer-height))]":!S.value,"h-[calc(100%-var(--tags-view-height)-var(--app-footer-height))]":S.value}]},[ce(o(ne,{class:[`${ue}-content-scrollbar`,{"mt-[var(--tags-view-height)] !pb-[calc(var(--tags-view-height)+var(--app-footer-height))]":S.value,"pb-[var(--app-footer-height)]":!S.value}]},{default:()=>[pe.value?o(Ee,{class:["layout-border__bottom layout-border__top relative",{"!fixed w-full top-[calc(var(--top-tool-height)+1px)] left-0":S.value}],style:"transition: width var(--transition-time-02), left var(--transition-time-02);"},null):void 0,o(Ue,null,null)]}),[[Se("loading"),Be.value]])])]),renderCutMenu:()=>o(G,null,[o("div",{class:"relative flex items-center bg-[var(--top-header-bg-color)] layout-border__bottom"},[Pe.value?o(Ae,{class:"custom-hover !pr-15px"},null):void 0,o(Fe,{class:"flex-1"},null)]),o("div",{class:"absolute left-0 top-[var(--logo-height)] h-[calc(100%-var(--logo-height))] w-[calc(100%-2px)] flex"},[o(Va,null,null),o("div",{class:[`${ue}-content`,"h-[100%]",{"w-[calc(100%-var(--tab-menu-min-width))] left-[var(--tab-menu-min-width)]":F.value&&!he.value,"w-[calc(100%-var(--tab-menu-max-width))] left-[var(--tab-menu-max-width)]":!F.value&&!he.value,"w-[calc(100%-var(--tab-menu-min-width)-var(--left-menu-max-width))] ml-[var(--left-menu-max-width)]":F.value&&he.value,"w-[calc(100%-var(--tab-menu-max-width)-var(--left-menu-max-width))] ml-[var(--left-menu-max-width)]":!F.value&&he.value}],style:"transition: all var(--transition-time-02);"},[ce(o(ne,{class:[`${ue}-content-scrollbar`,{"!h-[calc(100%-var(--tags-view-height))] mt-[calc(var(--tags-view-height))]":S.value&&pe.value}]},{default:()=>[pe.value?o(Ee,{class:["relative layout-border__bottom layout-border__top",{"!fixed top-0 left-0 z-10":S.value,"w-[calc(100%-var(--tab-menu-min-width))] !left-[var(--tab-menu-min-width)] mt-[var(--logo-height)]":F.value&&S.value,"w-[calc(100%-var(--tab-menu-max-width))] !left-[var(--tab-menu-max-width)] mt-[var(--logo-height)]":!F.value&&S.value,"!fixed top-0 !left-[var(--tab-menu-min-width)+var(--left-menu-max-width)] z-10":S.value&&he.value,"w-[calc(100%-var(--tab-menu-min-width)-var(--left-menu-max-width))] !left-[var(--tab-menu-min-width)+var(--left-menu-max-width)] mt-[var(--logo-height)]":F.value&&S.value&&he.value,"w-[calc(100%-var(--tab-menu-max-width)-var(--left-menu-max-width))] !left-[var(--tab-menu-max-width)+var(--left-menu-max-width)] mt-[var(--logo-height)]":!F.value&&S.value&&he.value}],style:"transition: width var(--transition-time-02), left var(--transition-time-02);"},null):void 0,o(Ue,null,null)]}),[[Se("loading"),Be.value]])])])])}),{getPrefixCls:Ln}=U(),ul=Ln("layout"),qe=Y(),Tn=v(()=>qe.getMobile),Sn=v(()=>qe.getCollapse),pl=v(()=>qe.getLayout),Vn=()=>{qe.setCollapse(!0)},Rn=()=>{switch(a(pl)){case"classic":const{renderClassic:l}=We();return l();case"topLeft":const{renderTopLeft:t}=We();return t();case"top":const{renderTop:e}=We();return e();case"cutMenu":const{renderCutMenu:n}=We();return n()}},zn=A({name:"Layout",setup(){return()=>o("section",{class:[ul,`${ul}__${pl.value}`,"w-[100%] h-[100%] relative"]},[Tn.value&&!Sn.value?o("div",{class:"absolute left-0 top-0 z-99 h-full w-full bg-[var(--el-color-black)] opacity-30",onClick:Vn},null):void 0,Rn(),o(ha,null,null)])}}),Dn=Q(zn,[["__scopeId","data-v-ab0b0390"]]);export{Dn as default};
