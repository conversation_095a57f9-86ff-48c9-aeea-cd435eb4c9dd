import{C as ja,E as uf}from"./utils-vendor-Vtb-rlR8.js";import{c6 as ff,c7 as pf,c8 as df,c9 as hf,ca as vf,cb as mf}from"./vendor-DLCNhz7G.js";var tt={};/*!
* shared v9.10.2
* (c) 2024 ka<PERSON><PERSON> ka<PERSON>
* Released under the MIT License.
*/const Na=typeof window<"u";let Fa,Ma;{const e=Na&&window.performance;e&&e.mark&&e.measure&&e.clearMarks&&e.clearMeasures&&(Fa=t=>{e.mark(t)},Ma=(t,r,n)=>{e.measure(t,r,n),e.clearMarks(r),e.clearMarks(n)})}const gf=/\{([0-9a-zA-Z]+)\}/g;function yf(e,...t){return t.length===1&&Wn(t[0])&&(t=t[0]),(!t||!t.hasOwnProperty)&&(t={}),e.replace(gf,(r,n)=>t.hasOwnProperty(n)?t[n]:"")}const bf=(e,t=!1)=>t?Symbol.for(e):Symbol(e),_f=(e,t,r)=>Da({l:e,k:t,s:r}),Da=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),Ef=e=>typeof e=="number"&&isFinite(e),wf=e=>Xo(e)==="[object Date]",Of=e=>Xo(e)==="[object RegExp]",Sf=e=>Jo(e)&&Object.keys(e).length===0,xf=Object.assign;let Va;const Cf=()=>Va||(Va=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Tf(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const kf=Object.prototype.hasOwnProperty;function Lf(e,t){return kf.call(e,t)}const Yo=Array.isArray,zo=e=>typeof e=="function",Rf=e=>typeof e=="string",Pf=e=>typeof e=="boolean",If=e=>typeof e=="symbol",Wn=e=>e!==null&&typeof e=="object",Af=e=>Wn(e)&&zo(e.then)&&zo(e.catch),Qo=Object.prototype.toString,Xo=e=>Qo.call(e),Jo=e=>{if(!Wn(e))return!1;const t=Object.getPrototypeOf(e);return t===null||t.constructor===Object},jf=e=>e==null?"":Yo(e)||Jo(e)&&e.toString===Qo?JSON.stringify(e,null,2):String(e);function Nf(e,t=""){return e.reduce((r,n,o)=>o===0?r+n:r+t+n,"")}const Ua=2;function Ff(e,t=0,r=e.length){const n=e.split(/\r?\n/);let o=0;const i=[];for(let a=0;a<n.length;a++)if(o+=n[a].length+1,o>=t){for(let s=a-Ua;s<=a+Ua||r>o;s++){if(s<0||s>=n.length)continue;const c=s+1;i.push(`${c}${" ".repeat(3-String(c).length)}|  ${n[s]}`);const l=n[s].length;if(s===a){const u=t-(o-l)+1,f=Math.max(1,r>o?l-u:r-t);i.push("   |  "+" ".repeat(u)+"^".repeat(f))}else if(s>a){if(r>o){const u=Math.max(Math.min(r-o,l),1);i.push("   |  "+"^".repeat(u))}o+=l+1}}break}return i.join(`
`)}function Mf(e){let t=e;return()=>++t}function Df(e,t){}const $a={};function Vf(e){$a[e]||($a[e]=!0)}function Uf(){const e=new Map;return{events:e,on(t,r){const n=e.get(t);n&&n.push(r)||e.set(t,[r])},off(t,r){const n=e.get(t);n&&n.splice(n.indexOf(r)>>>0,1)},emit(t,r){(e.get(t)||[]).slice().map(n=>n(r)),(e.get("*")||[]).slice().map(n=>n(t,r))}}}const Pr=e=>!Wn(e)||Yo(e);function $f(e,t){if(Pr(e)||Pr(t))throw new Error("Invalid value");const r=[{src:e,des:t}];for(;r.length;){const{src:n,des:o}=r.pop();Object.keys(n).forEach(i=>{Pr(n[i])||Pr(o[i])?o[i]=n[i]:r.push({src:n[i],des:o[i]})})}}const Hf=Object.freeze(Object.defineProperty({__proto__:null,assign:xf,createEmitter:Uf,deepCopy:$f,escapeHtml:Tf,format:yf,friendlyJSONstringify:Da,generateCodeFrame:Ff,generateFormatCacheKey:_f,getGlobalThis:Cf,hasOwn:Lf,inBrowser:Na,incrementer:Mf,isArray:Yo,isBoolean:Pf,isDate:wf,isEmptyObject:Sf,isFunction:zo,isNumber:Ef,isObject:Wn,isPlainObject:Jo,isPromise:Af,isRegExp:Of,isString:Rf,isSymbol:If,join:Nf,makeSymbol:bf,get mark(){return Fa},get measure(){return Ma},objectToString:Qo,toDisplayString:jf,toTypeString:Xo,warn:Df,warnOnce:Vf},Symbol.toStringTag,{value:"Module"})),Wf=ja(Hf);/**
* @vue/shared v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Bf(e,t){const r=new Set(e.split(","));return n=>r.has(n)}const Ha=()=>{},qf=Object.assign,Gf=Object.prototype.hasOwnProperty,Ir=(e,t)=>Gf.call(e,t),jt=Array.isArray,Ar=e=>Wa(e)==="[object Map]",Zo=e=>typeof e=="function",Kf=e=>typeof e=="string",jr=e=>typeof e=="symbol",Bn=e=>e!==null&&typeof e=="object",Yf=Object.prototype.toString,Wa=e=>Yf.call(e),zf=e=>Wa(e).slice(8,-1),ei=e=>Kf(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,yn=(e,t)=>!Object.is(e,t),Qf=(e,t,r)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:r})};/**
* @vue/reactivity v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let nt;class ti{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this.parent=nt,!t&&nt&&(this.index=(nt.scopes||(nt.scopes=[])).push(this)-1)}get active(){return this._active}run(t){if(this._active){const r=nt;try{return nt=this,t()}finally{nt=r}}}on(){nt=this}off(){nt=this.parent}stop(t){if(this._active){let r,n;for(r=0,n=this.effects.length;r<n;r++)this.effects[r].stop();for(r=0,n=this.cleanups.length;r<n;r++)this.cleanups[r]();if(this.scopes)for(r=0,n=this.scopes.length;r<n;r++)this.scopes[r].stop(!0);if(!this.detached&&this.parent&&!t){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index)}this.parent=void 0,this._active=!1}}}function Ba(e){return new ti(e)}function qa(e,t=nt){t&&t.active&&t.effects.push(e)}function Nr(){return nt}function ni(e){nt&&nt.cleanups.push(e)}let Jt;class bn{constructor(t,r,n,o){this.fn=t,this.trigger=r,this.scheduler=n,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,qa(this,o)}get dirty(){if(this._dirtyLevel===2||this._dirtyLevel===3){this._dirtyLevel=1,Zt();for(let t=0;t<this._depsLength;t++){const r=this.deps[t];if(r.computed&&(Xf(r.computed),this._dirtyLevel>=4))break}this._dirtyLevel===1&&(this._dirtyLevel=0),en()}return this._dirtyLevel>=4}set dirty(t){this._dirtyLevel=t?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let t=Nt,r=Jt;try{return Nt=!0,Jt=this,this._runnings++,Ga(this),this.fn()}finally{Ka(this),this._runnings--,Jt=r,Nt=t}}stop(){var t;this.active&&(Ga(this),Ka(this),(t=this.onStop)==null||t.call(this),this.active=!1)}}function Xf(e){return e.value}function Ga(e){e._trackId++,e._depsLength=0}function Ka(e){if(e.deps.length>e._depsLength){for(let t=e._depsLength;t<e.deps.length;t++)Ya(e.deps[t],e);e.deps.length=e._depsLength}}function Ya(e,t){const r=e.get(t);r!==void 0&&t._trackId!==r&&(e.delete(t),e.size===0&&e.cleanup())}function Jf(e,t){e.effect instanceof bn&&(e=e.effect.fn);const r=new bn(e,Ha,()=>{r.dirty&&r.run()});t&&(qf(r,t),t.scope&&qa(r,t.scope)),(!t||!t.lazy)&&r.run();const n=r.run.bind(r);return n.effect=r,n}function Zf(e){e.effect.stop()}let Nt=!0,ri=0;const za=[];function Zt(){za.push(Nt),Nt=!1}function en(){const e=za.pop();Nt=e===void 0?!0:e}function oi(){ri++}function ii(){for(ri--;!ri&&ai.length;)ai.shift()()}function Qa(e,t,r){if(t.get(e)!==e._trackId){t.set(e,e._trackId);const n=e.deps[e._depsLength];n!==t?(n&&Ya(n,e),e.deps[e._depsLength++]=t):e._depsLength++}}const ai=[];function Xa(e,t,r){oi();for(const n of e.keys()){let o;n._dirtyLevel<t&&(o??(o=e.get(n)===n._trackId))&&(n._shouldSchedule||(n._shouldSchedule=n._dirtyLevel===0),n._dirtyLevel=t),n._shouldSchedule&&(o??(o=e.get(n)===n._trackId))&&(n.trigger(),(!n._runnings||n.allowRecurse)&&n._dirtyLevel!==2&&(n._shouldSchedule=!1,n.scheduler&&ai.push(n.scheduler)))}ii()}const Ja=(e,t)=>{const r=new Map;return r.cleanup=e,r.computed=t,r},Fr=new WeakMap,tn=Symbol(""),si=Symbol("");function ze(e,t,r){if(Nt&&Jt){let n=Fr.get(e);n||Fr.set(e,n=new Map);let o=n.get(r);o||n.set(r,o=Ja(()=>n.delete(r))),Qa(Jt,o)}}function St(e,t,r,n,o,i){const a=Fr.get(e);if(!a)return;let s=[];if(t==="clear")s=[...a.values()];else if(r==="length"&&jt(e)){const c=Number(n);a.forEach((l,u)=>{(u==="length"||!jr(u)&&u>=c)&&s.push(l)})}else switch(r!==void 0&&s.push(a.get(r)),t){case"add":jt(e)?ei(r)&&s.push(a.get("length")):(s.push(a.get(tn)),Ar(e)&&s.push(a.get(si)));break;case"delete":jt(e)||(s.push(a.get(tn)),Ar(e)&&s.push(a.get(si)));break;case"set":Ar(e)&&s.push(a.get(tn));break}oi();for(const c of s)c&&Xa(c,4);ii()}function ep(e,t){var r;return(r=Fr.get(e))==null?void 0:r.get(t)}const tp=Bf("__proto__,__v_isRef,__isVue"),Za=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(jr)),es=np();function np(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...r){const n=Se(this);for(let i=0,a=this.length;i<a;i++)ze(n,"get",i+"");const o=n[t](...r);return o===-1||o===!1?n[t](...r.map(Se)):o}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...r){Zt(),oi();const n=Se(this)[t].apply(this,r);return ii(),en(),n}}),e}function rp(e){const t=Se(this);return ze(t,"has",e),t.hasOwnProperty(e)}class ts{constructor(t=!1,r=!1){this._isReadonly=t,this._isShallow=r}get(t,r,n){const o=this._isReadonly,i=this._isShallow;if(r==="__v_isReactive")return!o;if(r==="__v_isReadonly")return o;if(r==="__v_isShallow")return i;if(r==="__v_raw")return n===(o?i?fs:us:i?ls:cs).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(n)?t:void 0;const a=jt(t);if(!o){if(a&&Ir(es,r))return Reflect.get(es,r,n);if(r==="hasOwnProperty")return rp}const s=Reflect.get(t,r,n);return(jr(r)?Za.has(r):tp(r))||(o||ze(t,"get",r),i)?s:$e(s)?a&&ei(r)?s:s.value:Bn(s)?o?qr(s):_n(s):s}}class ns extends ts{constructor(t=!1){super(!1,t)}set(t,r,n,o){let i=t[r];if(!this._isShallow){const c=nn(i);if(!qn(n)&&!nn(n)&&(i=Se(i),n=Se(n)),!jt(t)&&$e(i)&&!$e(n))return c?!1:(i.value=n,!0)}const a=jt(t)&&ei(r)?Number(r)<t.length:Ir(t,r),s=Reflect.set(t,r,n,o);return t===Se(o)&&(a?yn(n,i)&&St(t,"set",r,n):St(t,"add",r,n)),s}deleteProperty(t,r){const n=Ir(t,r);t[r];const o=Reflect.deleteProperty(t,r);return o&&n&&St(t,"delete",r,void 0),o}has(t,r){const n=Reflect.has(t,r);return(!jr(r)||!Za.has(r))&&ze(t,"has",r),n}ownKeys(t){return ze(t,"iterate",jt(t)?"length":tn),Reflect.ownKeys(t)}}class rs extends ts{constructor(t=!1){super(!0,t)}set(t,r){return!0}deleteProperty(t,r){return!0}}const op=new ns,ip=new rs,ap=new ns(!0),sp=new rs(!0),ci=e=>e,Mr=e=>Reflect.getPrototypeOf(e);function Dr(e,t,r=!1,n=!1){e=e.__v_raw;const o=Se(e),i=Se(t);r||(yn(t,i)&&ze(o,"get",t),ze(o,"get",i));const{has:a}=Mr(o),s=n?ci:r?ui:Gn;if(a.call(o,t))return s(e.get(t));if(a.call(o,i))return s(e.get(i));e!==o&&e.get(t)}function Vr(e,t=!1){const r=this.__v_raw,n=Se(r),o=Se(e);return t||(yn(e,o)&&ze(n,"has",e),ze(n,"has",o)),e===o?r.has(e):r.has(e)||r.has(o)}function Ur(e,t=!1){return e=e.__v_raw,!t&&ze(Se(e),"iterate",tn),Reflect.get(e,"size",e)}function os(e){e=Se(e);const t=Se(this);return Mr(t).has.call(t,e)||(t.add(e),St(t,"add",e,e)),this}function is(e,t){t=Se(t);const r=Se(this),{has:n,get:o}=Mr(r);let i=n.call(r,e);i||(e=Se(e),i=n.call(r,e));const a=o.call(r,e);return r.set(e,t),i?yn(t,a)&&St(r,"set",e,t):St(r,"add",e,t),this}function as(e){const t=Se(this),{has:r,get:n}=Mr(t);let o=r.call(t,e);o||(e=Se(e),o=r.call(t,e)),n&&n.call(t,e);const i=t.delete(e);return o&&St(t,"delete",e,void 0),i}function ss(){const e=Se(this),t=e.size!==0,r=e.clear();return t&&St(e,"clear",void 0,void 0),r}function $r(e,t){return function(r,n){const o=this,i=o.__v_raw,a=Se(i),s=t?ci:e?ui:Gn;return!e&&ze(a,"iterate",tn),i.forEach((c,l)=>r.call(n,s(c),s(l),o))}}function Hr(e,t,r){return function(...n){const o=this.__v_raw,i=Se(o),a=Ar(i),s=e==="entries"||e===Symbol.iterator&&a,c=e==="keys"&&a,l=o[e](...n),u=r?ci:t?ui:Gn;return!t&&ze(i,"iterate",c?si:tn),{next(){const{value:f,done:p}=l.next();return p?{value:f,done:p}:{value:s?[u(f[0]),u(f[1])]:u(f),done:p}},[Symbol.iterator](){return this}}}}function Ft(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function cp(){const e={get(o){return Dr(this,o)},get size(){return Ur(this)},has:Vr,add:os,set:is,delete:as,clear:ss,forEach:$r(!1,!1)},t={get(o){return Dr(this,o,!1,!0)},get size(){return Ur(this)},has:Vr,add:os,set:is,delete:as,clear:ss,forEach:$r(!1,!0)},r={get(o){return Dr(this,o,!0)},get size(){return Ur(this,!0)},has(o){return Vr.call(this,o,!0)},add:Ft("add"),set:Ft("set"),delete:Ft("delete"),clear:Ft("clear"),forEach:$r(!0,!1)},n={get(o){return Dr(this,o,!0,!0)},get size(){return Ur(this,!0)},has(o){return Vr.call(this,o,!0)},add:Ft("add"),set:Ft("set"),delete:Ft("delete"),clear:Ft("clear"),forEach:$r(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(o=>{e[o]=Hr(o,!1,!1),r[o]=Hr(o,!0,!1),t[o]=Hr(o,!1,!0),n[o]=Hr(o,!0,!0)}),[e,r,t,n]}const[lp,up,fp,pp]=cp();function Wr(e,t){const r=t?e?pp:fp:e?up:lp;return(n,o,i)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?n:Reflect.get(Ir(r,o)&&o in n?r:n,o,i)}const dp={get:Wr(!1,!1)},hp={get:Wr(!1,!0)},vp={get:Wr(!0,!1)},mp={get:Wr(!0,!0)},cs=new WeakMap,ls=new WeakMap,us=new WeakMap,fs=new WeakMap;function gp(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function yp(e){return e.__v_skip||!Object.isExtensible(e)?0:gp(zf(e))}function _n(e){return nn(e)?e:Gr(e,!1,op,dp,cs)}function Br(e){return Gr(e,!1,ap,hp,ls)}function qr(e){return Gr(e,!0,ip,vp,us)}function bp(e){return Gr(e,!0,sp,mp,fs)}function Gr(e,t,r,n,o){if(!Bn(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=o.get(e);if(i)return i;const a=yp(e);if(a===0)return e;const s=new Proxy(e,a===2?n:r);return o.set(e,s),s}function Mt(e){return nn(e)?Mt(e.__v_raw):!!(e&&e.__v_isReactive)}function nn(e){return!!(e&&e.__v_isReadonly)}function qn(e){return!!(e&&e.__v_isShallow)}function li(e){return Mt(e)||nn(e)}function Se(e){const t=e&&e.__v_raw;return t?Se(t):e}function Kr(e){return Object.isExtensible(e)&&Qf(e,"__v_skip",!0),e}const Gn=e=>Bn(e)?_n(e):e,ui=e=>Bn(e)?qr(e):e;class ps{constructor(t,r,n,o){this.getter=t,this._setter=r,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new bn(()=>t(this._value),()=>En(this,this.effect._dirtyLevel===2?2:3)),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=n}get value(){const t=Se(this);return(!t._cacheable||t.effect.dirty)&&yn(t._value,t._value=t.effect.run())&&En(t,4),fi(t),t.effect._dirtyLevel>=2&&En(t,2),t._value}set value(t){this._setter(t)}get _dirty(){return this.effect.dirty}set _dirty(t){this.effect.dirty=t}}function _p(e,t,r=!1){let n,o;const i=Zo(e);return i?(n=e,o=Ha):(n=e.get,o=e.set),new ps(n,o,i||!o,r)}function fi(e){var t;Nt&&Jt&&(e=Se(e),Qa(Jt,(t=e.dep)!=null?t:e.dep=Ja(()=>e.dep=void 0,e instanceof ps?e:void 0)))}function En(e,t=4,r){e=Se(e);const n=e.dep;n&&Xa(n,t)}function $e(e){return!!(e&&e.__v_isRef===!0)}function Be(e){return ds(e,!1)}function Yr(e){return ds(e,!0)}function ds(e,t){return $e(e)?e:new Ep(e,t)}class Ep{constructor(t,r){this.__v_isShallow=r,this.dep=void 0,this.__v_isRef=!0,this._rawValue=r?t:Se(t),this._value=r?t:Gn(t)}get value(){return fi(this),this._value}set value(t){const r=this.__v_isShallow||qn(t)||nn(t);t=r?t:Se(t),yn(t,this._rawValue)&&(this._rawValue=t,this._value=r?t:Gn(t),En(this,4))}}function wp(e){En(e,4)}function _t(e){return $e(e)?e.value:e}function Op(e){return Zo(e)?e():_t(e)}const Sp={get:(e,t,r)=>_t(Reflect.get(e,t,r)),set:(e,t,r,n)=>{const o=e[t];return $e(o)&&!$e(r)?(o.value=r,!0):Reflect.set(e,t,r,n)}};function pi(e){return Mt(e)?e:new Proxy(e,Sp)}class xp{constructor(t){this.dep=void 0,this.__v_isRef=!0;const{get:r,set:n}=t(()=>fi(this),()=>En(this));this._get=r,this._set=n}get value(){return this._get()}set value(t){this._set(t)}}function hs(e){return new xp(e)}function vs(e){const t=jt(e)?new Array(e.length):{};for(const r in e)t[r]=gs(e,r);return t}class Cp{constructor(t,r,n){this._object=t,this._key=r,this._defaultValue=n,this.__v_isRef=!0}get value(){const t=this._object[this._key];return t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return ep(Se(this._object),this._key)}}class Tp{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0}get value(){return this._getter()}}function ms(e,t,r){return $e(e)?e:Zo(e)?new Tp(e):Bn(e)&&arguments.length>1?gs(e,t,r):Be(e)}function gs(e,t,r){const n=e[t];return $e(n)?n:new Cp(e,t,r)}const kp={GET:"get",HAS:"has",ITERATE:"iterate"},Lp={SET:"set",ADD:"add",DELETE:"delete",CLEAR:"clear"};/**
* @vue/shared v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function ys(e,t){const r=new Set(e.split(","));return n=>r.has(n)}const Le={},wn=[],ut=()=>{},Rp=()=>!1,zr=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),bs=e=>e.startsWith("onUpdate:"),He=Object.assign,di=(e,t)=>{const r=e.indexOf(t);r>-1&&e.splice(r,1)},Pp=Object.prototype.hasOwnProperty,Te=(e,t)=>Pp.call(e,t),Ee=Array.isArray,_s=e=>Qr(e)==="[object Map]",Es=e=>Qr(e)==="[object Set]",Ip=e=>Qr(e)==="[object RegExp]",be=e=>typeof e=="function",De=e=>typeof e=="string",ws=e=>typeof e=="symbol",Ae=e=>e!==null&&typeof e=="object",hi=e=>(Ae(e)||be(e))&&be(e.then)&&be(e.catch),Os=Object.prototype.toString,Qr=e=>Os.call(e),Ss=e=>Qr(e)==="[object Object]",On=ys(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Xr=e=>{const t=Object.create(null);return r=>t[r]||(t[r]=e(r))},Ap=/-(\w)/g,ft=Xr(e=>e.replace(Ap,(t,r)=>r?r.toUpperCase():"")),jp=/\B([A-Z])/g,Kn=Xr(e=>e.replace(jp,"-$1").toLowerCase()),Jr=Xr(e=>e.charAt(0).toUpperCase()+e.slice(1)),Sn=Xr(e=>e?`on${Jr(e)}`:""),Yn=(e,t)=>!Object.is(e,t),zn=(e,t)=>{for(let r=0;r<e.length;r++)e[r](t)},vi=(e,t,r)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:r})},Np=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Fp=e=>{const t=De(e)?Number(e):NaN;return isNaN(t)?e:t};let xs;const Cs=()=>xs||(xs=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{}),Mp="Infinity,undefined,NaN,isFinite,isNaN,parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,BigInt,console,Error",Dp=ys(Mp);function xn(e){if(Ee(e)){const t={};for(let r=0;r<e.length;r++){const n=e[r],o=De(n)?Hp(n):xn(n);if(o)for(const i in o)t[i]=o[i]}return t}else if(De(e)||Ae(e))return e}const Vp=/;(?![^(]*\))/g,Up=/:([^]+)/,$p=/\/\*[^]*?\*\//g;function Hp(e){const t={};return e.replace($p,"").split(Vp).forEach(r=>{if(r){const n=r.split(Up);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function Cn(e){let t="";if(De(e))t=e;else if(Ee(e))for(let r=0;r<e.length;r++){const n=Cn(e[r]);n&&(t+=n+" ")}else if(Ae(e))for(const r in e)e[r]&&(t+=r+" ");return t.trim()}function Ts(e){if(!e)return null;let{class:t,style:r}=e;return t&&!De(t)&&(e.class=Cn(t)),r&&(e.style=xn(r)),e}const ks=e=>De(e)?e:e==null?"":Ee(e)||Ae(e)&&(e.toString===Os||!be(e.toString))?JSON.stringify(e,Ls,2):String(e),Ls=(e,t)=>t&&t.__v_isRef?Ls(e,t.value):_s(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((r,[n,o],i)=>(r[mi(n,i)+" =>"]=o,r),{})}:Es(t)?{[`Set(${t.size})`]:[...t.values()].map(r=>mi(r))}:ws(t)?mi(t):Ae(t)&&!Ee(t)&&!Ss(t)?String(t):t,mi=(e,t="")=>{var r;return ws(e)?`Symbol(${(r=e.description)!=null?r:t})`:e};/**
* @vue/runtime-core v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Wp(e,t){}const Bp={SETUP_FUNCTION:0,0:"SETUP_FUNCTION",RENDER_FUNCTION:1,1:"RENDER_FUNCTION",WATCH_GETTER:2,2:"WATCH_GETTER",WATCH_CALLBACK:3,3:"WATCH_CALLBACK",WATCH_CLEANUP:4,4:"WATCH_CLEANUP",NATIVE_EVENT_HANDLER:5,5:"NATIVE_EVENT_HANDLER",COMPONENT_EVENT_HANDLER:6,6:"COMPONENT_EVENT_HANDLER",VNODE_HOOK:7,7:"VNODE_HOOK",DIRECTIVE_HOOK:8,8:"DIRECTIVE_HOOK",TRANSITION_HOOK:9,9:"TRANSITION_HOOK",APP_ERROR_HANDLER:10,10:"APP_ERROR_HANDLER",APP_WARN_HANDLER:11,11:"APP_WARN_HANDLER",FUNCTION_REF:12,12:"FUNCTION_REF",ASYNC_COMPONENT_LOADER:13,13:"ASYNC_COMPONENT_LOADER",SCHEDULER:14,14:"SCHEDULER"},qp={sp:"serverPrefetch hook",bc:"beforeCreate hook",c:"created hook",bm:"beforeMount hook",m:"mounted hook",bu:"beforeUpdate hook",u:"updated",bum:"beforeUnmount hook",um:"unmounted hook",a:"activated hook",da:"deactivated hook",ec:"errorCaptured hook",rtc:"renderTracked hook",rtg:"renderTriggered hook",0:"setup function",1:"render function",2:"watcher getter",3:"watcher callback",4:"watcher cleanup function",5:"native event handler",6:"component event handler",7:"vnode hook",8:"directive hook",9:"transition hook",10:"app errorHandler",11:"app warnHandler",12:"ref function",13:"async component loader",14:"scheduler flush. This is likely a Vue internals bug. Please open an issue at https://github.com/vuejs/core ."};function xt(e,t,r,n){try{return n?e(...n):e()}catch(o){rn(o,t,r)}}function rt(e,t,r,n){if(be(e)){const i=xt(e,t,r,n);return i&&hi(i)&&i.catch(a=>{rn(a,t,r)}),i}const o=[];for(let i=0;i<e.length;i++)o.push(rt(e[i],t,r,n));return o}function rn(e,t,r,n=!0){const o=t?t.vnode:null;if(t){let i=t.parent;const a=t.proxy,s=`https://vuejs.org/error-reference/#runtime-${r}`;for(;i;){const l=i.ec;if(l){for(let u=0;u<l.length;u++)if(l[u](e,a,s)===!1)return}i=i.parent}const c=t.appContext.config.errorHandler;if(c){xt(c,null,10,[e,a,s]);return}}}let Qn=!1,gi=!1;const Ke=[];let Et=0;const Tn=[];let Dt=null,on=0;const Rs=Promise.resolve();let yi=null;function an(e){const t=yi||Rs;return e?t.then(this?e.bind(this):e):t}function Gp(e){let t=Et+1,r=Ke.length;for(;t<r;){const n=t+r>>>1,o=Ke[n],i=Xn(o);i<e||i===e&&o.pre?t=n+1:r=n}return t}function Zr(e){(!Ke.length||!Ke.includes(e,Qn&&e.allowRecurse?Et+1:Et))&&(e.id==null?Ke.push(e):Ke.splice(Gp(e.id),0,e),Ps())}function Ps(){!Qn&&!gi&&(gi=!0,yi=Rs.then(As))}function Kp(e){const t=Ke.indexOf(e);t>Et&&Ke.splice(t,1)}function eo(e){Ee(e)?Tn.push(...e):(!Dt||!Dt.includes(e,e.allowRecurse?on+1:on))&&Tn.push(e),Ps()}function Is(e,t,r=Qn?Et+1:0){for(;r<Ke.length;r++){const n=Ke[r];if(n&&n.pre){if(e&&n.id!==e.uid)continue;Ke.splice(r,1),r--,n()}}}function to(e){if(Tn.length){const t=[...new Set(Tn)].sort((r,n)=>Xn(r)-Xn(n));if(Tn.length=0,Dt){Dt.push(...t);return}for(Dt=t,on=0;on<Dt.length;on++)Dt[on]();Dt=null,on=0}}const Xn=e=>e.id==null?1/0:e.id,Yp=(e,t)=>{const r=Xn(e)-Xn(t);if(r===0){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return r};function As(e){gi=!1,Qn=!0,Ke.sort(Yp);try{for(Et=0;Et<Ke.length;Et++){const t=Ke[Et];t&&t.active!==!1&&xt(t,null,14)}}finally{Et=0,Ke.length=0,to(),Qn=!1,yi=null,(Ke.length||Tn.length)&&As()}}let kn,no=[];function js(e,t){var r,n;kn=e,kn?(kn.enabled=!0,no.forEach(({event:o,args:i})=>kn.emit(o,...i)),no=[]):typeof window<"u"&&window.HTMLElement&&!((n=(r=window.navigator)==null?void 0:r.userAgent)!=null&&n.includes("jsdom"))?((t.__VUE_DEVTOOLS_HOOK_REPLAY__=t.__VUE_DEVTOOLS_HOOK_REPLAY__||[]).push(o=>{js(o,t)}),setTimeout(()=>{kn||(t.__VUE_DEVTOOLS_HOOK_REPLAY__=null,no=[])},3e3)):no=[]}function zp(e,t,...r){if(e.isUnmounted)return;const n=e.vnode.props||Le;let o=r;const i=t.startsWith("update:"),a=i&&t.slice(7);if(a&&a in n){const u=`${a==="modelValue"?"model":a}Modifiers`,{number:f,trim:p}=n[u]||Le;p&&(o=r.map(h=>De(h)?h.trim():h)),f&&(o=r.map(Np))}let s,c=n[s=Sn(t)]||n[s=Sn(ft(t))];!c&&i&&(c=n[s=Sn(Kn(t))]),c&&rt(c,e,6,o);const l=n[s+"Once"];if(l){if(!e.emitted)e.emitted={};else if(e.emitted[s])return;e.emitted[s]=!0,rt(l,e,6,o)}}function Ns(e,t,r=!1){const n=t.emitsCache,o=n.get(e);if(o!==void 0)return o;const i=e.emits;let a={},s=!1;if(!be(e)){const c=l=>{const u=Ns(l,t,!0);u&&(s=!0,He(a,u))};!r&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!i&&!s?(Ae(e)&&n.set(e,null),null):(Ee(i)?i.forEach(c=>a[c]=null):He(a,i),Ae(e)&&n.set(e,a),a)}function ro(e,t){return!e||!zr(t)?!1:(t=t.slice(2).replace(/Once$/,""),Te(e,t[0].toLowerCase()+t.slice(1))||Te(e,Kn(t))||Te(e,t))}let je=null,oo=null;function Jn(e){const t=je;return je=e,oo=e&&e.type.__scopeId||null,t}function Fs(e){oo=e}function Ms(){oo=null}const Qp=e=>io;function io(e,t=je,r){if(!t||e._n)return e;const n=(...o)=>{n._d&&Bi(-1);const i=Jn(t);let a;try{a=e(...o)}finally{Jn(i),n._d&&Bi(1)}return a};return n._n=!0,n._c=!0,n._d=!0,n}function ao(e){const{type:t,vnode:r,proxy:n,withProxy:o,props:i,propsOptions:[a],slots:s,attrs:c,emit:l,render:u,renderCache:f,data:p,setupState:h,ctx:b,inheritAttrs:g}=e;let j,k;const E=Jn(e);try{if(r.shapeFlag&4){const y=o||n,C=y;j=at(u.call(C,y,f,i,h,p,b)),k=c}else{const y=t;j=at(y.length>1?y(i,{attrs:c,slots:s,emit:l}):y(i,null)),k=t.props?c:Jp(c)}}catch(y){lr.length=0,rn(y,e,1),j=Re(Ge)}let m=j;if(k&&g!==!1){const y=Object.keys(k),{shapeFlag:C}=m;y.length&&C&7&&(a&&y.some(bs)&&(k=Zp(k,a)),m=mt(m,k))}return r.dirs&&(m=mt(m),m.dirs=m.dirs?m.dirs.concat(r.dirs):r.dirs),r.transition&&(m.transition=r.transition),j=m,Jn(E),j}function Xp(e,t=!0){let r;for(let n=0;n<e.length;n++){const o=e[n];if(Lt(o)){if(o.type!==Ge||o.children==="v-if"){if(r)return;r=o}}else return}return r}const Jp=e=>{let t;for(const r in e)(r==="class"||r==="style"||zr(r))&&((t||(t={}))[r]=e[r]);return t},Zp=(e,t)=>{const r={};for(const n in e)(!bs(n)||!(n.slice(9)in t))&&(r[n]=e[n]);return r};function ed(e,t,r){const{props:n,children:o,component:i}=e,{props:a,children:s,patchFlag:c}=t,l=i.emitsOptions;if(t.dirs||t.transition)return!0;if(r&&c>=0){if(c&1024)return!0;if(c&16)return n?Ds(n,a,l):!!a;if(c&8){const u=t.dynamicProps;for(let f=0;f<u.length;f++){const p=u[f];if(a[p]!==n[p]&&!ro(l,p))return!0}}}else return(o||s)&&(!s||!s.$stable)?!0:n===a?!1:n?a?Ds(n,a,l):!0:!!a;return!1}function Ds(e,t,r){const n=Object.keys(t);if(n.length!==Object.keys(e).length)return!0;for(let o=0;o<n.length;o++){const i=n[o];if(t[i]!==e[i]&&!ro(r,i))return!0}return!1}function bi({vnode:e,parent:t},r){for(;t;){const n=t.subTree;if(n.suspense&&n.suspense.activeBranch===e&&(n.el=e.el),n===e)(e=t.vnode).el=r,t=t.parent;else break}}const _i="components",td="directives";function Vs(e,t){return Ei(_i,e,!0,t)||e}const Us=Symbol.for("v-ndc");function $s(e){return De(e)?Ei(_i,e,!1)||e:e||Us}function Hs(e){return Ei(td,e)}function Ei(e,t,r=!0,n=!1){const o=je||Ve;if(o){const i=o.type;if(e===_i){const s=Ji(i,!1);if(s&&(s===t||s===ft(t)||s===Jr(ft(t))))return i}const a=Ws(o[e]||i[e],t)||Ws(o.appContext[e],t);return!a&&n?i:a}}function Ws(e,t){return e&&(e[t]||e[ft(t)]||e[Jr(ft(t))])}const Bs=e=>e.__isSuspense;let wi=0;const nd={name:"Suspense",__isSuspense:!0,process(e,t,r,n,o,i,a,s,c,l){if(e==null)od(t,r,n,o,i,a,s,c,l);else{if(i&&i.deps>0&&!e.suspense.isInFallback){t.suspense=e.suspense,t.suspense.vnode=t,t.el=e.el;return}id(e,t,r,n,o,a,s,c,l)}},hydrate:ad,create:Oi,normalize:sd},rd=nd;function Zn(e,t){const r=e.props&&e.props[t];be(r)&&r()}function od(e,t,r,n,o,i,a,s,c){const{p:l,o:{createElement:u}}=c,f=u("div"),p=e.suspense=Oi(e,o,n,t,f,r,i,a,s,c);l(null,p.pendingBranch=e.ssContent,f,null,n,p,i,a),p.deps>0?(Zn(e,"onPending"),Zn(e,"onFallback"),l(null,e.ssFallback,t,r,n,null,i,a),Ln(p,e.ssFallback)):p.resolve(!1,!0)}function id(e,t,r,n,o,i,a,s,{p:c,um:l,o:{createElement:u}}){const f=t.suspense=e.suspense;f.vnode=t,t.el=e.el;const p=t.ssContent,h=t.ssFallback,{activeBranch:b,pendingBranch:g,isInFallback:j,isHydrating:k}=f;if(g)f.pendingBranch=p,vt(p,g)?(c(g,p,f.hiddenContainer,null,o,f,i,a,s),f.deps<=0?f.resolve():j&&(k||(c(b,h,r,n,o,null,i,a,s),Ln(f,h)))):(f.pendingId=wi++,k?(f.isHydrating=!1,f.activeBranch=g):l(g,o,f),f.deps=0,f.effects.length=0,f.hiddenContainer=u("div"),j?(c(null,p,f.hiddenContainer,null,o,f,i,a,s),f.deps<=0?f.resolve():(c(b,h,r,n,o,null,i,a,s),Ln(f,h))):b&&vt(p,b)?(c(b,p,r,n,o,f,i,a,s),f.resolve(!0)):(c(null,p,f.hiddenContainer,null,o,f,i,a,s),f.deps<=0&&f.resolve()));else if(b&&vt(p,b))c(b,p,r,n,o,f,i,a,s),Ln(f,p);else if(Zn(t,"onPending"),f.pendingBranch=p,p.shapeFlag&512?f.pendingId=p.component.suspenseId:f.pendingId=wi++,c(null,p,f.hiddenContainer,null,o,f,i,a,s),f.deps<=0)f.resolve();else{const{timeout:E,pendingId:m}=f;E>0?setTimeout(()=>{f.pendingId===m&&f.fallback(h)},E):E===0&&f.fallback(h)}}function Oi(e,t,r,n,o,i,a,s,c,l,u=!1){const{p:f,m:p,um:h,n:b,o:{parentNode:g,remove:j}}=l;let k;const E=cd(e);E&&(t!=null&&t.pendingBranch)&&(k=t.pendingId,t.deps++);const m=e.props?Fp(e.props.timeout):void 0,y=i,C={vnode:e,parent:t,parentComponent:r,namespace:a,container:n,hiddenContainer:o,deps:0,pendingId:wi++,timeout:typeof m=="number"?m:-1,activeBranch:null,pendingBranch:null,isInFallback:!u,isHydrating:u,isUnmounted:!1,effects:[],resolve(S=!1,T=!1){const{vnode:L,activeBranch:R,pendingBranch:$,pendingId:W,effects:D,parentComponent:te,container:he}=C;let pe=!1;C.isHydrating?C.isHydrating=!1:S||(pe=R&&$.transition&&$.transition.mode==="out-in",pe&&(R.transition.afterLeave=()=>{W===C.pendingId&&(p($,he,i===y?b(R):i,0),eo(D))}),R&&(g(R.el)!==C.hiddenContainer&&(i=b(R)),h(R,te,C,!0)),pe||p($,he,i,0)),Ln(C,$),C.pendingBranch=null,C.isInFallback=!1;let z=C.parent,ce=!1;for(;z;){if(z.pendingBranch){z.effects.push(...D),ce=!0;break}z=z.parent}!ce&&!pe&&eo(D),C.effects=[],E&&t&&t.pendingBranch&&k===t.pendingId&&(t.deps--,t.deps===0&&!T&&t.resolve()),Zn(L,"onResolve")},fallback(S){if(!C.pendingBranch)return;const{vnode:T,activeBranch:L,parentComponent:R,container:$,namespace:W}=C;Zn(T,"onFallback");const D=b(L),te=()=>{C.isInFallback&&(f(null,S,$,D,R,null,W,s,c),Ln(C,S))},he=S.transition&&S.transition.mode==="out-in";he&&(L.transition.afterLeave=te),C.isInFallback=!0,h(L,R,null,!0),he||te()},move(S,T,L){C.activeBranch&&p(C.activeBranch,S,T,L),C.container=S},next(){return C.activeBranch&&b(C.activeBranch)},registerDep(S,T){const L=!!C.pendingBranch;L&&C.deps++;const R=S.vnode.el;S.asyncDep.catch($=>{rn($,S,0)}).then($=>{if(S.isUnmounted||C.isUnmounted||C.pendingId!==S.suspenseId)return;S.asyncResolved=!0;const{vnode:W}=S;Qi(S,$,!1),R&&(W.el=R);const D=!R&&S.subTree.el;T(S,W,g(R||S.subTree.el),R?null:b(S.subTree),C,a,c),D&&j(D),bi(S,W.el),L&&--C.deps===0&&C.resolve()})},unmount(S,T){C.isUnmounted=!0,C.activeBranch&&h(C.activeBranch,r,S,T),C.pendingBranch&&h(C.pendingBranch,r,S,T)}};return C}function ad(e,t,r,n,o,i,a,s,c){const l=t.suspense=Oi(t,n,r,e.parentNode,document.createElement("div"),null,o,i,a,s,!0),u=c(e,l.pendingBranch=t.ssContent,r,l,i,a);return l.deps===0&&l.resolve(!1,!0),u}function sd(e){const{shapeFlag:t,children:r}=e,n=t&32;e.ssContent=qs(n?r.default:r),e.ssFallback=n?qs(r.fallback):Re(Ge)}function qs(e){let t;if(be(e)){const r=dn&&e._c;r&&(e._d=!1,$t()),e=e(),r&&(e._d=!0,t=Qe,$c())}return Ee(e)&&(e=Xp(e)),e=at(e),t&&!e.dynamicChildren&&(e.dynamicChildren=t.filter(r=>r!==e)),e}function Gs(e,t){t&&t.pendingBranch?Ee(e)?t.effects.push(...e):t.effects.push(e):eo(e)}function Ln(e,t){e.activeBranch=t;const{vnode:r,parentComponent:n}=e;let o=t.el;for(;!o&&t.component;)t=t.component.subTree,o=t.el;r.el=o,n&&n.subTree===r&&(n.vnode.el=o,bi(n,o))}function cd(e){var t;return((t=e.props)==null?void 0:t.suspensible)!=null&&e.props.suspensible!==!1}const Ks=Symbol.for("v-scx"),Ys=()=>it(Ks);function so(e,t){return er(e,null,t)}function zs(e,t){return er(e,null,{flush:"post"})}function Qs(e,t){return er(e,null,{flush:"sync"})}const co={};function ot(e,t,r){return er(e,t,r)}function er(e,t,{immediate:r,deep:n,flush:o,once:i,onTrack:a,onTrigger:s}=Le){if(t&&i){const S=t;t=(...T)=>{S(...T),C()}}const c=Ve,l=S=>n===!0?S:sn(S,n===!1?1:void 0);let u,f=!1,p=!1;if($e(e)?(u=()=>e.value,f=qn(e)):Mt(e)?(u=()=>l(e),f=!0):Ee(e)?(p=!0,f=e.some(S=>Mt(S)||qn(S)),u=()=>e.map(S=>{if($e(S))return S.value;if(Mt(S))return l(S);if(be(S))return xt(S,c,2)})):be(e)?t?u=()=>xt(e,c,2):u=()=>(h&&h(),rt(e,c,3,[b])):u=ut,t&&n){const S=u;u=()=>sn(S())}let h,b=S=>{h=m.onStop=()=>{xt(S,c,4),h=m.onStop=void 0}},g;if(fr)if(b=ut,t?r&&rt(t,c,3,[u(),p?[]:void 0,b]):u(),o==="sync"){const S=Ys();g=S.__watcherHandles||(S.__watcherHandles=[])}else return ut;let j=p?new Array(e.length).fill(co):co;const k=()=>{if(!(!m.active||!m.dirty))if(t){const S=m.run();(n||f||(p?S.some((T,L)=>Yn(T,j[L])):Yn(S,j)))&&(h&&h(),rt(t,c,3,[S,j===co?void 0:p&&j[0]===co?[]:j,b]),j=S)}else m.run()};k.allowRecurse=!!t;let E;o==="sync"?E=k:o==="post"?E=()=>qe(k,c&&c.suspense):(k.pre=!0,c&&(k.id=c.uid),E=()=>Zr(k));const m=new bn(u,ut,E),y=Nr(),C=()=>{m.stop(),y&&di(y.effects,m)};return t?r?k():j=m.run():o==="post"?qe(m.run.bind(m),c&&c.suspense):m.run(),g&&g.push(C),C}function ld(e,t,r){const n=this.proxy,o=De(e)?e.includes(".")?Xs(n,e):()=>n[e]:e.bind(n,n);let i;be(t)?i=t:(i=t.handler,r=t);const a=hn(this),s=er(o,i.bind(n),r);return a(),s}function Xs(e,t){const r=t.split(".");return()=>{let n=e;for(let o=0;o<r.length&&n;o++)n=n[r[o]];return n}}function sn(e,t,r=0,n){if(!Ae(e)||e.__v_skip)return e;if(t&&t>0){if(r>=t)return e;r++}if(n=n||new Set,n.has(e))return e;if(n.add(e),$e(e))sn(e.value,t,r,n);else if(Ee(e))for(let o=0;o<e.length;o++)sn(e[o],t,r,n);else if(Es(e)||_s(e))e.forEach(o=>{sn(o,t,r,n)});else if(Ss(e))for(const o in e)sn(e[o],t,r,n);return e}function Js(e,t){if(je===null)return e;const r=Co(je)||je.proxy,n=e.dirs||(e.dirs=[]);for(let o=0;o<t.length;o++){let[i,a,s,c=Le]=t[o];i&&(be(i)&&(i={mounted:i,updated:i}),i.deep&&sn(a),n.push({dir:i,instance:r,value:a,oldValue:void 0,arg:s,modifiers:c}))}return e}function wt(e,t,r,n){const o=e.dirs,i=t&&t.dirs;for(let a=0;a<o.length;a++){const s=o[a];i&&(s.oldValue=i[a].value);let c=s.dir[n];c&&(Zt(),rt(c,r,8,[e.el,s,e,t]),en())}}const Vt=Symbol("_leaveCb"),lo=Symbol("_enterCb");function Si(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return kt(()=>{e.isMounted=!0}),or(()=>{e.isUnmounting=!0}),e}const pt=[Function,Array],xi={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:pt,onEnter:pt,onAfterEnter:pt,onEnterCancelled:pt,onBeforeLeave:pt,onLeave:pt,onAfterLeave:pt,onLeaveCancelled:pt,onBeforeAppear:pt,onAppear:pt,onAfterAppear:pt,onAppearCancelled:pt},ud={name:"BaseTransition",props:xi,setup(e,{slots:t}){const r=dt(),n=Si();return()=>{const o=t.default&&uo(t.default(),!0);if(!o||!o.length)return;let i=o[0];if(o.length>1){for(const p of o)if(p.type!==Ge){i=p;break}}const a=Se(e),{mode:s}=a;if(n.isLeaving)return Ci(i);const c=tc(i);if(!c)return Ci(i);const l=Rn(c,a,n,r);cn(c,l);const u=r.subTree,f=u&&tc(u);if(f&&f.type!==Ge&&!vt(c,f)){const p=Rn(f,a,n,r);if(cn(f,p),s==="out-in")return n.isLeaving=!0,p.afterLeave=()=>{n.isLeaving=!1,r.update.active!==!1&&(r.effect.dirty=!0,r.update())},Ci(i);s==="in-out"&&c.type!==Ge&&(p.delayLeave=(h,b,g)=>{const j=ec(n,f);j[String(f.key)]=f,h[Vt]=()=>{b(),h[Vt]=void 0,delete l.delayedLeave},l.delayedLeave=g})}return i}}},Zs=ud;function ec(e,t){const{leavingVNodes:r}=e;let n=r.get(t.type);return n||(n=Object.create(null),r.set(t.type,n)),n}function Rn(e,t,r,n){const{appear:o,mode:i,persisted:a=!1,onBeforeEnter:s,onEnter:c,onAfterEnter:l,onEnterCancelled:u,onBeforeLeave:f,onLeave:p,onAfterLeave:h,onLeaveCancelled:b,onBeforeAppear:g,onAppear:j,onAfterAppear:k,onAppearCancelled:E}=t,m=String(e.key),y=ec(r,e),C=(L,R)=>{L&&rt(L,n,9,R)},S=(L,R)=>{const $=R[1];C(L,R),Ee(L)?L.every(W=>W.length<=1)&&$():L.length<=1&&$()},T={mode:i,persisted:a,beforeEnter(L){let R=s;if(!r.isMounted)if(o)R=g||s;else return;L[Vt]&&L[Vt](!0);const $=y[m];$&&vt(e,$)&&$.el[Vt]&&$.el[Vt](),C(R,[L])},enter(L){let R=c,$=l,W=u;if(!r.isMounted)if(o)R=j||c,$=k||l,W=E||u;else return;let D=!1;const te=L[lo]=he=>{D||(D=!0,he?C(W,[L]):C($,[L]),T.delayedLeave&&T.delayedLeave(),L[lo]=void 0)};R?S(R,[L,te]):te()},leave(L,R){const $=String(e.key);if(L[lo]&&L[lo](!0),r.isUnmounting)return R();C(f,[L]);let W=!1;const D=L[Vt]=te=>{W||(W=!0,R(),te?C(b,[L]):C(h,[L]),L[Vt]=void 0,y[$]===e&&delete y[$])};y[$]=e,p?S(p,[L,D]):D()},clone(L){return Rn(L,t,r,n)}};return T}function Ci(e){if(tr(e))return e=mt(e),e.children=null,e}function tc(e){return tr(e)?e.children?e.children[0]:void 0:e}function cn(e,t){e.shapeFlag&6&&e.component?cn(e.component.subTree,t):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function uo(e,t=!1,r){let n=[],o=0;for(let i=0;i<e.length;i++){let a=e[i];const s=r==null?a.key:String(r)+String(a.key!=null?a.key:i);a.type===We?(a.patchFlag&128&&o++,n=n.concat(uo(a.children,t,s))):(t||a.type!==Ge)&&n.push(s!=null?mt(a,{key:s}):a)}if(o>1)for(let i=0;i<n.length;i++)n[i].patchFlag=-2;return n}/*! #__NO_SIDE_EFFECTS__ */function Ct(e,t){return be(e)?He({name:e.name},t,{setup:e}):e}const ln=e=>!!e.type.__asyncLoader;/*! #__NO_SIDE_EFFECTS__ */function nc(e){be(e)&&(e={loader:e});const{loader:t,loadingComponent:r,errorComponent:n,delay:o=200,timeout:i,suspensible:a=!0,onError:s}=e;let c=null,l,u=0;const f=()=>(u++,c=null,p()),p=()=>{let h;return c||(h=c=t().catch(b=>{if(b=b instanceof Error?b:new Error(String(b)),s)return new Promise((g,j)=>{s(b,()=>g(f()),()=>j(b),u+1)});throw b}).then(b=>h!==c&&c?c:(b&&(b.__esModule||b[Symbol.toStringTag]==="Module")&&(b=b.default),l=b,b)))};return Ct({name:"AsyncComponentWrapper",__asyncLoader:p,get __asyncResolved(){return l},setup(){const h=Ve;if(l)return()=>Ti(l,h);const b=E=>{c=null,rn(E,h,13,!n)};if(a&&h.suspense||fr)return p().then(E=>()=>Ti(E,h)).catch(E=>(b(E),()=>n?Re(n,{error:E}):null));const g=Be(!1),j=Be(),k=Be(!!o);return o&&setTimeout(()=>{k.value=!1},o),i!=null&&setTimeout(()=>{if(!g.value&&!j.value){const E=new Error(`Async component timed out after ${i}ms.`);b(E),j.value=E}},i),p().then(()=>{g.value=!0,h.parent&&tr(h.parent.vnode)&&(h.parent.effect.dirty=!0,Zr(h.parent.update))}).catch(E=>{b(E),j.value=E}),()=>{if(g.value&&l)return Ti(l,h);if(j.value&&n)return Re(n,{error:j.value});if(r&&!k.value)return Re(r)}}})}function Ti(e,t){const{ref:r,props:n,children:o,ce:i}=t.vnode,a=Re(e,n,o);return a.ref=r,a.ce=i,delete t.vnode.ce,a}const tr=e=>e.type.__isKeepAlive,fd={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number]},setup(e,{slots:t}){const r=dt(),n=r.ctx;if(!n.renderer)return()=>{const E=t.default&&t.default();return E&&E.length===1?E[0]:E};const o=new Map,i=new Set;let a=null;const s=r.suspense,{renderer:{p:c,m:l,um:u,o:{createElement:f}}}=n,p=f("div");n.activate=(E,m,y,C,S)=>{const T=E.component;l(E,m,y,0,s),c(T.vnode,E,m,y,T,s,C,E.slotScopeIds,S),qe(()=>{T.isDeactivated=!1,T.a&&zn(T.a);const L=E.props&&E.props.onVnodeMounted;L&&Xe(L,T.parent,E)},s)},n.deactivate=E=>{const m=E.component;l(E,p,null,1,s),qe(()=>{m.da&&zn(m.da);const y=E.props&&E.props.onVnodeUnmounted;y&&Xe(y,m.parent,E),m.isDeactivated=!0},s)};function h(E){Ri(E),u(E,r,s,!0)}function b(E){o.forEach((m,y)=>{const C=Ji(m.type);C&&(!E||!E(C))&&g(y)})}function g(E){const m=o.get(E);!a||!vt(m,a)?h(m):a&&Ri(a),o.delete(E),i.delete(E)}ot(()=>[e.include,e.exclude],([E,m])=>{E&&b(y=>nr(E,y)),m&&b(y=>!nr(m,y))},{flush:"post",deep:!0});let j=null;const k=()=>{j!=null&&o.set(j,Pi(r.subTree))};return kt(k),rr(k),or(()=>{o.forEach(E=>{const{subTree:m,suspense:y}=r,C=Pi(m);if(E.type===C.type&&E.key===C.key){Ri(C);const S=C.component.da;S&&qe(S,y);return}h(E)})}),()=>{if(j=null,!t.default)return null;const E=t.default(),m=E[0];if(E.length>1)return a=null,E;if(!Lt(m)||!(m.shapeFlag&4)&&!(m.shapeFlag&128))return a=null,m;let y=Pi(m);const C=y.type,S=Ji(ln(y)?y.type.__asyncResolved||{}:C),{include:T,exclude:L,max:R}=e;if(T&&(!S||!nr(T,S))||L&&S&&nr(L,S))return a=y,m;const $=y.key==null?C:y.key,W=o.get($);return y.el&&(y=mt(y),m.shapeFlag&128&&(m.ssContent=y)),j=$,W?(y.el=W.el,y.component=W.component,y.transition&&cn(y,y.transition),y.shapeFlag|=512,i.delete($),i.add($)):(i.add($),R&&i.size>parseInt(R,10)&&g(i.values().next().value)),y.shapeFlag|=256,a=y,Bs(m.type)?m:y}}},rc=fd;function nr(e,t){return Ee(e)?e.some(r=>nr(r,t)):De(e)?e.split(",").includes(t):Ip(e)?e.test(t):!1}function ki(e,t){oc(e,"a",t)}function Li(e,t){oc(e,"da",t)}function oc(e,t,r=Ve){const n=e.__wdc||(e.__wdc=()=>{let o=r;for(;o;){if(o.isDeactivated)return;o=o.parent}return e()});if(fo(t,n,r),r){let o=r.parent;for(;o&&o.parent;)tr(o.parent.vnode)&&pd(n,t,r,o),o=o.parent}}function pd(e,t,r,n){const o=fo(t,e,n,!0);Pn(()=>{di(n[t],o)},r)}function Ri(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function Pi(e){return e.shapeFlag&128?e.ssContent:e}function fo(e,t,r=Ve,n=!1){if(r){const o=r[e]||(r[e]=[]),i=t.__weh||(t.__weh=(...a)=>{if(r.isUnmounted)return;Zt();const s=hn(r),c=rt(t,r,e,a);return s(),en(),c});return n?o.unshift(i):o.push(i),i}}const Tt=e=>(t,r=Ve)=>(!fr||e==="sp")&&fo(e,(...n)=>t(...n),r),Ii=Tt("bm"),kt=Tt("m"),po=Tt("bu"),rr=Tt("u"),or=Tt("bum"),Pn=Tt("um"),ic=Tt("sp"),ac=Tt("rtg"),sc=Tt("rtc");function cc(e,t=Ve){fo("ec",e,t)}function lc(e,t,r,n){let o;const i=r&&r[n];if(Ee(e)||De(e)){o=new Array(e.length);for(let a=0,s=e.length;a<s;a++)o[a]=t(e[a],a,void 0,i&&i[a])}else if(typeof e=="number"){o=new Array(e);for(let a=0;a<e;a++)o[a]=t(a+1,a,void 0,i&&i[a])}else if(Ae(e))if(e[Symbol.iterator])o=Array.from(e,(a,s)=>t(a,s,void 0,i&&i[s]));else{const a=Object.keys(e);o=new Array(a.length);for(let s=0,c=a.length;s<c;s++){const l=a[s];o[s]=t(e[l],l,s,i&&i[s])}}else o=[];return r&&(r[n]=o),o}function uc(e,t){for(let r=0;r<t.length;r++){const n=t[r];if(Ee(n))for(let o=0;o<n.length;o++)e[n[o].name]=n[o].fn;else n&&(e[n.name]=n.key?(...o)=>{const i=n.fn(...o);return i&&(i.key=n.key),i}:n.fn)}return e}function fc(e,t,r={},n,o){if(je.isCE||je.parent&&ln(je.parent)&&je.parent.isCE)return t!=="default"&&(r.name=t),Re("slot",r,n&&n());let i=e[t];i&&i._c&&(i._d=!1),$t();const a=i&&pc(i(r)),s=bo(We,{key:r.key||a&&a.key||`_${t}`},a||(n?n():[]),a&&e._===1?64:-2);return!o&&s.scopeId&&(s.slotScopeIds=[s.scopeId+"-s"]),i&&i._c&&(i._d=!0),s}function pc(e){return e.some(t=>Lt(t)?!(t.type===Ge||t.type===We&&!pc(t.children)):!0)?e:null}function dc(e,t){const r={};for(const n in e)r[t&&/[A-Z]/.test(n)?`on:${n}`:Sn(n)]=e[n];return r}const Ai=e=>e?Gc(e)?Co(e)||e.proxy:Ai(e.parent):null,ir=He(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Ai(e.parent),$root:e=>Ai(e.root),$emit:e=>e.emit,$options:e=>Mi(e),$forceUpdate:e=>e.f||(e.f=()=>{e.effect.dirty=!0,Zr(e.update)}),$nextTick:e=>e.n||(e.n=an.bind(e.proxy)),$watch:e=>ld.bind(e)}),ji=(e,t)=>e!==Le&&!e.__isScriptSetup&&Te(e,t),Ni={get({_:e},t){const{ctx:r,setupState:n,data:o,props:i,accessCache:a,type:s,appContext:c}=e;let l;if(t[0]!=="$"){const h=a[t];if(h!==void 0)switch(h){case 1:return n[t];case 2:return o[t];case 4:return r[t];case 3:return i[t]}else{if(ji(n,t))return a[t]=1,n[t];if(o!==Le&&Te(o,t))return a[t]=2,o[t];if((l=e.propsOptions[0])&&Te(l,t))return a[t]=3,i[t];if(r!==Le&&Te(r,t))return a[t]=4,r[t];Fi&&(a[t]=0)}}const u=ir[t];let f,p;if(u)return t==="$attrs"&&ze(e,"get",t),u(e);if((f=s.__cssModules)&&(f=f[t]))return f;if(r!==Le&&Te(r,t))return a[t]=4,r[t];if(p=c.config.globalProperties,Te(p,t))return p[t]},set({_:e},t,r){const{data:n,setupState:o,ctx:i}=e;return ji(o,t)?(o[t]=r,!0):n!==Le&&Te(n,t)?(n[t]=r,!0):Te(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=r,!0)},has({_:{data:e,setupState:t,accessCache:r,ctx:n,appContext:o,propsOptions:i}},a){let s;return!!r[a]||e!==Le&&Te(e,a)||ji(t,a)||(s=i[0])&&Te(s,a)||Te(n,a)||Te(ir,a)||Te(o.config.globalProperties,a)},defineProperty(e,t,r){return r.get!=null?e._.accessCache[t]=0:Te(r,"value")&&this.set(e,t,r.value,null),Reflect.defineProperty(e,t,r)}},dd=He({},Ni,{get(e,t){if(t!==Symbol.unscopables)return Ni.get(e,t,e)},has(e,t){return t[0]!=="_"&&!Dp(t)}});function hd(){return null}function vd(){return null}function md(e){}function gd(e){}function yd(){return null}function bd(){}function _d(e,t){return null}function hc(){return mc().slots}function vc(){return mc().attrs}function mc(){const e=dt();return e.setupContext||(e.setupContext=zc(e))}function ar(e){return Ee(e)?e.reduce((t,r)=>(t[r]=null,t),{}):e}function Ed(e,t){const r=ar(e);for(const n in t){if(n.startsWith("__skip"))continue;let o=r[n];o?Ee(o)||be(o)?o=r[n]={type:o,default:t[n]}:o.default=t[n]:o===null&&(o=r[n]={default:t[n]}),o&&t[`__skip_${n}`]&&(o.skipFactory=!0)}return r}function wd(e,t){return!e||!t?e||t:Ee(e)&&Ee(t)?e.concat(t):He({},ar(e),ar(t))}function Od(e,t){const r={};for(const n in e)t.includes(n)||Object.defineProperty(r,n,{enumerable:!0,get:()=>e[n]});return r}function Sd(e){const t=dt();let r=e();return zi(),hi(r)&&(r=r.catch(n=>{throw hn(t),n})),[r,()=>hn(t)]}let Fi=!0;function xd(e){const t=Mi(e),r=e.proxy,n=e.ctx;Fi=!1,t.beforeCreate&&gc(t.beforeCreate,e,"bc");const{data:o,computed:i,methods:a,watch:s,provide:c,inject:l,created:u,beforeMount:f,mounted:p,beforeUpdate:h,updated:b,activated:g,deactivated:j,beforeDestroy:k,beforeUnmount:E,destroyed:m,unmounted:y,render:C,renderTracked:S,renderTriggered:T,errorCaptured:L,serverPrefetch:R,expose:$,inheritAttrs:W,components:D,directives:te,filters:he}=t;if(l&&Cd(l,n,null),a)for(const z in a){const ce=a[z];be(ce)&&(n[z]=ce.bind(r))}if(o){const z=o.call(r,r);Ae(z)&&(e.data=_n(z))}if(Fi=!0,i)for(const z in i){const ce=i[z],de=be(ce)?ce.bind(r,r):be(ce.get)?ce.get.bind(r,r):ut,Ie=!be(ce)&&be(ce.set)?ce.set.bind(r):ut,Oe=Ne({get:de,set:Ie});Object.defineProperty(n,z,{enumerable:!0,configurable:!0,get:()=>Oe.value,set:V=>Oe.value=V})}if(s)for(const z in s)yc(s[z],n,r,z);if(c){const z=be(c)?c.call(r):c;Reflect.ownKeys(z).forEach(ce=>{An(ce,z[ce])})}u&&gc(u,e,"c");function pe(z,ce){Ee(ce)?ce.forEach(de=>z(de.bind(r))):ce&&z(ce.bind(r))}if(pe(Ii,f),pe(kt,p),pe(po,h),pe(rr,b),pe(ki,g),pe(Li,j),pe(cc,L),pe(sc,S),pe(ac,T),pe(or,E),pe(Pn,y),pe(ic,R),Ee($))if($.length){const z=e.exposed||(e.exposed={});$.forEach(ce=>{Object.defineProperty(z,ce,{get:()=>r[ce],set:de=>r[ce]=de})})}else e.exposed||(e.exposed={});C&&e.render===ut&&(e.render=C),W!=null&&(e.inheritAttrs=W),D&&(e.components=D),te&&(e.directives=te)}function Cd(e,t,r=ut){Ee(e)&&(e=Di(e));for(const n in e){const o=e[n];let i;Ae(o)?"default"in o?i=it(o.from||n,o.default,!0):i=it(o.from||n):i=it(o),$e(i)?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>i.value,set:a=>i.value=a}):t[n]=i}}function gc(e,t,r){rt(Ee(e)?e.map(n=>n.bind(t.proxy)):e.bind(t.proxy),t,r)}function yc(e,t,r,n){const o=n.includes(".")?Xs(r,n):()=>r[n];if(De(e)){const i=t[e];be(i)&&ot(o,i)}else if(be(e))ot(o,e.bind(r));else if(Ae(e))if(Ee(e))e.forEach(i=>yc(i,t,r,n));else{const i=be(e.handler)?e.handler.bind(r):t[e.handler];be(i)&&ot(o,i,e)}}function Mi(e){const t=e.type,{mixins:r,extends:n}=t,{mixins:o,optionsCache:i,config:{optionMergeStrategies:a}}=e.appContext,s=i.get(t);let c;return s?c=s:!o.length&&!r&&!n?c=t:(c={},o.length&&o.forEach(l=>ho(c,l,a,!0)),ho(c,t,a)),Ae(t)&&i.set(t,c),c}function ho(e,t,r,n=!1){const{mixins:o,extends:i}=t;i&&ho(e,i,r,!0),o&&o.forEach(a=>ho(e,a,r,!0));for(const a in t)if(!(n&&a==="expose")){const s=Td[a]||r&&r[a];e[a]=s?s(e[a],t[a]):t[a]}return e}const Td={data:bc,props:_c,emits:_c,methods:sr,computed:sr,beforeCreate:Ye,created:Ye,beforeMount:Ye,mounted:Ye,beforeUpdate:Ye,updated:Ye,beforeDestroy:Ye,beforeUnmount:Ye,destroyed:Ye,unmounted:Ye,activated:Ye,deactivated:Ye,errorCaptured:Ye,serverPrefetch:Ye,components:sr,directives:sr,watch:Ld,provide:bc,inject:kd};function bc(e,t){return t?e?function(){return He(be(e)?e.call(this,this):e,be(t)?t.call(this,this):t)}:t:e}function kd(e,t){return sr(Di(e),Di(t))}function Di(e){if(Ee(e)){const t={};for(let r=0;r<e.length;r++)t[e[r]]=e[r];return t}return e}function Ye(e,t){return e?[...new Set([].concat(e,t))]:t}function sr(e,t){return e?He(Object.create(null),e,t):t}function _c(e,t){return e?Ee(e)&&Ee(t)?[...new Set([...e,...t])]:He(Object.create(null),ar(e),ar(t??{})):t}function Ld(e,t){if(!e)return t;if(!t)return e;const r=He(Object.create(null),e);for(const n in t)r[n]=Ye(e[n],t[n]);return r}function Ec(){return{app:null,config:{isNativeTag:Rp,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Rd=0;function Pd(e,t){return function(r,n=null){be(r)||(r=He({},r)),n!=null&&!Ae(n)&&(n=null);const o=Ec(),i=new WeakSet;let a=!1;const s=o.app={_uid:Rd++,_component:r,_props:n,_container:null,_context:o,_instance:null,version:Xc,get config(){return o.config},set config(c){},use(c,...l){return i.has(c)||(c&&be(c.install)?(i.add(c),c.install(s,...l)):be(c)&&(i.add(c),c(s,...l))),s},mixin(c){return o.mixins.includes(c)||o.mixins.push(c),s},component(c,l){return l?(o.components[c]=l,s):o.components[c]},directive(c,l){return l?(o.directives[c]=l,s):o.directives[c]},mount(c,l,u){if(!a){const f=Re(r,n);return f.appContext=o,u===!0?u="svg":u===!1&&(u=void 0),l&&t?t(f,c):e(f,c,u),a=!0,s._container=c,c.__vue_app__=s,Co(f.component)||f.component.proxy}},unmount(){a&&(e(null,s._container),delete s._container.__vue_app__)},provide(c,l){return o.provides[c]=l,s},runWithContext(c){const l=In;In=s;try{return c()}finally{In=l}}};return s}}let In=null;function An(e,t){if(Ve){let r=Ve.provides;const n=Ve.parent&&Ve.parent.provides;n===r&&(r=Ve.provides=Object.create(n)),r[e]=t}}function it(e,t,r=!1){const n=Ve||je;if(n||In){const o=n?n.parent==null?n.vnode.appContext&&n.vnode.appContext.provides:n.parent.provides:In._context.provides;if(o&&e in o)return o[e];if(arguments.length>1)return r&&be(t)?t.call(n&&n.proxy):t}}function wc(){return!!(Ve||je||In)}function Id(e,t,r,n=!1){const o={},i={};vi(i,_o,1),e.propsDefaults=Object.create(null),Oc(e,t,o,i);for(const a in e.propsOptions[0])a in o||(o[a]=void 0);r?e.props=n?o:Br(o):e.type.props?e.props=o:e.props=i,e.attrs=i}function Ad(e,t,r,n){const{props:o,attrs:i,vnode:{patchFlag:a}}=e,s=Se(o),[c]=e.propsOptions;let l=!1;if((n||a>0)&&!(a&16)){if(a&8){const u=e.vnode.dynamicProps;for(let f=0;f<u.length;f++){let p=u[f];if(ro(e.emitsOptions,p))continue;const h=t[p];if(c)if(Te(i,p))h!==i[p]&&(i[p]=h,l=!0);else{const b=ft(p);o[b]=Vi(c,s,b,h,e,!1)}else h!==i[p]&&(i[p]=h,l=!0)}}}else{Oc(e,t,o,i)&&(l=!0);let u;for(const f in s)(!t||!Te(t,f)&&((u=Kn(f))===f||!Te(t,u)))&&(c?r&&(r[f]!==void 0||r[u]!==void 0)&&(o[f]=Vi(c,s,f,void 0,e,!0)):delete o[f]);if(i!==s)for(const f in i)(!t||!Te(t,f))&&(delete i[f],l=!0)}l&&St(e,"set","$attrs")}function Oc(e,t,r,n){const[o,i]=e.propsOptions;let a=!1,s;if(t)for(let c in t){if(On(c))continue;const l=t[c];let u;o&&Te(o,u=ft(c))?!i||!i.includes(u)?r[u]=l:(s||(s={}))[u]=l:ro(e.emitsOptions,c)||(!(c in n)||l!==n[c])&&(n[c]=l,a=!0)}if(i){const c=Se(r),l=s||Le;for(let u=0;u<i.length;u++){const f=i[u];r[f]=Vi(o,c,f,l[f],e,!Te(l,f))}}return a}function Vi(e,t,r,n,o,i){const a=e[r];if(a!=null){const s=Te(a,"default");if(s&&n===void 0){const c=a.default;if(a.type!==Function&&!a.skipFactory&&be(c)){const{propsDefaults:l}=o;if(r in l)n=l[r];else{const u=hn(o);n=l[r]=c.call(null,t),u()}}else n=c}a[0]&&(i&&!s?n=!1:a[1]&&(n===""||n===Kn(r))&&(n=!0))}return n}function Sc(e,t,r=!1){const n=t.propsCache,o=n.get(e);if(o)return o;const i=e.props,a={},s=[];let c=!1;if(!be(e)){const u=f=>{c=!0;const[p,h]=Sc(f,t,!0);He(a,p),h&&s.push(...h)};!r&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!i&&!c)return Ae(e)&&n.set(e,wn),wn;if(Ee(i))for(let u=0;u<i.length;u++){const f=ft(i[u]);xc(f)&&(a[f]=Le)}else if(i)for(const u in i){const f=ft(u);if(xc(f)){const p=i[u],h=a[f]=Ee(p)||be(p)?{type:p}:He({},p);if(h){const b=kc(Boolean,h.type),g=kc(String,h.type);h[0]=b>-1,h[1]=g<0||b<g,(b>-1||Te(h,"default"))&&s.push(f)}}}const l=[a,s];return Ae(e)&&n.set(e,l),l}function xc(e){return e[0]!=="$"&&!On(e)}function Cc(e){return e===null?"null":typeof e=="function"?e.name||"":typeof e=="object"&&e.constructor&&e.constructor.name||""}function Tc(e,t){return Cc(e)===Cc(t)}function kc(e,t){return Ee(t)?t.findIndex(r=>Tc(r,e)):be(t)&&Tc(t,e)?0:-1}const Lc=e=>e[0]==="_"||e==="$stable",Ui=e=>Ee(e)?e.map(at):[at(e)],jd=(e,t,r)=>{if(t._n)return t;const n=io((...o)=>Ui(t(...o)),r);return n._c=!1,n},Rc=(e,t,r)=>{const n=e._ctx;for(const o in e){if(Lc(o))continue;const i=e[o];if(be(i))t[o]=jd(o,i,n);else if(i!=null){const a=Ui(i);t[o]=()=>a}}},Pc=(e,t)=>{const r=Ui(t);e.slots.default=()=>r},Nd=(e,t)=>{if(e.vnode.shapeFlag&32){const r=t._;r?(e.slots=Se(t),vi(t,"_",r)):Rc(t,e.slots={})}else e.slots={},t&&Pc(e,t);vi(e.slots,_o,1)},Fd=(e,t,r)=>{const{vnode:n,slots:o}=e;let i=!0,a=Le;if(n.shapeFlag&32){const s=t._;s?r&&s===1?i=!1:(He(o,t),!r&&s===1&&delete o._):(i=!t.$stable,Rc(t,o)),a=t}else t&&(Pc(e,t),a={default:1});if(i)for(const s in o)!Lc(s)&&a[s]==null&&delete o[s]};function vo(e,t,r,n,o=!1){if(Ee(e)){e.forEach((p,h)=>vo(p,t&&(Ee(t)?t[h]:t),r,n,o));return}if(ln(n)&&!o)return;const i=n.shapeFlag&4?Co(n.component)||n.component.proxy:n.el,a=o?null:i,{i:s,r:c}=e,l=t&&t.r,u=s.refs===Le?s.refs={}:s.refs,f=s.setupState;if(l!=null&&l!==c&&(De(l)?(u[l]=null,Te(f,l)&&(f[l]=null)):$e(l)&&(l.value=null)),be(c))xt(c,s,12,[a,u]);else{const p=De(c),h=$e(c);if(p||h){const b=()=>{if(e.f){const g=p?Te(f,c)?f[c]:u[c]:c.value;o?Ee(g)&&di(g,i):Ee(g)?g.includes(i)||g.push(i):p?(u[c]=[i],Te(f,c)&&(f[c]=u[c])):(c.value=[i],e.k&&(u[e.k]=c.value))}else p?(u[c]=a,Te(f,c)&&(f[c]=a)):h&&(c.value=a,e.k&&(u[e.k]=a))};a?(b.id=-1,qe(b,r)):b()}}}let un=!1;const Md=e=>e.namespaceURI.includes("svg")&&e.tagName!=="foreignObject",Dd=e=>e.namespaceURI.includes("MathML"),mo=e=>{if(Md(e))return"svg";if(Dd(e))return"mathml"},go=e=>e.nodeType===8;function Vd(e){const{mt:t,p:r,o:{patchProp:n,createText:o,nextSibling:i,parentNode:a,remove:s,insert:c,createComment:l}}=e,u=(m,y)=>{if(!y.hasChildNodes()){r(null,m,y),to(),y._vnode=m;return}un=!1,f(y.firstChild,m,null,null,null),to(),y._vnode=m},f=(m,y,C,S,T,L=!1)=>{const R=go(m)&&m.data==="[",$=()=>g(m,y,C,S,T,R),{type:W,ref:D,shapeFlag:te,patchFlag:he}=y;let pe=m.nodeType;y.el=m,he===-2&&(L=!1,y.dynamicChildren=null);let z=null;switch(W){case Ut:pe!==3?y.children===""?(c(y.el=o(""),a(m),m),z=m):z=$():(m.data!==y.children&&(un=!0,m.data=y.children),z=i(m));break;case Ge:E(m)?(z=i(m),k(y.el=m.content.firstChild,m,C)):pe!==8||R?z=$():z=i(m);break;case pn:if(R&&(m=i(m),pe=m.nodeType),pe===1||pe===3){z=m;const ce=!y.children.length;for(let de=0;de<y.staticCount;de++)ce&&(y.children+=z.nodeType===1?z.outerHTML:z.data),de===y.staticCount-1&&(y.anchor=z),z=i(z);return R?i(z):z}else $();break;case We:R?z=b(m,y,C,S,T,L):z=$();break;default:if(te&1)(pe!==1||y.type.toLowerCase()!==m.tagName.toLowerCase())&&!E(m)?z=$():z=p(m,y,C,S,T,L);else if(te&6){y.slotScopeIds=T;const ce=a(m);if(R?z=j(m):go(m)&&m.data==="teleport start"?z=j(m,m.data,"teleport end"):z=i(m),t(y,ce,null,C,S,mo(ce),L),ln(y)){let de;R?(de=Re(We),de.anchor=z?z.previousSibling:ce.lastChild):de=m.nodeType===3?Oo(""):Re("div"),de.el=m,y.component.subTree=de}}else te&64?pe!==8?z=$():z=y.type.hydrate(m,y,C,S,T,L,e,h):te&128&&(z=y.type.hydrate(m,y,C,S,mo(a(m)),T,L,e,f))}return D!=null&&vo(D,null,S,y),z},p=(m,y,C,S,T,L)=>{L=L||!!y.dynamicChildren;const{type:R,props:$,patchFlag:W,shapeFlag:D,dirs:te,transition:he}=y,pe=R==="input"||R==="option";if(pe||W!==-1){te&&wt(y,null,C,"created");let z=!1;if(E(m)){z=Nc(S,he)&&C&&C.vnode.props&&C.vnode.props.appear;const de=m.content.firstChild;z&&he.beforeEnter(de),k(de,m,C),y.el=m=de}if(D&16&&!($&&($.innerHTML||$.textContent))){let de=h(m.firstChild,y,m,C,S,T,L);for(;de;){un=!0;const Ie=de;de=de.nextSibling,s(Ie)}}else D&8&&m.textContent!==y.children&&(un=!0,m.textContent=y.children);if($)if(pe||!L||W&48)for(const de in $)(pe&&(de.endsWith("value")||de==="indeterminate")||zr(de)&&!On(de)||de[0]===".")&&n(m,de,null,$[de],void 0,void 0,C);else $.onClick&&n(m,"onClick",null,$.onClick,void 0,void 0,C);let ce;(ce=$&&$.onVnodeBeforeMount)&&Xe(ce,C,y),te&&wt(y,null,C,"beforeMount"),((ce=$&&$.onVnodeMounted)||te||z)&&Gs(()=>{ce&&Xe(ce,C,y),z&&he.enter(m),te&&wt(y,null,C,"mounted")},S)}return m.nextSibling},h=(m,y,C,S,T,L,R)=>{R=R||!!y.dynamicChildren;const $=y.children,W=$.length;for(let D=0;D<W;D++){const te=R?$[D]:$[D]=at($[D]);if(m)m=f(m,te,S,T,L,R);else{if(te.type===Ut&&!te.children)continue;un=!0,r(null,te,C,null,S,T,mo(C),L)}}return m},b=(m,y,C,S,T,L)=>{const{slotScopeIds:R}=y;R&&(T=T?T.concat(R):R);const $=a(m),W=h(i(m),y,$,C,S,T,L);return W&&go(W)&&W.data==="]"?i(y.anchor=W):(un=!0,c(y.anchor=l("]"),$,W),W)},g=(m,y,C,S,T,L)=>{if(un=!0,y.el=null,L){const W=j(m);for(;;){const D=i(m);if(D&&D!==W)s(D);else break}}const R=i(m),$=a(m);return s(m),r(null,y,$,R,C,S,mo($),T),R},j=(m,y="[",C="]")=>{let S=0;for(;m;)if(m=i(m),m&&go(m)&&(m.data===y&&S++,m.data===C)){if(S===0)return i(m);S--}return m},k=(m,y,C)=>{const S=y.parentNode;S&&S.replaceChild(m,y);let T=C;for(;T;)T.vnode.el===y&&(T.vnode.el=T.subTree.el=m),T=T.parent},E=m=>m.nodeType===1&&m.tagName.toLowerCase()==="template";return[u,f]}const qe=Gs;function Ic(e){return jc(e)}function Ac(e){return jc(e,Vd)}function jc(e,t){const r=Cs();r.__VUE__=!0;const{insert:n,remove:o,patchProp:i,createElement:a,createText:s,createComment:c,setText:l,setElementText:u,parentNode:f,nextSibling:p,setScopeId:h=ut,insertStaticContent:b}=e,g=(v,d,_,M=null,A=null,B=null,J=void 0,G=null,Z=!!d.dynamicChildren)=>{if(v===d)return;v&&!vt(v,d)&&(M=x(v),N(v,A,B,!0),v=null),d.patchFlag===-2&&(Z=!1,d.dynamicChildren=null);const{type:q,ref:ee,shapeFlag:ne}=d;switch(q){case Ut:j(v,d,_,M);break;case Ge:k(v,d,_,M);break;case pn:v==null&&E(d,_,M,J);break;case We:D(v,d,_,M,A,B,J,G,Z);break;default:ne&1?C(v,d,_,M,A,B,J,G,Z):ne&6?te(v,d,_,M,A,B,J,G,Z):(ne&64||ne&128)&&q.process(v,d,_,M,A,B,J,G,Z,Y)}ee!=null&&A&&vo(ee,v&&v.ref,B,d||v,!d)},j=(v,d,_,M)=>{if(v==null)n(d.el=s(d.children),_,M);else{const A=d.el=v.el;d.children!==v.children&&l(A,d.children)}},k=(v,d,_,M)=>{v==null?n(d.el=c(d.children||""),_,M):d.el=v.el},E=(v,d,_,M)=>{[v.el,v.anchor]=b(v.children,d,_,M,v.el,v.anchor)},m=({el:v,anchor:d},_,M)=>{let A;for(;v&&v!==d;)A=p(v),n(v,_,M),v=A;n(d,_,M)},y=({el:v,anchor:d})=>{let _;for(;v&&v!==d;)_=p(v),o(v),v=_;o(d)},C=(v,d,_,M,A,B,J,G,Z)=>{d.type==="svg"?J="svg":d.type==="math"&&(J="mathml"),v==null?S(d,_,M,A,B,J,G,Z):R(v,d,A,B,J,G,Z)},S=(v,d,_,M,A,B,J,G)=>{let Z,q;const{props:ee,shapeFlag:ne,transition:w,dirs:I}=v;if(Z=v.el=a(v.type,B,ee&&ee.is,ee),ne&8?u(Z,v.children):ne&16&&L(v.children,Z,null,M,A,$i(v,B),J,G),I&&wt(v,null,M,"created"),T(Z,v,v.scopeId,J,M),ee){for(const fe in ee)fe!=="value"&&!On(fe)&&i(Z,fe,null,ee[fe],B,v.children,M,A,K);"value"in ee&&i(Z,"value",null,ee.value,B),(q=ee.onVnodeBeforeMount)&&Xe(q,M,v)}I&&wt(v,null,M,"beforeMount");const re=Nc(A,w);re&&w.beforeEnter(Z),n(Z,d,_),((q=ee&&ee.onVnodeMounted)||re||I)&&qe(()=>{q&&Xe(q,M,v),re&&w.enter(Z),I&&wt(v,null,M,"mounted")},A)},T=(v,d,_,M,A)=>{if(_&&h(v,_),M)for(let B=0;B<M.length;B++)h(v,M[B]);if(A){let B=A.subTree;if(d===B){const J=A.vnode;T(v,J,J.scopeId,J.slotScopeIds,A.parent)}}},L=(v,d,_,M,A,B,J,G,Z=0)=>{for(let q=Z;q<v.length;q++){const ee=v[q]=G?Ht(v[q]):at(v[q]);g(null,ee,d,_,M,A,B,J,G)}},R=(v,d,_,M,A,B,J)=>{const G=d.el=v.el;let{patchFlag:Z,dynamicChildren:q,dirs:ee}=d;Z|=v.patchFlag&16;const ne=v.props||Le,w=d.props||Le;let I;if(_&&fn(_,!1),(I=w.onVnodeBeforeUpdate)&&Xe(I,_,d,v),ee&&wt(d,v,_,"beforeUpdate"),_&&fn(_,!0),q?$(v.dynamicChildren,q,G,_,M,$i(d,A),B):J||de(v,d,G,null,_,M,$i(d,A),B,!1),Z>0){if(Z&16)W(G,d,ne,w,_,M,A);else if(Z&2&&ne.class!==w.class&&i(G,"class",null,w.class,A),Z&4&&i(G,"style",ne.style,w.style,A),Z&8){const re=d.dynamicProps;for(let fe=0;fe<re.length;fe++){const _e=re[fe],Ce=ne[_e],H=w[_e];(H!==Ce||_e==="value")&&i(G,_e,Ce,H,A,v.children,_,M,K)}}Z&1&&v.children!==d.children&&u(G,d.children)}else!J&&q==null&&W(G,d,ne,w,_,M,A);((I=w.onVnodeUpdated)||ee)&&qe(()=>{I&&Xe(I,_,d,v),ee&&wt(d,v,_,"updated")},M)},$=(v,d,_,M,A,B,J)=>{for(let G=0;G<d.length;G++){const Z=v[G],q=d[G],ee=Z.el&&(Z.type===We||!vt(Z,q)||Z.shapeFlag&70)?f(Z.el):_;g(Z,q,ee,null,M,A,B,J,!0)}},W=(v,d,_,M,A,B,J)=>{if(_!==M){if(_!==Le)for(const G in _)!On(G)&&!(G in M)&&i(v,G,_[G],null,J,d.children,A,B,K);for(const G in M){if(On(G))continue;const Z=M[G],q=_[G];Z!==q&&G!=="value"&&i(v,G,q,Z,J,d.children,A,B,K)}"value"in M&&i(v,"value",_.value,M.value,J)}},D=(v,d,_,M,A,B,J,G,Z)=>{const q=d.el=v?v.el:s(""),ee=d.anchor=v?v.anchor:s("");let{patchFlag:ne,dynamicChildren:w,slotScopeIds:I}=d;I&&(G=G?G.concat(I):I),v==null?(n(q,_,M),n(ee,_,M),L(d.children||[],_,ee,A,B,J,G,Z)):ne>0&&ne&64&&w&&v.dynamicChildren?($(v.dynamicChildren,w,_,A,B,J,G),(d.key!=null||A&&d===A.subTree)&&Hi(v,d,!0)):de(v,d,_,ee,A,B,J,G,Z)},te=(v,d,_,M,A,B,J,G,Z)=>{d.slotScopeIds=G,v==null?d.shapeFlag&512?A.ctx.activate(d,_,M,J,Z):he(d,_,M,A,B,J,Z):pe(v,d,Z)},he=(v,d,_,M,A,B,J)=>{const G=v.component=qc(v,M,A);if(tr(v)&&(G.ctx.renderer=Y),Kc(G),G.asyncDep){if(A&&A.registerDep(G,z),!v.el){const Z=G.subTree=Re(Ge);k(null,Z,d,_)}}else z(G,v,d,_,A,B,J)},pe=(v,d,_)=>{const M=d.component=v.component;if(ed(v,d,_))if(M.asyncDep&&!M.asyncResolved){ce(M,d,_);return}else M.next=d,Kp(M.update),M.effect.dirty=!0,M.update();else d.el=v.el,M.vnode=d},z=(v,d,_,M,A,B,J)=>{const G=()=>{if(v.isMounted){let{next:ee,bu:ne,u:w,parent:I,vnode:re}=v;{const se=Fc(v);if(se){ee&&(ee.el=re.el,ce(v,ee,J)),se.asyncDep.then(()=>{v.isUnmounted||G()});return}}let fe=ee,_e;fn(v,!1),ee?(ee.el=re.el,ce(v,ee,J)):ee=re,ne&&zn(ne),(_e=ee.props&&ee.props.onVnodeBeforeUpdate)&&Xe(_e,I,ee,re),fn(v,!0);const Ce=ao(v),H=v.subTree;v.subTree=Ce,g(H,Ce,f(H.el),x(H),v,A,B),ee.el=Ce.el,fe===null&&bi(v,Ce.el),w&&qe(w,A),(_e=ee.props&&ee.props.onVnodeUpdated)&&qe(()=>Xe(_e,I,ee,re),A)}else{let ee;const{el:ne,props:w}=d,{bm:I,m:re,parent:fe}=v,_e=ln(d);if(fn(v,!1),I&&zn(I),!_e&&(ee=w&&w.onVnodeBeforeMount)&&Xe(ee,fe,d),fn(v,!0),ne&&ue){const Ce=()=>{v.subTree=ao(v),ue(ne,v.subTree,v,A,null)};_e?d.type.__asyncLoader().then(()=>!v.isUnmounted&&Ce()):Ce()}else{const Ce=v.subTree=ao(v);g(null,Ce,_,M,v,A,B),d.el=Ce.el}if(re&&qe(re,A),!_e&&(ee=w&&w.onVnodeMounted)){const Ce=d;qe(()=>Xe(ee,fe,Ce),A)}(d.shapeFlag&256||fe&&ln(fe.vnode)&&fe.vnode.shapeFlag&256)&&v.a&&qe(v.a,A),v.isMounted=!0,d=_=M=null}},Z=v.effect=new bn(G,ut,()=>Zr(q),v.scope),q=v.update=()=>{Z.dirty&&Z.run()};q.id=v.uid,fn(v,!0),q()},ce=(v,d,_)=>{d.component=v;const M=v.vnode.props;v.vnode=d,v.next=null,Ad(v,d.props,M,_),Fd(v,d.children,_),Zt(),Is(v),en()},de=(v,d,_,M,A,B,J,G,Z=!1)=>{const q=v&&v.children,ee=v?v.shapeFlag:0,ne=d.children,{patchFlag:w,shapeFlag:I}=d;if(w>0){if(w&128){Oe(q,ne,_,M,A,B,J,G,Z);return}else if(w&256){Ie(q,ne,_,M,A,B,J,G,Z);return}}I&8?(ee&16&&K(q,A,B),ne!==q&&u(_,ne)):ee&16?I&16?Oe(q,ne,_,M,A,B,J,G,Z):K(q,A,B,!0):(ee&8&&u(_,""),I&16&&L(ne,_,M,A,B,J,G,Z))},Ie=(v,d,_,M,A,B,J,G,Z)=>{v=v||wn,d=d||wn;const q=v.length,ee=d.length,ne=Math.min(q,ee);let w;for(w=0;w<ne;w++){const I=d[w]=Z?Ht(d[w]):at(d[w]);g(v[w],I,_,null,A,B,J,G,Z)}q>ee?K(v,A,B,!0,!1,ne):L(d,_,M,A,B,J,G,Z,ne)},Oe=(v,d,_,M,A,B,J,G,Z)=>{let q=0;const ee=d.length;let ne=v.length-1,w=ee-1;for(;q<=ne&&q<=w;){const I=v[q],re=d[q]=Z?Ht(d[q]):at(d[q]);if(vt(I,re))g(I,re,_,null,A,B,J,G,Z);else break;q++}for(;q<=ne&&q<=w;){const I=v[ne],re=d[w]=Z?Ht(d[w]):at(d[w]);if(vt(I,re))g(I,re,_,null,A,B,J,G,Z);else break;ne--,w--}if(q>ne){if(q<=w){const I=w+1,re=I<ee?d[I].el:M;for(;q<=w;)g(null,d[q]=Z?Ht(d[q]):at(d[q]),_,re,A,B,J,G,Z),q++}}else if(q>w)for(;q<=ne;)N(v[q],A,B,!0),q++;else{const I=q,re=q,fe=new Map;for(q=re;q<=w;q++){const ie=d[q]=Z?Ht(d[q]):at(d[q]);ie.key!=null&&fe.set(ie.key,q)}let _e,Ce=0;const H=w-re+1;let se=!1,me=0;const ge=new Array(H);for(q=0;q<H;q++)ge[q]=0;for(q=I;q<=ne;q++){const ie=v[q];if(Ce>=H){N(ie,A,B,!0);continue}let le;if(ie.key!=null)le=fe.get(ie.key);else for(_e=re;_e<=w;_e++)if(ge[_e-re]===0&&vt(ie,d[_e])){le=_e;break}le===void 0?N(ie,A,B,!0):(ge[le-re]=q+1,le>=me?me=le:se=!0,g(ie,d[le],_,null,A,B,J,G,Z),Ce++)}const Q=se?Ud(ge):wn;for(_e=Q.length-1,q=H-1;q>=0;q--){const ie=re+q,le=d[ie],ye=ie+1<ee?d[ie+1].el:M;ge[q]===0?g(null,le,_,ye,A,B,J,G,Z):se&&(_e<0||q!==Q[_e]?V(le,_,ye,2):_e--)}}},V=(v,d,_,M,A=null)=>{const{el:B,type:J,transition:G,children:Z,shapeFlag:q}=v;if(q&6){V(v.component.subTree,d,_,M);return}if(q&128){v.suspense.move(d,_,M);return}if(q&64){J.move(v,d,_,Y);return}if(J===We){n(B,d,_);for(let ee=0;ee<Z.length;ee++)V(Z[ee],d,_,M);n(v.anchor,d,_);return}if(J===pn){m(v,d,_);return}if(M!==2&&q&1&&G)if(M===0)G.beforeEnter(B),n(B,d,_),qe(()=>G.enter(B),A);else{const{leave:ee,delayLeave:ne,afterLeave:w}=G,I=()=>n(B,d,_),re=()=>{ee(B,()=>{I(),w&&w()})};ne?ne(B,I,re):re()}else n(B,d,_)},N=(v,d,_,M=!1,A=!1)=>{const{type:B,props:J,ref:G,children:Z,dynamicChildren:q,shapeFlag:ee,patchFlag:ne,dirs:w}=v;if(G!=null&&vo(G,null,_,v,!0),ee&256){d.ctx.deactivate(v);return}const I=ee&1&&w,re=!ln(v);let fe;if(re&&(fe=J&&J.onVnodeBeforeUnmount)&&Xe(fe,d,v),ee&6)P(v.component,_,M);else{if(ee&128){v.suspense.unmount(_,M);return}I&&wt(v,null,d,"beforeUnmount"),ee&64?v.type.remove(v,d,_,A,Y,M):q&&(B!==We||ne>0&&ne&64)?K(q,d,_,!1,!0):(B===We&&ne&384||!A&&ee&16)&&K(Z,d,_),M&&X(v)}(re&&(fe=J&&J.onVnodeUnmounted)||I)&&qe(()=>{fe&&Xe(fe,d,v),I&&wt(v,null,d,"unmounted")},_)},X=v=>{const{type:d,el:_,anchor:M,transition:A}=v;if(d===We){oe(_,M);return}if(d===pn){y(v);return}const B=()=>{o(_),A&&!A.persisted&&A.afterLeave&&A.afterLeave()};if(v.shapeFlag&1&&A&&!A.persisted){const{leave:J,delayLeave:G}=A,Z=()=>J(_,B);G?G(v.el,B,Z):Z()}else B()},oe=(v,d)=>{let _;for(;v!==d;)_=p(v),o(v),v=_;o(d)},P=(v,d,_)=>{const{bum:M,scope:A,update:B,subTree:J,um:G}=v;M&&zn(M),A.stop(),B&&(B.active=!1,N(J,v,d,_)),G&&qe(G,d),qe(()=>{v.isUnmounted=!0},d),d&&d.pendingBranch&&!d.isUnmounted&&v.asyncDep&&!v.asyncResolved&&v.suspenseId===d.pendingId&&(d.deps--,d.deps===0&&d.resolve())},K=(v,d,_,M=!1,A=!1,B=0)=>{for(let J=B;J<v.length;J++)N(v[J],d,_,M,A)},x=v=>v.shapeFlag&6?x(v.component.subTree):v.shapeFlag&128?v.suspense.next():p(v.anchor||v.el);let F=!1;const U=(v,d,_)=>{v==null?d._vnode&&N(d._vnode,null,null,!0):g(d._vnode||null,v,d,null,null,null,_),F||(F=!0,Is(),to(),F=!1),d._vnode=v},Y={p:g,um:N,m:V,r:X,mt:he,mc:L,pc:de,pbc:$,n:x,o:e};let ae,ue;return t&&([ae,ue]=t(Y)),{render:U,hydrate:ae,createApp:Pd(U,ae)}}function $i({type:e,props:t},r){return r==="svg"&&e==="foreignObject"||r==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:r}function fn({effect:e,update:t},r){e.allowRecurse=t.allowRecurse=r}function Nc(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Hi(e,t,r=!1){const n=e.children,o=t.children;if(Ee(n)&&Ee(o))for(let i=0;i<n.length;i++){const a=n[i];let s=o[i];s.shapeFlag&1&&!s.dynamicChildren&&((s.patchFlag<=0||s.patchFlag===32)&&(s=o[i]=Ht(o[i]),s.el=a.el),r||Hi(a,s)),s.type===Ut&&(s.el=a.el)}}function Ud(e){const t=e.slice(),r=[0];let n,o,i,a,s;const c=e.length;for(n=0;n<c;n++){const l=e[n];if(l!==0){if(o=r[r.length-1],e[o]<l){t[n]=o,r.push(n);continue}for(i=0,a=r.length-1;i<a;)s=i+a>>1,e[r[s]]<l?i=s+1:a=s;l<e[r[i]]&&(i>0&&(t[n]=r[i-1]),r[i]=n)}}for(i=r.length,a=r[i-1];i-- >0;)r[i]=a,a=t[a];return r}function Fc(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Fc(t)}const $d=e=>e.__isTeleport,cr=e=>e&&(e.disabled||e.disabled===""),Mc=e=>typeof SVGElement<"u"&&e instanceof SVGElement,Dc=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,Wi=(e,t)=>{const r=e&&e.to;return De(r)?t?t(r):null:r},Hd={name:"Teleport",__isTeleport:!0,process(e,t,r,n,o,i,a,s,c,l){const{mc:u,pc:f,pbc:p,o:{insert:h,querySelector:b,createText:g,createComment:j}}=l,k=cr(t.props);let{shapeFlag:E,children:m,dynamicChildren:y}=t;if(e==null){const C=t.el=g(""),S=t.anchor=g("");h(C,r,n),h(S,r,n);const T=t.target=Wi(t.props,b),L=t.targetAnchor=g("");T&&(h(L,T),a==="svg"||Mc(T)?a="svg":(a==="mathml"||Dc(T))&&(a="mathml"));const R=($,W)=>{E&16&&u(m,$,W,o,i,a,s,c)};k?R(r,S):T&&R(T,L)}else{t.el=e.el;const C=t.anchor=e.anchor,S=t.target=e.target,T=t.targetAnchor=e.targetAnchor,L=cr(e.props),R=L?r:S,$=L?C:T;if(a==="svg"||Mc(S)?a="svg":(a==="mathml"||Dc(S))&&(a="mathml"),y?(p(e.dynamicChildren,y,R,o,i,a,s),Hi(e,t,!0)):c||f(e,t,R,$,o,i,a,s,!1),k)L?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):yo(t,r,C,l,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const W=t.target=Wi(t.props,b);W&&yo(t,W,null,l,0)}else L&&yo(t,S,T,l,1)}Uc(t)},remove(e,t,r,n,{um:o,o:{remove:i}},a){const{shapeFlag:s,children:c,anchor:l,targetAnchor:u,target:f,props:p}=e;if(f&&i(u),a&&i(l),s&16){const h=a||!cr(p);for(let b=0;b<c.length;b++){const g=c[b];o(g,t,r,h,!!g.dynamicChildren)}}},move:yo,hydrate:Wd};function yo(e,t,r,{o:{insert:n},m:o},i=2){i===0&&n(e.targetAnchor,t,r);const{el:a,anchor:s,shapeFlag:c,children:l,props:u}=e,f=i===2;if(f&&n(a,t,r),(!f||cr(u))&&c&16)for(let p=0;p<l.length;p++)o(l[p],t,r,2);f&&n(s,t,r)}function Wd(e,t,r,n,o,i,{o:{nextSibling:a,parentNode:s,querySelector:c}},l){const u=t.target=Wi(t.props,c);if(u){const f=u._lpa||u.firstChild;if(t.shapeFlag&16)if(cr(t.props))t.anchor=l(a(e),t,s(e),r,n,o,i),t.targetAnchor=f;else{t.anchor=a(e);let p=f;for(;p;)if(p=a(p),p&&p.nodeType===8&&p.data==="teleport anchor"){t.targetAnchor=p,u._lpa=t.targetAnchor&&a(t.targetAnchor);break}l(f,t,u,r,n,o,i)}Uc(t)}return t.anchor&&a(t.anchor)}const Vc=Hd;function Uc(e){const t=e.ctx;if(t&&t.ut){let r=e.children[0].el;for(;r&&r!==e.targetAnchor;)r.nodeType===1&&r.setAttribute("data-v-owner",t.uid),r=r.nextSibling;t.ut()}}const We=Symbol.for("v-fgt"),Ut=Symbol.for("v-txt"),Ge=Symbol.for("v-cmt"),pn=Symbol.for("v-stc"),lr=[];let Qe=null;function $t(e=!1){lr.push(Qe=e?null:[])}function $c(){lr.pop(),Qe=lr[lr.length-1]||null}let dn=1;function Bi(e){dn+=e}function Hc(e){return e.dynamicChildren=dn>0?Qe||wn:null,$c(),dn>0&&Qe&&Qe.push(e),e}function ur(e,t,r,n,o,i){return Hc(wo(e,t,r,n,o,i,!0))}function bo(e,t,r,n,o){return Hc(Re(e,t,r,n,o,!0))}function Lt(e){return e?e.__v_isVNode===!0:!1}function vt(e,t){return e.type===t.type&&e.key===t.key}function Bd(e){}const _o="__vInternal",Wc=({key:e})=>e??null,Eo=({ref:e,ref_key:t,ref_for:r})=>(typeof e=="number"&&(e=""+e),e!=null?De(e)||$e(e)||be(e)?{i:je,r:e,k:t,f:!!r}:e:null);function wo(e,t=null,r=null,n=0,o=null,i=e===We?0:1,a=!1,s=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Wc(t),ref:t&&Eo(t),scopeId:oo,slotScopeIds:null,children:r,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:n,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:je};return s?(Gi(c,r),i&128&&e.normalize(c)):r&&(c.shapeFlag|=De(r)?8:16),dn>0&&!a&&Qe&&(c.patchFlag>0||i&6)&&c.patchFlag!==32&&Qe.push(c),c}const Re=qd;function qd(e,t=null,r=null,n=0,o=null,i=!1){if((!e||e===Us)&&(e=Ge),Lt(e)){const s=mt(e,t,!0);return r&&Gi(s,r),dn>0&&!i&&Qe&&(s.shapeFlag&6?Qe[Qe.indexOf(e)]=s:Qe.push(s)),s.patchFlag|=-2,s}if(Zd(e)&&(e=e.__vccOpts),t){t=qi(t);let{class:s,style:c}=t;s&&!De(s)&&(t.class=Cn(s)),Ae(c)&&(li(c)&&!Ee(c)&&(c=He({},c)),t.style=xn(c))}const a=De(e)?1:Bs(e)?128:$d(e)?64:Ae(e)?4:be(e)?2:0;return wo(e,t,r,n,o,a,i,!0)}function qi(e){return e?li(e)||_o in e?He({},e):e:null}function mt(e,t,r=!1){const{props:n,ref:o,patchFlag:i,children:a}=e,s=t?Ki(n||{},t):n;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:s,key:s&&Wc(s),ref:t&&t.ref?r&&o?Ee(o)?o.concat(Eo(t)):[o,Eo(t)]:Eo(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==We?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&mt(e.ssContent),ssFallback:e.ssFallback&&mt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function Oo(e=" ",t=0){return Re(Ut,null,e,t)}function Gd(e,t){const r=Re(pn,null,e);return r.staticCount=t,r}function Bc(e="",t=!1){return t?($t(),bo(Ge,null,e)):Re(Ge,null,e)}function at(e){return e==null||typeof e=="boolean"?Re(Ge):Ee(e)?Re(We,null,e.slice()):typeof e=="object"?Ht(e):Re(Ut,null,String(e))}function Ht(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:mt(e)}function Gi(e,t){let r=0;const{shapeFlag:n}=e;if(t==null)t=null;else if(Ee(t))r=16;else if(typeof t=="object")if(n&65){const o=t.default;o&&(o._c&&(o._d=!1),Gi(e,o()),o._c&&(o._d=!0));return}else{r=32;const o=t._;!o&&!(_o in t)?t._ctx=je:o===3&&je&&(je.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else be(t)?(t={default:t,_ctx:je},r=32):(t=String(t),n&64?(r=16,t=[Oo(t)]):r=8);e.children=t,e.shapeFlag|=r}function Ki(...e){const t={};for(let r=0;r<e.length;r++){const n=e[r];for(const o in n)if(o==="class")t.class!==n.class&&(t.class=Cn([t.class,n.class]));else if(o==="style")t.style=xn([t.style,n.style]);else if(zr(o)){const i=t[o],a=n[o];a&&i!==a&&!(Ee(i)&&i.includes(a))&&(t[o]=i?[].concat(i,a):a)}else o!==""&&(t[o]=n[o])}return t}function Xe(e,t,r,n=null){rt(e,t,7,[r,n])}const Kd=Ec();let Yd=0;function qc(e,t,r){const n=e.type,o=(t?t.appContext:e.appContext)||Kd,i={uid:Yd++,vnode:e,type:n,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,scope:new ti(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(o.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Sc(n,o),emitsOptions:Ns(n,o),emit:null,emitted:null,propsDefaults:Le,inheritAttrs:n.inheritAttrs,ctx:Le,data:Le,props:Le,attrs:Le,slots:Le,refs:Le,setupState:Le,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:r,suspenseId:r?r.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=zp.bind(null,i),e.ce&&e.ce(i),i}let Ve=null;const dt=()=>Ve||je;let So,Yi;{const e=Cs(),t=(r,n)=>{let o;return(o=e[r])||(o=e[r]=[]),o.push(n),i=>{o.length>1?o.forEach(a=>a(i)):o[0](i)}};So=t("__VUE_INSTANCE_SETTERS__",r=>Ve=r),Yi=t("__VUE_SSR_SETTERS__",r=>fr=r)}const hn=e=>{const t=Ve;return So(e),e.scope.on(),()=>{e.scope.off(),So(t)}},zi=()=>{Ve&&Ve.scope.off(),So(null)};function Gc(e){return e.vnode.shapeFlag&4}let fr=!1;function Kc(e,t=!1){t&&Yi(t);const{props:r,children:n}=e.vnode,o=Gc(e);Id(e,r,o,t),Nd(e,n);const i=o?zd(e,t):void 0;return t&&Yi(!1),i}function zd(e,t){const r=e.type;e.accessCache=Object.create(null),e.proxy=Kr(new Proxy(e.ctx,Ni));const{setup:n}=r;if(n){const o=e.setupContext=n.length>1?zc(e):null,i=hn(e);Zt();const a=xt(n,e,0,[e.props,o]);if(en(),i(),hi(a)){if(a.then(zi,zi),t)return a.then(s=>{Qi(e,s,t)}).catch(s=>{rn(s,e,0)});e.asyncDep=a}else Qi(e,a,t)}else Yc(e,t)}function Qi(e,t,r){be(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Ae(t)&&(e.setupState=pi(t)),Yc(e,r)}let xo,Xi;function Qd(e){xo=e,Xi=t=>{t.render._rc&&(t.withProxy=new Proxy(t.ctx,dd))}}const Xd=()=>!xo;function Yc(e,t,r){const n=e.type;if(!e.render){if(!t&&xo&&!n.render){const o=n.template||Mi(e).template;if(o){const{isCustomElement:i,compilerOptions:a}=e.appContext.config,{delimiters:s,compilerOptions:c}=n,l=He(He({isCustomElement:i,delimiters:s},a),c);n.render=xo(o,l)}}e.render=n.render||ut,Xi&&Xi(e)}{const o=hn(e);Zt();try{xd(e)}finally{en(),o()}}}function Jd(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get(t,r){return ze(e,"get","$attrs"),t[r]}}))}function zc(e){const t=r=>{e.exposed=r||{}};return{get attrs(){return Jd(e)},slots:e.slots,emit:e.emit,expose:t}}function Co(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(pi(Kr(e.exposed)),{get(t,r){if(r in t)return t[r];if(r in ir)return ir[r](e)},has(t,r){return r in t||r in ir}}))}function Ji(e,t=!0){return be(e)?e.displayName||e.name:e.name||t&&e.__name}function Zd(e){return be(e)&&"__vccOpts"in e}const Ne=(e,t)=>_p(e,t,fr);function eh(e,t,r=Le){const n=dt(),o=ft(t),i=Kn(t),a=hs((c,l)=>{let u;return Qs(()=>{const f=e[t];Yn(u,f)&&(u=f,l())}),{get(){return c(),r.get?r.get(u):u},set(f){const p=n.vnode.props;!(p&&(t in p||o in p||i in p)&&(`onUpdate:${t}`in p||`onUpdate:${o}`in p||`onUpdate:${i}`in p))&&Yn(f,u)&&(u=f,l()),n.emit(`update:${t}`,r.set?r.set(f):f)}}}),s=t==="modelValue"?"modelModifiers":`${t}Modifiers`;return a[Symbol.iterator]=()=>{let c=0;return{next(){return c<2?{value:c++?e[s]||{}:a,done:!1}:{done:!0}}}},a}function pr(e,t,r){const n=arguments.length;return n===2?Ae(t)&&!Ee(t)?Lt(t)?Re(e,null,[t]):Re(e,t):Re(e,null,t):(n>3?r=Array.prototype.slice.call(arguments,2):n===3&&Lt(r)&&(r=[r]),Re(e,t,r))}function th(){}function nh(e,t,r,n){const o=r[n];if(o&&Qc(o,e))return o;const i=t();return i.memo=e.slice(),r[n]=i}function Qc(e,t){const r=e.memo;if(r.length!=t.length)return!1;for(let n=0;n<r.length;n++)if(Yn(r[n],t[n]))return!1;return dn>0&&Qe&&Qe.push(e),!0}const Xc="3.4.21",Jc=ut,rh=qp,oh=kn,ih=js,ah={createComponentInstance:qc,setupComponent:Kc,renderComponentRoot:ao,setCurrentRenderingInstance:Jn,isVNode:Lt,normalizeVNode:at},sh=ah,ch=null,lh=null,uh=null;/**
* @vue/shared v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function fh(e,t){const r=new Set(e.split(","));return n=>r.has(n)}const Zi={},ph=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),dh=e=>e.startsWith("onUpdate:"),dr=Object.assign,st=Array.isArray,hr=e=>nl(e)==="[object Set]",Zc=e=>nl(e)==="[object Date]",el=e=>typeof e=="function",vr=e=>typeof e=="string",tl=e=>typeof e=="symbol",ea=e=>e!==null&&typeof e=="object",hh=Object.prototype.toString,nl=e=>hh.call(e),ta=e=>{const t=Object.create(null);return r=>t[r]||(t[r]=e(r))},vh=/-(\w)/g,na=ta(e=>e.replace(vh,(t,r)=>r?r.toUpperCase():"")),mh=/\B([A-Z])/g,Wt=ta(e=>e.replace(mh,"-$1").toLowerCase()),gh=ta(e=>e.charAt(0).toUpperCase()+e.slice(1)),yh=(e,t)=>{for(let r=0;r<e.length;r++)e[r](t)},To=e=>{const t=parseFloat(e);return isNaN(t)?e:t},ra=e=>{const t=vr(e)?Number(e):NaN;return isNaN(t)?e:t},bh="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",_h=fh(bh);function rl(e){return!!e||e===""}function Eh(e,t){if(e.length!==t.length)return!1;let r=!0;for(let n=0;r&&n<e.length;n++)r=Bt(e[n],t[n]);return r}function Bt(e,t){if(e===t)return!0;let r=Zc(e),n=Zc(t);if(r||n)return r&&n?e.getTime()===t.getTime():!1;if(r=tl(e),n=tl(t),r||n)return e===t;if(r=st(e),n=st(t),r||n)return r&&n?Eh(e,t):!1;if(r=ea(e),n=ea(t),r||n){if(!r||!n)return!1;const o=Object.keys(e).length,i=Object.keys(t).length;if(o!==i)return!1;for(const a in e){const s=e.hasOwnProperty(a),c=t.hasOwnProperty(a);if(s&&!c||!s&&c||!Bt(e[a],t[a]))return!1}}return String(e)===String(t)}function ko(e,t){return e.findIndex(r=>Bt(r,t))}/**
* @vue/runtime-dom v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const wh="http://www.w3.org/2000/svg",Oh="http://www.w3.org/1998/Math/MathML",qt=typeof document<"u"?document:null,ol=qt&&qt.createElement("template"),Sh={insert:(e,t,r)=>{t.insertBefore(e,r||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,r,n)=>{const o=t==="svg"?qt.createElementNS(wh,e):t==="mathml"?qt.createElementNS(Oh,e):qt.createElement(e,r?{is:r}:void 0);return e==="select"&&n&&n.multiple!=null&&o.setAttribute("multiple",n.multiple),o},createText:e=>qt.createTextNode(e),createComment:e=>qt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>qt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,r,n,o,i){const a=r?r.previousSibling:t.lastChild;if(o&&(o===i||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),r),!(o===i||!(o=o.nextSibling)););else{ol.innerHTML=n==="svg"?`<svg>${e}</svg>`:n==="mathml"?`<math>${e}</math>`:e;const s=ol.content;if(n==="svg"||n==="mathml"){const c=s.firstChild;for(;c.firstChild;)s.appendChild(c.firstChild);s.removeChild(c)}t.insertBefore(s,r)}return[a?a.nextSibling:t.firstChild,r?r.previousSibling:t.lastChild]}},Gt="transition",mr="animation",jn=Symbol("_vtc"),Lo=(e,{slots:t})=>pr(Zs,sl(e),t);Lo.displayName="Transition";const il={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},xh=Lo.props=dr({},xi,il),vn=(e,t=[])=>{st(e)?e.forEach(r=>r(...t)):e&&e(...t)},al=e=>e?st(e)?e.some(t=>t.length>1):e.length>1:!1;function sl(e){const t={};for(const D in e)D in il||(t[D]=e[D]);if(e.css===!1)return t;const{name:r="v",type:n,duration:o,enterFromClass:i=`${r}-enter-from`,enterActiveClass:a=`${r}-enter-active`,enterToClass:s=`${r}-enter-to`,appearFromClass:c=i,appearActiveClass:l=a,appearToClass:u=s,leaveFromClass:f=`${r}-leave-from`,leaveActiveClass:p=`${r}-leave-active`,leaveToClass:h=`${r}-leave-to`}=e,b=Ch(o),g=b&&b[0],j=b&&b[1],{onBeforeEnter:k,onEnter:E,onEnterCancelled:m,onLeave:y,onLeaveCancelled:C,onBeforeAppear:S=k,onAppear:T=E,onAppearCancelled:L=m}=t,R=(D,te,he)=>{Kt(D,te?u:s),Kt(D,te?l:a),he&&he()},$=(D,te)=>{D._isLeaving=!1,Kt(D,f),Kt(D,h),Kt(D,p),te&&te()},W=D=>(te,he)=>{const pe=D?T:E,z=()=>R(te,D,he);vn(pe,[te,z]),cl(()=>{Kt(te,D?c:i),Rt(te,D?u:s),al(pe)||ll(te,n,g,z)})};return dr(t,{onBeforeEnter(D){vn(k,[D]),Rt(D,i),Rt(D,a)},onBeforeAppear(D){vn(S,[D]),Rt(D,c),Rt(D,l)},onEnter:W(!1),onAppear:W(!0),onLeave(D,te){D._isLeaving=!0;const he=()=>$(D,te);Rt(D,f),dl(),Rt(D,p),cl(()=>{D._isLeaving&&(Kt(D,f),Rt(D,h),al(y)||ll(D,n,j,he))}),vn(y,[D,he])},onEnterCancelled(D){R(D,!1),vn(m,[D])},onAppearCancelled(D){R(D,!0),vn(L,[D])},onLeaveCancelled(D){$(D),vn(C,[D])}})}function Ch(e){if(e==null)return null;if(ea(e))return[oa(e.enter),oa(e.leave)];{const t=oa(e);return[t,t]}}function oa(e){return ra(e)}function Rt(e,t){t.split(/\s+/).forEach(r=>r&&e.classList.add(r)),(e[jn]||(e[jn]=new Set)).add(t)}function Kt(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.remove(n));const r=e[jn];r&&(r.delete(t),r.size||(e[jn]=void 0))}function cl(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Th=0;function ll(e,t,r,n){const o=e._endId=++Th,i=()=>{o===e._endId&&n()};if(r)return setTimeout(i,r);const{type:a,timeout:s,propCount:c}=ul(e,t);if(!a)return n();const l=a+"end";let u=0;const f=()=>{e.removeEventListener(l,p),i()},p=h=>{h.target===e&&++u>=c&&f()};setTimeout(()=>{u<c&&f()},s+1),e.addEventListener(l,p)}function ul(e,t){const r=window.getComputedStyle(e),n=b=>(r[b]||"").split(", "),o=n(`${Gt}Delay`),i=n(`${Gt}Duration`),a=fl(o,i),s=n(`${mr}Delay`),c=n(`${mr}Duration`),l=fl(s,c);let u=null,f=0,p=0;t===Gt?a>0&&(u=Gt,f=a,p=i.length):t===mr?l>0&&(u=mr,f=l,p=c.length):(f=Math.max(a,l),u=f>0?a>l?Gt:mr:null,p=u?u===Gt?i.length:c.length:0);const h=u===Gt&&/\b(transform|all)(,|$)/.test(n(`${Gt}Property`).toString());return{type:u,timeout:f,propCount:p,hasTransform:h}}function fl(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((r,n)=>pl(r)+pl(e[n])))}function pl(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function dl(){return document.body.offsetHeight}function kh(e,t,r){const n=e[jn];n&&(t=(t?[t,...n]:[...n]).join(" ")),t==null?e.removeAttribute("class"):r?e.setAttribute("class",t):e.className=t}const Ro=Symbol("_vod"),hl=Symbol("_vsh"),ia={beforeMount(e,{value:t},{transition:r}){e[Ro]=e.style.display==="none"?"":e.style.display,r&&t?r.beforeEnter(e):gr(e,t)},mounted(e,{value:t},{transition:r}){r&&t&&r.enter(e)},updated(e,{value:t,oldValue:r},{transition:n}){!t!=!r&&(n?t?(n.beforeEnter(e),gr(e,!0),n.enter(e)):n.leave(e,()=>{gr(e,!1)}):gr(e,t))},beforeUnmount(e,{value:t}){gr(e,t)}};function gr(e,t){e.style.display=t?e[Ro]:"none",e[hl]=!t}function Lh(){ia.getSSRProps=({value:e})=>{if(!e)return{style:{display:"none"}}}}const vl=Symbol("");function ml(e){const t=dt();if(!t)return;const r=t.ut=(o=e(t.proxy))=>{Array.from(document.querySelectorAll(`[data-v-owner="${t.uid}"]`)).forEach(i=>sa(i,o))},n=()=>{const o=e(t.proxy);aa(t.subTree,o),r(o)};zs(n),kt(()=>{const o=new MutationObserver(n);o.observe(t.subTree.el.parentNode,{childList:!0}),Pn(()=>o.disconnect())})}function aa(e,t){if(e.shapeFlag&128){const r=e.suspense;e=r.activeBranch,r.pendingBranch&&!r.isHydrating&&r.effects.push(()=>{aa(r.activeBranch,t)})}for(;e.component;)e=e.component.subTree;if(e.shapeFlag&1&&e.el)sa(e.el,t);else if(e.type===We)e.children.forEach(r=>aa(r,t));else if(e.type===pn){let{el:r,anchor:n}=e;for(;r&&(sa(r,t),r!==n);)r=r.nextSibling}}function sa(e,t){if(e.nodeType===1){const r=e.style;let n="";for(const o in t)r.setProperty(`--${o}`,t[o]),n+=`--${o}: ${t[o]};`;r[vl]=n}}const Rh=/(^|;)\s*display\s*:/;function Ph(e,t,r){const n=e.style,o=vr(r);let i=!1;if(r&&!o){if(t)if(vr(t))for(const a of t.split(";")){const s=a.slice(0,a.indexOf(":")).trim();r[s]==null&&Po(n,s,"")}else for(const a in t)r[a]==null&&Po(n,a,"");for(const a in r)a==="display"&&(i=!0),Po(n,a,r[a])}else if(o){if(t!==r){const a=n[vl];a&&(r+=";"+a),n.cssText=r,i=Rh.test(r)}}else t&&e.removeAttribute("style");Ro in e&&(e[Ro]=i?n.display:"",e[hl]&&(n.display="none"))}const gl=/\s*!important$/;function Po(e,t,r){if(st(r))r.forEach(n=>Po(e,t,n));else if(r==null&&(r=""),t.startsWith("--"))e.setProperty(t,r);else{const n=Ih(e,t);gl.test(r)?e.setProperty(Wt(n),r.replace(gl,""),"important"):e[n]=r}}const yl=["Webkit","Moz","ms"],ca={};function Ih(e,t){const r=ca[t];if(r)return r;let n=ft(t);if(n!=="filter"&&n in e)return ca[t]=n;n=gh(n);for(let o=0;o<yl.length;o++){const i=yl[o]+n;if(i in e)return ca[t]=i}return t}const bl="http://www.w3.org/1999/xlink";function Ah(e,t,r,n,o){if(n&&t.startsWith("xlink:"))r==null?e.removeAttributeNS(bl,t.slice(6,t.length)):e.setAttributeNS(bl,t,r);else{const i=_h(t);r==null||i&&!rl(r)?e.removeAttribute(t):e.setAttribute(t,i?"":r)}}function jh(e,t,r,n,o,i,a){if(t==="innerHTML"||t==="textContent"){n&&a(n,o,i),e[t]=r??"";return}const s=e.tagName;if(t==="value"&&s!=="PROGRESS"&&!s.includes("-")){const l=s==="OPTION"?e.getAttribute("value")||"":e.value,u=r??"";(l!==u||!("_value"in e))&&(e.value=u),r==null&&e.removeAttribute(t),e._value=r;return}let c=!1;if(r===""||r==null){const l=typeof e[t];l==="boolean"?r=rl(r):r==null&&l==="string"?(r="",c=!0):l==="number"&&(r=0,c=!0)}try{e[t]=r}catch{}c&&e.removeAttribute(t)}function Pt(e,t,r,n){e.addEventListener(t,r,n)}function Nh(e,t,r,n){e.removeEventListener(t,r,n)}const _l=Symbol("_vei");function Fh(e,t,r,n,o=null){const i=e[_l]||(e[_l]={}),a=i[t];if(n&&a)a.value=n;else{const[s,c]=Mh(t);if(n){const l=i[t]=Uh(n,o);Pt(e,s,l,c)}else a&&(Nh(e,s,a,c),i[t]=void 0)}}const El=/(?:Once|Passive|Capture)$/;function Mh(e){let t;if(El.test(e)){t={};let r;for(;r=e.match(El);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Wt(e.slice(2)),t]}let la=0;const Dh=Promise.resolve(),Vh=()=>la||(Dh.then(()=>la=0),la=Date.now());function Uh(e,t){const r=n=>{if(!n._vts)n._vts=Date.now();else if(n._vts<=r.attached)return;rt($h(n,r.value),t,5,[n])};return r.value=e,r.attached=Vh(),r}function $h(e,t){if(st(t)){const r=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{r.call(e),e._stopped=!0},t.map(n=>o=>!o._stopped&&n&&n(o))}else return t}const wl=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Hh=(e,t,r,n,o,i,a,s,c)=>{const l=o==="svg";t==="class"?kh(e,n,l):t==="style"?Ph(e,r,n):ph(t)?dh(t)||Fh(e,t,r,n,a):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Wh(e,t,n,l))?jh(e,t,n,i,a,s,c):(t==="true-value"?e._trueValue=n:t==="false-value"&&(e._falseValue=n),Ah(e,t,n,l))};function Wh(e,t,r,n){if(n)return!!(t==="innerHTML"||t==="textContent"||t in e&&wl(t)&&el(r));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const o=e.tagName;if(o==="IMG"||o==="VIDEO"||o==="CANVAS"||o==="SOURCE")return!1}return wl(t)&&vr(r)?!1:t in e}/*! #__NO_SIDE_EFFECTS__ */function Ol(e,t){const r=Ct(e);class n extends Ko{constructor(i){super(r,i,t)}}return n.def=r,n}/*! #__NO_SIDE_EFFECTS__ */const Bh=e=>Ol(e,Hl),qh=typeof HTMLElement<"u"?HTMLElement:class{};class Ko extends qh{constructor(t,r={},n){super(),this._def=t,this._props=r,this._instance=null,this._connected=!1,this._resolved=!1,this._numberProps=null,this._ob=null,this.shadowRoot&&n?n(this._createVNode(),this.shadowRoot):(this.attachShadow({mode:"open"}),this._def.__asyncLoader||this._resolveProps(this._def))}connectedCallback(){this._connected=!0,this._instance||(this._resolved?this._update():this._resolveDef())}disconnectedCallback(){this._connected=!1,this._ob&&(this._ob.disconnect(),this._ob=null),an(()=>{this._connected||(Fo(null,this.shadowRoot),this._instance=null)})}_resolveDef(){this._resolved=!0;for(let n=0;n<this.attributes.length;n++)this._setAttr(this.attributes[n].name);this._ob=new MutationObserver(n=>{for(const o of n)this._setAttr(o.attributeName)}),this._ob.observe(this,{attributes:!0});const t=(n,o=!1)=>{const{props:i,styles:a}=n;let s;if(i&&!st(i))for(const c in i){const l=i[c];(l===Number||l&&l.type===Number)&&(c in this._props&&(this._props[c]=ra(this._props[c])),(s||(s=Object.create(null)))[na(c)]=!0)}this._numberProps=s,o&&this._resolveProps(n),this._applyStyles(a),this._update()},r=this._def.__asyncLoader;r?r().then(n=>t(n,!0)):t(this._def)}_resolveProps(t){const{props:r}=t,n=st(r)?r:Object.keys(r||{});for(const o of Object.keys(this))o[0]!=="_"&&n.includes(o)&&this._setProp(o,this[o],!0,!1);for(const o of n.map(na))Object.defineProperty(this,o,{get(){return this._getProp(o)},set(i){this._setProp(o,i)}})}_setAttr(t){let r=this.getAttribute(t);const n=na(t);this._numberProps&&this._numberProps[n]&&(r=ra(r)),this._setProp(n,r,!1)}_getProp(t){return this._props[t]}_setProp(t,r,n=!0,o=!0){r!==this._props[t]&&(this._props[t]=r,o&&this._instance&&this._update(),n&&(r===!0?this.setAttribute(Wt(t),""):typeof r=="string"||typeof r=="number"?this.setAttribute(Wt(t),r+""):r||this.removeAttribute(Wt(t))))}_update(){Fo(this._createVNode(),this.shadowRoot)}_createVNode(){const t=Re(this._def,dr({},this._props));return this._instance||(t.ce=r=>{this._instance=r,r.isCE=!0;const n=(i,a)=>{this.dispatchEvent(new CustomEvent(i,{detail:a}))};r.emit=(i,...a)=>{n(i,a),Wt(i)!==i&&n(Wt(i),a)};let o=this;for(;o=o&&(o.parentNode||o.host);)if(o instanceof Ko){r.parent=o._instance,r.provides=o._instance.provides;break}}),t}_applyStyles(t){t&&t.forEach(r=>{const n=document.createElement("style");n.textContent=r,this.shadowRoot.appendChild(n)})}}function Gh(e="$style"){{const t=dt();if(!t)return Zi;const r=t.type.__cssModules;return r&&r[e]||Zi}}const Sl=new WeakMap,xl=new WeakMap,Io=Symbol("_moveCb"),Cl=Symbol("_enterCb"),Tl={name:"TransitionGroup",props:dr({},xh,{tag:String,moveClass:String}),setup(e,{slots:t}){const r=dt(),n=Si();let o,i;return rr(()=>{if(!o.length)return;const a=e.moveClass||`${e.name||"v"}-move`;if(!Qh(o[0].el,r.vnode.el,a))return;o.forEach(Kh),o.forEach(Yh);const s=o.filter(zh);dl(),s.forEach(c=>{const l=c.el,u=l.style;Rt(l,a),u.transform=u.webkitTransform=u.transitionDuration="";const f=l[Io]=p=>{p&&p.target!==l||(!p||/transform$/.test(p.propertyName))&&(l.removeEventListener("transitionend",f),l[Io]=null,Kt(l,a))};l.addEventListener("transitionend",f)})}),()=>{const a=Se(e),s=sl(a);let c=a.tag||We;o=i,i=t.default?uo(t.default()):[];for(let l=0;l<i.length;l++){const u=i[l];u.key!=null&&cn(u,Rn(u,s,n,r))}if(o)for(let l=0;l<o.length;l++){const u=o[l];cn(u,Rn(u,s,n,r)),Sl.set(u,u.el.getBoundingClientRect())}return Re(c,null,i)}}},vy=e=>delete e.mode;Tl.props;const kl=Tl;function Kh(e){const t=e.el;t[Io]&&t[Io](),t[Cl]&&t[Cl]()}function Yh(e){xl.set(e,e.el.getBoundingClientRect())}function zh(e){const t=Sl.get(e),r=xl.get(e),n=t.left-r.left,o=t.top-r.top;if(n||o){const i=e.el.style;return i.transform=i.webkitTransform=`translate(${n}px,${o}px)`,i.transitionDuration="0s",e}}function Qh(e,t,r){const n=e.cloneNode(),o=e[jn];o&&o.forEach(s=>{s.split(/\s+/).forEach(c=>c&&n.classList.remove(c))}),r.split(/\s+/).forEach(s=>s&&n.classList.add(s)),n.style.display="none";const i=t.nodeType===1?t:t.parentNode;i.appendChild(n);const{hasTransform:a}=ul(n);return i.removeChild(n),a}const Yt=e=>{const t=e.props["onUpdate:modelValue"]||!1;return st(t)?r=>yh(t,r):t};function Xh(e){e.target.composing=!0}function Ll(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const ht=Symbol("_assign"),yr={created(e,{modifiers:{lazy:t,trim:r,number:n}},o){e[ht]=Yt(o);const i=n||o.props&&o.props.type==="number";Pt(e,t?"change":"input",a=>{if(a.target.composing)return;let s=e.value;r&&(s=s.trim()),i&&(s=To(s)),e[ht](s)}),r&&Pt(e,"change",()=>{e.value=e.value.trim()}),t||(Pt(e,"compositionstart",Xh),Pt(e,"compositionend",Ll),Pt(e,"change",Ll))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,modifiers:{lazy:r,trim:n,number:o}},i){if(e[ht]=Yt(i),e.composing)return;const a=o||e.type==="number"?To(e.value):e.value,s=t??"";a!==s&&(document.activeElement===e&&e.type!=="range"&&(r||n&&e.value.trim()===s)||(e.value=s))}},Ao={deep:!0,created(e,t,r){e[ht]=Yt(r),Pt(e,"change",()=>{const n=e._modelValue,o=Nn(e),i=e.checked,a=e[ht];if(st(n)){const s=ko(n,o),c=s!==-1;if(i&&!c)a(n.concat(o));else if(!i&&c){const l=[...n];l.splice(s,1),a(l)}}else if(hr(n)){const s=new Set(n);i?s.add(o):s.delete(o),a(s)}else a(Al(e,i))})},mounted:Rl,beforeUpdate(e,t,r){e[ht]=Yt(r),Rl(e,t,r)}};function Rl(e,{value:t,oldValue:r},n){e._modelValue=t,st(t)?e.checked=ko(t,n.props.value)>-1:hr(t)?e.checked=t.has(n.props.value):t!==r&&(e.checked=Bt(t,Al(e,!0)))}const jo={created(e,{value:t},r){e.checked=Bt(t,r.props.value),e[ht]=Yt(r),Pt(e,"change",()=>{e[ht](Nn(e))})},beforeUpdate(e,{value:t,oldValue:r},n){e[ht]=Yt(n),t!==r&&(e.checked=Bt(t,n.props.value))}},Pl={deep:!0,created(e,{value:t,modifiers:{number:r}},n){const o=hr(t);Pt(e,"change",()=>{const i=Array.prototype.filter.call(e.options,a=>a.selected).map(a=>r?To(Nn(a)):Nn(a));e[ht](e.multiple?o?new Set(i):i:i[0]),e._assigning=!0,an(()=>{e._assigning=!1})}),e[ht]=Yt(n)},mounted(e,{value:t,modifiers:{number:r}}){Il(e,t,r)},beforeUpdate(e,t,r){e[ht]=Yt(r)},updated(e,{value:t,modifiers:{number:r}}){e._assigning||Il(e,t,r)}};function Il(e,t,r){const n=e.multiple,o=st(t);if(!(n&&!o&&!hr(t))){for(let i=0,a=e.options.length;i<a;i++){const s=e.options[i],c=Nn(s);if(n)if(o){const l=typeof c;l==="string"||l==="number"?s.selected=t.includes(r?To(c):c):s.selected=ko(t,c)>-1}else s.selected=t.has(c);else if(Bt(Nn(s),t)){e.selectedIndex!==i&&(e.selectedIndex=i);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function Nn(e){return"_value"in e?e._value:e.value}function Al(e,t){const r=t?"_trueValue":"_falseValue";return r in e?e[r]:t}const jl={created(e,t,r){No(e,t,r,null,"created")},mounted(e,t,r){No(e,t,r,null,"mounted")},beforeUpdate(e,t,r,n){No(e,t,r,n,"beforeUpdate")},updated(e,t,r,n){No(e,t,r,n,"updated")}};function Nl(e,t){switch(e){case"SELECT":return Pl;case"TEXTAREA":return yr;default:switch(t){case"checkbox":return Ao;case"radio":return jo;default:return yr}}}function No(e,t,r,n,o){const i=Nl(e.tagName,r.props&&r.props.type)[o];i&&i(e,t,r,n)}function Jh(){yr.getSSRProps=({value:e})=>({value:e}),jo.getSSRProps=({value:e},t)=>{if(t.props&&Bt(t.props.value,e))return{checked:!0}},Ao.getSSRProps=({value:e},t)=>{if(st(e)){if(t.props&&ko(e,t.props.value)>-1)return{checked:!0}}else if(hr(e)){if(t.props&&e.has(t.props.value))return{checked:!0}}else if(e)return{checked:!0}},jl.getSSRProps=(e,t)=>{if(typeof t.type!="string")return;const r=Nl(t.type.toUpperCase(),t.props&&t.props.type);if(r.getSSRProps)return r.getSSRProps(e,t)}}const Zh=["ctrl","shift","alt","meta"],ev={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Zh.some(r=>e[`${r}Key`]&&!t.includes(r))},Fl=(e,t)=>{const r=e._withMods||(e._withMods={}),n=t.join(".");return r[n]||(r[n]=(o,...i)=>{for(let a=0;a<t.length;a++){const s=ev[t[a]];if(s&&s(o,t))return}return e(o,...i)})},tv={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},Ml=(e,t)=>{const r=e._withKeys||(e._withKeys={}),n=t.join(".");return r[n]||(r[n]=o=>{if(!("key"in o))return;const i=Wt(o.key);if(t.some(a=>a===i||tv[a]===i))return e(o)})},Dl=dr({patchProp:Hh},Sh);let br,Vl=!1;function Ul(){return br||(br=Ic(Dl))}function $l(){return br=Vl?br:Ac(Dl),Vl=!0,br}const Fo=(...e)=>{Ul().render(...e)},Hl=(...e)=>{$l().hydrate(...e)},Wl=(...e)=>{const t=Ul().createApp(...e),{mount:r}=t;return t.mount=n=>{const o=ql(n);if(!o)return;const i=t._component;!el(i)&&!i.render&&!i.template&&(i.template=o.innerHTML),o.innerHTML="";const a=r(o,!1,Bl(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),a},t},nv=(...e)=>{const t=$l().createApp(...e),{mount:r}=t;return t.mount=n=>{const o=ql(n);if(o)return r(o,!0,Bl(o))},t};function Bl(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function ql(e){return vr(e)?document.querySelector(e):e}let Gl=!1;const rv=()=>{Gl||(Gl=!0,Jh(),Lh())};/**
* vue v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const ov=()=>{},iv=Object.freeze(Object.defineProperty({__proto__:null,BaseTransition:Zs,BaseTransitionPropsValidators:xi,Comment:Ge,DeprecationTypes:uh,EffectScope:ti,ErrorCodes:Bp,ErrorTypeStrings:rh,Fragment:We,KeepAlive:rc,ReactiveEffect:bn,Static:pn,Suspense:rd,Teleport:Vc,Text:Ut,TrackOpTypes:kp,Transition:Lo,TransitionGroup:kl,TriggerOpTypes:Lp,VueElement:Ko,assertNumber:Wp,callWithAsyncErrorHandling:rt,callWithErrorHandling:xt,camelize:ft,capitalize:Jr,cloneVNode:mt,compatUtils:lh,compile:ov,computed:Ne,createApp:Wl,createBlock:bo,createCommentVNode:Bc,createElementBlock:ur,createElementVNode:wo,createHydrationRenderer:Ac,createPropsRestProxy:Od,createRenderer:Ic,createSSRApp:nv,createSlots:uc,createStaticVNode:Gd,createTextVNode:Oo,createVNode:Re,customRef:hs,defineAsyncComponent:nc,defineComponent:Ct,defineCustomElement:Ol,defineEmits:vd,defineExpose:md,defineModel:bd,defineOptions:gd,defineProps:hd,defineSSRCustomElement:Bh,defineSlots:yd,devtools:oh,effect:Jf,effectScope:Ba,getCurrentInstance:dt,getCurrentScope:Nr,getTransitionRawChildren:uo,guardReactiveProps:qi,h:pr,handleError:rn,hasInjectionContext:wc,hydrate:Hl,initCustomFormatter:th,initDirectivesForSSR:rv,inject:it,isMemoSame:Qc,isProxy:li,isReactive:Mt,isReadonly:nn,isRef:$e,isRuntimeOnly:Xd,isShallow:qn,isVNode:Lt,markRaw:Kr,mergeDefaults:Ed,mergeModels:wd,mergeProps:Ki,nextTick:an,normalizeClass:Cn,normalizeProps:Ts,normalizeStyle:xn,onActivated:ki,onBeforeMount:Ii,onBeforeUnmount:or,onBeforeUpdate:po,onDeactivated:Li,onErrorCaptured:cc,onMounted:kt,onRenderTracked:sc,onRenderTriggered:ac,onScopeDispose:ni,onServerPrefetch:ic,onUnmounted:Pn,onUpdated:rr,openBlock:$t,popScopeId:Ms,provide:An,proxyRefs:pi,pushScopeId:Fs,queuePostFlushCb:eo,reactive:_n,readonly:qr,ref:Be,registerRuntimeCompiler:Qd,render:Fo,renderList:lc,renderSlot:fc,resolveComponent:Vs,resolveDirective:Hs,resolveDynamicComponent:$s,resolveFilter:ch,resolveTransitionHooks:Rn,setBlockTracking:Bi,setDevtoolsHook:ih,setTransitionHooks:cn,shallowReactive:Br,shallowReadonly:bp,shallowRef:Yr,ssrContextKey:Ks,ssrUtils:sh,stop:Zf,toDisplayString:ks,toHandlerKey:Sn,toHandlers:dc,toRaw:Se,toRef:ms,toRefs:vs,toValue:Op,transformVNodeArgs:Bd,triggerRef:wp,unref:_t,useAttrs:vc,useCssModule:Gh,useCssVars:ml,useModel:eh,useSSRContext:Ys,useSlots:hc,useTransitionState:Si,vModelCheckbox:Ao,vModelDynamic:jl,vModelRadio:jo,vModelSelect:Pl,vModelText:yr,vShow:ia,version:Xc,warn:Jc,watch:ot,watchEffect:so,watchPostEffect:zs,watchSyncEffect:Qs,withAsyncContext:Sd,withCtx:io,withDefaults:_d,withDirectives:Js,withKeys:Ml,withMemo:nh,withModifiers:Fl,withScopeId:Qp},Symbol.toStringTag,{value:"Module"})),Kl=ja(iv);/*!
* vue-i18n v9.10.2
* (c) 2024 kazuya kawaguchi
* Released under the MIT License.
*/var O=Wf,ve=ff,we=Kl;const Yl="9.10.2",zl=ve.CoreWarnCodes.__EXTEND_POINT__,It=O.incrementer(zl),Pe={FALLBACK_TO_ROOT:zl,NOT_SUPPORTED_PRESERVE:It(),NOT_SUPPORTED_FORMATTER:It(),NOT_SUPPORTED_PRESERVE_DIRECTIVE:It(),NOT_SUPPORTED_GET_CHOICE_INDEX:It(),COMPONENT_NAME_LEGACY_COMPATIBLE:It(),NOT_FOUND_PARENT_SCOPE:It(),IGNORE_OBJ_FLATTEN:It(),NOTICE_DROP_ALLOW_COMPOSITION:It(),NOTICE_DROP_TRANSLATE_EXIST_COMPATIBLE_FLAG:It()},av={[Pe.FALLBACK_TO_ROOT]:"Fall back to {type} '{key}' with root locale.",[Pe.NOT_SUPPORTED_PRESERVE]:"Not supported 'preserve'.",[Pe.NOT_SUPPORTED_FORMATTER]:"Not supported 'formatter'.",[Pe.NOT_SUPPORTED_PRESERVE_DIRECTIVE]:"Not supported 'preserveDirectiveContent'.",[Pe.NOT_SUPPORTED_GET_CHOICE_INDEX]:"Not supported 'getChoiceIndex'.",[Pe.COMPONENT_NAME_LEGACY_COMPATIBLE]:"Component name legacy compatible: '{name}' -> 'i18n'",[Pe.NOT_FOUND_PARENT_SCOPE]:"Not found parent scope. use the global scope.",[Pe.IGNORE_OBJ_FLATTEN]:"Ignore object flatten: '{key}' key has an string value",[Pe.NOTICE_DROP_ALLOW_COMPOSITION]:"'allowComposition' option will be dropped in the next major version. For more information, please see \u{1F449} https://tinyurl.com/2p97mcze",[Pe.NOTICE_DROP_TRANSLATE_EXIST_COMPATIBLE_FLAG]:"'translateExistCompatible' option will be dropped in the next major version."};function Je(e,...t){return O.format(av[e],...t)}const Ql=ve.CoreErrorCodes.__EXTEND_POINT__,Ze=O.incrementer(Ql),xe={UNEXPECTED_RETURN_TYPE:Ql,INVALID_ARGUMENT:Ze(),MUST_BE_CALL_SETUP_TOP:Ze(),NOT_INSTALLED:Ze(),NOT_AVAILABLE_IN_LEGACY_MODE:Ze(),REQUIRED_VALUE:Ze(),INVALID_VALUE:Ze(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:Ze(),NOT_INSTALLED_WITH_PROVIDE:Ze(),UNEXPECTED_ERROR:Ze(),NOT_COMPATIBLE_LEGACY_VUE_I18N:Ze(),BRIDGE_SUPPORT_VUE_2_ONLY:Ze(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:Ze(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:Ze(),__EXTEND_POINT__:Ze()};function Ue(e,...t){return ve.createCompileError(e,null,{messages:sv,args:t})}const sv={[xe.UNEXPECTED_RETURN_TYPE]:"Unexpected return type in composer",[xe.INVALID_ARGUMENT]:"Invalid argument",[xe.MUST_BE_CALL_SETUP_TOP]:"Must be called at the top of a `setup` function",[xe.NOT_INSTALLED]:"Need to install with `app.use` function",[xe.UNEXPECTED_ERROR]:"Unexpected error",[xe.NOT_AVAILABLE_IN_LEGACY_MODE]:"Not available in legacy mode",[xe.REQUIRED_VALUE]:"Required in value: {0}",[xe.INVALID_VALUE]:"Invalid value",[xe.CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN]:"Cannot setup vue-devtools plugin",[xe.NOT_INSTALLED_WITH_PROVIDE]:"Need to install with `provide` function",[xe.NOT_COMPATIBLE_LEGACY_VUE_I18N]:"Not compatible legacy VueI18n.",[xe.BRIDGE_SUPPORT_VUE_2_ONLY]:"vue-i18n-bridge support Vue 2.x only",[xe.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION]:"Must define \u2018i18n\u2019 option or custom block in Composition API with using local scope in Legacy API mode",[xe.NOT_AVAILABLE_COMPOSITION_IN_LEGACY]:"Not available Compostion API in Legacy API mode. Please make sure that the legacy API mode is working properly"},ua=O.makeSymbol("__translateVNode"),fa=O.makeSymbol("__datetimeParts"),pa=O.makeSymbol("__numberParts"),da=O.makeSymbol("__enableEmitter"),ha=O.makeSymbol("__disableEmitter"),Xl=O.makeSymbol("__setPluralRules");O.makeSymbol("__intlifyMeta");const Jl=O.makeSymbol("__injectWithOption"),va=O.makeSymbol("__dispose"),cv="__VUE_I18N_BRIDGE__";function _r(e){if(!O.isObject(e))return e;for(const t in e)if(O.hasOwn(e,t))if(!t.includes("."))O.isObject(e[t])&&_r(e[t]);else{const r=t.split("."),n=r.length-1;let o=e,i=!1;for(let a=0;a<n;a++){if(r[a]in o||(o[r[a]]={}),!O.isObject(o[r[a]])){O.warn(Je(Pe.IGNORE_OBJ_FLATTEN,{key:r[a]})),i=!0;break}o=o[r[a]]}i||(o[r[n]]=e[t],delete e[t]),O.isObject(o[r[n]])&&_r(o[r[n]])}return e}function Mo(e,t){const{messages:r,__i18n:n,messageResolver:o,flatJson:i}=t,a=O.isPlainObject(r)?r:O.isArray(n)?{}:{[e]:{}};if(O.isArray(n)&&n.forEach(s=>{if("locale"in s&&"resource"in s){const{locale:c,resource:l}=s;c?(a[c]=a[c]||{},O.deepCopy(l,a[c])):O.deepCopy(l,a)}else O.isString(s)&&O.deepCopy(JSON.parse(s),a)}),o==null&&i)for(const s in a)O.hasOwn(a,s)&&_r(a[s]);return a}function Zl(e){return e.type}function eu(e,t,r){let n=O.isObject(t.messages)?t.messages:{};"__i18nGlobal"in r&&(n=Mo(e.locale.value,{messages:n,__i18n:r.__i18nGlobal}));const o=Object.keys(n);o.length&&o.forEach(i=>{e.mergeLocaleMessage(i,n[i])});{if(O.isObject(t.datetimeFormats)){const i=Object.keys(t.datetimeFormats);i.length&&i.forEach(a=>{e.mergeDateTimeFormat(a,t.datetimeFormats[a])})}if(O.isObject(t.numberFormats)){const i=Object.keys(t.numberFormats);i.length&&i.forEach(a=>{e.mergeNumberFormat(a,t.numberFormats[a])})}}}function tu(e){return we.createVNode(we.Text,null,e,0)}const nu="__INTLIFY_META__",ru=()=>[],lv=()=>!1;let ou=0;function iu(e){return(t,r,n,o)=>e(r,n,we.getCurrentInstance()||void 0,o)}const uv=()=>{const e=we.getCurrentInstance();let t=null;return e&&(t=Zl(e)[nu])?{[nu]:t}:null};function ma(e={},t){const{__root:r,__injectWithOption:n}=e,o=r===void 0,i=e.flatJson,a=O.inBrowser?we.ref:we.shallowRef,s=!!e.translateExistCompatible;s&&O.warnOnce(Je(Pe.NOTICE_DROP_TRANSLATE_EXIST_COMPATIBLE_FLAG));let c=O.isBoolean(e.inheritLocale)?e.inheritLocale:!0;const l=a(r&&c?r.locale.value:O.isString(e.locale)?e.locale:ve.DEFAULT_LOCALE),u=a(r&&c?r.fallbackLocale.value:O.isString(e.fallbackLocale)||O.isArray(e.fallbackLocale)||O.isPlainObject(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:l.value),f=a(Mo(l.value,e)),p=a(O.isPlainObject(e.datetimeFormats)?e.datetimeFormats:{[l.value]:{}}),h=a(O.isPlainObject(e.numberFormats)?e.numberFormats:{[l.value]:{}});let b=r?r.missingWarn:O.isBoolean(e.missingWarn)||O.isRegExp(e.missingWarn)?e.missingWarn:!0,g=r?r.fallbackWarn:O.isBoolean(e.fallbackWarn)||O.isRegExp(e.fallbackWarn)?e.fallbackWarn:!0,j=r?r.fallbackRoot:O.isBoolean(e.fallbackRoot)?e.fallbackRoot:!0,k=!!e.fallbackFormat,E=O.isFunction(e.missing)?e.missing:null,m=O.isFunction(e.missing)?iu(e.missing):null,y=O.isFunction(e.postTranslation)?e.postTranslation:null,C=r?r.warnHtmlMessage:O.isBoolean(e.warnHtmlMessage)?e.warnHtmlMessage:!0,S=!!e.escapeParameter;const T=r?r.modifiers:O.isPlainObject(e.modifiers)?e.modifiers:{};let L=e.pluralRules||r&&r.pluralRules,R;R=(()=>{o&&ve.setFallbackContext(null);const w={version:Yl,locale:l.value,fallbackLocale:u.value,messages:f.value,modifiers:T,pluralRules:L,missing:m===null?void 0:m,missingWarn:b,fallbackWarn:g,fallbackFormat:k,unresolving:!0,postTranslation:y===null?void 0:y,warnHtmlMessage:C,escapeParameter:S,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};w.datetimeFormats=p.value,w.numberFormats=h.value,w.__datetimeFormatters=O.isPlainObject(R)?R.__datetimeFormatters:void 0,w.__numberFormatters=O.isPlainObject(R)?R.__numberFormatters:void 0,w.__v_emitter=O.isPlainObject(R)?R.__v_emitter:void 0;const I=ve.createCoreContext(w);return o&&ve.setFallbackContext(I),I})(),ve.updateFallbackLocale(R,l.value,u.value);function $(){return[l.value,u.value,f.value,p.value,h.value]}const W=we.computed({get:()=>l.value,set:w=>{l.value=w,R.locale=l.value}}),D=we.computed({get:()=>u.value,set:w=>{u.value=w,R.fallbackLocale=u.value,ve.updateFallbackLocale(R,l.value,w)}}),te=we.computed(()=>f.value),he=we.computed(()=>p.value),pe=we.computed(()=>h.value);function z(){return O.isFunction(y)?y:null}function ce(w){y=w,R.postTranslation=w}function de(){return E}function Ie(w){w!==null&&(m=iu(w)),E=w,R.missing=m}function Oe(w,I){return w!=="translate"||!I.resolvedMessage}const V=(w,I,re,fe,_e,Ce)=>{$();let H;try{ve.setAdditionalMeta(uv()),o||(R.fallbackContext=r?ve.getFallbackContext():void 0),H=w(R)}finally{ve.setAdditionalMeta(null),o||(R.fallbackContext=void 0)}if(re!=="translate exists"&&O.isNumber(H)&&H===ve.NOT_REOSLVED||re==="translate exists"&&!H){const[se,me]=I();if(r&&O.isString(se)&&Oe(re,me)){j&&(ve.isTranslateFallbackWarn(g,se)||ve.isTranslateMissingWarn(b,se))&&O.warn(Je(Pe.FALLBACK_TO_ROOT,{key:se,type:re}));{const{__v_emitter:ge}=R;ge&&j&&ge.emit("fallback",{type:re,key:se,to:"global",groupId:`${re}:${se}`})}}return r&&j?fe(r):_e(se)}else{if(Ce(H))return H;throw Ue(xe.UNEXPECTED_RETURN_TYPE)}};function N(...w){return V(I=>Reflect.apply(ve.translate,null,[I,...w]),()=>ve.parseTranslateArgs(...w),"translate",I=>Reflect.apply(I.t,I,[...w]),I=>I,I=>O.isString(I))}function X(...w){const[I,re,fe]=w;if(fe&&!O.isObject(fe))throw Ue(xe.INVALID_ARGUMENT);return N(I,re,O.assign({resolvedMessage:!0},fe||{}))}function oe(...w){return V(I=>Reflect.apply(ve.datetime,null,[I,...w]),()=>ve.parseDateTimeArgs(...w),"datetime format",I=>Reflect.apply(I.d,I,[...w]),()=>ve.MISSING_RESOLVE_VALUE,I=>O.isString(I))}function P(...w){return V(I=>Reflect.apply(ve.number,null,[I,...w]),()=>ve.parseNumberArgs(...w),"number format",I=>Reflect.apply(I.n,I,[...w]),()=>ve.MISSING_RESOLVE_VALUE,I=>O.isString(I))}function K(w){return w.map(I=>O.isString(I)||O.isNumber(I)||O.isBoolean(I)?tu(String(I)):I)}const x={normalize:K,interpolate:w=>w,type:"vnode"};function F(...w){return V(I=>{let re;const fe=I;try{fe.processor=x,re=Reflect.apply(ve.translate,null,[fe,...w])}finally{fe.processor=null}return re},()=>ve.parseTranslateArgs(...w),"translate",I=>I[ua](...w),I=>[tu(I)],I=>O.isArray(I))}function U(...w){return V(I=>Reflect.apply(ve.number,null,[I,...w]),()=>ve.parseNumberArgs(...w),"number format",I=>I[pa](...w),ru,I=>O.isString(I)||O.isArray(I))}function Y(...w){return V(I=>Reflect.apply(ve.datetime,null,[I,...w]),()=>ve.parseDateTimeArgs(...w),"datetime format",I=>I[fa](...w),ru,I=>O.isString(I)||O.isArray(I))}function ae(w){L=w,R.pluralRules=L}function ue(w,I){return V(()=>{if(!w)return!1;const re=O.isString(I)?I:l.value,fe=_(re),_e=R.messageResolver(fe,w);return s?_e!=null:ve.isMessageAST(_e)||ve.isMessageFunction(_e)||O.isString(_e)},()=>[w],"translate exists",re=>Reflect.apply(re.te,re,[w,I]),lv,re=>O.isBoolean(re))}function v(w){let I=null;const re=ve.fallbackWithLocaleChain(R,u.value,l.value);for(let fe=0;fe<re.length;fe++){const _e=f.value[re[fe]]||{},Ce=R.messageResolver(_e,w);if(Ce!=null){I=Ce;break}}return I}function d(w){return v(w)??(r?r.tm(w)||{}:{})}function _(w){return f.value[w]||{}}function M(w,I){if(i){const re={[w]:I};for(const fe in re)O.hasOwn(re,fe)&&_r(re[fe]);I=re[w]}f.value[w]=I,R.messages=f.value}function A(w,I){f.value[w]=f.value[w]||{};const re={[w]:I};if(i)for(const fe in re)O.hasOwn(re,fe)&&_r(re[fe]);I=re[w],O.deepCopy(I,f.value[w]),R.messages=f.value}function B(w){return p.value[w]||{}}function J(w,I){p.value[w]=I,R.datetimeFormats=p.value,ve.clearDateTimeFormat(R,w,I)}function G(w,I){p.value[w]=O.assign(p.value[w]||{},I),R.datetimeFormats=p.value,ve.clearDateTimeFormat(R,w,I)}function Z(w){return h.value[w]||{}}function q(w,I){h.value[w]=I,R.numberFormats=h.value,ve.clearNumberFormat(R,w,I)}function ee(w,I){h.value[w]=O.assign(h.value[w]||{},I),R.numberFormats=h.value,ve.clearNumberFormat(R,w,I)}ou++,r&&O.inBrowser&&(we.watch(r.locale,w=>{c&&(l.value=w,R.locale=w,ve.updateFallbackLocale(R,l.value,u.value))}),we.watch(r.fallbackLocale,w=>{c&&(u.value=w,R.fallbackLocale=w,ve.updateFallbackLocale(R,l.value,u.value))}));const ne={id:ou,locale:W,fallbackLocale:D,get inheritLocale(){return c},set inheritLocale(w){c=w,w&&r&&(l.value=r.locale.value,u.value=r.fallbackLocale.value,ve.updateFallbackLocale(R,l.value,u.value))},get availableLocales(){return Object.keys(f.value).sort()},messages:te,get modifiers(){return T},get pluralRules(){return L||{}},get isGlobal(){return o},get missingWarn(){return b},set missingWarn(w){b=w,R.missingWarn=b},get fallbackWarn(){return g},set fallbackWarn(w){g=w,R.fallbackWarn=g},get fallbackRoot(){return j},set fallbackRoot(w){j=w},get fallbackFormat(){return k},set fallbackFormat(w){k=w,R.fallbackFormat=k},get warnHtmlMessage(){return C},set warnHtmlMessage(w){C=w,R.warnHtmlMessage=w},get escapeParameter(){return S},set escapeParameter(w){S=w,R.escapeParameter=w},t:N,getLocaleMessage:_,setLocaleMessage:M,mergeLocaleMessage:A,getPostTranslationHandler:z,setPostTranslationHandler:ce,getMissingHandler:de,setMissingHandler:Ie,[Xl]:ae};return ne.datetimeFormats=he,ne.numberFormats=pe,ne.rt=X,ne.te=ue,ne.tm=d,ne.d=oe,ne.n=P,ne.getDateTimeFormat=B,ne.setDateTimeFormat=J,ne.mergeDateTimeFormat=G,ne.getNumberFormat=Z,ne.setNumberFormat=q,ne.mergeNumberFormat=ee,ne[Jl]=n,ne[ua]=F,ne[fa]=Y,ne[pa]=U,ne[da]=w=>{R.__v_emitter=w},ne[ha]=()=>{R.__v_emitter=void 0},ne}function fv(e){const t=O.isString(e.locale)?e.locale:ve.DEFAULT_LOCALE,r=O.isString(e.fallbackLocale)||O.isArray(e.fallbackLocale)||O.isPlainObject(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:t,n=O.isFunction(e.missing)?e.missing:void 0,o=O.isBoolean(e.silentTranslationWarn)||O.isRegExp(e.silentTranslationWarn)?!e.silentTranslationWarn:!0,i=O.isBoolean(e.silentFallbackWarn)||O.isRegExp(e.silentFallbackWarn)?!e.silentFallbackWarn:!0,a=O.isBoolean(e.fallbackRoot)?e.fallbackRoot:!0,s=!!e.formatFallbackMessages,c=O.isPlainObject(e.modifiers)?e.modifiers:{},l=e.pluralizationRules,u=O.isFunction(e.postTranslation)?e.postTranslation:void 0,f=O.isString(e.warnHtmlInMessage)?e.warnHtmlInMessage!=="off":!0,p=!!e.escapeParameterHtml,h=O.isBoolean(e.sync)?e.sync:!0;e.formatter&&O.warn(Je(Pe.NOT_SUPPORTED_FORMATTER)),e.preserveDirectiveContent&&O.warn(Je(Pe.NOT_SUPPORTED_PRESERVE_DIRECTIVE));let b=e.messages;if(O.isPlainObject(e.sharedMessages)){const S=e.sharedMessages;b=Object.keys(S).reduce((T,L)=>{const R=T[L]||(T[L]={});return O.assign(R,S[L]),T},b||{})}const{__i18n:g,__root:j,__injectWithOption:k}=e,E=e.datetimeFormats,m=e.numberFormats,y=e.flatJson,C=e.translateExistCompatible;return{locale:t,fallbackLocale:r,messages:b,flatJson:y,datetimeFormats:E,numberFormats:m,missing:n,missingWarn:o,fallbackWarn:i,fallbackRoot:a,fallbackFormat:s,modifiers:c,pluralRules:l,postTranslation:u,warnHtmlMessage:f,escapeParameter:p,messageResolver:e.messageResolver,inheritLocale:h,translateExistCompatible:C,__i18n:g,__root:j,__injectWithOption:k}}function ga(e={},t){{const r=ma(fv(e)),{__extender:n}=e,o={id:r.id,get locale(){return r.locale.value},set locale(i){r.locale.value=i},get fallbackLocale(){return r.fallbackLocale.value},set fallbackLocale(i){r.fallbackLocale.value=i},get messages(){return r.messages.value},get datetimeFormats(){return r.datetimeFormats.value},get numberFormats(){return r.numberFormats.value},get availableLocales(){return r.availableLocales},get formatter(){return O.warn(Je(Pe.NOT_SUPPORTED_FORMATTER)),{interpolate(){return[]}}},set formatter(i){O.warn(Je(Pe.NOT_SUPPORTED_FORMATTER))},get missing(){return r.getMissingHandler()},set missing(i){r.setMissingHandler(i)},get silentTranslationWarn(){return O.isBoolean(r.missingWarn)?!r.missingWarn:r.missingWarn},set silentTranslationWarn(i){r.missingWarn=O.isBoolean(i)?!i:i},get silentFallbackWarn(){return O.isBoolean(r.fallbackWarn)?!r.fallbackWarn:r.fallbackWarn},set silentFallbackWarn(i){r.fallbackWarn=O.isBoolean(i)?!i:i},get modifiers(){return r.modifiers},get formatFallbackMessages(){return r.fallbackFormat},set formatFallbackMessages(i){r.fallbackFormat=i},get postTranslation(){return r.getPostTranslationHandler()},set postTranslation(i){r.setPostTranslationHandler(i)},get sync(){return r.inheritLocale},set sync(i){r.inheritLocale=i},get warnHtmlInMessage(){return r.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(i){r.warnHtmlMessage=i!=="off"},get escapeParameterHtml(){return r.escapeParameter},set escapeParameterHtml(i){r.escapeParameter=i},get preserveDirectiveContent(){return O.warn(Je(Pe.NOT_SUPPORTED_PRESERVE_DIRECTIVE)),!0},set preserveDirectiveContent(i){O.warn(Je(Pe.NOT_SUPPORTED_PRESERVE_DIRECTIVE))},get pluralizationRules(){return r.pluralRules||{}},__composer:r,t(...i){const[a,s,c]=i,l={};let u=null,f=null;if(!O.isString(a))throw Ue(xe.INVALID_ARGUMENT);const p=a;return O.isString(s)?l.locale=s:O.isArray(s)?u=s:O.isPlainObject(s)&&(f=s),O.isArray(c)?u=c:O.isPlainObject(c)&&(f=c),Reflect.apply(r.t,r,[p,u||f||{},l])},rt(...i){return Reflect.apply(r.rt,r,[...i])},tc(...i){const[a,s,c]=i,l={plural:1};let u=null,f=null;if(!O.isString(a))throw Ue(xe.INVALID_ARGUMENT);const p=a;return O.isString(s)?l.locale=s:O.isNumber(s)?l.plural=s:O.isArray(s)?u=s:O.isPlainObject(s)&&(f=s),O.isString(c)?l.locale=c:O.isArray(c)?u=c:O.isPlainObject(c)&&(f=c),Reflect.apply(r.t,r,[p,u||f||{},l])},te(i,a){return r.te(i,a)},tm(i){return r.tm(i)},getLocaleMessage(i){return r.getLocaleMessage(i)},setLocaleMessage(i,a){r.setLocaleMessage(i,a)},mergeLocaleMessage(i,a){r.mergeLocaleMessage(i,a)},d(...i){return Reflect.apply(r.d,r,[...i])},getDateTimeFormat(i){return r.getDateTimeFormat(i)},setDateTimeFormat(i,a){r.setDateTimeFormat(i,a)},mergeDateTimeFormat(i,a){r.mergeDateTimeFormat(i,a)},n(...i){return Reflect.apply(r.n,r,[...i])},getNumberFormat(i){return r.getNumberFormat(i)},setNumberFormat(i,a){r.setNumberFormat(i,a)},mergeNumberFormat(i,a){r.mergeNumberFormat(i,a)},getChoiceIndex(i,a){return O.warn(Je(Pe.NOT_SUPPORTED_GET_CHOICE_INDEX)),-1}};return o.__extender=n,o.__enableEmitter=i=>{const a=r;a[da]&&a[da](i)},o.__disableEmitter=()=>{const i=r;i[ha]&&i[ha]()},o}}const ya={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>e==="parent"||e==="global",default:"parent"},i18n:{type:Object}};function pv({slots:e},t){return t.length===1&&t[0]==="default"?(e.default?e.default():[]).reduce((r,n)=>[...r,...n.type===we.Fragment?n.children:[n]],[]):t.reduce((r,n)=>{const o=e[n];return o&&(r[n]=o()),r},{})}function au(e){return we.Fragment}const dv=we.defineComponent({name:"i18n-t",props:O.assign({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>O.isNumber(e)||!isNaN(e)}},ya),setup(e,t){const{slots:r,attrs:n}=t,o=e.i18n||Uo({useScope:e.scope,__useComponent:!0});return()=>{const i=Object.keys(r).filter(f=>f!=="_"),a={};e.locale&&(a.locale=e.locale),e.plural!==void 0&&(a.plural=O.isString(e.plural)?+e.plural:e.plural);const s=pv(t,i),c=o[ua](e.keypath,s,a),l=O.assign({},n),u=O.isString(e.tag)||O.isObject(e.tag)?e.tag:au();return we.h(u,l,c)}}}),Er=dv,hv=Er;function vv(e){return O.isArray(e)&&!O.isString(e[0])}function su(e,t,r,n){const{slots:o,attrs:i}=t;return()=>{const a={part:!0};let s={};e.locale&&(a.locale=e.locale),O.isString(e.format)?a.key=e.format:O.isObject(e.format)&&(O.isString(e.format.key)&&(a.key=e.format.key),s=Object.keys(e.format).reduce((p,h)=>r.includes(h)?O.assign({},p,{[h]:e.format[h]}):p,{}));const c=n(e.value,a,s);let l=[a.key];O.isArray(c)?l=c.map((p,h)=>{const b=o[p.type],g=b?b({[p.type]:p.value,index:h,parts:c}):[p.value];return vv(g)&&(g[0].key=`${p.type}-${h}`),g}):O.isString(c)&&(l=[c]);const u=O.assign({},i),f=O.isString(e.tag)||O.isObject(e.tag)?e.tag:au();return we.h(f,u,l)}}const mv=we.defineComponent({name:"i18n-n",props:O.assign({value:{type:Number,required:!0},format:{type:[String,Object]}},ya),setup(e,t){const r=e.i18n||Uo({useScope:"parent",__useComponent:!0});return su(e,t,ve.NUMBER_FORMAT_OPTIONS_KEYS,(...n)=>r[pa](...n))}}),Do=mv,gv=Do,yv=we.defineComponent({name:"i18n-d",props:O.assign({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},ya),setup(e,t){const r=e.i18n||Uo({useScope:"parent",__useComponent:!0});return su(e,t,ve.DATETIME_FORMAT_OPTIONS_KEYS,(...n)=>r[fa](...n))}}),Vo=yv,bv=Vo;function _v(e,t){const r=e;if(e.mode==="composition")return r.__getInstance(t)||e.global;{const n=r.__getInstance(t);return n!=null?n.__composer:e.global.__composer}}function cu(e){const t=r=>{const{instance:n,modifiers:o,value:i}=r;if(!n||!n.$)throw Ue(xe.UNEXPECTED_ERROR);const a=_v(e,n.$);o.preserve&&O.warn(Je(Pe.NOT_SUPPORTED_PRESERVE));const s=lu(i);return[Reflect.apply(a.t,a,[...uu(s)]),a]};return{created:(r,n)=>{const[o,i]=t(n);O.inBrowser&&e.global===i&&(r.__i18nWatcher=we.watch(i.locale,()=>{n.instance&&n.instance.$forceUpdate()})),r.__composer=i,r.textContent=o},unmounted:r=>{O.inBrowser&&r.__i18nWatcher&&(r.__i18nWatcher(),r.__i18nWatcher=void 0,delete r.__i18nWatcher),r.__composer&&(r.__composer=void 0,delete r.__composer)},beforeUpdate:(r,{value:n})=>{if(r.__composer){const o=r.__composer,i=lu(n);r.textContent=Reflect.apply(o.t,o,[...uu(i)])}},getSSRProps:r=>{const[n]=t(r);return{textContent:n}}}}function lu(e){if(O.isString(e))return{path:e};if(O.isPlainObject(e)){if(!("path"in e))throw Ue(xe.REQUIRED_VALUE,"path");return e}else throw Ue(xe.INVALID_VALUE)}function uu(e){const{path:t,locale:r,args:n,choice:o,plural:i}=e,a={},s=n||{};return O.isString(r)&&(a.locale=r),O.isNumber(o)&&(a.plural=o),O.isNumber(i)&&(a.plural=i),[t,s,a]}function Ev(e,t,...r){const n=O.isPlainObject(r[0])?r[0]:{},o=!!n.useI18nComponentName,i=O.isBoolean(n.globalInstall)?n.globalInstall:!0;i&&o&&O.warn(Je(Pe.COMPONENT_NAME_LEGACY_COMPATIBLE,{name:Er.name})),i&&([o?"i18n":Er.name,"I18nT"].forEach(a=>e.component(a,Er)),[Do.name,"I18nN"].forEach(a=>e.component(a,Do)),[Vo.name,"I18nD"].forEach(a=>e.component(a,Vo))),e.directive("t",cu(t))}function wv(e,t,r){return{beforeCreate(){const n=we.getCurrentInstance();if(!n)throw Ue(xe.UNEXPECTED_ERROR);const o=this.$options;if(o.i18n){const i=o.i18n;if(o.__i18n&&(i.__i18n=o.__i18n),i.__root=t,this===this.$root)this.$i18n=fu(e,i);else{i.__injectWithOption=!0,i.__extender=r.__vueI18nExtend,this.$i18n=ga(i);const a=this.$i18n;a.__extender&&(a.__disposer=a.__extender(this.$i18n))}}else if(o.__i18n)if(this===this.$root)this.$i18n=fu(e,o);else{this.$i18n=ga({__i18n:o.__i18n,__injectWithOption:!0,__extender:r.__vueI18nExtend,__root:t});const i=this.$i18n;i.__extender&&(i.__disposer=i.__extender(this.$i18n))}else this.$i18n=e;o.__i18nGlobal&&eu(t,o,o),this.$t=(...i)=>this.$i18n.t(...i),this.$rt=(...i)=>this.$i18n.rt(...i),this.$tc=(...i)=>this.$i18n.tc(...i),this.$te=(i,a)=>this.$i18n.te(i,a),this.$d=(...i)=>this.$i18n.d(...i),this.$n=(...i)=>this.$i18n.n(...i),this.$tm=i=>this.$i18n.tm(i),r.__setInstance(n,this.$i18n)},mounted(){},unmounted(){const n=we.getCurrentInstance();if(!n)throw Ue(xe.UNEXPECTED_ERROR);const o=this.$i18n;delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,o.__disposer&&(o.__disposer(),delete o.__disposer,delete o.__extender),r.__deleteInstance(n),delete this.$i18n}}}function fu(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[Xl](t.pluralizationRules||e.pluralizationRules);const r=Mo(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(r).forEach(n=>e.mergeLocaleMessage(n,r[n])),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach(n=>e.mergeDateTimeFormat(n,t.datetimeFormats[n])),t.numberFormats&&Object.keys(t.numberFormats).forEach(n=>e.mergeNumberFormat(n,t.numberFormats[n])),e}const pu=O.makeSymbol("global-vue-i18n");function Ov(e={},t){const r=O.isBoolean(e.legacy)?e.legacy:!0,n=O.isBoolean(e.globalInjection)?e.globalInjection:!0,o=r?!!e.allowComposition:!0,i=new Map,[a,s]=xv(e,r),c=O.makeSymbol("vue-i18n");r&&o&&O.warn(Je(Pe.NOTICE_DROP_ALLOW_COMPOSITION));function l(p){return i.get(p)||null}function u(p,h){i.set(p,h)}function f(p){i.delete(p)}{const p={get mode(){return r?"legacy":"composition"},get allowComposition(){return o},async install(h,...b){if(h.__VUE_I18N_SYMBOL__=c,h.provide(h.__VUE_I18N_SYMBOL__,p),O.isPlainObject(b[0])){const k=b[0];p.__composerExtend=k.__composerExtend,p.__vueI18nExtend=k.__vueI18nExtend}let g=null;!r&&n&&(g=jv(h,p.global)),Ev(h,p,...b),r&&h.mixin(wv(s,s.__composer,p));const j=h.unmount;h.unmount=()=>{g&&g(),p.dispose(),j()}},get global(){return s},dispose(){a.stop()},__instances:i,__getInstance:l,__setInstance:u,__deleteInstance:f};return p}}function Uo(e={}){const t=we.getCurrentInstance();if(t==null)throw Ue(xe.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&t.appContext.app!=null&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw Ue(xe.NOT_INSTALLED);const r=Cv(t),n=kv(r),o=Zl(t),i=Tv(e,o);if(r.mode==="legacy"&&!e.__useComponent){if(!r.allowComposition)throw Ue(xe.NOT_AVAILABLE_IN_LEGACY_MODE);return Iv(t,i,n,e)}if(i==="global")return eu(n,e,o),n;if(i==="parent"){let c=Lv(r,t,e.__useComponent);return c==null&&(O.warn(Je(Pe.NOT_FOUND_PARENT_SCOPE)),c=n),c}const a=r;let s=a.__getInstance(t);if(s==null){const c=O.assign({},e);"__i18n"in o&&(c.__i18n=o.__i18n),n&&(c.__root=n),s=ma(c),a.__composerExtend&&(s[va]=a.__composerExtend(s)),Pv(a,t,s),a.__setInstance(t,s)}return s}const Sv=e=>{if(!(cv in e))throw Ue(xe.NOT_COMPATIBLE_LEGACY_VUE_I18N);return e};function xv(e,t,r){const n=we.effectScope();{const o=t?n.run(()=>ga(e)):n.run(()=>ma(e));if(o==null)throw Ue(xe.UNEXPECTED_ERROR);return[n,o]}}function Cv(e){{const t=we.inject(e.isCE?pu:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw Ue(e.isCE?xe.NOT_INSTALLED_WITH_PROVIDE:xe.UNEXPECTED_ERROR);return t}}function Tv(e,t){return O.isEmptyObject(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}function kv(e){return e.mode==="composition"?e.global:e.global.__composer}function Lv(e,t,r=!1){let n=null;const o=t.root;let i=Rv(t,r);for(;i!=null;){const a=e;if(e.mode==="composition")n=a.__getInstance(i);else{const s=a.__getInstance(i);s!=null&&(n=s.__composer,r&&n&&!n[Jl]&&(n=null))}if(n!=null||o===i)break;i=i.parent}return n}function Rv(e,t=!1){return e==null?null:t&&e.vnode.ctx||e.parent}function Pv(e,t,r){we.onMounted(()=>{},t),we.onUnmounted(()=>{const n=r;e.__deleteInstance(t);const o=n[va];o&&(o(),delete n[va])},t)}function Iv(e,t,r,n={}){const o=t==="local",i=we.shallowRef(null);if(o&&e.proxy&&!(e.proxy.$options.i18n||e.proxy.$options.__i18n))throw Ue(xe.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);const a=O.isBoolean(n.inheritLocale)?n.inheritLocale:!O.isString(n.locale),s=we.ref(!o||a?r.locale.value:O.isString(n.locale)?n.locale:ve.DEFAULT_LOCALE),c=we.ref(!o||a?r.fallbackLocale.value:O.isString(n.fallbackLocale)||O.isArray(n.fallbackLocale)||O.isPlainObject(n.fallbackLocale)||n.fallbackLocale===!1?n.fallbackLocale:s.value),l=we.ref(Mo(s.value,n)),u=we.ref(O.isPlainObject(n.datetimeFormats)?n.datetimeFormats:{[s.value]:{}}),f=we.ref(O.isPlainObject(n.numberFormats)?n.numberFormats:{[s.value]:{}}),p=o?r.missingWarn:O.isBoolean(n.missingWarn)||O.isRegExp(n.missingWarn)?n.missingWarn:!0,h=o?r.fallbackWarn:O.isBoolean(n.fallbackWarn)||O.isRegExp(n.fallbackWarn)?n.fallbackWarn:!0,b=o?r.fallbackRoot:O.isBoolean(n.fallbackRoot)?n.fallbackRoot:!0,g=!!n.fallbackFormat,j=O.isFunction(n.missing)?n.missing:null,k=O.isFunction(n.postTranslation)?n.postTranslation:null,E=o?r.warnHtmlMessage:O.isBoolean(n.warnHtmlMessage)?n.warnHtmlMessage:!0,m=!!n.escapeParameter,y=o?r.modifiers:O.isPlainObject(n.modifiers)?n.modifiers:{},C=n.pluralRules||o&&r.pluralRules;function S(){return[s.value,c.value,l.value,u.value,f.value]}const T=we.computed({get:()=>i.value?i.value.locale.value:s.value,set:d=>{i.value&&(i.value.locale.value=d),s.value=d}}),L=we.computed({get:()=>i.value?i.value.fallbackLocale.value:c.value,set:d=>{i.value&&(i.value.fallbackLocale.value=d),c.value=d}}),R=we.computed(()=>i.value?i.value.messages.value:l.value),$=we.computed(()=>u.value),W=we.computed(()=>f.value);function D(){return i.value?i.value.getPostTranslationHandler():k}function te(d){i.value&&i.value.setPostTranslationHandler(d)}function he(){return i.value?i.value.getMissingHandler():j}function pe(d){i.value&&i.value.setMissingHandler(d)}function z(d){return S(),d()}function ce(...d){return i.value?z(()=>Reflect.apply(i.value.t,null,[...d])):z(()=>"")}function de(...d){return i.value?Reflect.apply(i.value.rt,null,[...d]):""}function Ie(...d){return i.value?z(()=>Reflect.apply(i.value.d,null,[...d])):z(()=>"")}function Oe(...d){return i.value?z(()=>Reflect.apply(i.value.n,null,[...d])):z(()=>"")}function V(d){return i.value?i.value.tm(d):{}}function N(d,_){return i.value?i.value.te(d,_):!1}function X(d){return i.value?i.value.getLocaleMessage(d):{}}function oe(d,_){i.value&&(i.value.setLocaleMessage(d,_),l.value[d]=_)}function P(d,_){i.value&&i.value.mergeLocaleMessage(d,_)}function K(d){return i.value?i.value.getDateTimeFormat(d):{}}function x(d,_){i.value&&(i.value.setDateTimeFormat(d,_),u.value[d]=_)}function F(d,_){i.value&&i.value.mergeDateTimeFormat(d,_)}function U(d){return i.value?i.value.getNumberFormat(d):{}}function Y(d,_){i.value&&(i.value.setNumberFormat(d,_),f.value[d]=_)}function ae(d,_){i.value&&i.value.mergeNumberFormat(d,_)}const ue={get id(){return i.value?i.value.id:-1},locale:T,fallbackLocale:L,messages:R,datetimeFormats:$,numberFormats:W,get inheritLocale(){return i.value?i.value.inheritLocale:a},set inheritLocale(d){i.value&&(i.value.inheritLocale=d)},get availableLocales(){return i.value?i.value.availableLocales:Object.keys(l.value)},get modifiers(){return i.value?i.value.modifiers:y},get pluralRules(){return i.value?i.value.pluralRules:C},get isGlobal(){return i.value?i.value.isGlobal:!1},get missingWarn(){return i.value?i.value.missingWarn:p},set missingWarn(d){i.value&&(i.value.missingWarn=d)},get fallbackWarn(){return i.value?i.value.fallbackWarn:h},set fallbackWarn(d){i.value&&(i.value.missingWarn=d)},get fallbackRoot(){return i.value?i.value.fallbackRoot:b},set fallbackRoot(d){i.value&&(i.value.fallbackRoot=d)},get fallbackFormat(){return i.value?i.value.fallbackFormat:g},set fallbackFormat(d){i.value&&(i.value.fallbackFormat=d)},get warnHtmlMessage(){return i.value?i.value.warnHtmlMessage:E},set warnHtmlMessage(d){i.value&&(i.value.warnHtmlMessage=d)},get escapeParameter(){return i.value?i.value.escapeParameter:m},set escapeParameter(d){i.value&&(i.value.escapeParameter=d)},t:ce,getPostTranslationHandler:D,setPostTranslationHandler:te,getMissingHandler:he,setMissingHandler:pe,rt:de,d:Ie,n:Oe,tm:V,te:N,getLocaleMessage:X,setLocaleMessage:oe,mergeLocaleMessage:P,getDateTimeFormat:K,setDateTimeFormat:x,mergeDateTimeFormat:F,getNumberFormat:U,setNumberFormat:Y,mergeNumberFormat:ae};function v(d){d.locale.value=s.value,d.fallbackLocale.value=c.value,Object.keys(l.value).forEach(_=>{d.mergeLocaleMessage(_,l.value[_])}),Object.keys(u.value).forEach(_=>{d.mergeDateTimeFormat(_,u.value[_])}),Object.keys(f.value).forEach(_=>{d.mergeNumberFormat(_,f.value[_])}),d.escapeParameter=m,d.fallbackFormat=g,d.fallbackRoot=b,d.fallbackWarn=h,d.missingWarn=p,d.warnHtmlMessage=E}return we.onBeforeMount(()=>{if(e.proxy==null||e.proxy.$i18n==null)throw Ue(xe.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);const d=i.value=e.proxy.$i18n.__composer;t==="global"?(s.value=d.locale.value,c.value=d.fallbackLocale.value,l.value=d.messages.value,u.value=d.datetimeFormats.value,f.value=d.numberFormats.value):o&&v(d)}),ue}const Av=["locale","fallbackLocale","availableLocales"],du=["t","rt","d","n","tm","te"];function jv(e,t){const r=Object.create(null);return Av.forEach(n=>{const o=Object.getOwnPropertyDescriptor(t,n);if(!o)throw Ue(xe.UNEXPECTED_ERROR);const i=we.isRef(o.value)?{get(){return o.value.value},set(a){o.value.value=a}}:{get(){return o.get&&o.get()}};Object.defineProperty(r,n,i)}),e.config.globalProperties.$i18n=r,du.forEach(n=>{const o=Object.getOwnPropertyDescriptor(t,n);if(!o||!o.value)throw Ue(xe.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${n}`,o)}),()=>{delete e.config.globalProperties.$i18n,du.forEach(n=>{delete e.config.globalProperties[`$${n}`]})}}ve.registerMessageCompiler(ve.compile),ve.registerMessageResolver(ve.resolveValue),ve.registerLocaleFallbacker(ve.fallbackWithLocaleChain);{const e=O.getGlobalThis();e.__INTLIFY__=!0,ve.setDevToolsHook(e.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__)}tt.DatetimeFormat=Vo,tt.I18nD=bv,tt.I18nInjectionKey=pu,tt.I18nN=gv,tt.I18nT=hv,tt.NumberFormat=Do,tt.Translation=Er,tt.VERSION=Yl,tt.castToVueI18n=Sv,tt.createI18n=Ov,tt.useI18n=Uo,tt.vTDirective=cu;var Nv=tt,Fv=!1;/**
* @vue/shared v3.4.38
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const Mv=()=>{},Dv=Object.prototype.hasOwnProperty,Vv=(e,t)=>Dv.call(e,t),Uv=Array.isArray,$v=e=>ba(e)==="[object Date]",$o=e=>typeof e=="function",Hv=e=>typeof e=="string",hu=e=>e!==null&&typeof e=="object",Wv=e=>(hu(e)||$o(e))&&$o(e.then)&&$o(e.catch),Bv=Object.prototype.toString,ba=e=>Bv.call(e),qv=e=>ba(e).slice(8,-1),Gv=e=>ba(e)==="[object Object]",_a=e=>{const t=Object.create(null);return r=>t[r]||(t[r]=e(r))},Kv=/-(\w)/g,Yv=_a(e=>e.replace(Kv,(t,r)=>r?r.toUpperCase():"")),zv=/\B([A-Z])/g,Qv=_a(e=>e.replace(zv,"-$1").toLowerCase()),Xv=_a(e=>e.charAt(0).toUpperCase()+e.slice(1));function wr(){return wr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},wr.apply(this,arguments)}function vu(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}const Jv={silent:!1,logLevel:"warn"},Zv=["validator"],mu=Object.prototype,gu=mu.toString,em=mu.hasOwnProperty,yu=/^\s*function (\w+)/;function bu(e){var t;const r=(t=e==null?void 0:e.type)!==null&&t!==void 0?t:e;if(r){const n=r.toString().match(yu);return n?n[1]:""}return""}const mn=pf;function tm(){}let Fe=tm;const Fn=(e,t)=>em.call(e,t),nm=Number.isInteger||function(e){return typeof e=="number"&&isFinite(e)&&Math.floor(e)===e},Mn=Array.isArray||function(e){return gu.call(e)==="[object Array]"},Dn=e=>gu.call(e)==="[object Function]",Ho=(e,t)=>mn(e)&&Fn(e,"_vueTypes_name")&&(!t||e._vueTypes_name===t),_u=e=>mn(e)&&(Fn(e,"type")||["_vueTypes_name","validator","default","required"].some(t=>Fn(e,t)));function Ea(e,t){return Object.defineProperty(e.bind(t),"__original",{value:e})}function gn(e,t,r=!1){let n,o=!0,i="";n=mn(e)?e:{type:e};const a=Ho(n)?n._vueTypes_name+" - ":"";if(_u(n)&&n.type!==null){if(n.type===void 0||n.type===!0||!n.required&&t==null)return o;Mn(n.type)?(o=n.type.some(s=>gn(s,t,!0)===!0),i=n.type.map(s=>bu(s)).join(" or ")):(i=bu(n),o=i==="Array"?Mn(t):i==="Object"?mn(t):i==="String"||i==="Number"||i==="Boolean"||i==="Function"?function(s){if(s==null)return"";const c=s.constructor.toString().match(yu);return c?c[1].replace(/^Async/,""):""}(t)===i:t instanceof n.type)}if(!o){const s=`${a}value "${t}" should be of type "${i}"`;return r===!1?(Fe(s),!1):s}if(Fn(n,"validator")&&Dn(n.validator)){const s=Fe,c=[];if(Fe=l=>{c.push(l)},o=n.validator(t),Fe=s,!o){const l=(c.length>1?"* ":"")+c.join(`
* `);return c.length=0,r===!1?(Fe(l),o):l}}return o}function ct(e,t){const r=Object.defineProperties(t,{_vueTypes_name:{value:e,writable:!0},isRequired:{get(){return this.required=!0,this}},def:{value(o){return o===void 0?this.type===Boolean||Array.isArray(this.type)&&this.type.includes(Boolean)?void(this.default=void 0):(Fn(this,"default")&&delete this.default,this):Dn(o)||gn(this,o,!0)===!0?(this.default=Mn(o)?()=>[...o]:mn(o)?()=>Object.assign({},o):o,this):(Fe(`${this._vueTypes_name} - invalid default value: "${o}"`),this)}}}),{validator:n}=r;return Dn(n)&&(r.validator=Ea(n,r)),r}function gt(e,t){const r=ct(e,t);return Object.defineProperty(r,"validate",{value(n){return Dn(this.validator)&&Fe(`${this._vueTypes_name} - calling .validate() will overwrite the current custom validator function. Validator info:
${JSON.stringify(this)}`),this.validator=Ea(n,this),this}})}function Eu(e,t,r){const n=function(c){const l={};return Object.getOwnPropertyNames(c).forEach(u=>{l[u]=Object.getOwnPropertyDescriptor(c,u)}),Object.defineProperties({},l)}(t);if(n._vueTypes_name=e,!mn(r))return n;const{validator:o}=r,i=vu(r,Zv);if(Dn(o)){let{validator:c}=n;c&&(c=(s=(a=c).__original)!==null&&s!==void 0?s:a),n.validator=Ea(c?function(l){return c.call(this,l)&&o.call(this,l)}:o,n)}var a,s;return Object.assign(n,i)}function Wo(e){return e.replace(/^(?!\s*$)/gm,"  ")}const rm=()=>gt("any",{}),om=()=>gt("function",{type:Function}),wu=()=>gt("boolean",{type:Boolean}),im=()=>gt("string",{type:String}),am=()=>gt("number",{type:Number}),sm=()=>gt("array",{type:Array}),cm=()=>gt("object",{type:Object}),lm=()=>ct("integer",{type:Number,validator(e){const t=nm(e);return t===!1&&Fe(`integer - "${e}" is not an integer`),t}}),um=()=>ct("symbol",{validator(e){const t=typeof e=="symbol";return t===!1&&Fe(`symbol - invalid value "${e}"`),t}}),fm=()=>Object.defineProperty({type:null,validator(e){const t=e===null;return t===!1&&Fe("nullable - value should be null"),t}},"_vueTypes_name",{value:"nullable"});function pm(e,t="custom validation failed"){if(typeof e!="function")throw new TypeError("[VueTypes error]: You must provide a function as argument");return ct(e.name||"<<anonymous function>>",{type:null,validator(r){const n=e(r);return n||Fe(`${this._vueTypes_name} - ${t}`),n}})}function dm(e){if(!Mn(e))throw new TypeError("[VueTypes error]: You must provide an array as argument.");const t=`oneOf - value should be one of "${e.map(n=>typeof n=="symbol"?n.toString():n).join('", "')}".`,r={validator(n){const o=e.indexOf(n)!==-1;return o||Fe(t),o}};if(e.indexOf(null)===-1){const n=e.reduce((o,i)=>{if(i!=null){const a=i.constructor;o.indexOf(a)===-1&&o.push(a)}return o},[]);n.length>0&&(r.type=n)}return ct("oneOf",r)}function hm(e){if(!Mn(e))throw new TypeError("[VueTypes error]: You must provide an array as argument");let t=!1,r=!1,n=[];for(let i=0;i<e.length;i+=1){const a=e[i];if(_u(a)){if(Dn(a.validator)&&(t=!0),Ho(a,"oneOf")&&a.type){n=n.concat(a.type);continue}if(Ho(a,"nullable")){r=!0;continue}if(a.type===!0||!a.type){Fe('oneOfType - invalid usage of "true" and "null" as types.');continue}n=n.concat(a.type)}else n.push(a)}n=n.filter((i,a)=>n.indexOf(i)===a);const o=r===!1&&n.length>0?n:null;return ct("oneOfType",t?{type:o,validator(i){const a=[],s=e.some(c=>{const l=gn(c,i,!0);return typeof l=="string"&&a.push(l),l===!0});return s||Fe(`oneOfType - provided value does not match any of the ${a.length} passed-in validators:
${Wo(a.join(`
`))}`),s}}:{type:o})}function vm(e){return ct("arrayOf",{type:Array,validator(t){let r="";const n=t.every(o=>(r=gn(e,o,!0),r===!0));return n||Fe(`arrayOf - value validation error:
${Wo(r)}`),n}})}function mm(e){return ct("instanceOf",{type:e})}function gm(e){return ct("objectOf",{type:Object,validator(t){let r="";const n=Object.keys(t).every(o=>(r=gn(e,t[o],!0),r===!0));return n||Fe(`objectOf - value validation error:
${Wo(r)}`),n}})}function ym(e){const t=Object.keys(e),r=t.filter(o=>{var i;return!((i=e[o])===null||i===void 0||!i.required)}),n=ct("shape",{type:Object,validator(o){if(!mn(o))return!1;const i=Object.keys(o);if(r.length>0&&r.some(a=>i.indexOf(a)===-1)){const a=r.filter(s=>i.indexOf(s)===-1);return Fe(a.length===1?`shape - required property "${a[0]}" is not defined.`:`shape - required properties "${a.join('", "')}" are not defined.`),!1}return i.every(a=>{if(t.indexOf(a)===-1)return this._vueTypes_isLoose===!0||(Fe(`shape - shape definition does not include a "${a}" property. Allowed keys: "${t.join('", "')}".`),!1);const s=gn(e[a],o[a],!0);return typeof s=="string"&&Fe(`shape - "${a}" property validation error:
 ${Wo(s)}`),s===!0})}});return Object.defineProperty(n,"_vueTypes_isLoose",{writable:!0,value:!1}),Object.defineProperty(n,"loose",{get(){return this._vueTypes_isLoose=!0,this}}),n}const bm=["name","validate","getter"],_m=(e=>((e=class{static get any(){return rm()}static get func(){return om().def(this.defaults.func)}static get bool(){return this.defaults.bool===void 0?wu():wu().def(this.defaults.bool)}static get string(){return im().def(this.defaults.string)}static get number(){return am().def(this.defaults.number)}static get array(){return sm().def(this.defaults.array)}static get object(){return cm().def(this.defaults.object)}static get integer(){return lm().def(this.defaults.integer)}static get symbol(){return um()}static get nullable(){return fm()}static extend(t){if(Fe("VueTypes.extend is deprecated. Use the ES6+ method instead. See https://dwightjack.github.io/vue-types/advanced/extending-vue-types.html#extending-namespaced-validators-in-es6 for details."),Mn(t))return t.forEach(c=>this.extend(c)),this;const{name:r,validate:n=!1,getter:o=!1}=t,i=vu(t,bm);if(Fn(this,r))throw new TypeError(`[VueTypes error]: Type "${r}" already defined`);const{type:a}=i;if(Ho(a))return delete i.type,Object.defineProperty(this,r,o?{get:()=>Eu(r,a,i)}:{value(...c){const l=Eu(r,a,i);return l.validator&&(l.validator=l.validator.bind(l,...c)),l}});let s;return s=o?{get(){const c=Object.assign({},i);return n?gt(r,c):ct(r,c)},enumerable:!0}:{value(...c){const l=Object.assign({},i);let u;return u=n?gt(r,l):ct(r,l),l.validator&&(u.validator=l.validator.bind(u,...c)),u},enumerable:!0},Object.defineProperty(this,r,s)}}).defaults={},e.sensibleDefaults=void 0,e.config=Jv,e.custom=pm,e.oneOf=dm,e.instanceOf=mm,e.oneOfType=hm,e.arrayOf=vm,e.objectOf=gm,e.shape=ym,e.utils={validate:(t,r)=>gn(r,t,!0)===!0,toType:(t,r,n=!1)=>n?gt(t,r):ct(t,r)},e))();function Ou(e={func:()=>{},bool:!0,string:"",number:0,array:()=>[],object:()=>({}),integer:0}){var t;return(t=class extends _m{static get sensibleDefaults(){return wr({},this.defaults)}static set sensibleDefaults(r){this.defaults=r!==!1?wr({},r!==!0?r:e):{}}}).defaults=wr({},e),t}class my extends Ou(){}/*!
* vue-router v4.4.3
* (c) 2024 Eduardo San Martin Morote
* @license MIT
*/const Vn=typeof document<"u";function Em(e){return e.__esModule||e[Symbol.toStringTag]==="Module"}const ke=Object.assign;function wa(e,t){const r={};for(const n in t){const o=t[n];r[n]=yt(o)?o.map(e):e(o)}return r}const Or=()=>{},yt=Array.isArray,Su=/#/g,wm=/&/g,Om=/\//g,Sm=/=/g,xm=/\?/g,xu=/\+/g,Cm=/%5B/g,Tm=/%5D/g,Cu=/%5E/g,km=/%60/g,Tu=/%7B/g,Lm=/%7C/g,ku=/%7D/g,Rm=/%20/g;function Oa(e){return encodeURI(""+e).replace(Lm,"|").replace(Cm,"[").replace(Tm,"]")}function Pm(e){return Oa(e).replace(Tu,"{").replace(ku,"}").replace(Cu,"^")}function Sa(e){return Oa(e).replace(xu,"%2B").replace(Rm,"+").replace(Su,"%23").replace(wm,"%26").replace(km,"`").replace(Tu,"{").replace(ku,"}").replace(Cu,"^")}function Im(e){return Sa(e).replace(Sm,"%3D")}function Am(e){return Oa(e).replace(Su,"%23").replace(xm,"%3F")}function jm(e){return e==null?"":Am(e).replace(Om,"%2F")}function Sr(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Nm=/\/$/,Fm=e=>e.replace(Nm,"");function xa(e,t,r="/"){let n,o={},i="",a="";const s=t.indexOf("#");let c=t.indexOf("?");return s<c&&s>=0&&(c=-1),c>-1&&(n=t.slice(0,c),i=t.slice(c+1,s>-1?s:t.length),o=e(i)),s>-1&&(n=n||t.slice(0,s),a=t.slice(s,t.length)),n=Um(n??t,r),{fullPath:n+(i&&"?")+i+a,path:n,query:o,hash:Sr(a)}}function Mm(e,t){const r=t.query?e(t.query):"";return t.path+(r&&"?")+r+(t.hash||"")}function Lu(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Dm(e,t,r){const n=t.matched.length-1,o=r.matched.length-1;return n>-1&&n===o&&Un(t.matched[n],r.matched[o])&&Ru(t.params,r.params)&&e(t.query)===e(r.query)&&t.hash===r.hash}function Un(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Ru(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(!Vm(e[r],t[r]))return!1;return!0}function Vm(e,t){return yt(e)?Pu(e,t):yt(t)?Pu(t,e):e===t}function Pu(e,t){return yt(t)?e.length===t.length&&e.every((r,n)=>r===t[n]):e.length===1&&e[0]===t}function Um(e,t){if(e.startsWith("/"))return e;if(!e)return t;const r=t.split("/"),n=e.split("/"),o=n[n.length-1];(o===".."||o===".")&&n.push("");let i=r.length-1,a,s;for(a=0;a<n.length;a++)if(s=n[a],s!==".")if(s==="..")i>1&&i--;else break;return r.slice(0,i).join("/")+"/"+n.slice(a).join("/")}const zt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var xr;(function(e){e.pop="pop",e.push="push"})(xr||(xr={}));var Cr;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Cr||(Cr={}));function $m(e){if(!e)if(Vn){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Fm(e)}const Hm=/^[^#]+#/;function Wm(e,t){return e.replace(Hm,"#")+t}function Bm(e,t){const r=document.documentElement.getBoundingClientRect(),n=e.getBoundingClientRect();return{behavior:t.behavior,left:n.left-r.left-(t.left||0),top:n.top-r.top-(t.top||0)}}const Bo=()=>({left:window.scrollX,top:window.scrollY});function qm(e){let t;if("el"in e){const r=e.el,n=typeof r=="string"&&r.startsWith("#"),o=typeof r=="string"?n?document.getElementById(r.slice(1)):document.querySelector(r):r;if(!o)return;t=Bm(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Iu(e,t){return(history.state?history.state.position-t:-1)+e}const Ca=new Map;function Gm(e,t){Ca.set(e,t)}function Km(e){const t=Ca.get(e);return Ca.delete(e),t}let Ym=()=>location.protocol+"//"+location.host;function Au(e,t){const{pathname:r,search:n,hash:o}=t,i=e.indexOf("#");if(i>-1){let a=o.includes(e.slice(i))?e.slice(i).length:1,s=o.slice(a);return s[0]!=="/"&&(s="/"+s),Lu(s,"")}return Lu(r,e)+n+o}function zm(e,t,r,n){let o=[],i=[],a=null;const s=({state:p})=>{const h=Au(e,location),b=r.value,g=t.value;let j=0;if(p){if(r.value=h,t.value=p,a&&a===b){a=null;return}j=g?p.position-g.position:0}else n(h);o.forEach(k=>{k(r.value,b,{delta:j,type:xr.pop,direction:j?j>0?Cr.forward:Cr.back:Cr.unknown})})};function c(){a=r.value}function l(p){o.push(p);const h=()=>{const b=o.indexOf(p);b>-1&&o.splice(b,1)};return i.push(h),h}function u(){const{history:p}=window;p.state&&p.replaceState(ke({},p.state,{scroll:Bo()}),"")}function f(){for(const p of i)p();i=[],window.removeEventListener("popstate",s),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",s),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:c,listen:l,destroy:f}}function ju(e,t,r,n=!1,o=!1){return{back:e,current:t,forward:r,replaced:n,position:window.history.length,scroll:o?Bo():null}}function Qm(e){const{history:t,location:r}=window,n={value:Au(e,r)},o={value:t.state};o.value||i(n.value,{back:null,current:n.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function i(c,l,u){const f=e.indexOf("#"),p=f>-1?(r.host&&document.querySelector("base")?e:e.slice(f))+c:Ym()+e+c;try{t[u?"replaceState":"pushState"](l,"",p),o.value=l}catch{r[u?"replace":"assign"](p)}}function a(c,l){const u=ke({},t.state,ju(o.value.back,c,o.value.forward,!0),l,{position:o.value.position});i(c,u,!0),n.value=c}function s(c,l){const u=ke({},o.value,t.state,{forward:c,scroll:Bo()});i(u.current,u,!0);const f=ke({},ju(n.value,c,null),{position:u.position+1},l);i(c,f,!1),n.value=c}return{location:n,state:o,push:s,replace:a}}function Nu(e){e=$m(e);const t=Qm(e),r=zm(e,t.state,t.location,t.replace);function n(i,a=!0){a||r.pauseListeners(),history.go(i)}const o=ke({location:"",base:e,go:n,createHref:Wm.bind(null,e)},t,r);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function Xm(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),Nu(e)}function Jm(e){return typeof e=="string"||e&&typeof e=="object"}function Fu(e){return typeof e=="string"||typeof e=="symbol"}const Mu=Symbol("");var Du;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Du||(Du={}));function $n(e,t){return ke(new Error,{type:e,[Mu]:!0},t)}function At(e,t){return e instanceof Error&&Mu in e&&(t==null||!!(e.type&t))}const Vu="[^/]+?",Zm={sensitive:!1,strict:!1,start:!0,end:!0},eg=/[.+*?^${}()[\]/\\]/g;function tg(e,t){const r=ke({},Zm,t),n=[];let o=r.start?"^":"";const i=[];for(const l of e){const u=l.length?[]:[90];r.strict&&!l.length&&(o+="/");for(let f=0;f<l.length;f++){const p=l[f];let h=40+(r.sensitive?.25:0);if(p.type===0)f||(o+="/"),o+=p.value.replace(eg,"\\$&"),h+=40;else if(p.type===1){const{value:b,repeatable:g,optional:j,regexp:k}=p;i.push({name:b,repeatable:g,optional:j});const E=k||Vu;if(E!==Vu){h+=10;try{new RegExp(`(${E})`)}catch(y){throw new Error(`Invalid custom RegExp for param "${b}" (${E}): `+y.message)}}let m=g?`((?:${E})(?:/(?:${E}))*)`:`(${E})`;f||(m=j&&l.length<2?`(?:/${m})`:"/"+m),j&&(m+="?"),o+=m,h+=20,j&&(h+=-8),g&&(h+=-20),E===".*"&&(h+=-50)}u.push(h)}n.push(u)}if(r.strict&&r.end){const l=n.length-1;n[l][n[l].length-1]+=.7000000000000001}r.strict||(o+="/?"),r.end?o+="$":r.strict&&(o+="(?:/|$)");const a=new RegExp(o,r.sensitive?"":"i");function s(l){const u=l.match(a),f={};if(!u)return null;for(let p=1;p<u.length;p++){const h=u[p]||"",b=i[p-1];f[b.name]=h&&b.repeatable?h.split("/"):h}return f}function c(l){let u="",f=!1;for(const p of e){(!f||!u.endsWith("/"))&&(u+="/"),f=!1;for(const h of p)if(h.type===0)u+=h.value;else if(h.type===1){const{value:b,repeatable:g,optional:j}=h,k=b in l?l[b]:"";if(yt(k)&&!g)throw new Error(`Provided param "${b}" is an array but it is not repeatable (* or + modifiers)`);const E=yt(k)?k.join("/"):k;if(!E)if(j)p.length<2&&(u.endsWith("/")?u=u.slice(0,-1):f=!0);else throw new Error(`Missing required param "${b}"`);u+=E}}return u||"/"}return{re:a,score:n,keys:i,parse:s,stringify:c}}function ng(e,t){let r=0;for(;r<e.length&&r<t.length;){const n=t[r]-e[r];if(n)return n;r++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Uu(e,t){let r=0;const n=e.score,o=t.score;for(;r<n.length&&r<o.length;){const i=ng(n[r],o[r]);if(i)return i;r++}if(Math.abs(o.length-n.length)===1){if($u(n))return 1;if($u(o))return-1}return o.length-n.length}function $u(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const rg={type:0,value:""},og=/[a-zA-Z0-9_]/;function ig(e){if(!e)return[[]];if(e==="/")return[[rg]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(h){throw new Error(`ERR (${r})/"${l}": ${h}`)}let r=0,n=r;const o=[];let i;function a(){i&&o.push(i),i=[]}let s=0,c,l="",u="";function f(){l&&(r===0?i.push({type:0,value:l}):r===1||r===2||r===3?(i.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${l}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:l,regexp:u,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),l="")}function p(){l+=c}for(;s<e.length;){if(c=e[s++],c==="\\"&&r!==2){n=r,r=4;continue}switch(r){case 0:c==="/"?(l&&f(),a()):c===":"?(f(),r=1):p();break;case 4:p(),r=n;break;case 1:c==="("?r=2:og.test(c)?p():(f(),r=0,c!=="*"&&c!=="?"&&c!=="+"&&s--);break;case 2:c===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+c:r=3:u+=c;break;case 3:f(),r=0,c!=="*"&&c!=="?"&&c!=="+"&&s--,u="";break;default:t("Unknown state");break}}return r===2&&t(`Unfinished custom RegExp for param "${l}"`),f(),a(),o}function ag(e,t,r){const n=tg(ig(e.path),r),o=ke(n,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function sg(e,t){const r=[],n=new Map;t=Bu({strict:!1,end:!0,sensitive:!1},t);function o(f){return n.get(f)}function i(f,p,h){const b=!h,g=cg(f);g.aliasOf=h&&h.record;const j=Bu(t,f),k=[g];if("alias"in f){const y=typeof f.alias=="string"?[f.alias]:f.alias;for(const C of y)k.push(ke({},g,{components:h?h.record.components:g.components,path:C,aliasOf:h?h.record:g}))}let E,m;for(const y of k){const{path:C}=y;if(p&&C[0]!=="/"){const S=p.record.path,T=S[S.length-1]==="/"?"":"/";y.path=p.record.path+(C&&T+C)}if(E=ag(y,p,j),h?h.alias.push(E):(m=m||E,m!==E&&m.alias.push(E),b&&f.name&&!Wu(E)&&a(f.name)),qu(E)&&c(E),g.children){const S=g.children;for(let T=0;T<S.length;T++)i(S[T],E,h&&h.children[T])}h=h||E}return m?()=>{a(m)}:Or}function a(f){if(Fu(f)){const p=n.get(f);p&&(n.delete(f),r.splice(r.indexOf(p),1),p.children.forEach(a),p.alias.forEach(a))}else{const p=r.indexOf(f);p>-1&&(r.splice(p,1),f.record.name&&n.delete(f.record.name),f.children.forEach(a),f.alias.forEach(a))}}function s(){return r}function c(f){const p=fg(f,r);r.splice(p,0,f),f.record.name&&!Wu(f)&&n.set(f.record.name,f)}function l(f,p){let h,b={},g,j;if("name"in f&&f.name){if(h=n.get(f.name),!h)throw $n(1,{location:f});j=h.record.name,b=ke(Hu(p.params,h.keys.filter(m=>!m.optional).concat(h.parent?h.parent.keys.filter(m=>m.optional):[]).map(m=>m.name)),f.params&&Hu(f.params,h.keys.map(m=>m.name))),g=h.stringify(b)}else if(f.path!=null)g=f.path,h=r.find(m=>m.re.test(g)),h&&(b=h.parse(g),j=h.record.name);else{if(h=p.name?n.get(p.name):r.find(m=>m.re.test(p.path)),!h)throw $n(1,{location:f,currentLocation:p});j=h.record.name,b=ke({},p.params,f.params),g=h.stringify(b)}const k=[];let E=h;for(;E;)k.unshift(E.record),E=E.parent;return{name:j,path:g,params:b,matched:k,meta:ug(k)}}e.forEach(f=>i(f));function u(){r.length=0,n.clear()}return{addRoute:i,resolve:l,removeRoute:a,clearRoutes:u,getRoutes:s,getRecordMatcher:o}}function Hu(e,t){const r={};for(const n of t)n in e&&(r[n]=e[n]);return r}function cg(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:lg(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}function lg(e){const t={},r=e.props||!1;if("component"in e)t.default=r;else for(const n in e.components)t[n]=typeof r=="object"?r[n]:r;return t}function Wu(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function ug(e){return e.reduce((t,r)=>ke(t,r.meta),{})}function Bu(e,t){const r={};for(const n in e)r[n]=n in t?t[n]:e[n];return r}function fg(e,t){let r=0,n=t.length;for(;r!==n;){const i=r+n>>1;Uu(e,t[i])<0?n=i:r=i+1}const o=pg(e);return o&&(n=t.lastIndexOf(o,n-1)),n}function pg(e){let t=e;for(;t=t.parent;)if(qu(t)&&Uu(e,t)===0)return t}function qu({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function dg(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let n=0;n<r.length;++n){const o=r[n].replace(xu," "),i=o.indexOf("="),a=Sr(i<0?o:o.slice(0,i)),s=i<0?null:Sr(o.slice(i+1));if(a in t){let c=t[a];yt(c)||(c=t[a]=[c]),c.push(s)}else t[a]=s}return t}function Gu(e){let t="";for(let r in e){const n=e[r];if(r=Im(r),n==null){n!==void 0&&(t+=(t.length?"&":"")+r);continue}(yt(n)?n.map(o=>o&&Sa(o)):[n&&Sa(n)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+r,o!=null&&(t+="="+o))})}return t}function hg(e){const t={};for(const r in e){const n=e[r];n!==void 0&&(t[r]=yt(n)?n.map(o=>o==null?null:""+o):n==null?n:""+n)}return t}const vg=Symbol(""),Ku=Symbol(""),qo=Symbol(""),Ta=Symbol(""),ka=Symbol("");function Tr(){let e=[];function t(n){return e.push(n),()=>{const o=e.indexOf(n);o>-1&&e.splice(o,1)}}function r(){e=[]}return{add:t,list:()=>e.slice(),reset:r}}function Qt(e,t,r,n,o,i=a=>a()){const a=n&&(n.enterCallbacks[o]=n.enterCallbacks[o]||[]);return()=>new Promise((s,c)=>{const l=p=>{p===!1?c($n(4,{from:r,to:t})):p instanceof Error?c(p):Jm(p)?c($n(2,{from:t,to:p})):(a&&n.enterCallbacks[o]===a&&typeof p=="function"&&a.push(p),s())},u=i(()=>e.call(n&&n.instances[o],t,r,l));let f=Promise.resolve(u);e.length<3&&(f=f.then(l)),f.catch(p=>c(p))})}function La(e,t,r,n,o=i=>i()){const i=[];for(const a of e)for(const s in a.components){let c=a.components[s];if(!(t!=="beforeRouteEnter"&&!a.instances[s]))if(mg(c)){const l=(c.__vccOpts||c)[t];l&&i.push(Qt(l,r,n,a,s,o))}else{let l=c();i.push(()=>l.then(u=>{if(!u)return Promise.reject(new Error(`Couldn't resolve component "${s}" at "${a.path}"`));const f=Em(u)?u.default:u;a.components[s]=f;const p=(f.__vccOpts||f)[t];return p&&Qt(p,r,n,a,s,o)()}))}}return i}function mg(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Yu(e){const t=it(qo),r=it(Ta),n=Ne(()=>{const c=_t(e.to);return t.resolve(c)}),o=Ne(()=>{const{matched:c}=n.value,{length:l}=c,u=c[l-1],f=r.matched;if(!u||!f.length)return-1;const p=f.findIndex(Un.bind(null,u));if(p>-1)return p;const h=zu(c[l-2]);return l>1&&zu(u)===h&&f[f.length-1].path!==h?f.findIndex(Un.bind(null,c[l-2])):p}),i=Ne(()=>o.value>-1&&_g(r.params,n.value.params)),a=Ne(()=>o.value>-1&&o.value===r.matched.length-1&&Ru(r.params,n.value.params));function s(c={}){return bg(c)?t[_t(e.replace)?"replace":"push"](_t(e.to)).catch(Or):Promise.resolve()}return{route:n,href:Ne(()=>n.value.href),isActive:i,isExactActive:a,navigate:s}}const gg=Ct({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Yu,setup(e,{slots:t}){const r=_n(Yu(e)),{options:n}=it(qo),o=Ne(()=>({[Qu(e.activeClass,n.linkActiveClass,"router-link-active")]:r.isActive,[Qu(e.exactActiveClass,n.linkExactActiveClass,"router-link-exact-active")]:r.isExactActive}));return()=>{const i=t.default&&t.default(r);return e.custom?i:pr("a",{"aria-current":r.isExactActive?e.ariaCurrentValue:null,href:r.href,onClick:r.navigate,class:o.value},i)}}}),yg=gg;function bg(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function _g(e,t){for(const r in t){const n=t[r],o=e[r];if(typeof n=="string"){if(n!==o)return!1}else if(!yt(o)||o.length!==n.length||n.some((i,a)=>i!==o[a]))return!1}return!0}function zu(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Qu=(e,t,r)=>e??t??r,Eg=Ct({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:r}){const n=it(ka),o=Ne(()=>e.route||n.value),i=it(Ku,0),a=Ne(()=>{let l=_t(i);const{matched:u}=o.value;let f;for(;(f=u[l])&&!f.components;)l++;return l}),s=Ne(()=>o.value.matched[a.value]);An(Ku,Ne(()=>a.value+1)),An(vg,s),An(ka,o);const c=Be();return ot(()=>[c.value,s.value,e.name],([l,u,f],[p,h,b])=>{u&&(u.instances[f]=l,h&&h!==u&&l&&l===p&&(u.leaveGuards.size||(u.leaveGuards=h.leaveGuards),u.updateGuards.size||(u.updateGuards=h.updateGuards))),l&&u&&(!h||!Un(u,h)||!p)&&(u.enterCallbacks[f]||[]).forEach(g=>g(l))},{flush:"post"}),()=>{const l=o.value,u=e.name,f=s.value,p=f&&f.components[u];if(!p)return Xu(r.default,{Component:p,route:l});const h=f.props[u],b=h?h===!0?l.params:typeof h=="function"?h(l):h:null,g=pr(p,ke({},b,t,{onVnodeUnmounted:j=>{j.component.isUnmounted&&(f.instances[u]=null)},ref:c}));return Xu(r.default,{Component:g,route:l})||g}}});function Xu(e,t){if(!e)return null;const r=e(t);return r.length===1?r[0]:r}const wg=Eg;function Og(e){const t=sg(e.routes,e),r=e.parseQuery||dg,n=e.stringifyQuery||Gu,o=e.history,i=Tr(),a=Tr(),s=Tr(),c=Yr(zt);let l=zt;Vn&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=wa.bind(null,x=>""+x),f=wa.bind(null,jm),p=wa.bind(null,Sr);function h(x,F){let U,Y;return Fu(x)?(U=t.getRecordMatcher(x),Y=F):Y=x,t.addRoute(Y,U)}function b(x){const F=t.getRecordMatcher(x);F&&t.removeRoute(F)}function g(){return t.getRoutes().map(x=>x.record)}function j(x){return!!t.getRecordMatcher(x)}function k(x,F){if(F=ke({},F||c.value),typeof x=="string"){const d=xa(r,x,F.path),_=t.resolve({path:d.path},F),M=o.createHref(d.fullPath);return ke(d,_,{params:p(_.params),hash:Sr(d.hash),redirectedFrom:void 0,href:M})}let U;if(x.path!=null)U=ke({},x,{path:xa(r,x.path,F.path).path});else{const d=ke({},x.params);for(const _ in d)d[_]==null&&delete d[_];U=ke({},x,{params:f(d)}),F.params=f(F.params)}const Y=t.resolve(U,F),ae=x.hash||"";Y.params=u(p(Y.params));const ue=Mm(n,ke({},x,{hash:Pm(ae),path:Y.path})),v=o.createHref(ue);return ke({fullPath:ue,hash:ae,query:n===Gu?hg(x.query):x.query||{}},Y,{redirectedFrom:void 0,href:v})}function E(x){return typeof x=="string"?xa(r,x,c.value.path):ke({},x)}function m(x,F){if(l!==x)return $n(8,{from:F,to:x})}function y(x){return T(x)}function C(x){return y(ke(E(x),{replace:!0}))}function S(x){const F=x.matched[x.matched.length-1];if(F&&F.redirect){const{redirect:U}=F;let Y=typeof U=="function"?U(x):U;return typeof Y=="string"&&(Y=Y.includes("?")||Y.includes("#")?Y=E(Y):{path:Y},Y.params={}),ke({query:x.query,hash:x.hash,params:Y.path!=null?{}:x.params},Y)}}function T(x,F){const U=l=k(x),Y=c.value,ae=x.state,ue=x.force,v=x.replace===!0,d=S(U);if(d)return T(ke(E(d),{state:typeof d=="object"?ke({},ae,d.state):ae,force:ue,replace:v}),F||U);const _=U;_.redirectedFrom=F;let M;return!ue&&Dm(n,Y,U)&&(M=$n(16,{to:_,from:Y}),V(Y,Y,!0,!1)),(M?Promise.resolve(M):$(_,Y)).catch(A=>At(A)?At(A,2)?A:Oe(A):de(A,_,Y)).then(A=>{if(A){if(At(A,2))return T(ke({replace:v},E(A.to),{state:typeof A.to=="object"?ke({},ae,A.to.state):ae,force:ue}),F||_)}else A=D(_,Y,!0,v,ae);return W(_,Y,A),A})}function L(x,F){const U=m(x,F);return U?Promise.reject(U):Promise.resolve()}function R(x){const F=oe.values().next().value;return F&&typeof F.runWithContext=="function"?F.runWithContext(x):x()}function $(x,F){let U;const[Y,ae,ue]=Sg(x,F);U=La(Y.reverse(),"beforeRouteLeave",x,F);for(const d of Y)d.leaveGuards.forEach(_=>{U.push(Qt(_,x,F))});const v=L.bind(null,x,F);return U.push(v),K(U).then(()=>{U=[];for(const d of i.list())U.push(Qt(d,x,F));return U.push(v),K(U)}).then(()=>{U=La(ae,"beforeRouteUpdate",x,F);for(const d of ae)d.updateGuards.forEach(_=>{U.push(Qt(_,x,F))});return U.push(v),K(U)}).then(()=>{U=[];for(const d of ue)if(d.beforeEnter)if(yt(d.beforeEnter))for(const _ of d.beforeEnter)U.push(Qt(_,x,F));else U.push(Qt(d.beforeEnter,x,F));return U.push(v),K(U)}).then(()=>(x.matched.forEach(d=>d.enterCallbacks={}),U=La(ue,"beforeRouteEnter",x,F,R),U.push(v),K(U))).then(()=>{U=[];for(const d of a.list())U.push(Qt(d,x,F));return U.push(v),K(U)}).catch(d=>At(d,8)?d:Promise.reject(d))}function W(x,F,U){s.list().forEach(Y=>R(()=>Y(x,F,U)))}function D(x,F,U,Y,ae){const ue=m(x,F);if(ue)return ue;const v=F===zt,d=Vn?history.state:{};U&&(Y||v?o.replace(x.fullPath,ke({scroll:v&&d&&d.scroll},ae)):o.push(x.fullPath,ae)),c.value=x,V(x,F,U,v),Oe()}let te;function he(){te||(te=o.listen((x,F,U)=>{if(!P.listening)return;const Y=k(x),ae=S(Y);if(ae){T(ke(ae,{replace:!0}),Y).catch(Or);return}l=Y;const ue=c.value;Vn&&Gm(Iu(ue.fullPath,U.delta),Bo()),$(Y,ue).catch(v=>At(v,12)?v:At(v,2)?(T(v.to,Y).then(d=>{At(d,20)&&!U.delta&&U.type===xr.pop&&o.go(-1,!1)}).catch(Or),Promise.reject()):(U.delta&&o.go(-U.delta,!1),de(v,Y,ue))).then(v=>{v=v||D(Y,ue,!1),v&&(U.delta&&!At(v,8)?o.go(-U.delta,!1):U.type===xr.pop&&At(v,20)&&o.go(-1,!1)),W(Y,ue,v)}).catch(Or)}))}let pe=Tr(),z=Tr(),ce;function de(x,F,U){Oe(x);const Y=z.list();return Y.length&&Y.forEach(ae=>ae(x,F,U)),Promise.reject(x)}function Ie(){return ce&&c.value!==zt?Promise.resolve():new Promise((x,F)=>{pe.add([x,F])})}function Oe(x){return ce||(ce=!x,he(),pe.list().forEach(([F,U])=>x?U(x):F()),pe.reset()),x}function V(x,F,U,Y){const{scrollBehavior:ae}=e;if(!Vn||!ae)return Promise.resolve();const ue=!U&&Km(Iu(x.fullPath,0))||(Y||!U)&&history.state&&history.state.scroll||null;return an().then(()=>ae(x,F,ue)).then(v=>v&&qm(v)).catch(v=>de(v,x,F))}const N=x=>o.go(x);let X;const oe=new Set,P={currentRoute:c,listening:!0,addRoute:h,removeRoute:b,clearRoutes:t.clearRoutes,hasRoute:j,getRoutes:g,resolve:k,options:e,push:y,replace:C,go:N,back:()=>N(-1),forward:()=>N(1),beforeEach:i.add,beforeResolve:a.add,afterEach:s.add,onError:z.add,isReady:Ie,install(x){const F=this;x.component("RouterLink",yg),x.component("RouterView",wg),x.config.globalProperties.$router=F,Object.defineProperty(x.config.globalProperties,"$route",{enumerable:!0,get:()=>_t(c)}),Vn&&!X&&c.value===zt&&(X=!0,y(o.location).catch(ae=>{}));const U={};for(const ae in zt)Object.defineProperty(U,ae,{get:()=>c.value[ae],enumerable:!0});x.provide(qo,F),x.provide(Ta,Br(U)),x.provide(ka,c);const Y=x.unmount;oe.add(x),x.unmount=function(){oe.delete(x),oe.size<1&&(l=zt,te&&te(),te=null,c.value=zt,X=!1,ce=!1),Y()}}};function K(x){return x.reduce((F,U)=>F.then(()=>R(U)),Promise.resolve())}return P}function Sg(e,t){const r=[],n=[],o=[],i=Math.max(t.matched.length,e.matched.length);for(let a=0;a<i;a++){const s=t.matched[a];s&&(e.matched.find(l=>Un(l,s))?n.push(s):r.push(s));const c=e.matched[a];c&&(t.matched.find(l=>Un(l,c))||o.push(c))}return[r,n,o]}function xg(){return it(qo)}function Cg(e){return it(Ta)}var Tg=Object.defineProperty,kg=Object.defineProperties,Lg=Object.getOwnPropertyDescriptors,Ju=Object.getOwnPropertySymbols,Rg=Object.prototype.hasOwnProperty,Pg=Object.prototype.propertyIsEnumerable,Zu=(e,t,r)=>t in e?Tg(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,Ig=(e,t)=>{for(var r in t||(t={}))Rg.call(t,r)&&Zu(e,r,t[r]);if(Ju)for(var r of Ju(t))Pg.call(t,r)&&Zu(e,r,t[r]);return e},Ag=(e,t)=>kg(e,Lg(t));function Xt(e){let t=`\u8BF7\u4F7F\u7528 '@${e}' \u4E8B\u4EF6\uFF0C\u4E0D\u8981\u653E\u5728 props \u4E2D`;return t+=`
Please use '@${e}' event instead of props`,t}var ef=(e,t)=>{for(const[r,n]of t)e[r]=n;return e};const jg=Ct({props:{mode:{type:String,default:"default"},defaultContent:{type:Array,default:[]},defaultHtml:{type:String,default:""},defaultConfig:{type:Object,default:{}},modelValue:{type:String,default:""}},setup(e,t){const r=Be(null),n=Yr(null),o=Be(""),i=()=>{if(!r.value)return;const s=Se(e.defaultContent);df({selector:r.value,mode:e.mode,content:s||[],html:e.defaultHtml||e.modelValue||"",config:Ag(Ig({},e.defaultConfig),{onCreated(c){if(n.value=c,t.emit("onCreated",c),e.defaultConfig.onCreated){const l=Xt("onCreated");throw new Error(l)}},onChange(c){const l=c.getHtml();if(o.value=l,t.emit("update:modelValue",l),t.emit("onChange",c),e.defaultConfig.onChange){const u=Xt("onChange");throw new Error(u)}},onDestroyed(c){if(t.emit("onDestroyed",c),e.defaultConfig.onDestroyed){const l=Xt("onDestroyed");throw new Error(l)}},onMaxLength(c){if(t.emit("onMaxLength",c),e.defaultConfig.onMaxLength){const l=Xt("onMaxLength");throw new Error(l)}},onFocus(c){if(t.emit("onFocus",c),e.defaultConfig.onFocus){const l=Xt("onFocus");throw new Error(l)}},onBlur(c){if(t.emit("onBlur",c),e.defaultConfig.onBlur){const l=Xt("onBlur");throw new Error(l)}},customAlert(c,l){if(t.emit("customAlert",c,l),e.defaultConfig.customAlert){const u=Xt("customAlert");throw new Error(u)}},customPaste:(c,l)=>{if(e.defaultConfig.customPaste){const f=Xt("customPaste");throw new Error(f)}let u;return t.emit("customPaste",c,l,f=>{u=f}),u}})})};function a(s){const c=n.value;c==null||c.setHtml(s)}return kt(()=>{i()}),ot(()=>e.modelValue,s=>{s!==o.value&&a(s)}),{box:r}}}),Ng={ref:"box",style:{height:"100%"}};function Fg(e,t,r,n,o,i){return $t(),ur("div",Ng,null,512)}var Mg=ef(jg,[["render",Fg]]);const Dg=Ct({props:{editor:{type:Object},mode:{type:String,default:"default"},defaultConfig:{type:Object,default:{}}},setup(e){const t=Be(null),r=n=>{if(t.value){if(n==null)throw new Error("Not found instance of Editor when create <Toolbar/> component");hf.getToolbar(n)||vf({editor:n,selector:t.value||"<div></div>",mode:e.mode,config:e.defaultConfig})}};return so(()=>{const{editor:n}=e;n!=null&&r(n)}),{selector:t}}}),Vg={ref:"selector"};function Ug(e,t,r,n,o,i){return $t(),ur("div",Vg,null,512)}var $g=ef(Dg,[["render",Ug]]);function Go(e){return Nr()?(ni(e),!0):!1}function Ot(e){return typeof e=="function"?e():_t(e)}const tf=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const Hg=e=>e!=null,Wg=Object.prototype.toString,Bg=e=>Wg.call(e)==="[object Object]",Ra=()=>{};function qg(e,t){function r(...n){return new Promise((o,i)=>{Promise.resolve(e(()=>t.apply(this,n),{fn:t,thisArg:this,args:n})).then(o).catch(i)})}return r}function Gg(e,t={}){let r,n,o=Ra;const i=a=>{clearTimeout(a),o(),o=Ra};return a=>{const s=Ot(e),c=Ot(t.maxWait);return r&&i(r),s<=0||c!==void 0&&c<=0?(n&&(i(n),n=null),Promise.resolve(a())):new Promise((l,u)=>{o=t.rejectOnCancel?u:l,c&&!n&&(n=setTimeout(()=>{r&&i(r),n=null,l(a())},c)),r=setTimeout(()=>{n&&i(n),n=null,l(a())},s)})}}function nf(e){return dt()}function Kg(e,t=200,r={}){return qg(Gg(t,r),e)}function rf(e,t=!0,r){nf()?kt(e,r):t?e():an(e)}function Yg(e,t){nf()&&Pn(e,t)}function kr(e){var t;const r=Ot(e);return(t=r==null?void 0:r.$el)!=null?t:r}const Lr=tf?window:void 0,zg=tf?window.document:void 0;function Pa(...e){let t,r,n,o;if(typeof e[0]=="string"||Array.isArray(e[0])?([r,n,o]=e,t=Lr):[t,r,n,o]=e,!t)return Ra;Array.isArray(r)||(r=[r]),Array.isArray(n)||(n=[n]);const i=[],a=()=>{i.forEach(u=>u()),i.length=0},s=(u,f,p,h)=>(u.addEventListener(f,p,h),()=>u.removeEventListener(f,p,h)),c=ot(()=>[kr(t),Ot(o)],([u,f])=>{if(a(),!u)return;const p=Bg(f)?{...f}:f;i.push(...r.flatMap(h=>n.map(b=>s(u,h,b,p))))},{immediate:!0,flush:"post"}),l=()=>{c(),a()};return Go(l),l}function Qg(){const e=Be(!1),t=dt();return t&&kt(()=>{e.value=!0},t),e}function Ia(e){const t=Qg();return Ne(()=>(t.value,!!e()))}function Xg(e,t,r={}){const{window:n=Lr,...o}=r;let i;const a=Ia(()=>n&&"MutationObserver"in n),s=()=>{i&&(i.disconnect(),i=void 0)},c=Ne(()=>{const p=Ot(e),h=(Array.isArray(p)?p:[p]).map(kr).filter(Hg);return new Set(h)}),l=ot(()=>c.value,p=>{s(),a.value&&p.size&&(i=new MutationObserver(t),p.forEach(h=>i.observe(h,o)))},{immediate:!0,flush:"post"}),u=()=>i==null?void 0:i.takeRecords(),f=()=>{s(),l()};return Go(f),{isSupported:a,stop:f,takeRecords:u}}function Jg(e,t={}){const{window:r=Lr}=t,n=Ia(()=>r&&"matchMedia"in r&&typeof r.matchMedia=="function");let o;const i=Be(!1),a=l=>{i.value=l.matches},s=()=>{o&&("removeEventListener"in o?o.removeEventListener("change",a):o.removeListener(a))},c=so(()=>{n.value&&(s(),o=r.matchMedia(Ot(e)),"addEventListener"in o?o.addEventListener("change",a):o.addListener(a),i.value=o.matches)});return Go(()=>{c(),s(),o=void 0}),i}function Zg(e,t,r={}){const{window:n=Lr,initialValue:o="",observe:i=!1}=r,a=Be(o),s=Ne(()=>{var l;return kr(t)||((l=n==null?void 0:n.document)==null?void 0:l.documentElement)});function c(){var l;const u=Ot(e),f=Ot(s);if(f&&n){const p=(l=n.getComputedStyle(f).getPropertyValue(u))==null?void 0:l.trim();a.value=p||o}}return i&&Xg(s,c,{attributeFilter:["style","class"],window:n}),ot([s,()=>Ot(e)],c,{immediate:!0}),ot(a,l=>{var u;(u=s.value)!=null&&u.style&&s.value.style.setProperty(Ot(e),l)}),a}const of=["fullscreenchange","webkitfullscreenchange","webkitendfullscreen","mozfullscreenchange","MSFullscreenChange"];function ey(e,t={}){const{document:r=zg,autoExit:n=!1}=t,o=Ne(()=>{var k;return(k=kr(e))!=null?k:r==null?void 0:r.querySelector("html")}),i=Be(!1),a=Ne(()=>["requestFullscreen","webkitRequestFullscreen","webkitEnterFullscreen","webkitEnterFullScreen","webkitRequestFullScreen","mozRequestFullScreen","msRequestFullscreen"].find(k=>r&&k in r||o.value&&k in o.value)),s=Ne(()=>["exitFullscreen","webkitExitFullscreen","webkitExitFullScreen","webkitCancelFullScreen","mozCancelFullScreen","msExitFullscreen"].find(k=>r&&k in r||o.value&&k in o.value)),c=Ne(()=>["fullScreen","webkitIsFullScreen","webkitDisplayingFullscreen","mozFullScreen","msFullscreenElement"].find(k=>r&&k in r||o.value&&k in o.value)),l=["fullscreenElement","webkitFullscreenElement","mozFullScreenElement","msFullscreenElement"].find(k=>r&&k in r),u=Ia(()=>o.value&&r&&a.value!==void 0&&s.value!==void 0&&c.value!==void 0),f=()=>l?(r==null?void 0:r[l])===o.value:!1,p=()=>{if(c.value){if(r&&r[c.value]!=null)return r[c.value];{const k=o.value;if((k==null?void 0:k[c.value])!=null)return!!k[c.value]}}return!1};async function h(){if(!(!u.value||!i.value)){if(s.value)if((r==null?void 0:r[s.value])!=null)await r[s.value]();else{const k=o.value;(k==null?void 0:k[s.value])!=null&&await k[s.value]()}i.value=!1}}async function b(){if(!u.value||i.value)return;p()&&await h();const k=o.value;a.value&&(k==null?void 0:k[a.value])!=null&&(await k[a.value](),i.value=!0)}async function g(){await(i.value?h():b())}const j=()=>{const k=p();(!k||k&&f())&&(i.value=k)};return Pa(r,of,j,!1),Pa(()=>kr(o),of,j,!1),n&&Go(h),{isSupported:u,isFullscreen:i,enter:b,exit:h,toggle:g}}function ty(){const e=Be([]);return e.value.set=t=>{t&&e.value.push(t)},po(()=>{e.value.length=0}),e}function ny(e={}){const{window:t=Lr,initialWidth:r=Number.POSITIVE_INFINITY,initialHeight:n=Number.POSITIVE_INFINITY,listenOrientation:o=!0,includeScrollbar:i=!0}=e,a=Be(r),s=Be(n),c=()=>{t&&(i?(a.value=t.innerWidth,s.value=t.innerHeight):(a.value=t.document.documentElement.clientWidth,s.value=t.document.documentElement.clientHeight))};if(c(),rf(c),Pa("resize",c,{passive:!0}),o){const l=Jg("(orientation: portrait)");ot(l,()=>c())}return{width:a,height:s}}function ry(e,t){const r=e.hooks??{};let n;for(n in r){const o=r[n];o!==void 0&&t.addHook(n,o)}}function af(){return mf()}function oy(e={},t=af){const r=t();ry(e,r);const n=function(o,i){const a=i.value;if(i.oldValue===a)return;const s=`${a}`,c=i.arg,l=e.namedConfigurations,u=e.default??{};if(l&&c!==void 0){o.innerHTML=r.sanitize(s,l[c]??u);return}o.innerHTML=r.sanitize(s,u)};return{mounted:n,updated:n}}const iy={install(e,t={},r=af){e.directive("dompurify-html",oy(t,r))}};var sf={exports:{}};(function(e){e.exports=function(t){var r={};function n(o){if(r[o])return r[o].exports;var i=r[o]={i:o,l:!1,exports:{}};return t[o].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=t,n.c=r,n.d=function(o,i,a){n.o(o,i)||Object.defineProperty(o,i,{enumerable:!0,get:a})},n.r=function(o){typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(o,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(o,"__esModule",{value:!0})},n.t=function(o,i){if(i&1&&(o=n(o)),i&8||i&4&&typeof o=="object"&&o&&o.__esModule)return o;var a=Object.create(null);if(n.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:o}),i&2&&typeof o!="string")for(var s in o)n.d(a,s,(function(c){return o[c]}).bind(null,s));return a},n.n=function(o){var i=o&&o.__esModule?function(){return o.default}:function(){return o};return n.d(i,"a",i),i},n.o=function(o,i){return Object.prototype.hasOwnProperty.call(o,i)},n.p="",n(n.s="fb15")}({"00ee":function(t,r,n){var o=n("b622"),i=o("toStringTag"),a={};a[i]="z",t.exports=String(a)==="[object z]"},"0366":function(t,r,n){var o=n("1c0b");t.exports=function(i,a,s){if(o(i),a===void 0)return i;switch(s){case 0:return function(){return i.call(a)};case 1:return function(c){return i.call(a,c)};case 2:return function(c,l){return i.call(a,c,l)};case 3:return function(c,l,u){return i.call(a,c,l,u)}}return function(){return i.apply(a,arguments)}}},"057f":function(t,r,n){var o=n("fc6a"),i=n("241c").f,a={}.toString,s=typeof window=="object"&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],c=function(l){try{return i(l)}catch{return s.slice()}};t.exports.f=function(l){return s&&a.call(l)=="[object Window]"?c(l):i(o(l))}},"06cf":function(t,r,n){var o=n("83ab"),i=n("d1e7"),a=n("5c6c"),s=n("fc6a"),c=n("c04e"),l=n("5135"),u=n("0cfb"),f=Object.getOwnPropertyDescriptor;r.f=o?f:function(p,h){if(p=s(p),h=c(h,!0),u)try{return f(p,h)}catch{}if(l(p,h))return a(!i.f.call(p,h),p[h])}},"0cfb":function(t,r,n){var o=n("83ab"),i=n("d039"),a=n("cc12");t.exports=!o&&!i(function(){return Object.defineProperty(a("div"),"a",{get:function(){return 7}}).a!=7})},"159b":function(t,r,n){var o=n("da84"),i=n("fdbc"),a=n("17c2"),s=n("9112");for(var c in i){var l=o[c],u=l&&l.prototype;if(u&&u.forEach!==a)try{s(u,"forEach",a)}catch{u.forEach=a}}},"17c2":function(t,r,n){var o=n("b727").forEach,i=n("a640"),a=n("ae40"),s=i("forEach"),c=a("forEach");t.exports=!s||!c?function(l){return o(this,l,arguments.length>1?arguments[1]:void 0)}:[].forEach},"19aa":function(t,r){t.exports=function(n,o,i){if(!(n instanceof o))throw TypeError("Incorrect "+(i?i+" ":"")+"invocation");return n}},"1be4":function(t,r,n){var o=n("d066");t.exports=o("document","documentElement")},"1c0b":function(t,r){t.exports=function(n){if(typeof n!="function")throw TypeError(String(n)+" is not a function");return n}},"1c7e":function(t,r,n){var o=n("b622"),i=o("iterator"),a=!1;try{var s=0,c={next:function(){return{done:!!s++}},return:function(){a=!0}};c[i]=function(){return this},Array.from(c,function(){throw 2})}catch{}t.exports=function(l,u){if(!u&&!a)return!1;var f=!1;try{var p={};p[i]=function(){return{next:function(){return{done:f=!0}}}},l(p)}catch{}return f}},"1cdc":function(t,r,n){var o=n("342f");t.exports=/(iphone|ipod|ipad).*applewebkit/i.test(o)},"1d80":function(t,r){t.exports=function(n){if(n==null)throw TypeError("Can't call method on "+n);return n}},"1dde":function(t,r,n){var o=n("d039"),i=n("b622"),a=n("2d00"),s=i("species");t.exports=function(c){return a>=51||!o(function(){var l=[],u=l.constructor={};return u[s]=function(){return{foo:1}},l[c](Boolean).foo!==1})}},2266:function(t,r,n){var o=n("825a"),i=n("e95a"),a=n("50c4"),s=n("0366"),c=n("35a1"),l=n("2a62"),u=function(f,p){this.stopped=f,this.result=p};t.exports=function(f,p,h){var b=h&&h.that,g=!!(h&&h.AS_ENTRIES),j=!!(h&&h.IS_ITERATOR),k=!!(h&&h.INTERRUPTED),E=s(p,b,1+g+k),m,y,C,S,T,L,R,$=function(D){return m&&l(m),new u(!0,D)},W=function(D){return g?(o(D),k?E(D[0],D[1],$):E(D[0],D[1])):k?E(D,$):E(D)};if(j)m=f;else{if(y=c(f),typeof y!="function")throw TypeError("Target is not iterable");if(i(y)){for(C=0,S=a(f.length);S>C;C++)if(T=W(f[C]),T&&T instanceof u)return T;return new u(!1)}m=y.call(f)}for(L=m.next;!(R=L.call(m)).done;){try{T=W(R.value)}catch(D){throw l(m),D}if(typeof T=="object"&&T&&T instanceof u)return T}return new u(!1)}},"23cb":function(t,r,n){var o=n("a691"),i=Math.max,a=Math.min;t.exports=function(s,c){var l=o(s);return l<0?i(l+c,0):a(l,c)}},"23e7":function(t,r,n){var o=n("da84"),i=n("06cf").f,a=n("9112"),s=n("6eeb"),c=n("ce4e"),l=n("e893"),u=n("94ca");t.exports=function(f,p){var h=f.target,b=f.global,g=f.stat,j,k,E,m,y,C;if(b?k=o:g?k=o[h]||c(h,{}):k=(o[h]||{}).prototype,k)for(E in p){if(y=p[E],f.noTargetGet?(C=i(k,E),m=C&&C.value):m=k[E],j=u(b?E:h+(g?".":"#")+E,f.forced),!j&&m!==void 0){if(typeof y==typeof m)continue;l(y,m)}(f.sham||m&&m.sham)&&a(y,"sham",!0),s(k,E,y,f)}}},"241c":function(t,r,n){var o=n("ca84"),i=n("7839"),a=i.concat("length","prototype");r.f=Object.getOwnPropertyNames||function(s){return o(s,a)}},2626:function(t,r,n){var o=n("d066"),i=n("9bf2"),a=n("b622"),s=n("83ab"),c=a("species");t.exports=function(l){var u=o(l),f=i.f;s&&u&&!u[c]&&f(u,c,{configurable:!0,get:function(){return this}})}},"2a62":function(t,r,n){var o=n("825a");t.exports=function(i){var a=i.return;if(a!==void 0)return o(a.call(i)).value}},"2cf4":function(t,r,n){var o=n("da84"),i=n("d039"),a=n("0366"),s=n("1be4"),c=n("cc12"),l=n("1cdc"),u=n("605d"),f=o.location,p=o.setImmediate,h=o.clearImmediate,b=o.process,g=o.MessageChannel,j=o.Dispatch,k=0,E={},m="onreadystatechange",y,C,S,T=function(W){if(E.hasOwnProperty(W)){var D=E[W];delete E[W],D()}},L=function(W){return function(){T(W)}},R=function(W){T(W.data)},$=function(W){o.postMessage(W+"",f.protocol+"//"+f.host)};(!p||!h)&&(p=function(W){for(var D=[],te=1;arguments.length>te;)D.push(arguments[te++]);return E[++k]=function(){(typeof W=="function"?W:Function(W)).apply(void 0,D)},y(k),k},h=function(W){delete E[W]},u?y=function(W){b.nextTick(L(W))}:j&&j.now?y=function(W){j.now(L(W))}:g&&!l?(C=new g,S=C.port2,C.port1.onmessage=R,y=a(S.postMessage,S,1)):o.addEventListener&&typeof postMessage=="function"&&!o.importScripts&&f&&f.protocol!=="file:"&&!i($)?(y=$,o.addEventListener("message",R,!1)):m in c("script")?y=function(W){s.appendChild(c("script"))[m]=function(){s.removeChild(this),T(W)}}:y=function(W){setTimeout(L(W),0)}),t.exports={set:p,clear:h}},"2d00":function(t,r,n){var o=n("da84"),i=n("342f"),a=o.process,s=a&&a.versions,c=s&&s.v8,l,u;c?(l=c.split("."),u=l[0]+l[1]):i&&(l=i.match(/Edge\/(\d+)/),(!l||l[1]>=74)&&(l=i.match(/Chrome\/(\d+)/),l&&(u=l[1]))),t.exports=u&&+u},"342f":function(t,r,n){var o=n("d066");t.exports=o("navigator","userAgent")||""},"350a":function(t,r,n){n("c383")},"35a1":function(t,r,n){var o=n("f5df"),i=n("3f8c"),a=n("b622"),s=a("iterator");t.exports=function(c){if(c!=null)return c[s]||c["@@iterator"]||i[o(c)]}},"37e8":function(t,r,n){var o=n("83ab"),i=n("9bf2"),a=n("825a"),s=n("df75");t.exports=o?Object.defineProperties:function(c,l){a(c);for(var u=s(l),f=u.length,p=0,h;f>p;)i.f(c,h=u[p++],l[h]);return c}},"3b8e":function(t,r,n){},"3d3b":function(t,r,n){n("482a")},"3f8c":function(t,r){t.exports={}},4160:function(t,r,n){var o=n("23e7"),i=n("17c2");o({target:"Array",proto:!0,forced:[].forEach!=i},{forEach:i})},"428f":function(t,r,n){var o=n("da84");t.exports=o},"44ad":function(t,r,n){var o=n("d039"),i=n("c6b6"),a="".split;t.exports=o(function(){return!Object("z").propertyIsEnumerable(0)})?function(s){return i(s)=="String"?a.call(s,""):Object(s)}:Object},"44de":function(t,r,n){var o=n("da84");t.exports=function(i,a){var s=o.console;s&&s.error&&(arguments.length===1?s.error(i):s.error(i,a))}},"482a":function(t,r,n){},4840:function(t,r,n){var o=n("825a"),i=n("1c0b"),a=n("b622"),s=a("species");t.exports=function(c,l){var u=o(c).constructor,f;return u===void 0||(f=o(u)[s])==null?l:i(f)}},4930:function(t,r,n){var o=n("d039");t.exports=!!Object.getOwnPropertySymbols&&!o(function(){return!String(Symbol())})},"4d64":function(t,r,n){var o=n("fc6a"),i=n("50c4"),a=n("23cb"),s=function(c){return function(l,u,f){var p=o(l),h=i(p.length),b=a(f,h),g;if(c&&u!=u){for(;h>b;)if(g=p[b++],g!=g)return!0}else for(;h>b;b++)if((c||b in p)&&p[b]===u)return c||b||0;return!c&&-1}};t.exports={includes:s(!0),indexOf:s(!1)}},"4de4":function(t,r,n){var o=n("23e7"),i=n("b727").filter,a=n("1dde"),s=n("ae40"),c=a("filter"),l=s("filter");o({target:"Array",proto:!0,forced:!c||!l},{filter:function(u){return i(this,u,arguments.length>1?arguments[1]:void 0)}})},"50c4":function(t,r,n){var o=n("a691"),i=Math.min;t.exports=function(a){return a>0?i(o(a),9007199254740991):0}},5135:function(t,r){var n={}.hasOwnProperty;t.exports=function(o,i){return n.call(o,i)}},5692:function(t,r,n){var o=n("c430"),i=n("c6cd");(t.exports=function(a,s){return i[a]||(i[a]=s!==void 0?s:{})})("versions",[]).push({version:"3.8.1",mode:o?"pure":"global",copyright:"\xA9 2020 Denis Pushkarev (zloirock.ru)"})},"56ef":function(t,r,n){var o=n("d066"),i=n("241c"),a=n("7418"),s=n("825a");t.exports=o("Reflect","ownKeys")||function(c){var l=i.f(s(c)),u=a.f;return u?l.concat(u(c)):l}},"5c6c":function(t,r){t.exports=function(n,o){return{enumerable:!(n&1),configurable:!(n&2),writable:!(n&4),value:o}}},"605d":function(t,r,n){var o=n("c6b6"),i=n("da84");t.exports=o(i.process)=="process"},"65f0":function(t,r,n){var o=n("861d"),i=n("e8b5"),a=n("b622"),s=a("species");t.exports=function(c,l){var u;return i(c)&&(u=c.constructor,typeof u=="function"&&(u===Array||i(u.prototype))?u=void 0:o(u)&&(u=u[s],u===null&&(u=void 0))),new(u===void 0?Array:u)(l===0?0:l)}},"69f3":function(t,r,n){var o=n("7f9a"),i=n("da84"),a=n("861d"),s=n("9112"),c=n("5135"),l=n("c6cd"),u=n("f772"),f=n("d012"),p=i.WeakMap,h,b,g,j=function(T){return g(T)?b(T):h(T,{})},k=function(T){return function(L){var R;if(!a(L)||(R=b(L)).type!==T)throw TypeError("Incompatible receiver, "+T+" required");return R}};if(o){var E=l.state||(l.state=new p),m=E.get,y=E.has,C=E.set;h=function(T,L){return L.facade=T,C.call(E,T,L),L},b=function(T){return m.call(E,T)||{}},g=function(T){return y.call(E,T)}}else{var S=u("state");f[S]=!0,h=function(T,L){return L.facade=T,s(T,S,L),L},b=function(T){return c(T,S)?T[S]:{}},g=function(T){return c(T,S)}}t.exports={set:h,get:b,has:g,enforce:j,getterFor:k}},"6eeb":function(t,r,n){var o=n("da84"),i=n("9112"),a=n("5135"),s=n("ce4e"),c=n("8925"),l=n("69f3"),u=l.get,f=l.enforce,p=String(String).split("String");(t.exports=function(h,b,g,j){var k=j?!!j.unsafe:!1,E=j?!!j.enumerable:!1,m=j?!!j.noTargetGet:!1,y;if(typeof g=="function"&&(typeof b=="string"&&!a(g,"name")&&i(g,"name",b),y=f(g),y.source||(y.source=p.join(typeof b=="string"?b:""))),h===o){E?h[b]=g:s(b,g);return}else k?!m&&h[b]&&(E=!0):delete h[b];E?h[b]=g:i(h,b,g)})(Function.prototype,"toString",function(){return typeof this=="function"&&u(this).source||c(this)})},7418:function(t,r){r.f=Object.getOwnPropertySymbols},"746f":function(t,r,n){var o=n("428f"),i=n("5135"),a=n("e538"),s=n("9bf2").f;t.exports=function(c){var l=o.Symbol||(o.Symbol={});i(l,c)||s(l,c,{value:a.f(c)})}},7839:function(t,r){t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},"7b0b":function(t,r,n){var o=n("1d80");t.exports=function(i){return Object(o(i))}},"7c73":function(t,r,n){var o=n("825a"),i=n("37e8"),a=n("7839"),s=n("d012"),c=n("1be4"),l=n("cc12"),u=n("f772"),f=">",p="<",h="prototype",b="script",g=u("IE_PROTO"),j=function(){},k=function(S){return p+b+f+S+p+"/"+b+f},E=function(S){S.write(k("")),S.close();var T=S.parentWindow.Object;return S=null,T},m=function(){var S=l("iframe"),T="java"+b+":",L;return S.style.display="none",c.appendChild(S),S.src=String(T),L=S.contentWindow.document,L.open(),L.write(k("document.F=Object")),L.close(),L.F},y,C=function(){try{y=document.domain&&new ActiveXObject("htmlfile")}catch{}C=y?E(y):m();for(var S=a.length;S--;)delete C[h][a[S]];return C()};s[g]=!0,t.exports=Object.create||function(S,T){var L;return S!==null?(j[h]=o(S),L=new j,j[h]=null,L[g]=S):L=C(),T===void 0?L:i(L,T)}},"7f9a":function(t,r,n){var o=n("da84"),i=n("8925"),a=o.WeakMap;t.exports=typeof a=="function"&&/native code/.test(i(a))},"825a":function(t,r,n){var o=n("861d");t.exports=function(i){if(!o(i))throw TypeError(String(i)+" is not an object");return i}},"83ab":function(t,r,n){var o=n("d039");t.exports=!o(function(){return Object.defineProperty({},1,{get:function(){return 7}})[1]!=7})},8418:function(t,r,n){var o=n("c04e"),i=n("9bf2"),a=n("5c6c");t.exports=function(s,c,l){var u=o(c);u in s?i.f(s,u,a(0,l)):s[u]=l}},"861d":function(t,r){t.exports=function(n){return typeof n=="object"?n!==null:typeof n=="function"}},8875:function(t,r,n){var o,i,a;(function(s,c){i=[],o=c,a=typeof o=="function"?o.apply(r,i):o,a!==void 0&&(t.exports=a)})(typeof self<"u"?self:this,function(){function s(){var c=Object.getOwnPropertyDescriptor(document,"currentScript");if(!c&&"currentScript"in document&&document.currentScript||c&&c.get!==s&&document.currentScript)return document.currentScript;try{throw new Error}catch(y){var l=/.*at [^(]*\((.*):(.+):(.+)\)$/ig,u=/@([^@]*):(\d+):(\d+)\s*$/ig,f=l.exec(y.stack)||u.exec(y.stack),p=f&&f[1]||!1,h=f&&f[2]||!1,b=document.location.href.replace(document.location.hash,""),g,j,k,E=document.getElementsByTagName("script");p===b&&(g=document.documentElement.outerHTML,j=new RegExp("(?:[^\\n]+?\\n){0,"+(h-2)+"}[^<]*<script>([\\d\\D]*?)<\\/script>[\\d\\D]*","i"),k=g.replace(j,"$1").trim());for(var m=0;m<E.length;m++)if(E[m].readyState==="interactive"||E[m].src===p||p===b&&E[m].innerHTML&&E[m].innerHTML.trim()===k)return E[m];return null}}return s})},8925:function(t,r,n){var o=n("c6cd"),i=Function.toString;typeof o.inspectSource!="function"&&(o.inspectSource=function(a){return i.call(a)}),t.exports=o.inspectSource},"8bbf":function(t,r){t.exports=Kl},"90e3":function(t,r){var n=0,o=Math.random();t.exports=function(i){return"Symbol("+String(i===void 0?"":i)+")_"+(++n+o).toString(36)}},9112:function(t,r,n){var o=n("83ab"),i=n("9bf2"),a=n("5c6c");t.exports=o?function(s,c,l){return i.f(s,c,a(1,l))}:function(s,c,l){return s[c]=l,s}},"94ca":function(t,r,n){var o=n("d039"),i=/#|\.prototype\./,a=function(f,p){var h=c[s(f)];return h==u?!0:h==l?!1:typeof p=="function"?o(p):!!p},s=a.normalize=function(f){return String(f).replace(i,".").toLowerCase()},c=a.data={},l=a.NATIVE="N",u=a.POLYFILL="P";t.exports=a},"96cf":function(t,r,n){var o=function(i){var a=Object.prototype,s=a.hasOwnProperty,c,l=typeof Symbol=="function"?Symbol:{},u=l.iterator||"@@iterator",f=l.asyncIterator||"@@asyncIterator",p=l.toStringTag||"@@toStringTag";function h(V,N,X){return Object.defineProperty(V,N,{value:X,enumerable:!0,configurable:!0,writable:!0}),V[N]}try{h({},"")}catch{h=function(V,N,X){return V[N]=X}}function b(V,N,X,oe){var P=N&&N.prototype instanceof C?N:C,K=Object.create(P.prototype),x=new de(oe||[]);return K._invoke=he(V,X,x),K}i.wrap=b;function g(V,N,X){try{return{type:"normal",arg:V.call(N,X)}}catch(oe){return{type:"throw",arg:oe}}}var j="suspendedStart",k="suspendedYield",E="executing",m="completed",y={};function C(){}function S(){}function T(){}var L={};L[u]=function(){return this};var R=Object.getPrototypeOf,$=R&&R(R(Ie([])));$&&$!==a&&s.call($,u)&&(L=$);var W=T.prototype=C.prototype=Object.create(L);S.prototype=W.constructor=T,T.constructor=S,S.displayName=h(T,p,"GeneratorFunction");function D(V){["next","throw","return"].forEach(function(N){h(V,N,function(X){return this._invoke(N,X)})})}i.isGeneratorFunction=function(V){var N=typeof V=="function"&&V.constructor;return N?N===S||(N.displayName||N.name)==="GeneratorFunction":!1},i.mark=function(V){return Object.setPrototypeOf?Object.setPrototypeOf(V,T):(V.__proto__=T,h(V,p,"GeneratorFunction")),V.prototype=Object.create(W),V},i.awrap=function(V){return{__await:V}};function te(V,N){function X(K,x,F,U){var Y=g(V[K],V,x);if(Y.type==="throw")U(Y.arg);else{var ae=Y.arg,ue=ae.value;return ue&&typeof ue=="object"&&s.call(ue,"__await")?N.resolve(ue.__await).then(function(v){X("next",v,F,U)},function(v){X("throw",v,F,U)}):N.resolve(ue).then(function(v){ae.value=v,F(ae)},function(v){return X("throw",v,F,U)})}}var oe;function P(K,x){function F(){return new N(function(U,Y){X(K,x,U,Y)})}return oe=oe?oe.then(F,F):F()}this._invoke=P}D(te.prototype),te.prototype[f]=function(){return this},i.AsyncIterator=te,i.async=function(V,N,X,oe,P){P===void 0&&(P=Promise);var K=new te(b(V,N,X,oe),P);return i.isGeneratorFunction(N)?K:K.next().then(function(x){return x.done?x.value:K.next()})};function he(V,N,X){var oe=j;return function(P,K){if(oe===E)throw new Error("Generator is already running");if(oe===m){if(P==="throw")throw K;return Oe()}for(X.method=P,X.arg=K;;){var x=X.delegate;if(x){var F=pe(x,X);if(F){if(F===y)continue;return F}}if(X.method==="next")X.sent=X._sent=X.arg;else if(X.method==="throw"){if(oe===j)throw oe=m,X.arg;X.dispatchException(X.arg)}else X.method==="return"&&X.abrupt("return",X.arg);oe=E;var U=g(V,N,X);if(U.type==="normal"){if(oe=X.done?m:k,U.arg===y)continue;return{value:U.arg,done:X.done}}else U.type==="throw"&&(oe=m,X.method="throw",X.arg=U.arg)}}}function pe(V,N){var X=V.iterator[N.method];if(X===c){if(N.delegate=null,N.method==="throw"){if(V.iterator.return&&(N.method="return",N.arg=c,pe(V,N),N.method==="throw"))return y;N.method="throw",N.arg=new TypeError("The iterator does not provide a 'throw' method")}return y}var oe=g(X,V.iterator,N.arg);if(oe.type==="throw")return N.method="throw",N.arg=oe.arg,N.delegate=null,y;var P=oe.arg;if(!P)return N.method="throw",N.arg=new TypeError("iterator result is not an object"),N.delegate=null,y;if(P.done)N[V.resultName]=P.value,N.next=V.nextLoc,N.method!=="return"&&(N.method="next",N.arg=c);else return P;return N.delegate=null,y}D(W),h(W,p,"Generator"),W[u]=function(){return this},W.toString=function(){return"[object Generator]"};function z(V){var N={tryLoc:V[0]};1 in V&&(N.catchLoc=V[1]),2 in V&&(N.finallyLoc=V[2],N.afterLoc=V[3]),this.tryEntries.push(N)}function ce(V){var N=V.completion||{};N.type="normal",delete N.arg,V.completion=N}function de(V){this.tryEntries=[{tryLoc:"root"}],V.forEach(z,this),this.reset(!0)}i.keys=function(V){var N=[];for(var X in V)N.push(X);return N.reverse(),function oe(){for(;N.length;){var P=N.pop();if(P in V)return oe.value=P,oe.done=!1,oe}return oe.done=!0,oe}};function Ie(V){if(V){var N=V[u];if(N)return N.call(V);if(typeof V.next=="function")return V;if(!isNaN(V.length)){var X=-1,oe=function P(){for(;++X<V.length;)if(s.call(V,X))return P.value=V[X],P.done=!1,P;return P.value=c,P.done=!0,P};return oe.next=oe}}return{next:Oe}}i.values=Ie;function Oe(){return{value:c,done:!0}}return de.prototype={constructor:de,reset:function(V){if(this.prev=0,this.next=0,this.sent=this._sent=c,this.done=!1,this.delegate=null,this.method="next",this.arg=c,this.tryEntries.forEach(ce),!V)for(var N in this)N.charAt(0)==="t"&&s.call(this,N)&&!isNaN(+N.slice(1))&&(this[N]=c)},stop:function(){this.done=!0;var V=this.tryEntries[0],N=V.completion;if(N.type==="throw")throw N.arg;return this.rval},dispatchException:function(V){if(this.done)throw V;var N=this;function X(U,Y){return K.type="throw",K.arg=V,N.next=U,Y&&(N.method="next",N.arg=c),!!Y}for(var oe=this.tryEntries.length-1;oe>=0;--oe){var P=this.tryEntries[oe],K=P.completion;if(P.tryLoc==="root")return X("end");if(P.tryLoc<=this.prev){var x=s.call(P,"catchLoc"),F=s.call(P,"finallyLoc");if(x&&F){if(this.prev<P.catchLoc)return X(P.catchLoc,!0);if(this.prev<P.finallyLoc)return X(P.finallyLoc)}else if(x){if(this.prev<P.catchLoc)return X(P.catchLoc,!0)}else if(F){if(this.prev<P.finallyLoc)return X(P.finallyLoc)}else throw new Error("try statement without catch or finally")}}},abrupt:function(V,N){for(var X=this.tryEntries.length-1;X>=0;--X){var oe=this.tryEntries[X];if(oe.tryLoc<=this.prev&&s.call(oe,"finallyLoc")&&this.prev<oe.finallyLoc){var P=oe;break}}P&&(V==="break"||V==="continue")&&P.tryLoc<=N&&N<=P.finallyLoc&&(P=null);var K=P?P.completion:{};return K.type=V,K.arg=N,P?(this.method="next",this.next=P.finallyLoc,y):this.complete(K)},complete:function(V,N){if(V.type==="throw")throw V.arg;return V.type==="break"||V.type==="continue"?this.next=V.arg:V.type==="return"?(this.rval=this.arg=V.arg,this.method="return",this.next="end"):V.type==="normal"&&N&&(this.next=N),y},finish:function(V){for(var N=this.tryEntries.length-1;N>=0;--N){var X=this.tryEntries[N];if(X.finallyLoc===V)return this.complete(X.completion,X.afterLoc),ce(X),y}},catch:function(V){for(var N=this.tryEntries.length-1;N>=0;--N){var X=this.tryEntries[N];if(X.tryLoc===V){var oe=X.completion;if(oe.type==="throw"){var P=oe.arg;ce(X)}return P}}throw new Error("illegal catch attempt")},delegateYield:function(V,N,X){return this.delegate={iterator:Ie(V),resultName:N,nextLoc:X},this.method==="next"&&(this.arg=c),y}},i}(t.exports);try{regeneratorRuntime=o}catch{Function("r","regeneratorRuntime = r")(o)}},"9bf2":function(t,r,n){var o=n("83ab"),i=n("0cfb"),a=n("825a"),s=n("c04e"),c=Object.defineProperty;r.f=o?c:function(l,u,f){if(a(l),u=s(u,!0),a(f),i)try{return c(l,u,f)}catch{}if("get"in f||"set"in f)throw TypeError("Accessors not supported");return"value"in f&&(l[u]=f.value),l}},a4d3:function(t,r,n){var o=n("23e7"),i=n("da84"),a=n("d066"),s=n("c430"),c=n("83ab"),l=n("4930"),u=n("fdbf"),f=n("d039"),p=n("5135"),h=n("e8b5"),b=n("861d"),g=n("825a"),j=n("7b0b"),k=n("fc6a"),E=n("c04e"),m=n("5c6c"),y=n("7c73"),C=n("df75"),S=n("241c"),T=n("057f"),L=n("7418"),R=n("06cf"),$=n("9bf2"),W=n("d1e7"),D=n("9112"),te=n("6eeb"),he=n("5692"),pe=n("f772"),z=n("d012"),ce=n("90e3"),de=n("b622"),Ie=n("e538"),Oe=n("746f"),V=n("d44e"),N=n("69f3"),X=n("b727").forEach,oe=pe("hidden"),P="Symbol",K="prototype",x=de("toPrimitive"),F=N.set,U=N.getterFor(P),Y=Object[K],ae=i.Symbol,ue=a("JSON","stringify"),v=R.f,d=$.f,_=T.f,M=W.f,A=he("symbols"),B=he("op-symbols"),J=he("string-to-symbol-registry"),G=he("symbol-to-string-registry"),Z=he("wks"),q=i.QObject,ee=!q||!q[K]||!q[K].findChild,ne=c&&f(function(){return y(d({},"a",{get:function(){return d(this,"a",{value:7}).a}})).a!=7})?function(Q,ie,le){var ye=v(Y,ie);ye&&delete Y[ie],d(Q,ie,le),ye&&Q!==Y&&d(Y,ie,ye)}:d,w=function(Q,ie){var le=A[Q]=y(ae[K]);return F(le,{type:P,tag:Q,description:ie}),c||(le.description=ie),le},I=u?function(Q){return typeof Q=="symbol"}:function(Q){return Object(Q)instanceof ae},re=function(Q,ie,le){Q===Y&&re(B,ie,le),g(Q);var ye=E(ie,!0);return g(le),p(A,ye)?(le.enumerable?(p(Q,oe)&&Q[oe][ye]&&(Q[oe][ye]=!1),le=y(le,{enumerable:m(0,!1)})):(p(Q,oe)||d(Q,oe,m(1,{})),Q[oe][ye]=!0),ne(Q,ye,le)):d(Q,ye,le)},fe=function(Q,ie){g(Q);var le=k(ie),ye=C(le).concat(me(le));return X(ye,function(Me){(!c||Ce.call(le,Me))&&re(Q,Me,le[Me])}),Q},_e=function(Q,ie){return ie===void 0?y(Q):fe(y(Q),ie)},Ce=function(Q){var ie=E(Q,!0),le=M.call(this,ie);return this===Y&&p(A,ie)&&!p(B,ie)?!1:le||!p(this,ie)||!p(A,ie)||p(this,oe)&&this[oe][ie]?le:!0},H=function(Q,ie){var le=k(Q),ye=E(ie,!0);if(!(le===Y&&p(A,ye)&&!p(B,ye))){var Me=v(le,ye);return Me&&p(A,ye)&&!(p(le,oe)&&le[oe][ye])&&(Me.enumerable=!0),Me}},se=function(Q){var ie=_(k(Q)),le=[];return X(ie,function(ye){!p(A,ye)&&!p(z,ye)&&le.push(ye)}),le},me=function(Q){var ie=Q===Y,le=_(ie?B:k(Q)),ye=[];return X(le,function(Me){p(A,Me)&&(!ie||p(Y,Me))&&ye.push(A[Me])}),ye};if(l||(ae=function(){if(this instanceof ae)throw TypeError("Symbol is not a constructor");var Q=!arguments.length||arguments[0]===void 0?void 0:String(arguments[0]),ie=ce(Q),le=function(ye){this===Y&&le.call(B,ye),p(this,oe)&&p(this[oe],ie)&&(this[oe][ie]=!1),ne(this,ie,m(1,ye))};return c&&ee&&ne(Y,ie,{configurable:!0,set:le}),w(ie,Q)},te(ae[K],"toString",function(){return U(this).tag}),te(ae,"withoutSetter",function(Q){return w(ce(Q),Q)}),W.f=Ce,$.f=re,R.f=H,S.f=T.f=se,L.f=me,Ie.f=function(Q){return w(de(Q),Q)},c&&(d(ae[K],"description",{configurable:!0,get:function(){return U(this).description}}),s||te(Y,"propertyIsEnumerable",Ce,{unsafe:!0}))),o({global:!0,wrap:!0,forced:!l,sham:!l},{Symbol:ae}),X(C(Z),function(Q){Oe(Q)}),o({target:P,stat:!0,forced:!l},{for:function(Q){var ie=String(Q);if(p(J,ie))return J[ie];var le=ae(ie);return J[ie]=le,G[le]=ie,le},keyFor:function(Q){if(!I(Q))throw TypeError(Q+" is not a symbol");if(p(G,Q))return G[Q]},useSetter:function(){ee=!0},useSimple:function(){ee=!1}}),o({target:"Object",stat:!0,forced:!l,sham:!c},{create:_e,defineProperty:re,defineProperties:fe,getOwnPropertyDescriptor:H}),o({target:"Object",stat:!0,forced:!l},{getOwnPropertyNames:se,getOwnPropertySymbols:me}),o({target:"Object",stat:!0,forced:f(function(){L.f(1)})},{getOwnPropertySymbols:function(Q){return L.f(j(Q))}}),ue){var ge=!l||f(function(){var Q=ae();return ue([Q])!="[null]"||ue({a:Q})!="{}"||ue(Object(Q))!="{}"});o({target:"JSON",stat:!0,forced:ge},{stringify:function(Q,ie,le){for(var ye=[Q],Me=1,lt;arguments.length>Me;)ye.push(arguments[Me++]);if(lt=ie,!(!b(ie)&&Q===void 0||I(Q)))return h(ie)||(ie=function(bt,et){if(typeof lt=="function"&&(et=lt.call(this,bt,et)),!I(et))return et}),ye[1]=ie,ue.apply(null,ye)}})}ae[K][x]||D(ae[K],x,ae[K].valueOf),V(ae,P),z[oe]=!0},a640:function(t,r,n){var o=n("d039");t.exports=function(i,a){var s=[][i];return!!s&&o(function(){s.call(null,a||function(){throw 1},1)})}},a691:function(t,r){var n=Math.ceil,o=Math.floor;t.exports=function(i){return isNaN(i=+i)?0:(i>0?o:n)(i)}},ae40:function(t,r,n){var o=n("83ab"),i=n("d039"),a=n("5135"),s=Object.defineProperty,c={},l=function(u){throw u};t.exports=function(u,f){if(a(c,u))return c[u];f||(f={});var p=[][u],h=a(f,"ACCESSORS")?f.ACCESSORS:!1,b=a(f,0)?f[0]:l,g=a(f,1)?f[1]:void 0;return c[u]=!!p&&!i(function(){if(h&&!o)return!0;var j={length:-1};h?s(j,1,{enumerable:!0,get:l}):j[1]=1,p.call(j,b,g)})}},b041:function(t,r,n){var o=n("00ee"),i=n("f5df");t.exports=o?{}.toString:function(){return"[object "+i(this)+"]"}},b0c0:function(t,r,n){var o=n("83ab"),i=n("9bf2").f,a=Function.prototype,s=a.toString,c=/^\s*function ([^ (]*)/,l="name";o&&!(l in a)&&i(a,l,{configurable:!0,get:function(){try{return s.call(this).match(c)[1]}catch{return""}}})},b575:function(t,r,n){var o=n("da84"),i=n("06cf").f,a=n("2cf4").set,s=n("1cdc"),c=n("605d"),l=o.MutationObserver||o.WebKitMutationObserver,u=o.document,f=o.process,p=o.Promise,h=i(o,"queueMicrotask"),b=h&&h.value,g,j,k,E,m,y,C,S;b||(g=function(){var T,L;for(c&&(T=f.domain)&&T.exit();j;){L=j.fn,j=j.next;try{L()}catch(R){throw j?E():k=void 0,R}}k=void 0,T&&T.enter()},!s&&!c&&l&&u?(m=!0,y=u.createTextNode(""),new l(g).observe(y,{characterData:!0}),E=function(){y.data=m=!m}):p&&p.resolve?(C=p.resolve(void 0),S=C.then,E=function(){S.call(C,g)}):c?E=function(){f.nextTick(g)}:E=function(){a.call(o,g)}),t.exports=b||function(T){var L={fn:T,next:void 0};k&&(k.next=L),j||(j=L,E()),k=L}},b622:function(t,r,n){var o=n("da84"),i=n("5692"),a=n("5135"),s=n("90e3"),c=n("4930"),l=n("fdbf"),u=i("wks"),f=o.Symbol,p=l?f:f&&f.withoutSetter||s;t.exports=function(h){return a(u,h)||(c&&a(f,h)?u[h]=f[h]:u[h]=p("Symbol."+h)),u[h]}},b64b:function(t,r,n){var o=n("23e7"),i=n("7b0b"),a=n("df75"),s=n("d039"),c=s(function(){a(1)});o({target:"Object",stat:!0,forced:c},{keys:function(l){return a(i(l))}})},b727:function(t,r,n){var o=n("0366"),i=n("44ad"),a=n("7b0b"),s=n("50c4"),c=n("65f0"),l=[].push,u=function(f){var p=f==1,h=f==2,b=f==3,g=f==4,j=f==6,k=f==7,E=f==5||j;return function(m,y,C,S){for(var T=a(m),L=i(T),R=o(y,C,3),$=s(L.length),W=0,D=S||c,te=p?D(m,$):h||k?D(m,0):void 0,he,pe;$>W;W++)if((E||W in L)&&(he=L[W],pe=R(he,W,T),f))if(p)te[W]=pe;else if(pe)switch(f){case 3:return!0;case 5:return he;case 6:return W;case 2:l.call(te,he)}else switch(f){case 4:return!1;case 7:l.call(te,he)}return j?-1:b||g?g:te}};t.exports={forEach:u(0),map:u(1),filter:u(2),some:u(3),every:u(4),find:u(5),findIndex:u(6),filterOut:u(7)}},c04e:function(t,r,n){var o=n("861d");t.exports=function(i,a){if(!o(i))return i;var s,c;if(a&&typeof(s=i.toString)=="function"&&!o(c=s.call(i))||typeof(s=i.valueOf)=="function"&&!o(c=s.call(i))||!a&&typeof(s=i.toString)=="function"&&!o(c=s.call(i)))return c;throw TypeError("Can't convert object to primitive value")}},c383:function(t,r,n){},c430:function(t,r){t.exports=!1},c6b6:function(t,r){var n={}.toString;t.exports=function(o){return n.call(o).slice(8,-1)}},c6cd:function(t,r,n){var o=n("da84"),i=n("ce4e"),a="__core-js_shared__",s=o[a]||i(a,{});t.exports=s},c8ba:function(t,r){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch{typeof window=="object"&&(n=window)}t.exports=n},ca84:function(t,r,n){var o=n("5135"),i=n("fc6a"),a=n("4d64").indexOf,s=n("d012");t.exports=function(c,l){var u=i(c),f=0,p=[],h;for(h in u)!o(s,h)&&o(u,h)&&p.push(h);for(;l.length>f;)o(u,h=l[f++])&&(~a(p,h)||p.push(h));return p}},cc12:function(t,r,n){var o=n("da84"),i=n("861d"),a=o.document,s=i(a)&&i(a.createElement);t.exports=function(c){return s?a.createElement(c):{}}},cdf9:function(t,r,n){var o=n("825a"),i=n("861d"),a=n("f069");t.exports=function(s,c){if(o(s),i(c)&&c.constructor===s)return c;var l=a.f(s),u=l.resolve;return u(c),l.promise}},ce4e:function(t,r,n){var o=n("da84"),i=n("9112");t.exports=function(a,s){try{i(o,a,s)}catch{o[a]=s}return s}},d012:function(t,r){t.exports={}},d039:function(t,r){t.exports=function(n){try{return!!n()}catch{return!0}}},d066:function(t,r,n){var o=n("428f"),i=n("da84"),a=function(s){return typeof s=="function"?s:void 0};t.exports=function(s,c){return arguments.length<2?a(o[s])||a(i[s]):o[s]&&o[s][c]||i[s]&&i[s][c]}},d1e7:function(t,r,n){var o={}.propertyIsEnumerable,i=Object.getOwnPropertyDescriptor,a=i&&!o.call({1:2},1);r.f=a?function(s){var c=i(this,s);return!!c&&c.enumerable}:o},d3b7:function(t,r,n){var o=n("00ee"),i=n("6eeb"),a=n("b041");o||i(Object.prototype,"toString",a,{unsafe:!0})},d44e:function(t,r,n){var o=n("9bf2").f,i=n("5135"),a=n("b622"),s=a("toStringTag");t.exports=function(c,l,u){c&&!i(c=u?c:c.prototype,s)&&o(c,s,{configurable:!0,value:l})}},da84:function(t,r,n){(function(o){var i=function(a){return a&&a.Math==Math&&a};t.exports=i(typeof globalThis=="object"&&globalThis)||i(typeof window=="object"&&window)||i(typeof self=="object"&&self)||i(typeof o=="object"&&o)||function(){return this}()||Function("return this")()}).call(this,n("c8ba"))},dbb4:function(t,r,n){var o=n("23e7"),i=n("83ab"),a=n("56ef"),s=n("fc6a"),c=n("06cf"),l=n("8418");o({target:"Object",stat:!0,sham:!i},{getOwnPropertyDescriptors:function(u){for(var f=s(u),p=c.f,h=a(f),b={},g=0,j,k;h.length>g;)k=p(f,j=h[g++]),k!==void 0&&l(b,j,k);return b}})},df75:function(t,r,n){var o=n("ca84"),i=n("7839");t.exports=Object.keys||function(a){return o(a,i)}},e2cc:function(t,r,n){var o=n("6eeb");t.exports=function(i,a,s){for(var c in a)o(i,c,a[c],s);return i}},e439:function(t,r,n){var o=n("23e7"),i=n("d039"),a=n("fc6a"),s=n("06cf").f,c=n("83ab"),l=i(function(){s(1)}),u=!c||l;o({target:"Object",stat:!0,forced:u,sham:!c},{getOwnPropertyDescriptor:function(f,p){return s(a(f),p)}})},e538:function(t,r,n){var o=n("b622");r.f=o},e667:function(t,r){t.exports=function(n){try{return{error:!1,value:n()}}catch(o){return{error:!0,value:o}}}},e6cf:function(t,r,n){var o=n("23e7"),i=n("c430"),a=n("da84"),s=n("d066"),c=n("fea9"),l=n("6eeb"),u=n("e2cc"),f=n("d44e"),p=n("2626"),h=n("861d"),b=n("1c0b"),g=n("19aa"),j=n("8925"),k=n("2266"),E=n("1c7e"),m=n("4840"),y=n("2cf4").set,C=n("b575"),S=n("cdf9"),T=n("44de"),L=n("f069"),R=n("e667"),$=n("69f3"),W=n("94ca"),D=n("b622"),te=n("605d"),he=n("2d00"),pe=D("species"),z="Promise",ce=$.get,de=$.set,Ie=$.getterFor(z),Oe=c,V=a.TypeError,N=a.document,X=a.process,oe=s("fetch"),P=L.f,K=P,x=!!(N&&N.createEvent&&a.dispatchEvent),F=typeof PromiseRejectionEvent=="function",U="unhandledrejection",Y="rejectionhandled",ae=0,ue=1,v=2,d=1,_=2,M,A,B,J,G=W(z,function(){var H=j(Oe)!==String(Oe);if(!H&&(he===66||!te&&!F)||i&&!Oe.prototype.finally)return!0;if(he>=51&&/native code/.test(Oe))return!1;var se=Oe.resolve(1),me=function(Q){Q(function(){},function(){})},ge=se.constructor={};return ge[pe]=me,!(se.then(function(){})instanceof me)}),Z=G||!E(function(H){Oe.all(H).catch(function(){})}),q=function(H){var se;return h(H)&&typeof(se=H.then)=="function"?se:!1},ee=function(H,se){if(!H.notified){H.notified=!0;var me=H.reactions;C(function(){for(var ge=H.value,Q=H.state==ue,ie=0;me.length>ie;){var le=me[ie++],ye=Q?le.ok:le.fail,Me=le.resolve,lt=le.reject,bt=le.domain,et,Hn,Rr;try{ye?(Q||(H.rejection===_&&re(H),H.rejection=d),ye===!0?et=ge:(bt&&bt.enter(),et=ye(ge),bt&&(bt.exit(),Rr=!0)),et===le.promise?lt(V("Promise-chain cycle")):(Hn=q(et))?Hn.call(et,Me,lt):Me(et)):lt(ge)}catch(lf){bt&&!Rr&&bt.exit(),lt(lf)}}H.reactions=[],H.notified=!1,se&&!H.rejection&&w(H)})}},ne=function(H,se,me){var ge,Q;x?(ge=N.createEvent("Event"),ge.promise=se,ge.reason=me,ge.initEvent(H,!1,!0),a.dispatchEvent(ge)):ge={promise:se,reason:me},!F&&(Q=a["on"+H])?Q(ge):H===U&&T("Unhandled promise rejection",me)},w=function(H){y.call(a,function(){var se=H.facade,me=H.value,ge=I(H),Q;if(ge&&(Q=R(function(){te?X.emit("unhandledRejection",me,se):ne(U,se,me)}),H.rejection=te||I(H)?_:d,Q.error))throw Q.value})},I=function(H){return H.rejection!==d&&!H.parent},re=function(H){y.call(a,function(){var se=H.facade;te?X.emit("rejectionHandled",se):ne(Y,se,H.value)})},fe=function(H,se,me){return function(ge){H(se,ge,me)}},_e=function(H,se,me){H.done||(H.done=!0,me&&(H=me),H.value=se,H.state=v,ee(H,!0))},Ce=function(H,se,me){if(!H.done){H.done=!0,me&&(H=me);try{if(H.facade===se)throw V("Promise can't be resolved itself");var ge=q(se);ge?C(function(){var Q={done:!1};try{ge.call(se,fe(Ce,Q,H),fe(_e,Q,H))}catch(ie){_e(Q,ie,H)}}):(H.value=se,H.state=ue,ee(H,!1))}catch(Q){_e({done:!1},Q,H)}}};G&&(Oe=function(H){g(this,Oe,z),b(H),M.call(this);var se=ce(this);try{H(fe(Ce,se),fe(_e,se))}catch(me){_e(se,me)}},M=function(H){de(this,{type:z,done:!1,notified:!1,parent:!1,reactions:[],rejection:!1,state:ae,value:void 0})},M.prototype=u(Oe.prototype,{then:function(H,se){var me=Ie(this),ge=P(m(this,Oe));return ge.ok=typeof H=="function"?H:!0,ge.fail=typeof se=="function"&&se,ge.domain=te?X.domain:void 0,me.parent=!0,me.reactions.push(ge),me.state!=ae&&ee(me,!1),ge.promise},catch:function(H){return this.then(void 0,H)}}),A=function(){var H=new M,se=ce(H);this.promise=H,this.resolve=fe(Ce,se),this.reject=fe(_e,se)},L.f=P=function(H){return H===Oe||H===B?new A(H):K(H)},!i&&typeof c=="function"&&(J=c.prototype.then,l(c.prototype,"then",function(H,se){var me=this;return new Oe(function(ge,Q){J.call(me,ge,Q)}).then(H,se)},{unsafe:!0}),typeof oe=="function"&&o({global:!0,enumerable:!0,forced:!0},{fetch:function(H){return S(Oe,oe.apply(a,arguments))}}))),o({global:!0,wrap:!0,forced:G},{Promise:Oe}),f(Oe,z,!1,!0),p(z),B=s(z),o({target:z,stat:!0,forced:G},{reject:function(H){var se=P(this);return se.reject.call(void 0,H),se.promise}}),o({target:z,stat:!0,forced:i||G},{resolve:function(H){return S(i&&this===B?Oe:this,H)}}),o({target:z,stat:!0,forced:Z},{all:function(H){var se=this,me=P(se),ge=me.resolve,Q=me.reject,ie=R(function(){var le=b(se.resolve),ye=[],Me=0,lt=1;k(H,function(bt){var et=Me++,Hn=!1;ye.push(void 0),lt++,le.call(se,bt).then(function(Rr){Hn||(Hn=!0,ye[et]=Rr,--lt||ge(ye))},Q)}),--lt||ge(ye)});return ie.error&&Q(ie.value),me.promise},race:function(H){var se=this,me=P(se),ge=me.reject,Q=R(function(){var ie=b(se.resolve);k(H,function(le){ie.call(se,le).then(me.resolve,ge)})});return Q.error&&ge(Q.value),me.promise}})},e893:function(t,r,n){var o=n("5135"),i=n("56ef"),a=n("06cf"),s=n("9bf2");t.exports=function(c,l){for(var u=i(l),f=s.f,p=a.f,h=0;h<u.length;h++){var b=u[h];o(c,b)||f(c,b,p(l,b))}}},e8b5:function(t,r,n){var o=n("c6b6");t.exports=Array.isArray||function(i){return o(i)=="Array"}},e95a:function(t,r,n){var o=n("b622"),i=n("3f8c"),a=o("iterator"),s=Array.prototype;t.exports=function(c){return c!==void 0&&(i.Array===c||s[a]===c)}},f069:function(t,r,n){var o=n("1c0b"),i=function(a){var s,c;this.promise=new a(function(l,u){if(s!==void 0||c!==void 0)throw TypeError("Bad Promise constructor");s=l,c=u}),this.resolve=o(s),this.reject=o(c)};t.exports.f=function(a){return new i(a)}},f53c:function(t,r,n){n("3b8e")},f5df:function(t,r,n){var o=n("00ee"),i=n("c6b6"),a=n("b622"),s=a("toStringTag"),c=i(function(){return arguments}())=="Arguments",l=function(u,f){try{return u[f]}catch{}};t.exports=o?i:function(u){var f,p,h;return u===void 0?"Undefined":u===null?"Null":typeof(p=l(f=Object(u),s))=="string"?p:c?i(f):(h=i(f))=="Object"&&typeof f.callee=="function"?"Arguments":h}},f772:function(t,r,n){var o=n("5692"),i=n("90e3"),a=o("keys");t.exports=function(s){return a[s]||(a[s]=i(s))}},fb15:function(t,r,n){if(n.r(r),n.d(r,"emitContext",function(){return h}),n.d(r,"hideContext",function(){return b}),n.d(r,"directive",function(){return p}),n.d(r,"Contextmenu",function(){return R}),n.d(r,"ContextmenuItem",function(){return he}),n.d(r,"ContextmenuSubmenu",function(){return N}),typeof window<"u"){var o=window.document.currentScript;{var i=n("8875");o=i(),"currentScript"in document||Object.defineProperty(document,"currentScript",{get:i})}var a=o&&o.src.match(/(.+\/)[^/]+\.js(\?.*)?$/);a&&(n.p=a[1])}n("b0c0");var s=function(P){return{all:P=P||new Map,on:function(K,x){var F=P.get(K);F&&F.push(x)||P.set(K,[x])},off:function(K,x){var F=P.get(K);F&&F.splice(F.indexOf(x)>>>0,1)},emit:function(K,x){(P.get(K)||[]).slice().map(function(F){F(x)}),(P.get("*")||[]).slice().map(function(F){F(K,x)})}}},c=s(),l=c,u=function(P,K){P.addEventListener("contextmenu",function(x){x.preventDefault(),l.emit("add-contextmenu",{x:x.clientX,y:x.clientY,value:K.value})}),document.addEventListener("click",function(){l.emit("hide-contextmenu")})},f=function(){l.emit("hide-contextmenu")},p={mounted:u,unmounted:f},h=function(P,K){P.preventDefault(),l.emit("add-contextmenu",{x:P.clientX,y:P.clientY,value:K}),document.addEventListener("click",function(){l.emit("hide-contextmenu")})},b=function(P){P.preventDefault(),l.emit("hide-contextmenu")},g=n("8bbf"),j=Object(g.withScopeId)("data-v-f9312e22");Object(g.pushScopeId)("data-v-f9312e22");var k={class:"v-contextmenu",ref:"contextmenu"};Object(g.popScopeId)();var E=j(function(P,K,x,F,U,Y){return Object(g.openBlock)(),Object(g.createBlock)(g.Teleport,{to:"body"},[Object(g.withDirectives)(Object(g.createVNode)("div",k,[Object(g.renderSlot)(P.$slots,"default")],512),[[g.vShow,P.show&&P.bindingValue&&P.name===P.bindingValue.name]])])});n("a4d3"),n("4de4"),n("4160"),n("e439"),n("dbb4"),n("b64b"),n("159b");function m(P,K,x){return K in P?Object.defineProperty(P,K,{value:x,enumerable:!0,configurable:!0,writable:!0}):P[K]=x,P}function y(P,K){var x=Object.keys(P);if(Object.getOwnPropertySymbols){var F=Object.getOwnPropertySymbols(P);K&&(F=F.filter(function(U){return Object.getOwnPropertyDescriptor(P,U).enumerable})),x.push.apply(x,F)}return x}function C(P){for(var K=1;K<arguments.length;K++){var x=arguments[K]!=null?arguments[K]:{};K%2?y(Object(x),!0).forEach(function(F){m(P,F,x[F])}):Object.getOwnPropertyDescriptors?Object.defineProperties(P,Object.getOwnPropertyDescriptors(x)):y(Object(x)).forEach(function(F){Object.defineProperty(P,F,Object.getOwnPropertyDescriptor(x,F))})}return P}n("96cf"),n("d3b7"),n("e6cf");function S(P,K,x,F,U,Y,ae){try{var ue=P[Y](ae),v=ue.value}catch(d){x(d);return}ue.done?K(v):Promise.resolve(v).then(F,U)}function T(P){return function(){var K=this,x=arguments;return new Promise(function(F,U){var Y=P.apply(K,x);function ae(v){S(Y,F,U,ae,ue,"next",v)}function ue(v){S(Y,F,U,ae,ue,"throw",v)}ae(void 0)})}}var L=Object(g.defineComponent)({name:"ContextMenu",props:{cutomClass:String,name:String},setup:function(){var P=Object(g.ref)(!1),K=Object(g.ref)(),x=Object(g.ref)();function F(ue,v){var d={top:v,left:ue},_=window,M=_.innerWidth,A=_.innerHeight;if(K.value){var B=K.value,J=B.clientWidth,G=B.clientHeight;return v+G>A&&(d.top-=G),ue+J>M&&(d.left-=J),d.top<0&&(d.top=G<A?(A-G)/2:0),d.left<0&&(d.left=J<M?(M-J)/2:0),d}}function U(ue,v,d){return Y.apply(this,arguments)}function Y(){return Y=T(regeneratorRuntime.mark(function ue(v,d,_){var M,A;return regeneratorRuntime.wrap(function(B){for(;;)switch(B.prev=B.next){case 0:return P.value=!0,x.value=C({},_),l.emit("bindValue",x.value),B.next=5,Object(g.nextTick)();case 5:K.value&&(M=K.value,A=F(v,d),A&&(M.style.top="".concat(A.top+5,"px"),M.style.left="".concat(A.left+5,"px")));case 6:case"end":return B.stop()}},ue)})),Y.apply(this,arguments)}function ae(){P.value=!1}return Object(g.onMounted)(function(){l.on("add-contextmenu",function(ue){U(ue.x,ue.y,ue.value)}),l.on("hide-contextmenu",function(){ae()}),l.on("item-click",function(){P.value=!1})}),{bindingValue:x,show:P,contextmenu:K}}});n("350a"),L.render=E,L.__scopeId="data-v-f9312e22";var R=L,$=Object(g.withScopeId)("data-v-3470b981");Object(g.pushScopeId)("data-v-3470b981");var W={key:0,class:"v-contextmenu-divider"};Object(g.popScopeId)();var D=$(function(P,K,x,F,U,Y){return Object(g.openBlock)(),Object(g.createBlock)("div",null,[Object(g.createVNode)("div",{class:["v-contextmenu-item",P.itemClass],onClick:K[1]||(K[1]=function(){return P.handleClick&&P.handleClick.apply(P,arguments)})},[Object(g.renderSlot)(P.$slots,"default")],2),P.divider?(Object(g.openBlock)(),Object(g.createBlock)("div",W)):Object(g.createCommentVNode)("",!0)])}),te=Object(g.defineComponent)({name:"ContextMenuItem",props:{disabled:Boolean,divider:{type:Boolean,default:!1}},setup:function(P,K){var x=K.emit,F=Object(g.reactive)({value:{}});function U(){x("itemClickHandle",F.value),l.emit("item-click")}Object(g.onMounted)(function(){l.on("bindValue",function(ae){F.value=ae})});var Y=Object(g.reactive)({"v-contextmenu-item--disabled":Object(g.computed)(function(){return P.disabled})});return{itemClass:Y,handleClick:U}}});n("f53c"),te.render=D,te.__scopeId="data-v-3470b981";var he=te,pe=Object(g.withScopeId)("data-v-589b6ec6");Object(g.pushScopeId)("data-v-589b6ec6");var z={class:"v-contextmenu-submenu-label"},ce=Object(g.createVNode)("span",{class:"v-contextmenu-submenu-right"},null,-1),de={class:"v-contextmenu-submenu-children"},Ie={key:0,class:"v-contextmenu-divider"};Object(g.popScopeId)();var Oe=pe(function(P,K,x,F,U,Y){return Object(g.openBlock)(),Object(g.createBlock)("div",null,[Object(g.createVNode)("div",{class:["v-contextmenu-submenu",P.hover?" v-contextmenu-submenu--hover":""],onMouseenter:K[1]||(K[1]=function(){return P.mouseEnterEvent&&P.mouseEnterEvent.apply(P,arguments)}),onMouseleave:K[2]||(K[2]=function(){return P.mouseLeaveEvent&&P.mouseLeaveEvent.apply(P,arguments)})},[Object(g.createVNode)("div",z,[Object(g.createVNode)("span",null,[Object(g.renderSlot)(P.$slots,"label",{},function(){return[Object(g.createTextVNode)(Object(g.toDisplayString)(P.label),1)]})]),ce]),Object(g.withDirectives)(Object(g.createVNode)("div",de,[Object(g.renderSlot)(P.$slots,"default")],512),[[g.vShow,P.hover]])],34),P.divider?(Object(g.openBlock)(),Object(g.createBlock)("div",Ie)):Object(g.createCommentVNode)("",!0)])}),V=Object(g.defineComponent)({name:"ContextMenuSubmenu",props:{label:String,divider:{type:Boolean,default:!1}},setup:function(){var P=Object(g.ref)(!1);function K(){P.value=!0}function x(){P.value=!1}return{hover:P,mouseEnterEvent:K,mouseLeaveEvent:x}}});n("3d3b"),V.render=Oe,V.__scopeId="data-v-589b6ec6";var N=V,X=function(P){P.provide("emitContext",h),P.provide("hideContext",b),P.directive("contextmenu",p),P.component(R.name,R),P.component(he.name,he),P.component(N.name,N)},oe={install:X};r.default=oe},fc6a:function(t,r,n){var o=n("44ad"),i=n("1d80");t.exports=function(a){return o(i(a))}},fdbc:function(t,r){t.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},fdbf:function(t,r,n){var o=n("4930");t.exports=o&&!Symbol.sham&&typeof Symbol.iterator=="symbol"},fea9:function(t,r,n){var o=n("da84");t.exports=o.Promise}})})(sf);var ay=sf.exports;const sy=uf(ay);function cy(e,t,r,n){function o(i){return i instanceof r?i:new r(function(a){a(i)})}return new(r||(r=Promise))(function(i,a){function s(u){try{l(n.next(u))}catch(f){a(f)}}function c(u){try{l(n.throw(u))}catch(f){a(f)}}function l(u){u.done?i(u.value):o(u.value).then(s,c)}l((n=n.apply(e,[])).next())})}function ly(e,t){var r={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},n,o,i,a;return a={next:s(0),throw:s(1),return:s(2)},typeof Symbol=="function"&&(a[Symbol.iterator]=function(){return this}),a;function s(l){return function(u){return c([l,u])}}function c(l){if(n)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(r=0)),r;)try{if(n=1,o&&(i=l[0]&2?o.return:l[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,l[1])).done)return i;switch(o=0,i&&(l=[l[0]&2,i.value]),l[0]){case 0:case 1:i=l;break;case 4:return r.label++,{value:l[1],done:!1};case 5:r.label++,o=l[1],l=[0];continue;case 7:l=r.ops.pop(),r.trys.pop();continue;default:if(i=r.trys,!(i=i.length>0&&i[i.length-1])&&(l[0]===6||l[0]===2)){r=0;continue}if(l[0]===3&&(!i||l[1]>i[0]&&l[1]<i[3])){r.label=l[1];break}if(l[0]===6&&r.label<i[1]){r.label=i[1],i=l;break}if(i&&r.label<i[2]){r.label=i[2],r.ops.push(l);break}i[2]&&r.ops.pop(),r.trys.pop();continue}l=t.call(e,r)}catch(u){l=[6,u],o=0}finally{n=i=0}if(l[0]&5)throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}}var uy=function(e,t){return cy(void 0,void 0,void 0,function(){return ly(this,function(r){return[2,new Promise(function(n,o){try{if(window.DocsAPI)return n(null);var i=document.getElementById(t);if(i)var a=setInterval(function(){var c=i.getAttribute("loading");if(!c){if(clearInterval(a),window.DocsAPI)return n(null);var l=cf(t,e,n,o);i.remove(),document.body.appendChild(l)}},500);else{var s=cf(t,e,n,o);document.body.appendChild(s)}}catch{}})]})})},cf=function(e,t,r,n){var o=document.createElement("script");return o.id=e,o.type="text/javascript",o.src=t,o.async=!0,o.onload=function(){o.removeAttribute("loading"),r(null)},o.onerror=function(i){o.removeAttribute("loading"),n(i)},o.setAttribute("loading",""),o},Aa=Ct({name:"DocumentEditor",props:{id:{type:String,required:!0},documentServerUrl:{type:String,required:!0},config:{type:Object,required:!0},document_fileType:String,document_title:String,documentType:String,editorConfig_lang:String,height:String,type:String,width:String,onLoadComponentError:Function,events_onAppReady:Function,events_onDocumentStateChange:Function,events_onMetaChange:Function,events_onDocumentReady:Function,events_onInfo:Function,events_onWarning:Function,events_onError:Function,events_onRequestSharingSettings:Function,events_onRequestRename:Function,events_onMakeActionLink:Function,events_onRequestInsertImage:Function,events_onRequestSaveAs:Function,events_onRequestMailMergeRecipients:Function,events_onRequestCompareFile:Function,events_onRequestEditRights:Function,events_onRequestHistory:Function,events_onRequestHistoryClose:Function,events_onRequestHistoryData:Function,events_onRequestRestore:Function,events_onRequestSelectSpreadsheet:Function,events_onRequestSelectDocument:Function},mounted:function(){var e=this,t=this.documentServerUrl;t.endsWith("/")||(t+="/");var r="".concat(t,"web-apps/apps/api/documents/api.js");uy(r,"onlyoffice-api-script").then(function(){return e.onLoad()}).catch(function(){e.onError(-2)})},unmounted:function(){var e,t=this.id||"";!((e=window==null?void 0:window.DocEditor)===null||e===void 0)&&e.instances[t]&&(window.DocEditor.instances[t].destroyEditor(),window.DocEditor.instances[t]=void 0)},watch:{config:{handler:function(e,t){this.onChangeProps()},deep:!0},document_fileType:function(e,t){this.onChangeProps()},document_title:function(e,t){this.onChangeProps()},documentType:function(e,t){this.onChangeProps()},editorConfig_lang:function(e,t){this.onChangeProps()},height:function(e,t){this.onChangeProps()},type:function(e,t){this.onChangeProps()},width:function(e,t){this.onChangeProps()}},methods:{onLoad:function(){var e,t;try{var r=this.id||"";if(window.DocsAPI||this.onError(-3),!((e=window==null?void 0:window.DocEditor)===null||e===void 0)&&e.instances[r])return;!((t=window==null?void 0:window.DocEditor)===null||t===void 0)&&t.instances||(window.DocEditor={instances:{}});var n=Object.assign({document:{fileType:this.document_fileType,title:this.document_title},documentType:this.documentType,editorConfig:{lang:this.editorConfig_lang},events:{onAppReady:this.onAppReady,onDocumentStateChange:this.events_onDocumentStateChange,onMetaChange:this.events_onMetaChange,onDocumentReady:this.events_onDocumentReady,onInfo:this.events_onInfo,onWarning:this.events_onWarning,onError:this.events_onError,onRequestSharingSettings:this.events_onRequestSharingSettings,onRequestRename:this.events_onRequestRename,onMakeActionLink:this.events_onMakeActionLink,onRequestInsertImage:this.events_onRequestInsertImage,onRequestSaveAs:this.events_onRequestSaveAs,onRequestMailMergeRecipients:this.events_onRequestMailMergeRecipients,onRequestCompareFile:this.events_onRequestCompareFile,onRequestEditRights:this.events_onRequestEditRights,onRequestHistory:this.events_onRequestHistory,onRequestHistoryClose:this.events_onRequestHistoryClose,onRequestHistoryData:this.events_onRequestHistoryData,onRequestRestore:this.events_onRequestRestore,onRequestSelectSpreadsheet:this.events_onRequestSelectSpreadsheet,onRequestSelectDocument:this.events_onRequestSelectDocument},height:this.height,type:this.type,width:this.width},this.config||{}),o=window.DocsAPI.DocEditor(r,n);window.DocEditor.instances[r]=o}catch{this.onError(-1)}},onError:function(e){var t;switch(e){case-2:t="Error load DocsAPI from "+this.documentServerUrl;break;case-3:t="DocsAPI is not defined";break;default:t="Unknown error loading component",e=-1}typeof this.onLoadComponentError>"u"||this.onLoadComponentError(e,t)},onAppReady:function(){var e=this.id||"";this.events_onAppReady(window.DocEditor.instances[e])},onChangeProps:function(){var e,t=this.id||"";!((e=window==null?void 0:window.DocEditor)===null||e===void 0)&&e.instances[t]&&(window.DocEditor.instances[t].destroyEditor(),window.DocEditor.instances[t]=void 0,this.onLoad())}}});const fy=["id"];function py(e,t,r,n,o,i){return $t(),ur("div",{id:e.id},null,8,fy)}Aa.render=py,Aa.__file="src/components/DocumentEditor.vue";export{kl as $,it as A,$e as B,Ge as C,Ii as D,An as E,We as F,fc as G,Ki as H,hc as I,bo as J,io as K,Js as L,Cn as M,Mv as N,$s as O,Bc as P,Oo as Q,ks as R,Re as S,ia as T,Lo as U,ms as V,Pn as W,_n as X,vs as Y,rr as Z,xn as _,so as a,vc as a0,Fl as a1,ki as a2,mt as a3,Ut as a4,Vc as a5,Li as a6,lc as a7,Ml as a8,uc as a9,Ms as aA,Nv as aB,Ou as aC,gt as aD,xg as aE,nc as aF,Og as aG,Xm as aH,Nu as aI,Aa as aJ,ml as aK,$g as aL,Mg as aM,Cg as aN,Kg as aO,ny as aP,Zg as aQ,iy as aR,sy as aS,ty as aT,rc as aU,rf as aV,Yg as aW,ey as aX,$v as aa,Ts as ab,qi as ac,Se as ad,Ao as ae,jo as af,pr as ag,Vs as ah,po as ai,Wv as aj,yr as ak,dc as al,Kr as am,Ba as an,qv as ao,Hs as ap,Sn as aq,Fo as ar,Gv as as,Wl as at,Qv as au,Br as av,Fv as aw,wc as ax,Mt as ay,Fs as az,qr as b,dt as c,kt as d,Ne as e,Uv as f,Nr as g,hu as h,Hv as i,Xv as j,Yv as k,Ct as l,$t as m,an as n,ni as o,ur as p,wo as q,Be as r,Yr as s,Vv as t,_t as u,Jc as v,ot as w,$o as x,Lt as y,or as z};
