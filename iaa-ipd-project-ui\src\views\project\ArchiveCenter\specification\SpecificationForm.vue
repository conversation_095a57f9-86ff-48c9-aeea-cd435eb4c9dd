<template>
  <AmisRenderer :formid="formData?.templateContent?.id" :formjson="formData?.templateContent" />
  <Dialog title="选择审批人" v-model="approvalVisible">
    <el-form
      :model="startUserSelectAssignees"
      :rules="startUserSelectAssigneesFormRules"
      ref="startUserSelectAssigneesFormRef"
      label-position="top"
    >
      <el-form-item
        v-for="userTask in startUserSelectTasks"
        :key="userTask.id"
        :label="`任务【${userTask.name}】`"
        :prop="userTask.id"
      >
        <el-select
          v-model="startUserSelectAssignees[userTask.id]"
          multiple
          placeholder="请选择审批人"
          filterable
        >
          <el-option
            v-for="user in userList.filter((item) => item.id != 1)"
            :key="user.id"
            :label="user.nickname"
            :value="user.id"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button type="primary" @click="onSubmitForm(true)">提交审批</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { SpecificationFlowApi } from '@/api/bpm/specification'
import { SpecificationApi } from '@/api/project/specification'
import { cloneDeep } from 'lodash-es'
import download from '@/utils/download'
import * as DefinitionApi from '@/api/bpm/definition'
import { UserVO, getSimpleUserList } from '@/api/system/user'

const formData = ref({
  id: undefined,
  code: undefined,
  name: undefined,
  templateId: undefined,
  templateName: undefined,
  templateCode: undefined,
  templateContent: undefined as any,
  templateIsLatest: undefined,
  version: undefined,
  data: undefined,
  status: undefined
})

const message = useMessage()
const formType = ref<'create' | 'update'>('create')
const edit = ref(false)
const history = ref(false)
const amisStore = ref<any>(undefined)
const startUserSelectTasks = ref<any[]>([])
const startUserSelectAssignees = ref<any>({})
const startUserSelectAssigneesFormRules = ref<any>({})
const userList = ref<UserVO[]>([])
const startUserSelectAssigneesFormRef = ref()

const approvalVisible = ref(false)

const { query } = useRoute()
// 添加一个响应式标志变量
const isFormOpened = ref(false)
const openForm = async (template: any, isHistory: boolean, row?: any) => {
  isFormOpened.value = true
  history.value = isHistory
  startUserSelectTasks.value = []
  startUserSelectAssignees.value = {}
  if (!row) {
    formType.value = 'create'
    const tempTemplate = cloneDeep(template)
    formData.value.templateId = tempTemplate.id
    formData.value.templateName = tempTemplate.name
    formData.value.templateCode = tempTemplate.templateCode
    formData.value.templateContent = buildForm(tempTemplate.content)
    formData.value.templateIsLatest = tempTemplate.isLatest
  } else {
    formType.value = 'update'
    formData.value = cloneDeep(row)
    formData.value.templateContent = buildForm(formData.value.templateContent, formData.value.data)
  }
}

onMounted(async () => {
  await nextTick()
  if (!isFormOpened.value && query?.id) {
    const res = await SpecificationFlowApi.getSpecification(query.id as string)
    openForm({}, true, res)
  }
})

const updateFormTemplate = async () => {
  const res = await SpecificationApi.getLastTemplateByCode(formData.value.templateCode!)
  formData.value.templateId = res.id
  formData.value.templateContent = buildForm(res.content, formData.value.data)
  formData.value.templateIsLatest = res.isLatest
}

const emits = defineEmits(['success', 'cancel'])

const buildForm = (form: string, data?: any) => {
  const jsonForm = JSON.parse(form)
  if (data) {
    jsonForm['data'] = JSON.parse(data)
  }
  jsonForm['data']['isEditing'] = formType.value === 'create'
  const specificationForm = jsonForm.body.find((json) => json.type == 'form')
  if (!specificationForm) {
    message.error('表单校验失败,请联系管理员处理')
    return {}
  }
  specificationForm['disabledOn'] = '${!isEditing}'
  if (history.value) {
    jsonForm['data']['isEditing'] = false
    specificationForm['actions'] = []
    specificationForm['static'] = true
    return jsonForm
  }
  specificationForm['actions'] = [
    {
      type: 'action',
      label: '修改',
      level: 'warning',
      onClick: async (e, props) => {
        if (!formData.value.templateIsLatest) {
          await ElMessageBox.confirm('检测到当前模板不是最新版本，即将更新模板', {
            showCancelButton: false,
            confirmButtonText: '确定'
          })
          await updateFormTemplate()
          message.success('模板更新完成，请重新修改')
          return
        }
        edit.value = true
        props.store.updateData({ isEditing: true })
      },
      visibleOn: '${!isEditing}'
    },
    {
      type: 'action',
      label: '取消',
      level: 'default',
      visibleOn: '${isEditing}', // 仅在编辑状态下显示
      onClick: (e, props) => {
        props.store.updateData({ isEditing: false })
        emits('cancel')
        edit.value = false
      }
    },
    {
      type: 'submit',
      label: '保存',
      level: 'primary',
      visibleOn: '${isEditing}', // 仅在编辑状态下显示
      onClick: async (e, props) => {
        await onSubmit(props, false)
        return false
      }
    },
    {
      type: 'submit',
      label: '保存并提交审签',
      level: 'primary',
      visibleOn: '${isEditing}', // 仅在编辑状态下显示
      onClick: async (e, props) => {
        await onSubmit(props, true)
        return false
      }
    }
  ]
  return jsonForm
}
/** 提交表单 */
const onSubmit = async (props: any, approval: boolean) => {
  formData.value.data = JSON.stringify(props.data) as any
  if (formType.value === 'create') {
    const { value } = await ElMessageBox.prompt('请输入规格书名称', '规格书', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
      inputErrorMessage: '规格书名称不能为空'
    })
    formData.value.name = value as any
  }
  amisStore.value = props.store
  if (!approval) {
    onSubmitForm(false)
  } else {
    onOpenApprovalForm()
  }
}
/** 不提交审签保存数据 */
const onSubmitForm = async (approval: boolean) => {
  await startUserSelectAssigneesFormRef.value?.validate()
  approvalVisible.value = false
  const res = await SpecificationFlowApi.saveSpecification({
    ...formData.value,
    submitApproval: approval,
    startUserMap: startUserSelectAssignees.value
  })
  const data = await SpecificationApi.getSpecification(res)
  openForm({}, false, data)
  message.success('保存成功')
  emits('success', data)
  amisStore.value.updateData({ isEditing: false })
  edit.value = false
}

/** 提交审签数据，先唤出用户选择 */
const onOpenApprovalForm = async () => {
  const processDefinitionDetail = await DefinitionApi.getProcessDefinition(
    undefined,
    'specification_approval'
  )
  if (!processDefinitionDetail) {
    message.error('规格书审批流程未配置，请联系管理员')
    return
  }
  startUserSelectTasks.value = processDefinitionDetail?.startUserSelectTasks
  startUserSelectAssignees.value = {}
  // 设置指定审批人
  if (startUserSelectTasks.value?.length > 0) {
    // 设置校验规则
    for (const userTask of startUserSelectTasks.value) {
      startUserSelectAssignees.value[userTask.id] = []
      startUserSelectAssigneesFormRules.value[userTask.id] = [
        { required: true, message: '请选择审批人', trigger: 'blur' }
      ]
    }
    // 加载用户列表
    userList.value = await getSimpleUserList()
    approvalVisible.value = true
  } else {
    onSubmitForm(true)
  }
}

/** 导出word文档 */
const onExport = async () => {
  try {
    if (edit.value) {
      message.error('编辑中，不允许导出')
      return
    }
    if (formData.value.status != 3) {
      message.error('审批未通过，不允许导出')
      return
    }
    if (!formData.value.id) return
    // 导出的二次确认
    await message.exportConfirm()
    const res = await SpecificationApi.exportSpecification(formData.value.id)
    download.word(res, '规格书_' + formData.value.name + '.docx')
  } catch {}
}
defineExpose({
  openForm,
  onExport
})
</script>

<style lang="scss" scoped>
:deep(.cxd-Panel-footerWrap .cxd-Panel-btnToolbar) {
  text-align: center !important;
  padding: 10px !important;
}
:deep(.cxd-Panel-body) {
  height: calc(100vh - 165px) !important;
  overflow: auto;
}

:deep(.is-disabled.cxd-Form-control) {
  input::placeholder,
  textarea::placeholder {
    -webkit-text-fill-color: rgba(255, 255, 255, 0);
  }
  input,
  textarea {
    -webkit-text-fill-color: #000;
  }
  .cxd-TextControl-input,
  .cxd-TextareaControl-input {
    background-color: #fff !important;
  }
}
</style>
