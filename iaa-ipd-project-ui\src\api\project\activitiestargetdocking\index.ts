import request from '@/config/axios'

export const DockingApi = {
  /** 批量创建集成对象 */
  createBatch: (data: any) => {
    return request.post({ url: '/project/activities-docking/create-batch', data })
  },
  /** 获取项目活动集成对象列表 */
  getDockingList: (activitiesId: number) => {
    return request.get({
      url: '/project/activities-docking/list',
      params: {
        activitiesId
      }
    })
  },
  /** 删除集成对象 */
  delDocking: (id: number) => {
    return request.get({ url: '/project/activities-docking/delete', params: { id } })
  }
}
