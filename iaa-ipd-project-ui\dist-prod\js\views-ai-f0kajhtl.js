import{E as n,O as P,d as w}from"./views-Home-ewBLhuw2.js";import{ac as j,e as M}from"./element-plus-DgaixBsQ.js";import{l as S,r as c,X as z,d as N,m as y,p as v,S as f,u,a8 as U,q as h,F as _,a7 as b}from"./vue-vendor-BbSoq9WN.js";import"./views-Error-Cx8xxY17.js";import"./vendor-DLCNhz7G.js";import"./utils-vendor-Vtb-rlR8.js";import"./views-project-C9zQgjvz.js";import"./echarts-D356XqSJ.js";var r=(e,s,t)=>new Promise((g,i)=>{var m=a=>{try{l(t.next(a))}catch(o){i(o)}},p=a=>{try{l(t.throw(a))}catch(o){i(o)}},l=a=>a.done?g(a.value):Promise.resolve(a.value).then(m,p);l((t=t.apply(e,s)).next())});const V={getImagePageMy:e=>r(void 0,null,function*(){return yield n.get({url:"/ai/image/my-page",params:e})}),getImageMy:e=>r(void 0,null,function*(){return yield n.get({url:`/ai/image/get-my?id=${e}`})}),getImageListMyByIds:e=>r(void 0,null,function*(){return yield n.get({url:"/ai/image/my-list-by-ids",params:{ids:e.join(",")}})}),drawImage:e=>r(void 0,null,function*(){return yield n.post({url:"/ai/image/draw",data:e})}),deleteImageMy:e=>r(void 0,null,function*(){return yield n.delete({url:`/ai/image/delete-my?id=${e}`})}),midjourneyImagine:e=>r(void 0,null,function*(){return yield n.post({url:"/ai/image/midjourney/imagine",data:e})}),midjourneyAction:e=>r(void 0,null,function*(){return yield n.post({url:"/ai/image/midjourney/action",data:e})}),getImagePage:e=>r(void 0,null,function*(){return yield n.get({url:"/ai/image/page",params:e})}),updateImage:e=>r(void 0,null,function*(){return yield n.put({url:"/ai/image/update",data:e})}),deleteImage:e=>r(void 0,null,function*(){return yield n.delete({url:"/ai/image/delete?id="+e})})};var I=(e,s,t)=>new Promise((g,i)=>{var m=a=>{try{l(t.next(a))}catch(o){i(o)}},p=a=>{try{l(t.throw(a))}catch(o){i(o)}},l=a=>a.done?g(a.value):Promise.resolve(a.value).then(m,p);l((t=t.apply(e,s)).next())});const q={class:"square-container"},$={class:"gallery"},k=["src"],B=S({__name:"index",setup(e){const s=c(!0),t=c([]),g=c(0),i=z({pageNo:1,pageSize:10,publicStatus:!0,prompt:void 0}),m=()=>I(this,null,function*(){s.value=!0;try{const l=yield V.getImagePageMy(i);t.value=l.list,g.value=l.total}finally{s.value=!1}}),p=()=>{i.pageNo=1,m()};return N(()=>I(this,null,function*(){yield m()})),(l,a)=>{const o=M,x=P;return y(),v("div",q,[f(o,{modelValue:u(i).prompt,"onUpdate:modelValue":a[0]||(a[0]=d=>u(i).prompt=d),style:{width:"100%","margin-bottom":"20px"},size:"large",placeholder:"\u8BF7\u8F93\u5165\u8981\u641C\u7D22\u7684\u5185\u5BB9","suffix-icon":u(j),onKeyup:U(p,["enter"])},null,8,["modelValue","suffix-icon"]),h("div",$,[(y(!0),v(_,null,b(u(t),d=>(y(),v("div",{key:d.id,class:"gallery-item"},[h("img",{src:d.picUrl,class:"img"},null,8,k)]))),128))]),f(x,{total:u(g),page:u(i).pageNo,"onUpdate:page":a[1]||(a[1]=d=>u(i).pageNo=d),limit:u(i).pageSize,"onUpdate:limit":a[2]||(a[2]=d=>u(i).pageSize=d),onPagination:m},null,8,["total","page","limit"])])}}}),X=w(B,[["__scopeId","data-v-7ffec152"]]);export{X as default};
