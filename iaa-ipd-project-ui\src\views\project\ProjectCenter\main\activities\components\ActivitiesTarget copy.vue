<template>
  <template v-if="formData.targetType === 3">
    <el-empty description="无需输出" />
  </template>
  <!-- 附件输出 -->
  <template v-else-if="[0, 4].includes(formData.targetType!)">
    <el-collapse v-model="activeNames" ref="collapseRef" class="custom-collapse">
      <el-collapse-item
        title="输出参考"
        name="1"
        v-if="
          (formData.mold == 0 && formData.targetType == 0) ||
          (formData?.targetTemplateIds || [])?.length > 0
        "
      >
        <div class="flex">
          <div
            class="cursor-pointer w-70px h-120px p-5px hover:bg-#f8f8f8"
            v-for="file in fileTemplateList.filter((item) =>
              formData.targetTemplateIds?.includes(item.id)
            )"
            :key="file.id"
            @click="officeEditorRef.open(file.infraFileId, file.name)"
          >
            <img :src="getImagePath(file.uri, file.hasFolder)" class="w-60px h-60px" />
            <div
              class="line-clamp-2 overflow-hidden [display:-webkit-box] [-webkit-box-orient:vertical] [-webkit-line-clamp:2] h-40px text-.65vw word-break-normal"
            >
              {{ file.name }}
            </div>
          </div>
        </div>
      </el-collapse-item>
      <el-collapse-item title="附件输出" name="2">
        <el-button
          type="primary"
          plain
          class="!w-full mb-10px"
          size="small"
          @click="activitiesQuoteFileRef?.openForm()"
          v-if="
            allowPermission &&
            props.data.status !== 10 &&
            props.data.progress !== 100 &&
            allowTheOutput &&
            (formData?.workHoursType != '99' ? formData?.workHoursAudit : true)
          "
        >
          引用文件
        </el-button>
        <vxe-table
          class="w-100%"
          :header-cell-style="{ padding: '5px', backgroundColor: '#fff' }"
          :cell-style="{ padding: '5px' }"
          :header-cell-config="{ height: 24 }"
          :cell-config="{ height: 24 }"
          show-overflow
          :data="attachmentList"
          align="center"
          border
        >
          <vxe-column title="文件名" field="name" min-width="200" align="left">
            <template #default="{ row }">
              <el-link
                type="primary"
                @click="attachmentPreviewRef?.openForm(row.name, row.processInstanceId)"
              >
                {{ row.name }}
              </el-link>
            </template>
          </vxe-column>
          <vxe-column title="版本" field="currentVersion" width="60" />
          <vxe-column title="绑定参考" field="templateId" width="200">
            <template #default="{ row }">
              <el-select v-model="row.templateId" :disabled="true">
                <el-option
                  v-for="item in props.fileTemplateList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </template>
          </vxe-column>
          <vxe-column title="审签状态" field="approvalStatus" width="90">
            <template #default="{ row }">
              <DictTag type="project_target_approval_status" :value="row.approvalStatus" />
            </template>
          </vxe-column>
          <vxe-column
            title="审核通过时间"
            field="approvalTime"
            :formatter="dateFormatter3"
            width="120"
          />
          <vxe-column title="操作" width="120px" fixed="right" align="center">
            <template #default="{ row }">
              <el-button
                type="primary"
                link
                size="small"
                v-if="![0].includes(row.approvalStatus) && row.creator === getUser.id"
                @click="fileUploadAnewRef?.openForm(row)"
              >
                重传
              </el-button>
              <el-button
                type="primary"
                link
                size="small"
                v-if="row.approvalStatus != 1 && row.creator === getUser.id"
                @click="delAttachment(row)"
              >
                删除
              </el-button>
              <!-- <el-button type="primary" link size="small" @click="downloadAttachment(row)">
                下载
              </el-button> -->
            </template>
          </vxe-column>
        </vxe-table>
      </el-collapse-item>
      <el-collapse-item title="工时确认" name="3" v-if="formData.workHoursType != '99'">
        <div class="flex justify-between mb-5px">
          <span class="text-1rem font-bold">已选工时：{{ formData.workHoursTotal || 0 }}H</span>
          <el-button
            type="primary"
            plain
            size="small"
            v-if="attachmentList?.length === 0 && !formData?.workHoursAudit"
            @click="onAduitWork"
          >
            确认工时
          </el-button>
        </div>
        <vxe-table
          ref="workHoursRef"
          :header-cell-style="{ padding: '5px', backgroundColor: '#fff' }"
          :cell-style="{ padding: '5px' }"
          :header-cell-config="{ height: 24 }"
          :cell-config="{ height: 24 }"
          :row-config="{ keyField: 'id' }"
          :checkbox-config="{
            checkRowKeys: formData.workHoursIds,
            highlight: true,
            visibleMethod: visibleMethod,
            checkMethod: checkMethod
          }"
          show-overflow
          border
          align="center"
          :data="workHoursList"
          size="mini"
          :max-height="500"
          @checkbox-change="onCheckboxChange"
          @checkbox-all="onCheckboxChange"
        >
          <vxe-column type="checkbox" width="60" />
          <vxe-column title="分类" width="150" field="secondaryType">
            <template #default="{ row }">
              <DictTag type="project_testing_secondary_type" :value="row.secondaryType" />
            </template>
          </vxe-column>
          <vxe-column
            title="测试项"
            width="150"
            align="left"
            field="name"
            :filters="nameOptions"
            :filter-render="FilterValue.textFilterRender"
          />
          <vxe-column title="测试要求" min-width="200" align="left" field="demand" />
          <vxe-column title="测试说明" width="200" align="left" field="accountFor" />
          <vxe-column title="工时" width="100" field="workHours" />
        </vxe-table>
      </el-collapse-item>
    </el-collapse>
  </template>

  <template v-else-if="formData.targetType == 1">
    <template v-if="formData.targetDockingId == 0">
      <el-button
        type="primary"
        plain
        class="w-full"
        size="small"
        v-if="
          allowPermission &&
          props.data.status !== 10 &&
          props.data.progress !== 100 &&
          allowTheOutput
        "
        @click="openBomSelectedForm"
      >
        选取BOM
      </el-button>
      <vxe-table
        :data="dockingList"
        :header-cell-config="{ height: 30 }"
        :cell-config="{ height: 30 }"
        :cell-style="{ padding: '0 5px' }"
        :header-cell-style="{ padding: '0 5px' }"
        show-overflow
        border
        stripe
      >
        <vxe-column title="BOM" field="code" />
        <vxe-column title="操作" field="opereation" width="200" align="center">
          <template #default="{ row }">
            <el-button type="primary" link size="small" @click="openBomDetailsForm(row.code)"
              >查看详情</el-button
            >
            <el-button
              type="danger"
              link
              size="small"
              v-if="
                allowPermission &&
                props.data.status !== 10 &&
                props.data.progress !== 100 &&
                allowTheOutput
              "
              @click="deleteDocking(row.id)"
            >
              删除
            </el-button>
          </template>
        </vxe-column>
      </vxe-table>
    </template>
    <template v-else>
      <el-empty description="开发中，暂不支持" />
    </template>
  </template>
  <template v-else-if="formData.targetType == 5">
    <div
      class="flex mb-10px"
      v-if="
        allowPermission && props.data.status !== 10 && props.data.progress !== 100 && allowTheOutput
      "
    >
      <el-button
        type="success"
        plain
        size="small"
        class="!w-33%"
        @click="
          () => {
            specEdit = false
            openSpecSelectedFrom()
          }
        "
      >
        引用已有规格书
      </el-button>
      <el-popover trigger="click" placement="bottom-start" width="300">
        <template #reference>
          <el-button type="primary" plain size="small" class="!w-33%">创建新规格书</el-button>
        </template>
        <el-input v-model="templateName">
          <template #append>
            <el-button :icon="Search" @click="onTemplateList" />
          </template>
        </el-input>
        <div class="max-h-200px w-full border-1px border-solid border-#f1f1f1 rounded mt-5px">
          <div
            class="flex p-5px justify-between border-b-1px border-b-solid border-b-#f1f1f1"
            v-for="(item, index) in specDocTemplateList"
            :key="index"
          >
            <el-button type="primary" link @click="openCreateSpec(item)">
              {{ item.name }}
            </el-button>
          </div>
        </div>
      </el-popover>

      <el-button
        type="warning"
        plain
        size="small"
        class="!w-33%"
        @click="
          () => {
            specEdit = true
            openSpecSelectedFrom()
          }
        "
        >根据已有规格书修改</el-button
      >
    </div>
    <vxe-table
      :data="dockingList"
      :header-cell-config="{ height: 30 }"
      :cell-config="{ height: 30 }"
      :cell-style="{ padding: '0 5px' }"
      :header-cell-style="{ padding: '0 5px' }"
      show-overflow
      border
      stripe
    >
      <vxe-column title="规格书" field="name" />
      <vxe-column title="审批状态" field="approvalStatus" width="200" align="center">
        <template #default="{ row }">
          <DictTag type="specification_status" :value="row.approvalStatus" />
          <el-button
            type="primary"
            link
            v-if="row.approvalStatus != 0"
            @click="toBpm(row.processInstanceId)"
            >跳转流程</el-button
          >
        </template>
      </vxe-column>
      <vxe-column title="操作" field="opereation" width="200" align="center">
        <template #default="{ row }">
          <el-button
            type="warning"
            link
            size="small"
            v-if="
              row.approvalStatus == 0 &&
              allowPermission &&
              props.data.status !== 10 &&
              props.data.progress !== 100 &&
              allowTheOutput
            "
            @click="onPreviewSpec(row, false)"
          >
            修改
          </el-button>
          <el-button type="primary" link size="small" @click="onPreviewSpec(row, true)">
            查看详情
          </el-button>
          <el-button
            type="danger"
            link
            size="small"
            v-if="
              allowPermission &&
              props.data.status !== 10 &&
              props.data.progress !== 100 &&
              allowTheOutput
            "
            @click="deleteDocking(row.id)"
          >
            删除
          </el-button>
        </template>
      </vxe-column>
    </vxe-table>
  </template>
  <template v-else-if="formData.targetType == 6">
    <el-button
      type="primary"
      size="small"
      class="!w-100% mb-10px"
      plain
      @click="openActivitiesSelectdForm"
    >
      选取确认的工业设计活动
    </el-button>
    <vxe-table
      :data="dockingList"
      :header-cell-config="{ height: 30 }"
      :cell-config="{ height: 30 }"
      :cell-style="{ padding: '0 5px' }"
      :header-cell-style="{ padding: '0 5px' }"
      show-overflow
      border
      stripe
    >
      <vxe-column title="活动" field="code" />
      <vxe-column title="操作" field="opereation" width="200" align="center">
        <template #default="{ row }">
          <el-button type="primary" link size="small" @click="openBomDetailsForm(row.code)"
            >查看详情</el-button
          >
          <el-button
            type="danger"
            link
            size="small"
            v-if="
              allowPermission &&
              props.data.status !== 10 &&
              props.data.progress !== 100 &&
              allowTheOutput
            "
            @click="deleteDocking(row.id)"
          >
            删除
          </el-button>
        </template>
      </vxe-column>
    </vxe-table>
  </template>
  <template v-else>
    <el-empty description="开发中，暂不支持" />
  </template>
  <FileUploadAnew
    category="activities"
    :dynamic-id="formData.id!"
    ref="fileUploadAnewRef"
    :file-template-list="
      fileTemplateList.filter((item) => formData.targetTemplateIds?.includes(item.id))
    "
    :target-type="formData.targetType"
    :target-docking-id="formData.targetDockingId"
    @success="onListAttachment"
  />
  <OfficeEditor ref="officeEditorRef" :download="true" />
  <AttachmentPreview ref="attachmentPreviewRef" />
  <ActivitiesQuoteFile
    :basics-id="props.data?.basicsId"
    :file-template-list="props.fileTemplateList || []"
    :target-template-ids="props.data?.targetTemplateIds || []"
    :activities-id="props.data?.id"
    ref="activitiesQuoteFileRef"
    @view="attachmentPreviewRef?.openForm"
    @success="onListAttachment"
  />
  <Dialog title="选择BOM作为输出对象" v-model="bomSelectedVisble">
    <el-input
      v-model="bomQueryName"
      size="small"
      placeholder="输入内容发起BOM查询"
      @keydown.enter="onListBom"
    >
      <template #append>
        <el-button type="primary" :icon="Search" size="small" @click="onListBom" />
      </template>
    </el-input>
    <vxe-table
      class="mt-10px"
      :data="bomList"
      :header-cell-config="{ height: 30 }"
      :cell-config="{ height: 30 }"
      :cell-style="{ padding: '0 5px' }"
      :header-cell-style="{ padding: '0 5px' }"
      height="250px"
      border
      stripe
      show-overflow
    >
      <vxe-column title="BOM" field="key" />
      <vxe-column title="BOM版本" field="value" width="100px" align="center" />
      <vxe-column title="操作" align="center" width="100px">
        <template #default="{ row }">
          <el-button type="primary" link size="small" @click="onBomSelected(row)"> 选择 </el-button>
        </template>
      </vxe-column>
    </vxe-table>
    <el-divider> 已选BOM </el-divider>
    <vxe-table
      :data="bomSelectedList"
      :header-cell-config="{ height: 30 }"
      :cell-config="{ height: 30 }"
      :cell-style="{ padding: '0 5px' }"
      :header-cell-style="{ padding: '0 5px' }"
      height="250px"
      border
      stripe
    >
      <vxe-column title="BOM" field="key" />
      <vxe-column title="BOM版本" field="value" width="100px" align="center" />
      <vxe-column title="操作" align="center" width="100px">
        <template #default="{ row }">
          <el-button type="danger" link size="small" @click="onBomUnselected(row)">
            取消选择
          </el-button>
        </template>
      </vxe-column>
    </vxe-table>
    <template #footer>
      <el-button type="primary" @click="onSubmitBomList" :loading="bomSelectedLoading">
        保存
      </el-button>
    </template>
  </Dialog>
  <Dialog :title="bomTreeTitle" v-model="bomTreeVisible" width="70%">
    <vxe-table
      :data="bomTreeList"
      :header-cell-config="{ height: 30 }"
      :cell-config="{ height: 30 }"
      :cell-style="{ padding: '0 5px' }"
      :header-cell-style="{ padding: '0 5px' }"
      :tree-config="{ rowField: 'id', parentField: 'parentId', transform: true, expandAll: true }"
      border
      show-overflow
      align="center"
    >
      <vxe-column title="序号" type="seq" width="150" tree-node />
      <vxe-column title="品号" field="id" width="120" />
      <vxe-column title="版本" field="partVar" width="80" />
      <vxe-column title="品名" field="partName" align="left" width="250" />
      <vxe-column title="单位" field="unit" width="80" />
      <vxe-column title="规格" field="spec" align="left" />
      <vxe-column title="用量" field="counts" width="80" />
    </vxe-table>
  </Dialog>
  <Dialog title="选择规格书作为输出对象" v-model="specSelectedVisible">
    <el-input
      v-model="specName"
      size="small"
      placeholder="输入内容发起规格书查询"
      @keydown.enter="onListSpec"
    >
      <template #append>
        <el-button type="primary" :icon="Search" size="small" @click="onListSpec" />
      </template>
    </el-input>
    <vxe-table
      class="mt-10px"
      :data="specList"
      :header-cell-config="{ height: 30 }"
      :cell-config="{ height: 30 }"
      :cell-style="{ padding: '0 5px' }"
      :header-cell-style="{ padding: '0 5px' }"
      height="250px"
      border
      stripe
      show-overflow
    >
      <vxe-column title="规格书" field="name" />
      <vxe-column title="状态" field="status" width="100">
        <template #default="{ row }">
          <DictTag type="specification_status" :value="row.status" />
        </template>
      </vxe-column>
      <vxe-column title="版本" field="version" width="100px" align="center">
        <template #default="{ row }">
          {{ `V${row.version}` }}
        </template>
      </vxe-column>
      <vxe-column title="操作" align="center" width="100px">
        <template #default="{ row }">
          <el-button type="primary" link size="small" @click="onSpecSelect(row)" v-if="!specEdit">
            选择
          </el-button>
          <el-button
            type="warning"
            link
            size="small"
            v-if="specEdit"
            @click="onPreviewSpec({ code: row.id }, false)"
            >修改</el-button
          >
        </template>
      </vxe-column>
    </vxe-table>
    <template v-if="!specEdit">
      <el-divider> 已选规格书 </el-divider>
      <vxe-table
        :data="specSelectedList"
        :header-cell-config="{ height: 30 }"
        :cell-config="{ height: 30 }"
        :cell-style="{ padding: '0 5px' }"
        :header-cell-style="{ padding: '0 5px' }"
        height="250px"
        border
        stripe
      >
        <vxe-column title="规格书" field="name" />
        <vxe-column title="状态" field="status" width="100">
          <template #default="{ row }">
            <DictTag type="specification_status" :value="row.status" />
          </template>
        </vxe-column>
        <vxe-column title="版本" field="version" width="100px" align="center">
          <template #default="{ row }">
            {{ `V${row.version}` }}
          </template>
        </vxe-column>
        <vxe-column title="操作" align="center" width="100px">
          <template #default="{ row }">
            <el-button type="danger" link size="small" @click="onSepcUnSelected(row)">
              取消选择
            </el-button>
          </template>
        </vxe-column>
      </vxe-table>
    </template>
    <template #footer v-if="!specEdit">
      <el-button type="primary" @click="onSubmitSpecList" :loading="bomSelectedLoading">
        保存
      </el-button>
    </template>
  </Dialog>

  <Dialog title="规格书查看" v-model="specPreviewVisible" width="80%" class="file-viewer-dialog">
    <template #title>
      <div class="flex justify-between items-center">
        <div class="text-white leading-40px">
          {{ specData?.name || '创建' + specDocTemplate?.name }}
        </div>
        <el-button link v-if="specData?.id" @click="specificationRef?.onExport()" class="ml-50px">
          <Icon icon="ep:download" />
          <span class="text-white">下载为word文件</span>
        </el-button>
      </div>
    </template>
    <SpecificationForm
      ref="specificationRef"
      @success="onSubmitSpec"
      @cancel="specPreviewVisible = false"
    />
  </Dialog>

  <Dialog title="选择确认的活动作为输出对象" v-model="activitiesSelectedVisble">
    <el-input
      v-model="activitiesQueryName"
      size="small"
      placeholder="输入内容发起活动查询"
      @keydown.enter="onListActivities"
    >
      <template #append>
        <el-button type="primary" :icon="Search" size="small" @click="onListActivities" />
      </template>
    </el-input>
    <vxe-table
      class="mt-10px"
      :data="activitiesList"
      :header-cell-config="{ height: 30 }"
      :cell-config="{ height: 30 }"
      :cell-style="{ padding: '0 5px' }"
      :header-cell-style="{ padding: '0 5px' }"
      height="250px"
      border
      stripe
      show-overflow
    >
      <vxe-column title="活动主题" field="name">
        <template #default="{ row }">
          {{ `${row.orderNo} ${row.name}` }}
        </template>
      </vxe-column>
      <vxe-column title="进度" field="progress" width="100px" align="center" />
      <vxe-column title="操作" align="center" width="100px">
        <template #default="{ row }">
          <el-button
            type="primary"
            link
            size="small"
            @click="onActivitiesSelected(row)"
            v-if="row.progress == 100"
          >
            选择
          </el-button>
        </template>
      </vxe-column>
    </vxe-table>
    <el-divider> 已选活动 </el-divider>
    <vxe-table
      :data="activitiesSelectedList"
      :header-cell-config="{ height: 30 }"
      :cell-config="{ height: 30 }"
      :cell-style="{ padding: '0 5px' }"
      :header-cell-style="{ padding: '0 5px' }"
      height="250px"
      border
      stripe
    >
      <vxe-column title="活动主题" field="name">
        <template #default="{ row }">
          {{ `${row.orderNo} ${row.name}` }}
        </template>
      </vxe-column>
      <vxe-column title="进度" field="progress" width="100px" align="center" />
      <vxe-column title="操作" align="center" width="100px">
        <template #default="{ row }">
          <el-button type="danger" link size="small" @click="onActivitiesUnSelected(row)">
            取消选择
          </el-button>
        </template>
      </vxe-column>
    </vxe-table>
    <template #footer>
      <el-button type="primary" @click="onSubmitActivitiesList" :loading="activitiesSubmitLoading">
        保存
      </el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { Search } from '@element-plus/icons-vue'
import { ActivitiesVO, ActivitiesApi } from '@/api/project/activities'
import { DockingApi } from '@/api/project/activitiestargetdocking'
import { BomApi } from '@/api/docking/plm/bom'
import { propTypes } from '@/utils/propTypes'
import { getImagePath } from '@/utils/icon'
import { dateFormatter3 } from '@/utils/formatTime'
import { AttachmentApi, AttachmentRespVO } from '@/api/project/attachment'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { ElMessageBox } from 'element-plus'
import FileUploadAnew from '../../components/FileUploadAnew.vue'
import AttachmentPreview from '../../components/AttachmentPreview.vue'
import { useUserStore } from '@/store/modules/user'
import { downloadByUrl } from '@/utils/filt'
import * as FileApi from '@/api/infra/file'
import ActivitiesQuoteFile from './ActivitiesQuoteFile.vue'
import { TestingTemplateApi } from '@/api/project/testingtemplate'
import * as FilterValue from '@/views/project/ProjectCenter/main/components/Filter'
import { SpecificationApi } from '@/api/project/specification'
import SpecificationForm from '@/views/project/ArchiveCenter/specification/SpecificationForm.vue'

const nameOptions = ref([{ data: '' }])

const props = defineProps({
  data: propTypes.oneOf<ActivitiesVO>([]).isRequired,
  fileTemplateList: propTypes.arrayOf<any>([]).def([]),
  edit: propTypes.bool.def(false)
})

const formData = ref<ActivitiesVO>({})
const officeEditorRef = ref()
const attachmentList = ref<AttachmentRespVO[]>([])
const workHoursList = ref<any[]>([])
// const router = useRouter()
const activeNames = ref(['1', '2', '3'])
const message = useMessage()
const fileUploadAnewRef = ref()
const attachmentPreviewRef = ref()
const { getUser } = useUserStore()
const activitiesQuoteFileRef = ref()
const workHoursRef = ref()
const emits = defineEmits(['audit-work-hours', 'completed'])

const downloadAttachment = async (row: any) => {
  const res = await FileApi.get(row.infraFileId)
  downloadByUrl({ url: res.url, fileName: row.name })
}
/** 获取附件列表 */
const onListAttachment = async () => {
  const res = await AttachmentApi.getAttachmentList({
    category: 'activities',
    dynamicId: formData.value.id
  })
  attachmentList.value = res
}

const onListDocking = async () => {
  const res = await DockingApi.getDockingList(formData.value.id!)
  dockingList.value = res
}

const onAduitWork = async () => {
  await message.confirm('确定工时已确认完成？确认完成将工时不可修改')
  emits('audit-work-hours')
}

const onListWorkHoursTemplate = async () => {
  if (!formData.value.workHoursType) return
  if (formData.value.workHoursType == '99') return
  const res = await TestingTemplateApi.getTestingTemplateList(formData.value.workHoursType!)
  workHoursList.value = res
}

const allowPermission = computed(() => {
  return props.data.director?.includes(getUser.id) || props.data.coordinate?.includes(getUser.id)
})

const allowTheOutput = computed(() => {
  if (!props.data.dependencies || props.data.dependencies.length == 0) {
    return true
  }
  let allow = true
  props.data.dependencies.forEach((item) => {
    if (item.dependencyType == 'fs' && item.progress != 100) {
      allow = false
    }
  })

  return allow
})

const delAttachment = async (row: any) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入删除原因', '删除输出物', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '删除原因不能为空'
  })
  if (row.approvalStatus == 2) {
    await AttachmentApi.delAttachment({
      id: row.id,
      value
    })
  } else {
    await ProcessInstanceApi.cancelProcessInstanceByStartUser(row.processInstanceId, value)
  }
  message.success('删除成功')
  onListAttachment()
}

const onCheckboxChange = async () => {
  const data = workHoursRef.value?.getCheckboxRecords(true)
  formData.value.workHoursIds = data.map((item) => item.id)
  formData.value.workHoursTotal = parseFloat(
    data
      .reduce((acc, curr) => {
        // 确保 curr.workHours 是数字类型，防止 NaN
        return acc + (curr.workHours || 0)
      }, 0)
      .toFixed(2)
  )
  await ActivitiesApi.updateWorkHours({
    id: formData.value.id,
    workHoursIds: formData.value.workHoursIds,
    workHoursTotal: formData.value.workHoursTotal
  })
  message.success('确认成功')
}

const init = () => {
  if ([0, 4].includes(formData.value.targetType!)) {
    onListAttachment()
    onListWorkHoursTemplate()
  } else if (formData.value.targetType == 1) {
    //如果是BOM输出
    if (formData.value.targetDockingId == 0) {
      onListDocking()
    }
  } else if (formData.value.targetType == 5) {
    onListDocking()
    onTemplateList()
  }
}

//#region bom
const bomList = ref<any[]>([])
const bomQueryName = ref('')
const bomSelectedVisble = ref(false)
const bomSelectedList = ref<any[]>([])
const bomSelectedLoading = ref(false)
const dockingList = ref<any[]>([])
const bomTreeList = ref<any[]>([])
const bomTreeVisible = ref(false)
const bomTreeTitle = ref('')
const onListBom = async () => {
  const res = await BomApi.getBomList(bomQueryName.value)
  bomList.value = res
}
const openBomSelectedForm = () => {
  bomSelectedVisble.value = true
  onListBom()
}

const onBomSelected = (row: any) => {
  const exists = bomSelectedList.value.some(
    (item) => item.key === row.key && item.value == row.value
  )
  if (exists) {
    message.alertError('当前BOM已选择')
    return
  }
  bomSelectedList.value.push(row)
}

const onBomUnselected = (row: any) => {
  bomSelectedList.value = bomSelectedList.value.filter(
    (item) => !(item.key === row.key && item.value == row.value)
  )
}

const onSubmitBomList = async () => {
  if (!(bomSelectedList.value && bomSelectedList.value.length > 0)) {
    message.alertError('未选择任何BOM')
    return
  }
  bomSelectedLoading.value = true
  try {
    const data = bomSelectedList.value.map((item) => {
      return {
        activitiesId: formData.value.id,
        targetType: formData.value.targetType,
        targetDockingId: formData.value.targetDockingId,
        code: item.key + item.value,
        name: item.key + item.value
      }
    })
    await DockingApi.createBatch(data)
    bomQueryName.value = ''
    bomSelectedList.value = []
    bomList.value = []
    bomSelectedVisble.value = false
    onListDocking()
    await message.confirm('保存成功，请确认是否发起活动完成流程')
    emits('completed')
  } finally {
    bomSelectedLoading.value = false
  }
}

const openBomDetailsForm = async (bomName: string) => {
  const res = await BomApi.getBomTreeList(bomName)
  bomTreeList.value = res
  bomTreeVisible.value = true
  bomTreeTitle.value = bomName
}

//#endregion

//#region 规格书
const specSelectedVisible = ref(false)
const specList = ref<any[]>([])
const specName = ref('')
const specSelectedList = ref<any[]>([])
const specPreviewVisible = ref(false)
const specData = ref<any>({})
const specificationRef = ref()
const templateName = ref('')
const specDocTemplateList = ref<any[]>([])
const specDocTemplate = ref<any>({})
const specEdit = ref(false)
const onListSpec = async () => {
  const res = await SpecificationApi.listSpecification(specName.value)
  specList.value = res
}

const openSpecSelectedFrom = () => {
  specSelectedVisible.value = true
  onListSpec()
}

const onSpecSelect = (row: any) => {
  const exists = specSelectedList.value.some((item) => item.id === row.id)
  if (exists) {
    message.alertError('当前规格书已选择')
    return
  }
  specSelectedList.value.push(row)
}

const onSepcUnSelected = (row: any) => {
  specSelectedList.value = specSelectedList.value.filter((item) => item.id != row.id)
}

const onSubmitSpecList = async () => {
  if (!(specSelectedList.value && specSelectedList.value.length > 0)) {
    message.alertError('未选择任何BOM')
    return
  }
  bomSelectedLoading.value = true
  try {
    const data = specSelectedList.value.map((item) => {
      return {
        activitiesId: formData.value.id,
        targetType: formData.value.targetType,
        targetDockingId: formData.value.targetDockingId,
        code: item.id,
        name: item.name + '-V' + item.version,
        approvalStatus: item.status,
        processInstanceId: item.processInstanceId
      }
    })
    await DockingApi.createBatch(data)
    specName.value = ''
    specSelectedList.value = []
    specList.value = []
    specSelectedVisible.value = false
    await onListDocking()
    if (!specSelectedList.value.some((item) => item.status != 3)) {
      await message.confirm('保存成功，请确认是否发起活动完成流程')
      emits('completed')
    }
  } finally {
    bomSelectedLoading.value = false
  }
}

const onSubmitSpec = async (row) => {
  bomSelectedLoading.value = true
  try {
    const dockingData = dockingList.value.find((item) => item.code == row.id)
    console.log(dockingData)
    await DockingApi.createBatch([
      {
        id: dockingData?.id,
        activitiesId: formData.value.id,
        targetType: formData.value.targetType,
        targetDockingId: formData.value.targetDockingId,
        code: row.id,
        name: row.name + '-V' + row.version,
        approvalStatus: row.status,
        processInstanceId: row.processInstanceId
      }
    ])
    specName.value = ''
    specSelectedList.value = []
    specList.value = []
    specSelectedVisible.value = false
    specPreviewVisible.value = false
    await onListDocking()
    if (!specSelectedList.value.some((item) => item.status != 3)) {
      await message.confirm('保存成功，请确认是否发起活动完成流程')
      emits('completed')
    }
  } finally {
    bomSelectedLoading.value = false
  }
}

const onPreviewSpec = async (row, edit: boolean) => {
  const res = await SpecificationApi.getSpecification(row.code)
  specData.value = res
  specPreviewVisible.value = true
  await nextTick()
  specificationRef.value?.openForm({}, edit, specData.value)
}

const openCreateSpec = async (template: any) => {
  specPreviewVisible.value = true
  specDocTemplate.value = template
  await nextTick()
  specificationRef.value?.openForm(template, false)
}

const onTemplateList = async () => {
  const res = await SpecificationApi.listDocTemplate(templateName.value)
  specDocTemplateList.value = res
}
//#endregion

//#region 活动
const activitiesList = ref<any[]>([])
const activitiesQueryName = ref('')
const activitiesSelectedVisble = ref(false)
const activitiesSelectedList = ref<any[]>([])
const activitiesSubmitLoading = ref(false)

const onListActivities = async () => {
  const res = await ActivitiesApi.getActivitiesList({
    basicsId: props.data.basicsId,
    name: activitiesQueryName.value
  })
  activitiesList.value = res?.filter((item) => item.id != props.data.id)
}

const openActivitiesSelectdForm = () => {
  onListActivities()
  activitiesSelectedVisble.value = true
}

const onActivitiesSelected = (row) => {
  const exists = activitiesSelectedList.value.some((item) => item.id == row.id)
  if (exists) {
    message.error('已添加当前活动，请勿重复添加')
    return
  }
  activitiesSelectedList.value.push(row)
}

const onActivitiesUnSelected = (row) => {
  activitiesSelectedList.value = activitiesSelectedList.value.filter((item) => item.id != row.id)
}

const onSubmitActivitiesList = async () => {
  if (!activitiesSelectedList.value || activitiesSelectedList.value.length == 0) {
    message.error('未选择活动，无需提交')
    return
  }
  activitiesSubmitLoading.value = true
  try {
    const data = activitiesSelectedList.value.map((item) => {
      return {
        activitiesId: formData.value.id,
        targetType: formData.value.targetType,
        targetDockingId: formData.value.targetDockingId,
        code: item.id,
        name: `${item.orderNo} ${item.name}`,
        approvalStatus: item.status
      }
    })
    await DockingApi.createBatch(data)
    activitiesQueryName.value = ''
    activitiesSelectedList.value = []
    activitiesList.value = []
    activitiesSelectedVisble.value = false
    await onListDocking()
    await message.confirm('保存成功，请确认是否发起活动完成流程')
    emits('completed')
  } finally {
    activitiesSubmitLoading.value = false
  }
}
//#endregion

const deleteDocking = async (id: number) => {
  await message.delConfirm()
  await DockingApi.delDocking(id)
  onListDocking()
}
const router = useRouter()
const toBpm = (id: string) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: id
    }
  })
}

defineExpose({
  init
})

/** 选中方法 */
const visibleMethod = ({ row }: { row: any }) => {
  if (
    formData.value.status !== 10 &&
    formData.value.progress !== 100 &&
    !formData.value?.workHoursAudit
  ) {
    return true
  }
  return formData.value.workHoursIds?.includes(row.id)
}

const checkMethod = ({ row }) => {
  if (
    formData.value.status !== 10 &&
    formData.value.progress !== 100 &&
    (formData.value.director?.includes(getUser.id) ||
      formData.value.coordinate?.includes(getUser.id)) &&
    !formData.value?.workHoursAudit
  ) {
    return true
  } else {
    false
  }
}

watch(
  () => props.data,
  () => {
    formData.value = props.data
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
:deep(.el-collapse-item__header) {
  background-color: #fafafa !important;
  border: 0.3px dashed var(--el-color-info-light-5) !important;
  border-left: 5px solid var(--el-color-primary) !important;
  font-size: 1rem;
  height: 1.8rem;
}
:deep(.vxe-cell) {
  padding: 0px !important;
}
</style>
