import request from '@/config/axios'

export const ProcessProblemFlowApi = {
  /** 删除问题 */
  deleteProblem: async (data: any) => {
    return await request.post({ url: '/bpm/process-problem/delete', data })
  },
  /** 查询问题 */
  getProblem: async (processInstanceId: string) => {
    return await request.get({ url: `/bpm/process-problem/get/${processInstanceId}` })
  },
  /** 完成问题 */
  completeProblem: async (data: any) => {
    return await request.post({ url: '/bpm/process-problem/complete', data })
  }
}
