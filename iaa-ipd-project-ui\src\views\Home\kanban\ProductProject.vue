<template>
  <div class="p-l-10px h-full overflow-auto" ref="containerRef">
    <el-card shadow="never">
      <template #header>
        <vxe-toolbar size="mini" ref="toolbarRef" custom export />
      </template>

      <!-- 列表 -->
      <div class="h-[calc(100vh-150px)]">
        <div class="h-[calc(100%-50px)]">
          <vxe-table
            height="100%"
            ref="tableRef"
            :data="list"
            border
            stripe
            show-overflow
            :header-cell-config="{ height: 30 }"
            :column-config="{ resizable: true, isHover: true }"
            :cell-config="{ height: 36 }"
            :row-config="{ isHover: true, isCurrent: true }"
            :loading="loading"
            align="center"
            @filter-change="handleFilterChange"
            :filter-config="{ remote: true }"
            :edit-config="{ trigger: 'click', mode: 'row', showStatus: true }"
            :keep-source="true"
            :export-config="{ remote: true, message: false, exportMethod: onExport }"
          >
            <vxe-column
              title="项目"
              field="basicsName"
              :filters="FilterValue.basicsName"
              :filter-render="FilterTemplate.textFilterRender"
            >
              <template #default="{ row }">
                <ToProjectCenter :name="row.basicsName" :basics-id="row.basicsId" />
              </template>
            </vxe-column>
            <vxe-column
              title="活动"
              field="activityName"
              :filters="FilterValue.activityName"
              :filter-render="FilterTemplate.textFilterRender"
            >
              <template #default="{ row }">
                <ToProjectCenter
                  :name="row.activityName"
                  :basics-id="row.basicsId"
                  :activities-id="row.activityId"
                />
              </template>
            </vxe-column>
            <vxe-column title="负责人" field="diretor">
              <template #default="{ row }">
                {{ getUserNickName(userList, row.director) }}
              </template>
            </vxe-column>
            <vxe-column title="时间" field="date">
              <template #default="{ row }">
                {{ `${row.startDate} - ${row.endDate}` }}
              </template>
            </vxe-column>
            <vxe-column title="发生次数" field="occurrenceNumber">
              <template #default="{ row }">
                {{ `第${row.occurrenceNumber}次` }}
              </template>
            </vxe-column>
            <vxe-column title="入库确认" field="productArchiving">
              <template #default="{ row }">
                <dict-tag type="infra_boolean_string" :value="row.productArchiving" />
              </template>
            </vxe-column>
            <vxe-column title="销售确认" field="salesConfirm">
              <template #default="{ row }">
                <dict-tag type="infra_boolean_string" :value="row.salesConfirm" />
              </template>
            </vxe-column>
          </vxe-table>
        </div>
        <Pagination
          v-model:limit="queryParams.pageSize"
          v-model:page="queryParams.pageNo"
          :total="total"
          size="small"
          @pagination="onList"
        />
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { KanbanApi } from '@/api/project/kanban'
import ToProjectCenter from './ToProjectCenter.vue'
import * as FilterTemplate from '@/views/project/ProjectCenter/main/components/Filter'
import download from '@/utils/download'
import { UserVO, getSimpleUserList } from '@/api/system/user'
import { getUserNickName } from '@/views/project/ProjectCenter/details/components/utils'

const FilterValue = {
  basicsName: [{ data: '' }],
  activityName: [{ data: '' }]
}

const queryParams = ref({
  pageNo: 1,
  pageSize: 30
})

const list = ref<any[]>([])
const total = ref(0)
const loading = ref(false)
const toolbarRef = ref()
const tableRef = ref()
const message = useMessage()
const userList = ref<UserVO[]>([])

const onList = async () => {
  loading.value = true
  try {
    const res = await KanbanApi.getProductProjectList(queryParams.value)
    list.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const handleFilterChange = (params: any) => {
  // 特定字段列表
  const specialFields = ['']

  // 初始化 filters 对象
  const filters = {}

  params.filterList.forEach((item: any) => {
    const { field, values, datas } = item

    if (specialFields.includes(field) && values.length > 0) {
      // 特定字段且 values 有值
      filters[field] = values
    } else if (datas.length > 0) {
      // 其他字段且 datas 有值
      filters[field] = datas[0]
    }
  })

  // 清空不在 params.filterList 中的字段
  Object.keys(queryParams.value).forEach((key) => {
    if (
      !['pageNo', 'pageSize'].includes(key) &&
      !params.filterList.some((item: any) => item.field === key)
    ) {
      queryParams.value[key] = undefined
    }
  })
  // 更新 queryParams
  Object.assign(queryParams.value, filters)

  // 调用后端接口获取数据
  onList()
}

const onExport = async ({ options }) => {
  await message.exportConfirm()
  const columns = options.columns.map((item) => item.field)
  if (columns.length === 0) {
    message.warning('未选择需要导出的列')
    return
  }
  if (columns.includes('date')) {
    columns.splice(columns.indexOf('date'), 1, 'startDate', 'endDate')
  }
  const data = await KanbanApi.exportProductProject({
    ...queryParams.value,
    columns
  })
  download.excel(data, `${options.filename}.xlsx`)
}

const onListUser = async () => {
  userList.value = await getSimpleUserList()
}

onMounted(() => {
  onList()
  onListUser()
  nextTick(() => {
    unref(tableRef)?.connect(unref(toolbarRef))
  })
})
</script>

<style lang="scss" scoped>
:deep(.el-card__header) {
  padding: 0 5px !important;
}
</style>
