<template>
  <el-tabs
    v-model="currentTab"
    class="position-sticky top-0 z-10 bg-#fff"
    @tab-change="onTableChange"
  >
    <el-tab-pane label="基础信息" name="info" />
    <el-tab-pane label="输出物" name="target" />
  </el-tabs>
  <template v-if="currentTab === 'info'">
    <el-form label-width="100px" class="custom-form" size="small" ref="formRef">
      <el-form-item label="所属项目">
        <el-input v-model="formData.basicsName" :disabled="true" />
      </el-form-item>
      <el-row>
        <el-col :span="8">
          <el-form-item label="等级">
            <el-input v-model="formData.basicsLevel" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="类型">
            <el-input v-model="formData.basicsMold" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="平台">
            <el-input v-model="formData.basicsPlatform" :disabled="true" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="活动名称">
        <el-input v-model="formData.name" :disabled="true" />
      </el-form-item>
      <el-form-item label="活动内容">
        <el-input v-model="formData.content" type="textarea" :rows="3" :disabled="true" />
      </el-form-item>
      <el-form-item label="活动描述">
        <el-input v-model="formData.description" type="textarea" :rows="6" :disabled="true" />
      </el-form-item>
      <el-form-item label="负责人" prop="director">
        <user-avatar-list
          v-model="formData.director!"
          :user-list="userList"
          :size="28"
          :limit="3"
          :add="false"
        />
      </el-form-item>
      <el-form-item label="执行人">
        <user-avatar-list
          v-model="formData.coordinate!"
          :user-list="userList"
          :size="28"
          :limit="3"
          :add="false"
        />
      </el-form-item>
      <el-row>
        <el-col :span="12">
          <el-form-item label="开始时间" prop="startDate">
            <el-date-picker
              v-model="formData.startDate"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束时间" prop="endDate">
            <el-date-picker
              v-model="formData.endDate"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="进度">
        <el-progress
          :percentage="formData.progress"
          class="w-100% no-radius"
          :text-inside="true"
          :stroke-width="20"
          status="success"
        />
      </el-form-item>
      <el-form-item label="状态">
        <DictTag type="project_activities_status" :value="formData.status!" />
      </el-form-item>
    </el-form>
    <Comment
      ref="commentRef"
      category="activities"
      :limit="5"
      bgColor="#fff"
      :disabled="false"
      :user-list="[]"
    />
  </template>
  <template v-else>
    <!--附件输出-->
    <template v-if="[0, 4].includes(activitiesData.targetType)">
      <el-collapse v-model="activeNames" ref="collapseRef" class="custom-collapse">
        <el-collapse-item
          title="输出参考"
          name="1"
          v-if="formData.mold == 0 && formData.targetType == 0"
        >
          <div class="flex">
            <div
              class="cursor-pointer w-70px h-120px p-5px hover:bg-#f8f8f8"
              v-for="file in fileTemplateList.filter((item) =>
                formData.targetTemplateIds?.includes(item.id)
              )"
              :key="file.id"
              @click="officeEditorRef.open(file.infraFileId, file.name)"
            >
              <img :src="getImagePath(file.uri, file.hasFolder)" class="w-60px h-60px" />
              <div
                class="line-clamp-2 overflow-hidden [display:-webkit-box] [-webkit-box-orient:vertical] [-webkit-line-clamp:2] h-40px text-.65vw word-break-normal"
              >
                {{ file.name }}
              </div>
            </div>
          </div>
        </el-collapse-item>
        <el-collapse-item title="附件输出" name="2">
          <vxe-table
            class="w-100%"
            :header-cell-style="{ padding: '5px', backgroundColor: '#fff' }"
            :cell-style="{ padding: '5px', height: '30px' }"
            show-overflow
            :data="attachmentList"
            align="center"
            border
          >
            <vxe-column title="文件名" field="name" min-width="200" align="left">
              <template #default="{ row }">
                <el-link
                  type="primary"
                  @click="attachmentPreviewRef?.openForm(row.name, row.processInstanceId)"
                >
                  {{ row.name }}
                </el-link>
              </template>
            </vxe-column>
            <vxe-column title="版本" field="currentVersion" width="60" />
            <vxe-column title="绑定参考" field="templateId" width="200">
              <template #default="{ row }">
                <el-select v-model="row.templateId" :disabled="true">
                  <el-option
                    v-for="item in fileTemplateList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </template>
            </vxe-column>
            <vxe-column title="审签状态" field="approvalStatus" width="90">
              <template #default="{ row }">
                <DictTag type="project_target_approval_status" :value="row.approvalStatus" />
              </template>
            </vxe-column>
            <vxe-column
              title="审核通过时间"
              field="approvalTime"
              :formatter="dateFormatter3"
              width="120"
            />
          </vxe-table>
        </el-collapse-item>
        <el-collapse-item title="工时确认" name="3" v-if="formData.workHoursType != '99'">
          <span class="text-1rem font-bold">已选工时：{{ formData.workHoursTotal || 0 }}H</span>
          <vxe-table
            ref="workHoursRef"
            :header-cell-style="{ padding: '5px', backgroundColor: '#fff' }"
            :cell-style="{ padding: '5px' }"
            :header-cell-config="{ height: 24 }"
            :cell-config="{ height: 24 }"
            :row-config="{ keyField: 'id' }"
            :checkbox-config="{
              checkRowKeys: formData.workHoursIds,
              highlight: true,
              visibleMethod: visibleMethod,
              checkMethod: checkMethod
            }"
            show-overflow
            border
            align="center"
            :data="workHoursList"
            size="mini"
            :max-height="500"
          >
            <vxe-column type="checkbox" width="60" />
            <vxe-column title="分类" width="150" field="secondaryType">
              <template #default="{ row }">
                <DictTag type="project_testing_secondary_type" :value="row.secondaryType" />
              </template>
            </vxe-column>
            <vxe-column
              title="测试项"
              width="150"
              align="left"
              field="name"
              :filters="nameOptions"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column title="测试要求" min-width="200" align="left" field="demand" />
            <vxe-column title="测试说明" width="200" align="left" field="accountFor" />
            <vxe-column title="工时" width="100" field="workHours" />
          </vxe-table>
        </el-collapse-item>
      </el-collapse>
    </template>
    <!--PLM集成-->
    <template v-else-if="activitiesData.targetType == 1">
      <template v-if="activitiesData.targetDockingId == 0">
        <vxe-table
          :data="dockingList"
          :header-cell-config="{ height: 30 }"
          :cell-config="{ height: 30 }"
          :cell-style="{ padding: '0 5px' }"
          :header-cell-style="{ padding: '0 5px' }"
          show-overflow
          border
          stripe
        >
          <vxe-column title="BOM" field="code" />
          <vxe-column title="操作" field="opereation" width="200" align="center">
            <template #default="{ row }">
              <el-button type="primary" link size="small" @click="openBomDetailsForm(row.code)">
                查看详情
              </el-button>
            </template>
          </vxe-column>
        </vxe-table>
      </template>
      <template v-else>
        <el-empty description="开发中，暂不支持" />
      </template>
    </template>
    <!--规格书集成-->
    <template v-else-if="activitiesData.targetType == 5">
      <vxe-table
        :data="dockingList"
        :header-cell-config="{ height: 30 }"
        :cell-config="{ height: 30 }"
        :cell-style="{ padding: '0 5px' }"
        :header-cell-style="{ padding: '0 5px' }"
        show-overflow
        border
        stripe
      >
        <vxe-column title="规格书" field="name" />
        <vxe-column title="审批状态" field="approvalStatus" width="200" align="center">
          <template #default="{ row }">
            <DictTag type="specification_status" :value="row.approvalStatus" />
            <el-button
              type="primary"
              link
              v-if="row.approvalStatus != 0"
              @click="toBpm(row.processInstanceId)"
              >跳转流程</el-button
            >
          </template>
        </vxe-column>
        <vxe-column title="操作" field="opereation" width="200" align="center">
          <template #default="{ row }">
            <el-button type="primary" link size="small" @click="onPreviewSpec(row, true)">
              查看详情
            </el-button>
          </template>
        </vxe-column>
      </vxe-table>
    </template>
    <!--产品部项目确认集成-->
    <template v-else-if="activitiesData.targetType == 6">
      <vxe-table
        :data="dockingList"
        :header-cell-config="{ height: 30 }"
        :cell-config="{ height: 30 }"
        :cell-style="{ padding: '0 5px' }"
        :header-cell-style="{ padding: '0 5px' }"
        show-overflow
        border
        stripe
      >
        <vxe-column title="活动" field="name" />
        <vxe-column title="操作" field="opereation" width="200" align="center">
          <template #default="{ row }">
            <el-button
              type="primary"
              link
              size="small"
              @click="openActivitiesDetailsForm(row.code)"
            >
              查看详情
            </el-button>
          </template>
        </vxe-column>
      </vxe-table>
    </template>

    <OfficeEditor ref="officeEditorRef" :download="true" />
    <AttachmentPreview ref="attachmentPreviewRef" />
    <Dialog :title="bomTreeTitle" v-model="bomTreeVisible" width="70%">
      <vxe-table
        :data="bomTreeList"
        :header-cell-config="{ height: 30 }"
        :cell-config="{ height: 30 }"
        :cell-style="{ padding: '0 5px' }"
        :header-cell-style="{ padding: '0 5px' }"
        :tree-config="{ rowField: 'id', parentField: 'parentId', transform: true, expandAll: true }"
        border
        show-overflow
        align="center"
      >
        <vxe-column title="序号" type="seq" width="150" tree-node />
        <vxe-column title="品号" field="id" width="120" />
        <vxe-column title="版本" field="partVar" width="80" />
        <vxe-column title="品名" field="partName" align="left" width="250" />
        <vxe-column title="单位" field="unit" width="80" />
        <vxe-column title="规格" field="spec" align="left" />
        <vxe-column title="用量" field="counts" width="80" />
      </vxe-table>
    </Dialog>
    <Dialog title="规格书查看" v-model="specPreviewVisible" width="80%" class="file-viewer-dialog">
      <template #title>
        <div class="flex justify-between items-center">
          <div class="text-white leading-40px">
            {{ specData?.name }}
          </div>
          <el-button link v-if="specData?.id" @click="specificationRef?.onExport()" class="ml-50px">
            <Icon icon="ep:download" />
            <span class="text-white">下载为word文件</span>
          </el-button>
        </div>
      </template>
      <SpecificationForm ref="specificationRef" @cancel="specPreviewVisible = false" />
    </Dialog>
  </template>
</template>
<script lang="ts" setup>
import { getSimpleUserList, UserVO } from '@/api/system/user'
import { ActivitiesFlowApi } from '@/api/bpm/activities'
import { ActivitiesApi } from '@/api/project/activities'
import { DockingApi } from '@/api/project/activitiestargetdocking'
import { BomApi } from '@/api/docking/plm/bom'
import { propTypes } from '@/utils/propTypes'
import { getImagePath } from '@/utils/icon'
import { AttachmentApi, AttachmentRespVO } from '@/api/project/attachment'
import { dateFormatter3 } from '@/utils/formatTime'
import { FileTemplateApi } from '@/api/project/file/template'
import AttachmentPreview from '../../components/AttachmentPreview.vue'
import * as FilterValue from '@/views/project/ProjectCenter/main/components/Filter'
import { TestingTemplateApi } from '@/api/project/testingtemplate'
import { SpecificationApi } from '@/api/project/specification'
import SpecificationForm from '@/views/project/ArchiveCenter/specification/SpecificationForm.vue'
import { BasicsApi } from '@/api/project/basics'
import { useCache } from '@/hooks/web/useCache'
import { ElMessage } from 'element-plus'

const nameOptions = ref([{ data: '' }])

const commentRef = ref()
const currentTab = ref<string>('info')
const formData = ref<any>({})
const activitiesData = ref<any>({})
const userList = ref<UserVO[]>([])
const workHoursList = ref<any[]>([])
const activeNames = ref(['1', '2', '3'])
const fileTemplateList = ref<any[]>([])
const officeEditorRef = ref()
const attachmentList = ref<AttachmentRespVO[]>([])
const attachmentPreviewRef = ref()

const dockingList = ref<any[]>([])
const bomTreeList = ref<any[]>([])
const bomTreeVisible = ref(false)
const bomTreeTitle = ref('')

const specPreviewVisible = ref(false)
const specData = ref<any>({})
const specificationRef = ref()

const props = defineProps({
  processInstanceId: propTypes.string.def('')
})
/** 获取用户列表 */
const getUserList = async () => {
  const data = await getSimpleUserList()
  userList.value = data
}
/** 页签变化 */
const onTableChange = async () => {
  await nextTick()
  if (currentTab.value === 'target') {
    if ([0, 4].includes(activitiesData.value.targetType)) {
      onListAttachment()
      onListWorkHoursTemplate()
    } else if (activitiesData.value.targetType == 1) {
      if (activitiesData.value.targetDockingId == 0) {
        onListDocking()
      }
    } else if ([5, 6].includes(activitiesData.value.targetType)) {
      onListDocking()
    }
  }
}

const router = useRouter()
const toBpm = (id: string) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: id
    }
  })
}

const onPreviewSpec = async (row, edit: boolean) => {
  const res = await SpecificationApi.getSpecification(row.code)
  specData.value = res
  specPreviewVisible.value = true
  await nextTick()
  specificationRef.value?.openForm({}, edit, specData.value)
}

const { wsCache } = useCache()

const openActivitiesDetailsForm = async (code: string) => {
  const activity = await ActivitiesApi.getActivities(Number(code))
  if (!activity) {
    ElMessage.error('活动不存在')
    return
  }
  const basics = await BasicsApi.getBasics(activity.basicsId)
  wsCache.set('project_page_show_form', {
    categoryId: basics.categoryIds?.[0],
    basicsId: basics.id,
    page: 'activities',
    stage: activity.stage,
    id: activity.id,
    status: basics.status
  })
  router.push({
    name: 'ProjectCenter'
  })
}

watch(
  () => props.processInstanceId,
  async () => {
    if (!props.processInstanceId) return
    formData.value = await ActivitiesFlowApi.getActivities(props.processInstanceId)
    getActivities(formData.value.activitiesId)
  },
  { immediate: true }
)

const getActivities = async (activitiesId: number) => {
  const res = await ActivitiesApi.getActivities(activitiesId)
  activitiesData.value = res
  onListComment()
}

const onListComment = async () => {
  commentRef.value?.listEvent(activitiesData.value.id)
}
/** 获取附件列表 */
const onListAttachment = async () => {
  const res = await AttachmentApi.getAttachmentList({
    category: 'activities',
    dynamicId: activitiesData.value.id
  })
  attachmentList.value = res
}

const onListDocking = async () => {
  const res = await DockingApi.getDockingList(activitiesData.value.id!)
  dockingList.value = res
}

const onListWorkHoursTemplate = async () => {
  if (!formData.value.workHoursType) return
  if (formData.value.workHoursType == '99') return
  const res = await TestingTemplateApi.getTestingTemplateList(formData.value.workHoursType!)
  workHoursList.value = res
}

const listFileTemplate = async () => {
  const res = await FileTemplateApi.getFileTemplatePage({})
  fileTemplateList.value = res
}

const openBomDetailsForm = async (bomName: string) => {
  const res = await BomApi.getBomTreeList(bomName)
  bomTreeList.value = res
  bomTreeVisible.value = true
  bomTreeTitle.value = bomName
}

/** 选中方法 */
const visibleMethod = ({ row }: { row: any }) => {
  return formData.value.workHoursIds?.includes(row.id)
}

const checkMethod = ({ row }) => {
  if (formData.value.status !== 10 && formData.value.progress !== 100) {
    return false
  } else {
    true
  }
}
onMounted(() => {
  getUserList()
  listFileTemplate()
})
</script>
