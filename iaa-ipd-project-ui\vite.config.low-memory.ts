import { resolve } from 'path'
import { loadEnv } from 'vite'
import type { UserConfig, ConfigEnv } from 'vite'
import { createVitePlugins } from './build/vite'

// 当前执行node命令时文件夹的地址(工作目录)
const root = process.cwd()

// 路径查找
function pathResolve(dir: string) {
  return resolve(root, '.', dir)
}

// 低内存构建配置
export default ({ command, mode }: ConfigEnv): UserConfig => {
  let env = {} as any
  const isBuild = command === 'build'
  if (!isBuild) {
    env = loadEnv((process.argv[3] === '--mode' ? process.argv[4] : process.argv[3]), root)
  } else {
    env = loadEnv(mode, root)
  }
  
  return {
    base: env.VITE_BASE_PATH,
    root: root,
    server: {
      port: env.VITE_PORT,
      host: "0.0.0.0",
      open: env.VITE_OPEN === 'true',
    },
    plugins: createVitePlugins(),
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@import "./src/styles/variables.scss";',
          javascriptEnabled: true
        }
      }
    },
    resolve: {
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.scss', '.css'],
      alias: [
        {
          find: 'vue-i18n',
          replacement: 'vue-i18n/dist/vue-i18n.cjs.js'
        },
        {
          find: /\@\//,
          replacement: `${pathResolve('src')}/`
        }
      ]
    },
    build: {
      minify: 'esbuild', // 使用更快的 esbuild
      outDir: env.VITE_OUT_DIR || 'dist',
      sourcemap: false, // 禁用 sourcemap
      reportCompressedSize: false, // 禁用压缩大小报告
      chunkSizeWarningLimit: 1000, // 降低chunk大小警告限制
      rollupOptions: {
        maxParallelFileOps: 1, // 限制并行文件操作为1
        output: {
          // 简化的代码分割策略
          manualChunks: {
            'vue-vendor': ['vue', 'vue-router', 'pinia'],
            'element-vendor': ['element-plus'],
            'utils-vendor': ['lodash-es', 'axios', 'dayjs'],
            'echarts-vendor': ['echarts']
          },
          chunkFileNames: 'js/[name]-[hash].js',
          entryFileNames: 'js/[name]-[hash].js',
          assetFileNames: 'assets/[name]-[hash].[ext]'
        },
      },
    },
    optimizeDeps: {
      // 最小化预构建依赖
      include: [
        'vue',
        'vue-router',
        'pinia',
        'element-plus',
        'axios',
        'lodash-es'
      ],
      exclude: [
        '@iconify/json'
        // 保留所有实际使用的依赖以确保功能正常
      ],
      esbuildOptions: {
        target: 'es2015',
        logLevel: 'error'
      }
    },
    esbuild: {
      drop: ['console', 'debugger'],
      target: 'es2015',
      legalComments: 'none' // 移除法律注释以减少文件大小
    }
  }
}
