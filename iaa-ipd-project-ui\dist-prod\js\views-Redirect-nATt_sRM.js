import{l,aE as y,u as m,m as u,p as f}from"./vue-vendor-BbSoq9WN.js";import"./utils-vendor-Vtb-rlR8.js";import"./vendor-DLCNhz7G.js";import"./element-plus-DgaixBsQ.js";var b=Object.defineProperty,d=Object.defineProperties,O=Object.getOwnPropertyDescriptors,i=Object.getOwnPropertySymbols,P=Object.prototype.hasOwnProperty,h=Object.prototype.propertyIsEnumerable,n=(t,e,r)=>e in t?b(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,j=(t,e)=>{for(var r in e||(e={}))P.call(e,r)&&n(t,r,e[r]);if(i)for(var r of i(e))h.call(e,r)&&n(t,r,e[r]);return t},_=(t,e)=>d(t,O(e));const v=l(_(j({},{name:"Redirect"}),{__name:"Redirect",setup(t){const{currentRoute:e,replace:r}=y(),{params:a,query:c}=m(e),{path:o,_redirect_type:s="path"}=a;Reflect.deleteProperty(a,"_redirect_type"),Reflect.deleteProperty(a,"path");const p=Array.isArray(o)?o.join("/"):o;return r(s==="name"?{name:p,query:c,params:a}:{path:p.startsWith("/")?p:"/"+p,query:c}),(R,w)=>(u(),f("div"))}}));export{v as default};
