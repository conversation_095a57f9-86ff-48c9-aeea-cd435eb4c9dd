<template>
  <NoModalDrawer title="字典数据" v-model="visible" size="50%">
    <div class="h-[calc(100vh-120px)] mt-10px">
      <vxe-table
        :data="list"
        height="100%"
        show-overflow
        align="center"
        :tree-config="{ transform: true, rowField: 'id', parentField: 'parentId', showLine: true }"
        :header-cell-config="{ height: 30 }"
        :cell-config="{ height: 30 }"
        border
      >
        <vxe-column title="数据编号" field="id" tree-node width="150" />
        <vxe-column title="字典文本" field="label" />
        <vxe-column title="字典值" field="value" />
        <vxe-column title="排序" field="sort" />
        <vxe-column title="操作" field="operation" width="150">
          <template #default="{ row }">
            <el-button
              type="primary"
              link
              size="small"
              @click="openAddForm(row)"
              v-if="!row.parentId"
              >新增</el-button
            >
            <el-button type="warning" link size="small" @click="openEditForm(row)">修改</el-button>
            <el-button type="danger" link size="small" @click="onDelete(row.id)">删除</el-button>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <template #footer>
      <el-button type="primary" plain @click="openAddForm()">新增</el-button>
    </template>
  </NoModalDrawer>
  <Dialog title="新增字典数据" v-model="addDataVisible">
    <el-form label-width="120" :model="formData" :rules="formRules">
      <el-form-item label="父项">
        <el-select v-model="formData.parentId" placeholder="请选择系统菜单">
          <el-option
            v-for="parent in treeList"
            :key="parent.id"
            :value="parent.id"
            :label="parent.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="字典文本" prop="label">
        <el-input v-model="formData.label" />
      </el-form-item>
      <el-form-item label="字典值" prop="value">
        <el-input v-model="formData.value" :disabled="formData.id" />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number :min="0" v-model="formData.sort" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="onSubmit()" :loading="addLoading">保存</el-button>
      <el-button type="primary" @click="onSubmit(true)" :loading="addLoading">
        保存并继续添加
      </el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { SecondDictApi } from '@/api/project/seconddict'
import { handleTree } from '@/utils/tree'
import { cloneDeep } from 'lodash-es'

const visible = ref(false)
const list = ref<any[]>([])
const treeList = ref<any[]>([])
const loading = ref(false)
const dictTypeId = ref<number | undefined>(undefined)

const addDataVisible = ref(false)
const formData = ref({
  id: undefined,
  dictTypeId: undefined,
  parentId: undefined,
  label: undefined,
  value: undefined,
  sort: undefined
})
const formRules = reactive({
  label: [{ required: true, message: '字典文本不能为空', trigger: 'blur' }],
  value: [{ required: true, message: '字典值不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '字典排序不能为空', trigger: 'blur' }]
})
const addLoading = ref(false)
const message = useMessage()

function openAddForm(row?: any) {
  refresh()
  if (row) {
    formData.value.parentId = row.id
  }
  addDataVisible.value = true
}

function openEditForm(row?: any) {
  formData.value = row
  addDataVisible.value = true
}

async function onDelete(id: number) {
  await message.delConfirm()
  await SecondDictApi.deleteDictData(id)
  message.success('删除成功')
  onList(dictTypeId.value as any)
}

async function onSubmit(again: boolean = false) {
  addLoading.value = true
  try {
    formData.value.dictTypeId = dictTypeId.value as any
    await SecondDictApi.saveDictData(formData.value)
    message.success('保存成功')
    onList(dictTypeId.value as any)
    refresh()

    if (!again) {
      addDataVisible.value = false
    }
  } finally {
    addLoading.value = false
  }
}

function refresh() {
  formData.value = {
    id: undefined,
    dictTypeId: undefined,
    parentId: undefined,
    label: undefined,
    value: undefined,
    sort: undefined
  }
}

async function onList(id: number) {
  loading.value = true
  try {
    const res = await SecondDictApi.listDictDataByDictTypeId(id)
    list.value = res
    treeList.value = handleTree(cloneDeep(res))
  } finally {
    loading.value = false
  }
}
const openForm = (id: number) => {
  onList(id)
  formData.value.dictTypeId = id as any
  dictTypeId.value = id
  visible.value = true
}

defineExpose({
  openForm
})
</script>
