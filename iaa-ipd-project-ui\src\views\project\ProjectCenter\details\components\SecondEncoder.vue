<template>
  <Dialog
    :title="props.showEncoder ? '生成项目编码' : '项目编码列表'"
    v-model="numberVisible"
    class="file-viewer-dialog"
    width="800px"
  >
    <el-tabs v-model="currentTab" @tab-change="onTabChange" v-if="showEncoder">
      <el-tab-pane label="生成编码" name="generate" />
      <el-tab-pane label="编码列表" name="history" />
    </el-tabs>
    <el-form label-width="120" v-if="currentTab === 'generate'">
      <el-form-item v-for="item in segmentList" :key="item.id" :label="item.name">
        <div v-if="item.segment === 'placeholder'" class="placeholder">{{ item.value }}</div>
        <template v-else-if="item.segment === 'dict'">
          <template v-if="item.isMultiple">
            <el-checkbox-group v-model="numberData[item.id!]" :max="item.serialLength">
              <el-checkbox
                v-for="dict in numberDictData[item.id]"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-checkbox-group>
          </template>
          <TreeDictSelect v-else v-model="numberData[item.id!]" :data="numberDictData[item.id]" />
        </template>
        <el-input v-model="numberData[item.id!]" v-else-if="item.segment === 'custom'" />
        <div v-else-if="item.segment === 'serial'" class="placeholder">以系统生成为准</div>
      </el-form-item>
    </el-form>
    <template v-else>
      <el-form inline size="small">
        <el-form-item label="编码">
          <el-input v-model="encoderParams.code" />
        </el-form-item>
        <el-form-item label="详情">
          <el-input v-model="encoderParams.remark" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain @click="handleList">查询</el-button>
        </el-form-item>
      </el-form>
      <vxe-table
        :data="encoderList"
        :header-cell-config="{ height: 30 }"
        :cell-config="{ height: 30 }"
        show-overflow
        align="center"
        :height="400"
      >
        <vxe-column title="编码" field="code" />
        <vxe-column title="详情" field="remark" />
        <vxe-column title="状态" field="status" width="80">
          <template #default="{ row }">
            <DictTag type="second_encoder_status" :value="row.status" />
          </template>
        </vxe-column>
        <vxe-column title="绑定项目" field="basicsName" />
        <vxe-column title="使用人" field="creatorName" width="80" />
        <vxe-column title="操作" field="operation" width="100">
          <template #default="{ row }">
            <template v-if="row.status < 2">
              <el-button type="primary" link @click="onCheck(row)"> 使用 </el-button>
              <el-button
                type="warning"
                link
                v-if="row.status == 1 && row.creator == getUser.id"
                @click="onDelete(row)"
              >
                释放
              </el-button>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
      <!-- 分页 -->
      <Pagination
        size="small"
        :total="encoderTotal"
        v-model:page="encoderParams.pageNo"
        v-model:limit="encoderParams.pageSize"
        @pagination="onListEncoder"
      />
    </template>
    <template #footer v-if="currentTab === 'generate'">
      <el-button type="primary" @click="onEncoderCode">生成编码</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { SecondEncoderApi } from '@/api/project/secondencoder'
import { SecondDictApi } from '@/api/project/seconddict'
import TreeDictSelect from './TreeDictSelect.vue'
import { cloneDeep } from 'lodash-es'
import { propTypes } from '@/utils/propTypes'
import { useUserStore } from '@/store/modules/user'

const props = defineProps({
  ruleId: propTypes.number.isRequired,
  showEncoder: propTypes.bool.def(true)
})

const numberVisible = ref(false)
const segmentList = ref<any[]>([])
const numberData = ref<any>({})
const numberDictData = ref<any>({})
const message = useMessage()
const emits = defineEmits(['success'])
const currentTab = ref('generate')

const encoderList = ref<any[]>([])
const encoderTotal = ref(0)
const encoderParams = ref({
  pageSize: 20,
  pageNo: 1,
  ruleId: props.ruleId,
  code: undefined,
  remark: undefined
})

const onListEncoder = async () => {
  const res = await SecondEncoderApi.getEncoderPage(encoderParams.value)
  encoderList.value = res.list
  encoderTotal.value = res.total
}
const handleList = () => {
  encoderParams.value.pageNo = 1
  onListEncoder()
}

const onTabChange = async () => {
  if (currentTab.value == 'history') {
    handleList()
  } else {
    onListSegment()
  }
}

const onListSegment = async () => {
  numberData.value = {}
  const res = await SecondEncoderApi.getEncoderRuleSegmentList(props.ruleId)
  segmentList.value = res
  for (let numberId of res) {
    if (numberId.segment != 'dict') continue
    numberDictData.value[numberId.id] = await SecondDictApi.listDictDataByTypeCode(numberId.value)
  }
}

const onEncoderCode = async () => {
  numberData.value['ruleId'] = props.ruleId
  const data = cloneDeep(numberData.value)
  for (const key in data) {
    if (data[key] instanceof Array) {
      data[key] = JSON.stringify(data[key])
    }
  }
  const res = await SecondEncoderApi.generateEncoder(data)
  emits('success', res)
  message.success('成功')
  numberData.value = {}
  numberVisible.value = false
}
const { getUser } = useUserStore()
const onCheck = (row: any) => {
  if (row.creator != getUser.id) {
    message.success('当前编码已被' + row.creatorName + '锁定，请联系使用人释放')
    return
  }
  emits('success', row.code)
  numberVisible.value = false
}

const onDelete = async (row: any) => {
  if (row.basicsId) {
    message.success('当前编码已被项目使用，不允许释放')
    return
  }
  await message.delConfirm()
  await SecondEncoderApi.deleteEncoder(row.id)
  message.success('释放成功')
  handleList()
}

const openForm = () => {
  if (!props.showEncoder) {
    currentTab.value = 'history'
  }
  onTabChange()
  numberVisible.value = true
}

defineExpose({
  openForm
})
</script>

<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding-top: 5px !important;
}
</style>
