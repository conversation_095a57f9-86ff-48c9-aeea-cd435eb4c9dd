import{z as Rd,E as Nt,F as xa,G as Vt,p as Nd,H as Yd,I as Ud,J as jr,K as st,i as Or,s as Dr,e as ka,d as qd,b as vt,c as $d,L as Ml,M as Tl,a as Vl,h as zl,N as Bd,n as Na,m as Ya,o as Fd,B as Ma,O as Rr,y as sa,P as Kd,Q as Wd,R as ca,S as Gd,T as Zd,U as Jd,V as Nr,W as Ll,r as El,q as Yr,k as Ur,X as Xd}from"./element-plus-DgaixBsQ.js";import{l as Ie,r as p,e as Ye,w as Yt,m as h,J as N,K as i,M as ke,u as r,p as j,q as x,_ as Ua,n as Ue,aF as Qd,aG as qr,aH as es,aI as ts,I as as,a9 as $r,H as ls,a0 as rs,G as ua,P as se,S as a,Q as T,R as w,a as is,L as Pe,T as Br,d as lt,B as ze,F as ce,a7 as ve,az as qa,aA as $a,aE as Pl,ap as Sl,ah as Me,a8 as zt,ak as Ut,X as Ta,a2 as os,aJ as ns}from"./vue-vendor-BbSoq9WN.js";import{p as Le,u as Lt,C as Ee,_ as Ae,a as Zt,d as Fr,s as Il,__tla as ds}from"./views-Error-Cx8xxY17.js";import{bC as Kr,bD as Wr,bE as Al,bB as Hl,bF as ge,bG as jl,bH as ss,bI as cs}from"./vendor-DLCNhz7G.js";import{m as us,F as ps,G as Ol,e as Dl}from"./utils-vendor-Vtb-rlR8.js";import{s as fs,f as Gr,g as Rl,a as Nl,u as Zr,t as De,n as Ba}from"./views-project-C9zQgjvz.js";import{i as Jt}from"./echarts-D356XqSJ.js";let Jr,Yl,Xr,Qr,e0,v,Va,t0,a0,pa,_t,Et,l0,Ul,Xt,fa,r0,i0,o0,Fa,Ka,Qt,ql,$l,Bl,za,ea,qt,n0,d0,s0,Fl,c0,u0,p0,f0,m0,h0,v0,y0,g0,b0,_0,w0,ma,Pt,C0,$t,x0,k0,M0,T0,V0,z0,L0,Kl,E0,P0,Wl,S0,La,I0,A0,Wa,H0,Re,Gl,St,j0,qe,Ga,O0,D0,ta,R0,Za,Ea,N0,Zl,Ja,Y0,U0,q0,Jl,$0,B0,F0,K0,W0,G0,Z0,J0,ms=Promise.all([(()=>{try{return ds}catch{}})()]).then(async()=>{const Xl=Kr.default||Kr;JSON.parse('[{"prefix":"el","width":1200,"height":1200,"icons":{"edit":{"body":"<path fill=\\"currentColor\\" d=\\"M0 0v1200h1200V424.292l-196.875 196.875v381.958h-806.25v-806.25h381.958L775.708 0zm1050 0l-76.831 76.831l150 150L1200 150zM936.914 113.086L497.168 552.832l150 150l439.746-439.746zM441.943 622.339c-2.225.034-4.493.195-6.738.366v142.09h142.09c0-38.708-18.492-78.039-47.314-105.542c-23.842-22.751-54.675-37.428-88.038-36.914\\"/>"}}},{"prefix":"ep","width":1024,"height":1024,"icons":{"arrow-down":{"body":"<path fill=\\"currentColor\\" d=\\"M831.872 340.864L512 652.672L192.128 340.864a30.59 30.59 0 0 0-42.752 0a29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728a30.59 30.59 0 0 0-42.752 0z\\"/>"},"arrow-left":{"body":"<path fill=\\"currentColor\\" d=\\"M609.408 149.376L277.76 489.6a32 32 0 0 0 0 44.672l331.648 340.352a29.12 29.12 0 0 0 41.728 0a30.59 30.59 0 0 0 0-42.752L339.264 511.936l311.872-319.872a30.59 30.59 0 0 0 0-42.688a29.12 29.12 0 0 0-41.728 0\\"/>"},"arrow-right":{"body":"<path fill=\\"currentColor\\" d=\\"M340.864 149.312a30.59 30.59 0 0 0 0 42.752L652.736 512L340.864 831.872a30.59 30.59 0 0 0 0 42.752a29.12 29.12 0 0 0 41.728 0L714.24 534.336a32 32 0 0 0 0-44.672L382.592 149.376a29.12 29.12 0 0 0-41.728 0z\\"/>"},"arrow-up":{"body":"<path fill=\\"currentColor\\" d=\\"m488.832 344.32l-339.84 356.672a32 32 0 0 0 0 44.16l.384.384a29.44 29.44 0 0 0 42.688 0l320-335.872l319.872 335.872a29.44 29.44 0 0 0 42.688 0l.384-.384a32 32 0 0 0 0-44.16L535.168 344.32a32 32 0 0 0-46.336 0\\"/>"},"avatar":{"body":"<path fill=\\"currentColor\\" d=\\"M628.736 528.896A416 416 0 0 1 928 928H96a415.87 415.87 0 0 1 299.264-399.104L512 704zM720 304a208 208 0 1 1-416 0a208 208 0 0 1 416 0\\"/>"},"back":{"body":"<path fill=\\"currentColor\\" d=\\"M224 480h640a32 32 0 1 1 0 64H224a32 32 0 0 1 0-64\\"/><path fill=\\"currentColor\\" d=\\"m237.248 512l265.408 265.344a32 32 0 0 1-45.312 45.312l-288-288a32 32 0 0 1 0-45.312l288-288a32 32 0 1 1 45.312 45.312z\\"/>"},"basketball":{"body":"<path fill=\\"currentColor\\" d=\\"M778.752 788.224a382.46 382.46 0 0 0 116.032-245.632a256.51 256.51 0 0 0-241.728-13.952a762.9 762.9 0 0 1 125.696 259.584m-55.04 44.224a699.65 699.65 0 0 0-125.056-269.632a256.13 256.13 0 0 0-56.064 331.968a382.7 382.7 0 0 0 181.12-62.336m-254.08 61.248A320.13 320.13 0 0 1 557.76 513.6a716 716 0 0 0-48.192-48.128a320.13 320.13 0 0 1-379.264 88.384a382.4 382.4 0 0 0 110.144 229.696a382.4 382.4 0 0 0 229.184 110.08zM129.28 481.088a256.13 256.13 0 0 0 331.072-56.448a699.65 699.65 0 0 0-268.8-124.352a382.66 382.66 0 0 0-62.272 180.8m106.56-235.84a762.9 762.9 0 0 1 258.688 125.056a256.51 256.51 0 0 0-13.44-241.088A382.46 382.46 0 0 0 235.84 245.248m318.08-114.944c40.576 89.536 37.76 193.92-8.448 281.344a780 780 0 0 1 66.176 66.112a320.83 320.83 0 0 1 282.112-8.128a382.4 382.4 0 0 0-110.144-229.12a382.4 382.4 0 0 0-229.632-110.208zM828.8 828.8a448 448 0 1 1-633.6-633.6a448 448 0 0 1 633.6 633.6\\"/>"},"bell":{"body":"<path fill=\\"currentColor\\" d=\\"M512 64a64 64 0 0 1 64 64v64H448v-64a64 64 0 0 1 64-64\\"/><path fill=\\"currentColor\\" d=\\"M256 768h512V448a256 256 0 1 0-512 0zm256-640a320 320 0 0 1 320 320v384H192V448a320 320 0 0 1 320-320\\"/><path fill=\\"currentColor\\" d=\\"M96 768h832q32 0 32 32t-32 32H96q-32 0-32-32t32-32m352 128h128a64 64 0 0 1-128 0\\"/>"},"bell-filled":{"body":"<path fill=\\"currentColor\\" d=\\"M640 832a128 128 0 0 1-256 0zm192-64H134.4a38.4 38.4 0 0 1 0-76.8H192V448c0-154.88 110.08-284.16 256.32-313.6a64 64 0 1 1 127.36 0A320.13 320.13 0 0 1 832 448v243.2h57.6a38.4 38.4 0 0 1 0 76.8z\\"/>"},"bicycle":{"body":"<path fill=\\"currentColor\\" d=\\"M256 832a128 128 0 1 0 0-256a128 128 0 0 0 0 256m0 64a192 192 0 1 1 0-384a192 192 0 0 1 0 384\\"/><path fill=\\"currentColor\\" d=\\"M288 672h320q32 0 32 32t-32 32H288q-32 0-32-32t32-32\\"/><path fill=\\"currentColor\\" d=\\"M768 832a128 128 0 1 0 0-256a128 128 0 0 0 0 256m0 64a192 192 0 1 1 0-384a192 192 0 0 1 0 384\\"/><path fill=\\"currentColor\\" d=\\"M480 192a32 32 0 0 1 0-64h160a32 32 0 0 1 31.04 24.256l96 384a32 32 0 0 1-62.08 15.488L615.04 192zM96 384a32 32 0 0 1 0-64h128a32 32 0 0 1 30.336 21.888l64 192a32 32 0 1 1-60.672 20.224L200.96 384z\\"/><path fill=\\"currentColor\\" d=\\"m373.376 599.808l-42.752-47.616l320-288l42.752 47.616z\\"/>"},"calendar":{"body":"<path fill=\\"currentColor\\" d=\\"M128 384v512h768V192H768v32a32 32 0 1 1-64 0v-32H320v32a32 32 0 0 1-64 0v-32H128v128h768v64zm192-256h384V96a32 32 0 1 1 64 0v32h160a32 32 0 0 1 32 32v768a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h160V96a32 32 0 0 1 64 0zm-32 384h64a32 32 0 0 1 0 64h-64a32 32 0 0 1 0-64m0 192h64a32 32 0 1 1 0 64h-64a32 32 0 1 1 0-64m192-192h64a32 32 0 0 1 0 64h-64a32 32 0 0 1 0-64m0 192h64a32 32 0 1 1 0 64h-64a32 32 0 1 1 0-64m192-192h64a32 32 0 1 1 0 64h-64a32 32 0 1 1 0-64m0 192h64a32 32 0 1 1 0 64h-64a32 32 0 1 1 0-64\\"/>"},"caret-bottom":{"body":"<path fill=\\"currentColor\\" d=\\"m192 384l320 384l320-384z\\"/>"},"caret-top":{"body":"<path fill=\\"currentColor\\" d=\\"M512 320L192 704h639.936z\\"/>"},"cellphone":{"body":"<path fill=\\"currentColor\\" d=\\"M256 128a64 64 0 0 0-64 64v640a64 64 0 0 0 64 64h512a64 64 0 0 0 64-64V192a64 64 0 0 0-64-64zm0-64h512a128 128 0 0 1 128 128v640a128 128 0 0 1-128 128H256a128 128 0 0 1-128-128V192A128 128 0 0 1 256 64m128 128h256a32 32 0 1 1 0 64H384a32 32 0 0 1 0-64m128 640a64 64 0 1 1 0-128a64 64 0 0 1 0 128\\"/>"},"chat-dot-round":{"body":"<path fill=\\"currentColor\\" d=\\"m174.72 855.68l135.296-45.12l23.68 11.84C388.096 849.536 448.576 864 512 864c211.84 0 384-166.784 384-352S723.84 160 512 160S128 326.784 128 512c0 69.12 24.96 139.264 70.848 199.232l22.08 28.8l-46.272 115.584zm-45.248 82.56A32 32 0 0 1 89.6 896l58.368-145.92C94.72 680.32 64 596.864 64 512C64 299.904 256 96 512 96s448 203.904 448 416s-192 416-448 416a461.06 461.06 0 0 1-206.912-48.384l-175.616 58.56z\\"/><path fill=\\"currentColor\\" d=\\"M512 563.2a51.2 51.2 0 1 1 0-102.4a51.2 51.2 0 0 1 0 102.4m192 0a51.2 51.2 0 1 1 0-102.4a51.2 51.2 0 0 1 0 102.4m-384 0a51.2 51.2 0 1 1 0-102.4a51.2 51.2 0 0 1 0 102.4\\"/>"},"chat-dot-square":{"body":"<path fill=\\"currentColor\\" d=\\"M273.536 736H800a64 64 0 0 0 64-64V256a64 64 0 0 0-64-64H224a64 64 0 0 0-64 64v570.88zM296 800L147.968 918.4A32 32 0 0 1 96 893.44V256a128 128 0 0 1 128-128h576a128 128 0 0 1 128 128v416a128 128 0 0 1-128 128z\\"/><path fill=\\"currentColor\\" d=\\"M512 499.2a51.2 51.2 0 1 1 0-102.4a51.2 51.2 0 0 1 0 102.4m192 0a51.2 51.2 0 1 1 0-102.4a51.2 51.2 0 0 1 0 102.4m-384 0a51.2 51.2 0 1 1 0-102.4a51.2 51.2 0 0 1 0 102.4\\"/>"},"check":{"body":"<path fill=\\"currentColor\\" d=\\"M406.656 706.944L195.84 496.256a32 32 0 1 0-45.248 45.248l256 256l512-512a32 32 0 0 0-45.248-45.248L406.592 706.944z\\"/>"},"checked":{"body":"<path fill=\\"currentColor\\" d=\\"M704 192h160v736H160V192h160.064v64H704zM311.616 537.28l-45.312 45.248L447.36 763.52l316.8-316.8l-45.312-45.184L447.36 673.024zM384 192V96h256v96z\\"/>"},"circle-check":{"body":"<path fill=\\"currentColor\\" d=\\"M512 896a384 384 0 1 0 0-768a384 384 0 0 0 0 768m0 64a448 448 0 1 1 0-896a448 448 0 0 1 0 896\\"/><path fill=\\"currentColor\\" d=\\"M745.344 361.344a32 32 0 0 1 45.312 45.312l-288 288a32 32 0 0 1-45.312 0l-160-160a32 32 0 1 1 45.312-45.312L480 626.752z\\"/>"},"circle-close-filled":{"body":"<path fill=\\"currentColor\\" d=\\"M512 64a448 448 0 1 1 0 896a448 448 0 0 1 0-896m0 393.664L407.936 353.6a38.4 38.4 0 1 0-54.336 54.336L457.664 512L353.6 616.064a38.4 38.4 0 1 0 54.336 54.336L512 566.336L616.064 670.4a38.4 38.4 0 1 0 54.336-54.336L566.336 512L670.4 407.936a38.4 38.4 0 1 0-54.336-54.336z\\"/>"},"circle-plus-filled":{"body":"<path fill=\\"currentColor\\" d=\\"M512 64a448 448 0 1 1 0 896a448 448 0 0 1 0-896m-38.4 409.6H326.4a38.4 38.4 0 1 0 0 76.8h147.2v147.2a38.4 38.4 0 0 0 76.8 0V550.4h147.2a38.4 38.4 0 0 0 0-76.8H550.4V326.4a38.4 38.4 0 1 0-76.8 0z\\"/>"},"close":{"body":"<path fill=\\"currentColor\\" d=\\"M764.288 214.592L512 466.88L259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512L214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z\\"/>"},"coin":{"body":"<path fill=\\"currentColor\\" d=\\"m161.92 580.736l29.888 58.88C171.328 659.776 160 681.728 160 704c0 82.304 155.328 160 352 160s352-77.696 352-160c0-22.272-11.392-44.16-31.808-64.32l30.464-58.432C903.936 615.808 928 657.664 928 704c0 129.728-188.544 224-416 224S96 833.728 96 704c0-46.592 24.32-88.576 65.92-123.264\\"/><path fill=\\"currentColor\\" d=\\"m161.92 388.736l29.888 58.88C171.328 467.84 160 489.792 160 512c0 82.304 155.328 160 352 160s352-77.696 352-160c0-22.272-11.392-44.16-31.808-64.32l30.464-58.432C903.936 423.808 928 465.664 928 512c0 129.728-188.544 224-416 224S96 641.728 96 512c0-46.592 24.32-88.576 65.92-123.264\\"/><path fill=\\"currentColor\\" d=\\"M512 544c-227.456 0-416-94.272-416-224S284.544 96 512 96s416 94.272 416 224s-188.544 224-416 224m0-64c196.672 0 352-77.696 352-160S708.672 160 512 160s-352 77.696-352 160s155.328 160 352 160\\"/>"},"comment":{"body":"<path fill=\\"currentColor\\" d=\\"M736 504a56 56 0 1 1 0-112a56 56 0 0 1 0 112m-224 0a56 56 0 1 1 0-112a56 56 0 0 1 0 112m-224 0a56 56 0 1 1 0-112a56 56 0 0 1 0 112M128 128v640h192v160l224-160h352V128z\\"/>"},"copy-document":{"body":"<path fill=\\"currentColor\\" d=\\"M768 832a128 128 0 0 1-128 128H192A128 128 0 0 1 64 832V384a128 128 0 0 1 128-128v64a64 64 0 0 0-64 64v448a64 64 0 0 0 64 64h448a64 64 0 0 0 64-64z\\"/><path fill=\\"currentColor\\" d=\\"M384 128a64 64 0 0 0-64 64v448a64 64 0 0 0 64 64h448a64 64 0 0 0 64-64V192a64 64 0 0 0-64-64zm0-64h448a128 128 0 0 1 128 128v448a128 128 0 0 1-128 128H384a128 128 0 0 1-128-128V192A128 128 0 0 1 384 64\\"/>"},"cpu":{"body":"<path fill=\\"currentColor\\" d=\\"M320 256a64 64 0 0 0-64 64v384a64 64 0 0 0 64 64h384a64 64 0 0 0 64-64V320a64 64 0 0 0-64-64zm0-64h384a128 128 0 0 1 128 128v384a128 128 0 0 1-128 128H320a128 128 0 0 1-128-128V320a128 128 0 0 1 128-128\\"/><path fill=\\"currentColor\\" d=\\"M512 64a32 32 0 0 1 32 32v128h-64V96a32 32 0 0 1 32-32m160 0a32 32 0 0 1 32 32v128h-64V96a32 32 0 0 1 32-32m-320 0a32 32 0 0 1 32 32v128h-64V96a32 32 0 0 1 32-32m160 896a32 32 0 0 1-32-32V800h64v128a32 32 0 0 1-32 32m160 0a32 32 0 0 1-32-32V800h64v128a32 32 0 0 1-32 32m-320 0a32 32 0 0 1-32-32V800h64v128a32 32 0 0 1-32 32M64 512a32 32 0 0 1 32-32h128v64H96a32 32 0 0 1-32-32m0-160a32 32 0 0 1 32-32h128v64H96a32 32 0 0 1-32-32m0 320a32 32 0 0 1 32-32h128v64H96a32 32 0 0 1-32-32m896-160a32 32 0 0 1-32 32H800v-64h128a32 32 0 0 1 32 32m0-160a32 32 0 0 1-32 32H800v-64h128a32 32 0 0 1 32 32m0 320a32 32 0 0 1-32 32H800v-64h128a32 32 0 0 1 32 32\\"/>"},"d-arrow-left":{"body":"<path fill=\\"currentColor\\" d=\\"M529.408 149.376a29.12 29.12 0 0 1 41.728 0a30.59 30.59 0 0 1 0 42.688L259.264 511.936l311.872 319.936a30.59 30.59 0 0 1-.512 43.264a29.12 29.12 0 0 1-41.216-.512L197.76 534.272a32 32 0 0 1 0-44.672zm256 0a29.12 29.12 0 0 1 41.728 0a30.59 30.59 0 0 1 0 42.688L515.264 511.936l311.872 319.936a30.59 30.59 0 0 1-.512 43.264a29.12 29.12 0 0 1-41.216-.512L453.76 534.272a32 32 0 0 1 0-44.672z\\"/>"},"d-arrow-right":{"body":"<path fill=\\"currentColor\\" d=\\"M452.864 149.312a29.12 29.12 0 0 1 41.728.064L826.24 489.664a32 32 0 0 1 0 44.672L494.592 874.624a29.12 29.12 0 0 1-41.728 0a30.59 30.59 0 0 1 0-42.752L764.736 512L452.864 192a30.59 30.59 0 0 1 0-42.688m-256 0a29.12 29.12 0 0 1 41.728.064L570.24 489.664a32 32 0 0 1 0 44.672L238.592 874.624a29.12 29.12 0 0 1-41.728 0a30.59 30.59 0 0 1 0-42.752L508.736 512L196.864 192a30.59 30.59 0 0 1 0-42.688\\"/>"},"delete":{"body":"<path fill=\\"currentColor\\" d=\\"M160 256H96a32 32 0 0 1 0-64h256V95.936a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V192h256a32 32 0 1 1 0 64h-64v672a32 32 0 0 1-32 32H192a32 32 0 0 1-32-32zm448-64v-64H416v64zM224 896h576V256H224zm192-128a32 32 0 0 1-32-32V416a32 32 0 0 1 64 0v320a32 32 0 0 1-32 32m192 0a32 32 0 0 1-32-32V416a32 32 0 0 1 64 0v320a32 32 0 0 1-32 32\\"/>"},"discount":{"body":"<path fill=\\"currentColor\\" d=\\"M224 704h576V318.336L552.512 115.84a64 64 0 0 0-81.024 0L224 318.336zm0 64v128h576V768zM593.024 66.304l259.2 212.096A32 32 0 0 1 864 303.168V928a32 32 0 0 1-32 32H192a32 32 0 0 1-32-32V303.168a32 32 0 0 1 11.712-24.768l259.2-212.096a128 128 0 0 1 162.112 0\\"/><path fill=\\"currentColor\\" d=\\"M512 448a64 64 0 1 0 0-128a64 64 0 0 0 0 128m0 64a128 128 0 1 1 0-256a128 128 0 0 1 0 256\\"/>"},"document":{"body":"<path fill=\\"currentColor\\" d=\\"M832 384H576V128H192v768h640zm-26.496-64L640 154.496V320zM160 64h480l256 256v608a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32m160 448h384v64H320zm0-192h160v64H320zm0 384h384v64H320z\\"/>"},"download":{"body":"<path fill=\\"currentColor\\" d=\\"M160 832h704a32 32 0 1 1 0 64H160a32 32 0 1 1 0-64m384-253.696l236.288-236.352l45.248 45.248L508.8 704L192 387.2l45.248-45.248L480 584.704V128h64z\\"/>"},"edit":{"body":"<path fill=\\"currentColor\\" d=\\"M832 512a32 32 0 1 1 64 0v352a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h352a32 32 0 0 1 0 64H192v640h640z\\"/><path fill=\\"currentColor\\" d=\\"m469.952 554.24l52.8-7.552L847.104 222.4a32 32 0 1 0-45.248-45.248L477.44 501.44l-7.552 52.8zm422.4-422.4a96 96 0 0 1 0 135.808l-331.84 331.84a32 32 0 0 1-18.112 9.088L436.8 623.68a32 32 0 0 1-36.224-36.224l15.104-105.6a32 32 0 0 1 9.024-18.112l331.904-331.84a96 96 0 0 1 135.744 0z\\"/>"},"expand":{"body":"<path fill=\\"currentColor\\" d=\\"M128 192h768v128H128zm0 256h512v128H128zm0 256h768v128H128zm576-352l192 160l-192 128z\\"/>"},"fold":{"body":"<path fill=\\"currentColor\\" d=\\"M896 192H128v128h768zm0 256H384v128h512zm0 256H128v128h768zM320 384L128 512l192 128z\\"/>"},"folder-opened":{"body":"<path fill=\\"currentColor\\" d=\\"M878.08 448H241.92l-96 384h636.16zM832 384v-64H485.76L357.504 192H128v448l57.92-231.744A32 32 0 0 1 216.96 384zm-24.96 512H96a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h287.872l128.384 128H864a32 32 0 0 1 32 32v96h23.04a32 32 0 0 1 31.04 39.744l-112 448A32 32 0 0 1 807.04 896\\"/>"},"grid":{"body":"<path fill=\\"currentColor\\" d=\\"M640 384v256H384V384zm64 0h192v256H704zm-64 512H384V704h256zm64 0V704h192v192zm-64-768v192H384V128zm64 0h192v192H704zM320 384v256H128V384zm0 512H128V704h192zm0-768v192H128V128z\\"/>"},"help-filled":{"body":"<path fill=\\"currentColor\\" d=\\"M926.784 480H701.312A192.51 192.51 0 0 0 544 322.688V97.216A416.064 416.064 0 0 1 926.784 480m0 64A416.064 416.064 0 0 1 544 926.784V701.312A192.51 192.51 0 0 0 701.312 544zM97.28 544h225.472A192.51 192.51 0 0 0 480 701.312v225.472A416.064 416.064 0 0 1 97.216 544zm0-64A416.064 416.064 0 0 1 480 97.216v225.472A192.51 192.51 0 0 0 322.688 480H97.216z\\"/>"},"hide":{"body":"<path fill=\\"currentColor\\" d=\\"M876.8 156.8c0-9.6-3.2-16-9.6-22.4s-12.8-9.6-22.4-9.6s-16 3.2-22.4 9.6L736 220.8c-64-32-137.6-51.2-224-60.8c-160 16-288 73.6-377.6 176S0 496 0 512s48 73.6 134.4 176c22.4 25.6 44.8 48 73.6 67.2l-86.4 89.6c-6.4 6.4-9.6 12.8-9.6 22.4s3.2 16 9.6 22.4s12.8 9.6 22.4 9.6s16-3.2 22.4-9.6l704-710.4c3.2-6.4 6.4-12.8 6.4-22.4m-646.4 528Q115.2 579.2 76.8 512q43.2-72 153.6-172.8C304 272 400 230.4 512 224c64 3.2 124.8 19.2 176 44.8l-54.4 54.4C598.4 300.8 560 288 512 288c-64 0-115.2 22.4-160 64s-64 96-64 160c0 48 12.8 89.6 35.2 124.8L256 707.2c-9.6-6.4-19.2-16-25.6-22.4m140.8-96Q352 555.2 352 512c0-44.8 16-83.2 48-112s67.2-48 112-48c28.8 0 54.4 6.4 73.6 19.2zM889.599 336c-12.8-16-28.8-28.8-41.6-41.6l-48 48c73.6 67.2 124.8 124.8 150.4 169.6q-43.2 72-153.6 172.8c-73.6 67.2-172.8 108.8-284.8 115.2c-51.2-3.2-99.2-12.8-140.8-28.8l-48 48c57.6 22.4 118.4 38.4 188.8 44.8c160-16 288-73.6 377.6-176S1024 528 1024 512s-48.001-73.6-134.401-176\\"/><path fill=\\"currentColor\\" d=\\"M511.998 672c-12.8 0-25.6-3.2-38.4-6.4l-51.2 51.2c28.8 12.8 57.6 19.2 89.6 19.2c64 0 115.2-22.4 160-64c41.6-41.6 64-96 64-160c0-32-6.4-64-19.2-89.6l-51.2 51.2c3.2 12.8 6.4 25.6 6.4 38.4c0 44.8-16 83.2-48 112s-67.2 48-112 48\\"/>"},"home-filled":{"body":"<path fill=\\"currentColor\\" d=\\"M512 128L128 447.936V896h255.936V640H640v256h255.936V447.936z\\"/>"},"house":{"body":"<path fill=\\"currentColor\\" d=\\"M192 413.952V896h640V413.952L512 147.328zM139.52 374.4l352-293.312a32 32 0 0 1 40.96 0l352 293.312A32 32 0 0 1 896 398.976V928a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V398.976a32 32 0 0 1 11.52-24.576\\"/>"},"info-filled":{"body":"<path fill=\\"currentColor\\" d=\\"M512 64a448 448 0 1 1 0 896.064A448 448 0 0 1 512 64m67.2 275.072c33.28 0 60.288-23.104 60.288-57.344s-27.072-57.344-60.288-57.344c-33.28 0-60.16 23.104-60.16 57.344s26.88 57.344 60.16 57.344M590.912 699.2c0-6.848 2.368-24.64 1.024-34.752l-52.608 60.544c-10.88 11.456-24.512 19.392-30.912 17.28a12.99 12.99 0 0 1-8.256-14.72l87.68-276.992c7.168-35.136-12.544-67.2-54.336-71.296c-44.096 0-108.992 44.736-148.48 101.504c0 6.784-1.28 23.68.064 33.792l52.544-60.608c10.88-11.328 23.552-19.328 29.952-17.152a12.8 12.8 0 0 1 7.808 16.128L388.48 728.576c-10.048 32.256 8.96 63.872 55.04 71.04c67.84 0 107.904-43.648 147.456-100.416z\\"/>"},"key":{"body":"<path fill=\\"currentColor\\" d=\\"M448 456.064V96a32 32 0 0 1 32-32.064L672 64a32 32 0 0 1 0 64H512v128h160a32 32 0 0 1 0 64H512v128a256 256 0 1 1-64 8.064M512 896a192 192 0 1 0 0-384a192 192 0 0 0 0 384\\"/>"},"list":{"body":"<path fill=\\"currentColor\\" d=\\"M704 192h160v736H160V192h160v64h384zM288 512h448v-64H288zm0 256h448v-64H288zm96-576V96h256v96z\\"/>"},"lock":{"body":"<path fill=\\"currentColor\\" d=\\"M224 448a32 32 0 0 0-32 32v384a32 32 0 0 0 32 32h576a32 32 0 0 0 32-32V480a32 32 0 0 0-32-32zm0-64h576a96 96 0 0 1 96 96v384a96 96 0 0 1-96 96H224a96 96 0 0 1-96-96V480a96 96 0 0 1 96-96\\"/><path fill=\\"currentColor\\" d=\\"M512 544a32 32 0 0 1 32 32v192a32 32 0 1 1-64 0V576a32 32 0 0 1 32-32m192-160v-64a192 192 0 1 0-384 0v64zM512 64a256 256 0 0 1 256 256v128H256V320A256 256 0 0 1 512 64\\"/>"},"memo":{"body":"<path fill=\\"currentColor\\" d=\\"M480 320h192c21.33 0 32-10.67 32-32s-10.67-32-32-32H480c-21.33 0-32 10.67-32 32s10.67 32 32 32\\"/><path fill=\\"currentColor\\" d=\\"M887.01 72.99C881.01 67 873.34 64 864 64H160c-9.35 0-17.02 3-23.01 8.99C131 78.99 128 86.66 128 96v832c0 9.35 2.99 17.02 8.99 23.01S150.66 960 160 960h704c9.35 0 17.02-2.99 23.01-8.99S896 937.34 896 928V96c0-9.35-3-17.02-8.99-23.01M192 896V128h96v768zm640 0H352V128h480z\\"/><path fill=\\"currentColor\\" d=\\"M480 512h192c21.33 0 32-10.67 32-32s-10.67-32-32-32H480c-21.33 0-32 10.67-32 32s10.67 32 32 32m0 192h192c21.33 0 32-10.67 32-32s-10.67-32-32-32H480c-21.33 0-32 10.67-32 32s10.67 32 32 32\\"/>"},"menu":{"body":"<path fill=\\"currentColor\\" d=\\"M160 448a32 32 0 0 1-32-32V160.064a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V416a32 32 0 0 1-32 32zm448 0a32 32 0 0 1-32-32V160.064a32 32 0 0 1 32-32h255.936a32 32 0 0 1 32 32V416a32 32 0 0 1-32 32zM160 896a32 32 0 0 1-32-32V608a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32v256a32 32 0 0 1-32 32zm448 0a32 32 0 0 1-32-32V608a32 32 0 0 1 32-32h255.936a32 32 0 0 1 32 32v256a32 32 0 0 1-32 32z\\"/>"},"message":{"body":"<path fill=\\"currentColor\\" d=\\"M128 224v512a64 64 0 0 0 64 64h640a64 64 0 0 0 64-64V224zm0-64h768a64 64 0 0 1 64 64v512a128 128 0 0 1-128 128H192A128 128 0 0 1 64 736V224a64 64 0 0 1 64-64\\"/><path fill=\\"currentColor\\" d=\\"M904 224L656.512 506.88a192 192 0 0 1-289.024 0L120 224zm-698.944 0l210.56 240.704a128 128 0 0 0 192.704 0L818.944 224z\\"/>"},"minus":{"body":"<path fill=\\"currentColor\\" d=\\"M128 544h768a32 32 0 1 0 0-64H128a32 32 0 0 0 0 64\\"/>"},"more":{"body":"<path fill=\\"currentColor\\" d=\\"M176 416a112 112 0 1 0 0 224a112 112 0 0 0 0-224m0 64a48 48 0 1 1 0 96a48 48 0 0 1 0-96m336-64a112 112 0 1 1 0 224a112 112 0 0 1 0-224m0 64a48 48 0 1 0 0 96a48 48 0 0 0 0-96m336-64a112 112 0 1 1 0 224a112 112 0 0 1 0-224m0 64a48 48 0 1 0 0 96a48 48 0 0 0 0-96\\"/>"},"odometer":{"body":"<path fill=\\"currentColor\\" d=\\"M512 896a384 384 0 1 0 0-768a384 384 0 0 0 0 768m0 64a448 448 0 1 1 0-896a448 448 0 0 1 0 896\\"/><path fill=\\"currentColor\\" d=\\"M192 512a320 320 0 1 1 640 0a32 32 0 1 1-64 0a256 256 0 1 0-512 0a32 32 0 0 1-64 0\\"/><path fill=\\"currentColor\\" d=\\"M570.432 627.84A96 96 0 1 1 509.568 608l60.992-187.776A32 32 0 1 1 631.424 440l-60.992 187.776zM502.08 734.464a32 32 0 1 0 19.84-60.928a32 32 0 0 0-19.84 60.928\\"/>"},"phone":{"body":"<path fill=\\"currentColor\\" d=\\"M79.36 432.256L591.744 944.64a32 32 0 0 0 35.2 6.784l253.44-108.544a32 32 0 0 0 9.984-52.032l-153.856-153.92a32 32 0 0 0-36.928-6.016l-69.888 34.944L358.08 394.24l35.008-69.888a32 32 0 0 0-5.952-36.928L233.152 133.568a32 32 0 0 0-52.032 10.048L72.512 397.056a32 32 0 0 0 6.784 35.2zm60.48-29.952l81.536-190.08L325.568 316.48l-24.64 49.216l-20.608 41.216l32.576 32.64l271.552 271.552l32.64 32.64l41.216-20.672l49.28-24.576l104.192 104.128l-190.08 81.472zM512 320v-64a256 256 0 0 1 256 256h-64a192 192 0 0 0-192-192m0-192V64a448 448 0 0 1 448 448h-64a384 384 0 0 0-384-384\\"/>"},"picture":{"body":"<path fill=\\"currentColor\\" d=\\"M160 160v704h704V160zm-32-64h768a32 32 0 0 1 32 32v768a32 32 0 0 1-32 32H128a32 32 0 0 1-32-32V128a32 32 0 0 1 32-32\\"/><path fill=\\"currentColor\\" d=\\"M384 288q64 0 64 64t-64 64t-64-64t64-64M185.408 876.992l-50.816-38.912L350.72 556.032a96 96 0 0 1 134.592-17.856l1.856 1.472l122.88 99.136a32 32 0 0 0 44.992-4.864l216-269.888l49.92 39.936l-215.808 269.824l-.256.32a96 96 0 0 1-135.04 14.464l-122.88-99.072l-.64-.512a32 32 0 0 0-44.8 5.952z\\"/>"},"plus":{"body":"<path fill=\\"currentColor\\" d=\\"M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64z\\"/>"},"position":{"body":"<path fill=\\"currentColor\\" d=\\"m249.6 417.088l319.744 43.072l39.168 310.272L845.12 178.88zm-129.024 47.168a32 32 0 0 1-7.68-61.44l777.792-311.04a32 32 0 0 1 41.6 41.6l-310.336 775.68a32 32 0 0 1-61.44-7.808L512 516.992z\\"/>"},"promotion":{"body":"<path fill=\\"currentColor\\" d=\\"m64 448l832-320l-128 704l-446.08-243.328L832 192L242.816 545.472zm256 512V657.024L512 768z\\"/>"},"question-filled":{"body":"<path fill=\\"currentColor\\" d=\\"M512 64a448 448 0 1 1 0 896a448 448 0 0 1 0-896m23.744 191.488c-52.096 0-92.928 14.784-123.2 44.352c-30.976 29.568-45.76 70.4-45.76 122.496h80.256c0-29.568 5.632-52.8 17.6-68.992c13.376-19.712 35.2-28.864 66.176-28.864c23.936 0 42.944 6.336 56.32 19.712c12.672 13.376 19.712 31.68 19.712 54.912c0 17.6-6.336 34.496-19.008 49.984l-8.448 9.856c-45.76 40.832-73.216 70.4-82.368 89.408c-9.856 19.008-14.08 42.24-14.08 68.992v9.856h80.96v-9.856c0-16.896 3.52-31.68 10.56-45.76c6.336-12.672 15.488-24.64 28.16-35.2c33.792-29.568 54.208-48.576 60.544-55.616c16.896-22.528 26.048-51.392 26.048-86.592q0-64.416-42.24-101.376c-28.16-25.344-65.472-37.312-111.232-37.312m-12.672 406.208a54.27 54.27 0 0 0-38.72 14.784a49.4 49.4 0 0 0-15.488 38.016c0 15.488 4.928 28.16 15.488 38.016A54.85 54.85 0 0 0 523.072 768c15.488 0 28.16-4.928 38.72-14.784a51.52 51.52 0 0 0 16.192-38.72a51.97 51.97 0 0 0-15.488-38.016a55.94 55.94 0 0 0-39.424-14.784\\"/>"},"reading":{"body":"<path fill=\\"currentColor\\" d=\\"m512 863.36l384-54.848v-638.72L525.568 222.72a96 96 0 0 1-27.136 0L128 169.792v638.72zM137.024 106.432l370.432 52.928a32 32 0 0 0 9.088 0l370.432-52.928A64 64 0 0 1 960 169.792v638.72a64 64 0 0 1-54.976 63.36l-388.48 55.488a32 32 0 0 1-9.088 0l-388.48-55.488A64 64 0 0 1 64 808.512v-638.72a64 64 0 0 1 73.024-63.36\\"/><path fill=\\"currentColor\\" d=\\"M480 192h64v704h-64z\\"/>"},"refresh":{"body":"<path fill=\\"currentColor\\" d=\\"M771.776 794.88A384 384 0 0 1 128 512h64a320 320 0 0 0 555.712 216.448H654.72a32 32 0 1 1 0-64h149.056a32 32 0 0 1 32 32v148.928a32 32 0 1 1-64 0v-50.56zM276.288 295.616h92.992a32 32 0 0 1 0 64H220.16a32 32 0 0 1-32-32V178.56a32 32 0 0 1 64 0v50.56A384 384 0 0 1 896.128 512h-64a320 320 0 0 0-555.776-216.384z\\"/>"},"refresh-left":{"body":"<path fill=\\"currentColor\\" d=\\"M289.088 296.704h92.992a32 32 0 0 1 0 64H232.96a32 32 0 0 1-32-32V179.712a32 32 0 0 1 64 0v50.56a384 384 0 0 1 643.84 282.88a384 384 0 0 1-383.936 384a384 384 0 0 1-384-384h64a320 320 0 1 0 640 0a320 320 0 0 0-555.712-216.448z\\"/>"},"refresh-right":{"body":"<path fill=\\"currentColor\\" d=\\"M784.512 230.272v-50.56a32 32 0 1 1 64 0v149.056a32 32 0 0 1-32 32H667.52a32 32 0 1 1 0-64h92.992A320 320 0 1 0 524.8 833.152a320 320 0 0 0 320-320h64a384 384 0 0 1-384 384a384 384 0 0 1-384-384a384 384 0 0 1 643.712-282.88\\"/>"},"remove":{"body":"<path fill=\\"currentColor\\" d=\\"M352 480h320a32 32 0 1 1 0 64H352a32 32 0 0 1 0-64\\"/><path fill=\\"currentColor\\" d=\\"M512 896a384 384 0 1 0 0-768a384 384 0 0 0 0 768m0 64a448 448 0 1 1 0-896a448 448 0 0 1 0 896\\"/>"},"right":{"body":"<path fill=\\"currentColor\\" d=\\"M754.752 480H160a32 32 0 1 0 0 64h594.752L521.344 777.344a32 32 0 0 0 45.312 45.312l288-288a32 32 0 0 0 0-45.312l-288-288a32 32 0 1 0-45.312 45.312z\\"/>"},"scale-to-original":{"body":"<path fill=\\"currentColor\\" d=\\"M813.176 180.706a60.235 60.235 0 0 1 60.236 60.235v481.883a60.235 60.235 0 0 1-60.236 60.235H210.824a60.235 60.235 0 0 1-60.236-60.235V240.94a60.235 60.235 0 0 1 60.236-60.235h602.352zm0-60.235H210.824A120.47 120.47 0 0 0 90.353 240.94v481.883a120.47 120.47 0 0 0 120.47 120.47h602.353a120.47 120.47 0 0 0 120.471-120.47V240.94a120.47 120.47 0 0 0-120.47-120.47zm-120.47 180.705a30.12 30.12 0 0 0-30.118 30.118v301.177a30.118 30.118 0 0 0 60.236 0V331.294a30.12 30.12 0 0 0-30.118-30.118m-361.412 0a30.12 30.12 0 0 0-30.118 30.118v301.177a30.118 30.118 0 1 0 60.236 0V331.294a30.12 30.12 0 0 0-30.118-30.118M512 361.412a30.12 30.12 0 0 0-30.118 30.117v30.118a30.118 30.118 0 0 0 60.236 0V391.53A30.12 30.12 0 0 0 512 361.412M512 512a30.12 30.12 0 0 0-30.118 30.118v30.117a30.118 30.118 0 0 0 60.236 0v-30.117A30.12 30.12 0 0 0 512 512\\"/>"},"search":{"body":"<path fill=\\"currentColor\\" d=\\"m795.904 750.72l124.992 124.928a32 32 0 0 1-45.248 45.248L750.656 795.904a416 416 0 1 1 45.248-45.248zM480 832a352 352 0 1 0 0-704a352 352 0 0 0 0 704\\"/>"},"select":{"body":"<path fill=\\"currentColor\\" d=\\"M77.248 415.04a64 64 0 0 1 90.496 0l226.304 226.304L846.528 188.8a64 64 0 1 1 90.56 90.496l-543.04 543.04l-316.8-316.8a64 64 0 0 1 0-90.496\\"/>"},"setting":{"body":"<path fill=\\"currentColor\\" d=\\"M600.704 64a32 32 0 0 1 30.464 22.208l35.2 109.376c14.784 7.232 28.928 15.36 42.432 24.512l112.384-24.192a32 32 0 0 1 34.432 15.36L944.32 364.8a32 32 0 0 1-4.032 37.504l-77.12 85.12a357 357 0 0 1 0 49.024l77.12 85.248a32 32 0 0 1 4.032 37.504l-88.704 153.6a32 32 0 0 1-34.432 15.296L708.8 803.904c-13.44 9.088-27.648 17.28-42.368 24.512l-35.264 109.376A32 32 0 0 1 600.704 960H423.296a32 32 0 0 1-30.464-22.208L357.696 828.48a352 352 0 0 1-42.56-24.64l-112.32 24.256a32 32 0 0 1-34.432-15.36L79.68 659.2a32 32 0 0 1 4.032-37.504l77.12-85.248a357 357 0 0 1 0-48.896l-77.12-85.248A32 32 0 0 1 79.68 364.8l88.704-153.6a32 32 0 0 1 34.432-15.296l112.32 24.256c13.568-9.152 27.776-17.408 42.56-24.64l35.2-109.312A32 32 0 0 1 423.232 64H600.64zm-23.424 64H446.72l-36.352 113.088l-24.512 11.968a294 294 0 0 0-34.816 20.096l-22.656 15.36l-116.224-25.088l-65.28 113.152l79.68 88.192l-1.92 27.136a293 293 0 0 0 0 40.192l1.92 27.136l-79.808 88.192l65.344 113.152l116.224-25.024l22.656 15.296a294 294 0 0 0 34.816 20.096l24.512 11.968L446.72 896h130.688l36.48-113.152l24.448-11.904a288 288 0 0 0 34.752-20.096l22.592-15.296l116.288 25.024l65.28-113.152l-79.744-88.192l1.92-27.136a293 293 0 0 0 0-40.256l-1.92-27.136l79.808-88.128l-65.344-113.152l-116.288 24.96l-22.592-15.232a288 288 0 0 0-34.752-20.096l-24.448-11.904L577.344 128zM512 320a192 192 0 1 1 0 384a192 192 0 0 1 0-384m0 64a128 128 0 1 0 0 256a128 128 0 0 0 0-256\\"/>"},"sort":{"body":"<path fill=\\"currentColor\\" d=\\"M384 96a32 32 0 0 1 64 0v786.752a32 32 0 0 1-54.592 22.656L95.936 608a32 32 0 0 1 0-45.312h.128a32 32 0 0 1 45.184 0L384 805.632zm192 45.248a32 32 0 0 1 54.592-22.592L928.064 416a32 32 0 0 1 0 45.312h-.128a32 32 0 0 1-45.184 0L640 218.496V928a32 32 0 1 1-64 0z\\"/>"},"success-filled":{"body":"<path fill=\\"currentColor\\" d=\\"M512 64a448 448 0 1 1 0 896a448 448 0 0 1 0-896m-55.808 536.384l-99.52-99.584a38.4 38.4 0 1 0-54.336 54.336l126.72 126.72a38.27 38.27 0 0 0 54.336 0l262.4-262.464a38.4 38.4 0 1 0-54.272-54.336z\\"/>"},"suitcase":{"body":"<path fill=\\"currentColor\\" d=\\"M128 384h768v-64a64 64 0 0 0-64-64H192a64 64 0 0 0-64 64zm0 64v320a64 64 0 0 0 64 64h640a64 64 0 0 0 64-64V448zm64-256h640a128 128 0 0 1 128 128v448a128 128 0 0 1-128 128H192A128 128 0 0 1 64 768V320a128 128 0 0 1 128-128\\"/><path fill=\\"currentColor\\" d=\\"M384 128v64h256v-64zm0-64h256a64 64 0 0 1 64 64v64a64 64 0 0 1-64 64H384a64 64 0 0 1-64-64v-64a64 64 0 0 1 64-64\\"/>"},"switch-button":{"body":"<path fill=\\"currentColor\\" d=\\"M352 159.872V230.4a352 352 0 1 0 320 0v-70.528A416.128 416.128 0 0 1 512 960a416 416 0 0 1-160-800.128\\"/><path fill=\\"currentColor\\" d=\\"M512 64q32 0 32 32v320q0 32-32 32t-32-32V96q0-32 32-32\\"/>"},"ticket":{"body":"<path fill=\\"currentColor\\" d=\\"M640 832H64V640a128 128 0 1 0 0-256V192h576v160h64V192h256v192a128 128 0 1 0 0 256v192H704V672h-64zm0-416v192h64V416z\\"/>"},"tools":{"body":"<path fill=\\"currentColor\\" d=\\"M764.416 254.72a351.7 351.7 0 0 1 86.336 149.184H960v192.064H850.752a351.7 351.7 0 0 1-86.336 149.312l54.72 94.72l-166.272 96l-54.592-94.72a352.64 352.64 0 0 1-172.48 0L371.136 936l-166.272-96l54.72-94.72a351.7 351.7 0 0 1-86.336-149.312H64v-192h109.248a351.7 351.7 0 0 1 86.336-149.312L204.8 160l166.208-96h.192l54.656 94.592a352.64 352.64 0 0 1 172.48 0L652.8 64h.128L819.2 160l-54.72 94.72zM704 499.968a192 192 0 1 0-384 0a192 192 0 0 0 384 0\\"/>"},"top":{"body":"<path fill=\\"currentColor\\" d=\\"M572.235 205.282v600.365a30.118 30.118 0 1 1-60.235 0V205.282L292.382 438.633a28.913 28.913 0 0 1-42.646 0a33.43 33.43 0 0 1 0-45.236l271.058-288.045a28.913 28.913 0 0 1 42.647 0L834.5 393.397a33.43 33.43 0 0 1 0 45.176a28.913 28.913 0 0 1-42.647 0l-219.618-233.23z\\"/>"},"upload":{"body":"<path fill=\\"currentColor\\" d=\\"M160 832h704a32 32 0 1 1 0 64H160a32 32 0 1 1 0-64m384-578.304V704h-64V247.296L237.248 490.048L192 444.8L508.8 128l316.8 316.8l-45.312 45.248z\\"/>"},"upload-filled":{"body":"<path fill=\\"currentColor\\" d=\\"M544 864V672h128L512 480L352 672h128v192H320v-1.6c-5.376.32-10.496 1.6-16 1.6A240 240 0 0 1 64 624c0-123.136 93.12-223.488 212.608-237.248A239.81 239.81 0 0 1 512 192a239.87 239.87 0 0 1 235.456 194.752c119.488 13.76 212.48 114.112 212.48 237.248a240 240 0 0 1-240 240c-5.376 0-10.56-1.28-16-1.6v1.6z\\"/>"},"user":{"body":"<path fill=\\"currentColor\\" d=\\"M512 512a192 192 0 1 0 0-384a192 192 0 0 0 0 384m0 64a256 256 0 1 1 0-512a256 256 0 0 1 0 512m320 320v-96a96 96 0 0 0-96-96H288a96 96 0 0 0-96 96v96a32 32 0 1 1-64 0v-96a160 160 0 0 1 160-160h448a160 160 0 0 1 160 160v96a32 32 0 1 1-64 0\\"/>"},"video-play":{"body":"<path fill=\\"currentColor\\" d=\\"M512 64a448 448 0 1 1 0 896a448 448 0 0 1 0-896m0 832a384 384 0 0 0 0-768a384 384 0 0 0 0 768m-48-247.616L668.608 512L464 375.616zm10.624-342.656l249.472 166.336a48 48 0 0 1 0 79.872L474.624 718.272A48 48 0 0 1 400 678.336V345.6a48 48 0 0 1 74.624-39.936z\\"/>"},"view":{"body":"<path fill=\\"currentColor\\" d=\\"M512 160c320 0 512 352 512 352S832 864 512 864S0 512 0 512s192-352 512-352m0 64c-225.28 0-384.128 208.064-436.8 288c52.608 79.872 211.456 288 436.8 288c225.28 0 384.128-208.064 436.8-288c-52.608-79.872-211.456-288-436.8-288m0 64a224 224 0 1 1 0 448a224 224 0 0 1 0-448m0 64a160.19 160.19 0 0 0-160 160c0 88.192 71.744 160 160 160s160-71.808 160-160s-71.744-160-160-160\\"/>"},"wallet-filled":{"body":"<path fill=\\"currentColor\\" d=\\"M688 512a112 112 0 1 0 0 224h208v160H128V352h768v160zm32 160h-32a48 48 0 0 1 0-96h32a48 48 0 0 1 0 96m-80-544l128 160H384z\\"/>"},"warning":{"body":"<path fill=\\"currentColor\\" d=\\"M512 64a448 448 0 1 1 0 896a448 448 0 0 1 0-896m0 832a384 384 0 0 0 0-768a384 384 0 0 0 0 768m48-176a48 48 0 1 1-96 0a48 48 0 0 1 96 0m-48-464a32 32 0 0 1 32 32v288a32 32 0 0 1-64 0V288a32 32 0 0 1 32-32\\"/>"},"warning-filled":{"body":"<path fill=\\"currentColor\\" d=\\"M512 64a448 448 0 1 1 0 896a448 448 0 0 1 0-896m0 192a58.43 58.43 0 0 0-58.24 63.744l23.36 256.384a35.072 35.072 0 0 0 69.76 0l23.296-256.384A58.43 58.43 0 0 0 512 256m0 512a51.2 51.2 0 1 0 0-102.4a51.2 51.2 0 0 0 0 102.4\\"/>"},"zoom-in":{"body":"<path fill=\\"currentColor\\" d=\\"m795.904 750.72l124.992 124.928a32 32 0 0 1-45.248 45.248L750.656 795.904a416 416 0 1 1 45.248-45.248zM480 832a352 352 0 1 0 0-704a352 352 0 0 0 0 704m-32-384v-96a32 32 0 0 1 64 0v96h96a32 32 0 0 1 0 64h-96v96a32 32 0 0 1-64 0v-96h-96a32 32 0 0 1 0-64z\\"/>"},"zoom-out":{"body":"<path fill=\\"currentColor\\" d=\\"m795.904 750.72l124.992 124.928a32 32 0 0 1-45.248 45.248L750.656 795.904a416 416 0 1 1 45.248-45.248zM480 832a352 352 0 1 0 0-704a352 352 0 0 0 0 704M352 448h256a32 32 0 0 1 0 64H352a32 32 0 0 1 0-64\\"/>"}}},{"prefix":"ls","width":717,"height":717,"icons":{"a":{"body":"<path fill=\\"currentColor\\" d=\\"M491 222v-94h72v526h-72v-74c-50 55-124 91-206 91C133 671 0 551 0 401s133-270 285-270c82 0 156 36 206 91m0 185v-12c-4-110-95-198-207-198c-115 0-215 91-215 204s100 204 215 204c112 0 203-88 207-198\\"/>","width":563,"height":671},"c":{"body":"<path fill=\\"currentColor\\" d=\\"m507 242l-56 39c-38-51-98-84-167-84c-70 0-134 34-174 85c-26 34-41 75-41 119s15 85 41 119c40 51 104 85 174 85c69 0 129-33 167-84l56 39c-50 68-131 111-222 111c-92 0-178-44-230-112C20 514 0 459 0 401s20-113 55-158c52-68 138-112 230-112c91 0 172 43 222 111\\"/>","width":507,"height":671},"d":{"body":"<path fill=\\"currentColor\\" d=\\"M490 324V0h72v754h-72v-82c-53 58-132 96-216 96C122 768 0 648 0 498s122-270 274-270c84 0 163 38 216 96M275 702c115 0 215-91 215-204S390 294 275 294c-114 0-207 91-207 204s93 204 207 204\\"/>","width":562,"height":768},"e":{"body":"<path fill=\\"currentColor\\" d=\\"M559 399H69v2c0 113 100 204 215 204c82 0 152-47 186-115l61 29c-44 90-137 152-246 152C133 671 0 551 0 401c0-42 11-82 29-117c47-91 147-153 256-153c130 0 238 89 267 208c4 19 7 40 7 60M80 339h400c-27-82-104-142-196-142c-82 0-158 47-194 116c-4 8-7 17-10 26\\"/>","width":559,"height":671},"f":{"body":"<path fill=\\"currentColor\\" d=\\"M255 5v78c-11-7-25-11-39-11c-39 0-70 31-71 70v86h110v60H145v466H73V288H0v-60h73v-84c0-53 29-99 72-124c22-12 45-20 71-20c14 0 27 2 39 5\\"/>","width":255,"height":754},"g":{"body":"<path fill=\\"currentColor\\" d=\\"M15 697h75c36 70 111 118 195 118c103 0 189-74 205-172c1-4 1-30 1-68c-50 57-124 93-207 93C132 668 0 548 0 398s132-270 284-270c82 0 156 36 207 92c-1-54-1-92-1-92h72v484c0 14-1 29-3 42c-22 129-136 227-273 227c-122 0-231-76-271-184m268-95c114 0 207-91 207-204s-93-204-207-204c-115 0-215 91-215 204s100 204 215 204\\"/>","width":562,"height":881},"h":{"body":"<path fill=\\"currentColor\\" d=\\"M72 0v292c42-40 109-64 171-64s110 24 152 64c44 42 71 102 71 168v294h-71V456c-3-87-64-157-152-157c-87 0-169 70-171 157v298H0V0z\\"/>","width":466,"height":754},"i":{"body":"<path fill=\\"currentColor\\" d=\\"M82 82H0V0h82zm-5 608H5V164h72z\\"/>","width":82,"height":690},"k":{"body":"<path fill=\\"currentColor\\" d=\\"M411 228L137 466l319 288H349L81 513l-9 9v232H0V0h72v426l229-198z\\"/>","width":456,"height":754},"l":{"body":"<path fill=\\"currentColor\\" d=\\"M0 754V0h72v754z\\"/>","width":72,"height":754},"m":{"body":"<path fill=\\"currentColor\\" d=\\"M71 128v64c42-40 109-64 171-64s110 24 152 64c13 13 26 28 36 44c41-66 124-108 207-108c62 0 108 24 150 64c45 42 73 102 73 168v294h-73V356c-2-87-62-157-150-157s-170 71-171 159v296h-72V356c-3-87-64-157-152-157c-87 0-169 70-171 157v298H0V128z\\"/>","width":860,"height":654},"n":{"body":"<path fill=\\"currentColor\\" d=\\"M72 128v64c42-40 109-64 171-64s109 24 151 64c44 42 72 102 72 168v294h-72V356c-2-87-64-157-151-157c-88 0-169 70-171 157v298H0V128z\\"/>","width":466,"height":654},"o":{"body":"<path fill=\\"currentColor\\" d=\\"M285 113c152 0 275 121 275 271S437 654 285 654S0 534 0 384s133-271 285-271m-1 474c114 0 207-90 207-203s-93-204-207-204c-115 0-215 91-215 204s100 203 215 203\\"/>","width":560,"height":654},"p":{"body":"<path fill=\\"currentColor\\" d=\\"M72 113v97c53-58 132-97 216-97c152 0 275 121 275 271S440 654 288 654c-84 0-163-38-216-96v309H0V113zm215 474c115 0 207-90 207-203s-92-204-207-204S72 271 72 384s100 203 215 203\\"/>","width":563,"height":867},"r":{"body":"<path fill=\\"currentColor\\" d=\\"M71 128v64c42-40 99-64 161-64c19 0 38 3 55 7v75c-17-6-36-11-55-11c-87 0-159 70-161 157v298H0V128z\\"/>","width":287,"height":654},"s":{"body":"<path fill=\\"currentColor\\" d=\\"m280 220l45-47c-50-49-101-74-151-74c-43 0-78 14-107 41c-28 28-42 62-42 103c0 32 9 59 26 85c17 25 52 49 101 75c45 24 75 42 88 57c13 16 19 35 19 55c0 24-11 45-30 63c-19 17-43 27-71 27c-40 0-78-21-114-61L0 595c18 24 42 43 71 56s58 20 90 20c47 0 85-15 117-46c32-32 48-69 48-113c0-32-10-60-28-86c-18-25-55-51-107-78c-43-22-71-42-84-58s-20-33-20-50c0-20 9-38 25-53c16-14 35-22 58-22c36 0 73 18 110 55\\"/>","width":326,"height":671},"t":{"body":"<path fill=\\"currentColor\\" d=\\"M164 228h91v60h-91v466H92V288H0v-60h92V0h72z\\"/>","width":255,"height":754},"u":{"body":"<path fill=\\"currentColor\\" d=\\"M394 654v-47c-42 40-110 64-172 64s-108-24-150-64C28 564 0 505 0 439V128h72v315c2 87 63 157 150 157c88 0 169-70 172-157V128h72v526z\\"/>","width":466,"height":671},"v":{"body":"<path fill=\\"currentColor\\" d=\\"m249 495l169-367h79L249 667L0 128h79z\\"/>","width":497,"height":667},"y":{"body":"<path fill=\\"currentColor\\" d=\\"m249 494l169-366h78L149 881H69l140-301L0 128h79z\\"/>","width":496,"height":881}}},{"prefix":"ps","width":512,"height":512,"icons":{}},{"prefix":"ion","width":512,"height":512,"icons":{"at":{"body":"<path fill=\\"none\\" stroke=\\"currentColor\\" stroke-linecap=\\"round\\" stroke-linejoin=\\"round\\" stroke-width=\\"32\\" d=\\"M320 254.27c-4.5 51-40.12 80-80.55 80s-67.34-35.82-63.45-80s37.12-80 77.55-80s70.33 36 66.45 80\\"/><path fill=\\"none\\" stroke=\\"currentColor\\" stroke-linecap=\\"round\\" stroke-linejoin=\\"round\\" stroke-width=\\"32\\" d=\\"M319.77 415.77c-28.56 12-47.28 14.5-79.28 14.5c-97.2 0-169-78.8-160.49-176s94.31-176 191.51-176C381 78.27 441.19 150 432.73 246c-6.31 71.67-52.11 92.32-76.09 88.07c-22.56-4-41.18-24.42-37.74-63.5l8.48-96.25\\"/>"},"create":{"body":"<path fill=\\"currentColor\\" d=\\"M459.94 53.25a16.06 16.06 0 0 0-23.22-.56L424.35 65a8 8 0 0 0 0 11.31l11.34 11.32a8 8 0 0 0 11.34 0l12.06-12c6.1-6.09 6.67-16.01.85-22.38M399.34 90L218.82 270.2a9 9 0 0 0-2.31 3.93L208.16 299a3.91 3.91 0 0 0 4.86 4.86l24.85-8.35a9 9 0 0 0 3.93-2.31L422 112.66a9 9 0 0 0 0-12.66l-9.95-10a9 9 0 0 0-12.71 0\\"/><path fill=\\"currentColor\\" d=\\"M386.34 193.66L264.45 315.79A41.1 41.1 0 0 1 247.58 326l-25.9 8.67a35.92 35.92 0 0 1-44.33-44.33l8.67-25.9a41.1 41.1 0 0 1 10.19-16.87l122.13-121.91a8 8 0 0 0-5.65-13.66H104a56 56 0 0 0-56 56v240a56 56 0 0 0 56 56h240a56 56 0 0 0 56-56V199.31a8 8 0 0 0-13.66-5.65\\"/>"},"language-sharp":{"body":"<path fill=\\"currentColor\\" d=\\"M363 176L246 464h47.24l24.49-58h90.54l24.49 58H480Zm-26.69 186L363 279.85L389.69 362ZM272 320c-.25-.19-20.59-15.77-45.42-42.67c39.58-53.64 62-114.61 71.15-143.33H352V90H214V48h-44v42H32v44h219.25c-9.52 26.95-27.05 69.5-53.79 108.36c-32.68-43.44-47.14-75.88-47.33-76.22L143 152l-38 22l6.87 13.86c.89 1.56 17.19 37.9 54.71 86.57c.92 1.21 1.85 2.39 2.78 3.57c-49.72 56.86-89.15 79.09-89.66 79.47L64 368l23 36l19.3-11.47c2.2-1.67 41.33-24 92-80.78c24.52 26.28 43.22 40.83 44.3 41.67L255 362Z\\"/>"}}},{"prefix":"ph","width":256,"height":256,"icons":{"article-medium":{"body":"<path fill=\\"currentColor\\" d=\\"M56 136a8 8 0 0 1-8 8H24a8 8 0 0 1 0-16h8V64h-8a8 8 0 0 1 0-16h16a8 8 0 0 1 6.78 3.74L80 104.91l33.22-53.15A8 8 0 0 1 120 48h16a8 8 0 0 1 0 16h-8v64h8a8 8 0 0 1 0 16h-24a8 8 0 0 1 0-16V83.89l-25.22 40.35a8 8 0 0 1-13.56 0L48 83.89V128a8 8 0 0 1 8 8m112-24h64a8 8 0 0 0 0-16h-64a8 8 0 0 0 0 16m64 16h-64a8 8 0 0 0 0 16h64a8 8 0 0 0 0-16m0 32H80a8 8 0 0 0 0 16h152a8 8 0 0 0 0-16m0 32H80a8 8 0 0 0 0 16h152a8 8 0 0 0 0-16\\"/>"},"copy-bold":{"body":"<path fill=\\"currentColor\\" d=\\"M216 28H88a12 12 0 0 0-12 12v36H40a12 12 0 0 0-12 12v128a12 12 0 0 0 12 12h128a12 12 0 0 0 12-12v-36h36a12 12 0 0 0 12-12V40a12 12 0 0 0-12-12m-60 176H52V100h104Zm48-48h-24V88a12 12 0 0 0-12-12h-68V52h104Z\\"/>"},"function":{"body":"<path fill=\\"currentColor\\" d=\\"M208 40a8 8 0 0 1-8 8h-29.29a24 24 0 0 0-23.62 19.71l-9.5 52.29H184a8 8 0 0 1 0 16h-49.32l-10 55.16A40 40 0 0 1 85.29 224H56a8 8 0 0 1 0-16h29.29a24 24 0 0 0 23.62-19.71l9.5-52.29H72a8 8 0 0 1 0-16h49.32l10-55.16A40 40 0 0 1 170.71 32H200a8 8 0 0 1 8 8\\"/>"}}},{"prefix":"et","width":32,"height":32,"icons":{}},{"prefix":"il","width":750,"height":850,"icons":{}},{"prefix":"geo","width":100,"height":100,"icons":{}},{"prefix":"zmdi","width":432,"height":384,"icons":{"fullscreen":{"body":"<path fill=\\"currentColor\\" d=\\"M43 235v64h64v42H0V235zM0 149V43h107v42H43v64zm256 150v-64h43v106H192v-42zM192 43h107v106h-43V85h-64z\\"/>","width":304},"fullscreen-exit":{"body":"<path fill=\\"currentColor\\" d=\\"M0 277v-42h107v106H64v-64zm64-170V43h43v106H0v-42zm128 234V235h107v42h-64v64zm43-234h64v42H192V43h43z\\"/>","width":304}}},{"prefix":"mdi","width":24,"height":24,"icons":{"account-group":{"body":"<path fill=\\"currentColor\\" d=\\"M12 5.5A3.5 3.5 0 0 1 15.5 9a3.5 3.5 0 0 1-3.5 3.5A3.5 3.5 0 0 1 8.5 9A3.5 3.5 0 0 1 12 5.5M5 8c.56 0 1.08.15 1.53.42c-.15 1.43.27 2.85 1.13 3.96C7.16 13.34 6.16 14 5 14a3 3 0 0 1-3-3a3 3 0 0 1 3-3m14 0a3 3 0 0 1 3 3a3 3 0 0 1-3 3c-1.16 0-2.16-.66-2.66-1.62a5.54 5.54 0 0 0 1.13-3.96c.45-.27.97-.42 1.53-.42M5.5 18.25c0-2.07 2.91-3.75 6.5-3.75s6.5 1.68 6.5 3.75V20h-13zM0 20v-1.5c0-1.39 1.89-2.56 4.45-2.9c-.59.68-.95 1.62-.95 2.65V20zm24 0h-3.5v-1.75c0-1.03-.36-1.97-.95-2.65c2.56.34 4.45 1.51 4.45 2.9z\\"/>"},"arrow-right-drop-circle":{"body":"<path fill=\\"currentColor\\" d=\\"M2 12A10 10 0 0 1 12 2a10 10 0 0 1 10 10a10 10 0 0 1-10 10A10 10 0 0 1 2 12m8 5l5-5l-5-5z\\"/>"},"cash-multiple":{"body":"<path fill=\\"currentColor\\" d=\\"M5 6h18v12H5zm9 3a3 3 0 0 1 3 3a3 3 0 0 1-3 3a3 3 0 0 1-3-3a3 3 0 0 1 3-3M9 8a2 2 0 0 1-2 2v4a2 2 0 0 1 2 2h10a2 2 0 0 1 2-2v-4a2 2 0 0 1-2-2zm-8 2h2v10h16v2H1z\\"/>"},"certificate-outline":{"body":"<path fill=\\"currentColor\\" d=\\"m13 21l2-1l2 1v-7h-4m4-5V7l-2 1l-2-1v2l-2 1l2 1v2l2-1l2 1v-2l2-1m1-7H4a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h7v-2H4V5h16v10h-1v2h1a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 5H5V6h6m-2 5H5V9h4m2 5H5v-2h6Z\\"/>"},"cog-outline":{"body":"<path fill=\\"currentColor\\" d=\\"M12 8a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 2a2 2 0 0 0-2 2a2 2 0 0 0 2 2a2 2 0 0 0 2-2a2 2 0 0 0-2-2m-2 12c-.25 0-.46-.18-.5-.42l-.37-2.65c-.63-.25-1.17-.59-1.69-.99l-2.49 1.01c-.22.08-.49 0-.61-.22l-2-3.46a.493.493 0 0 1 .12-.64l2.11-1.66L4.5 12l.07-1l-2.11-1.63a.493.493 0 0 1-.12-.64l2-3.46c.12-.22.39-.31.61-.22l2.49 1c.52-.39 1.06-.73 1.69-.98l.37-2.65c.04-.24.25-.42.5-.42h4c.25 0 .46.18.5.42l.37 2.65c.63.25 1.17.59 1.69.98l2.49-1c.22-.09.49 0 .61.22l2 3.46c.13.22.07.49-.12.64L19.43 11l.07 1l-.07 1l2.11 1.63c.19.15.25.42.12.64l-2 3.46c-.12.22-.39.31-.61.22l-2.49-1c-.52.39-1.06.73-1.69.98l-.37 2.65c-.04.24-.25.42-.5.42zm1.25-18l-.37 2.61c-1.2.25-2.26.89-3.03 1.78L5.44 7.35l-.75 1.3L6.8 10.2a5.55 5.55 0 0 0 0 3.6l-2.12 1.56l.75 1.3l2.43-1.04c.77.88 1.82 1.52 3.01 1.76l.37 2.62h1.52l.37-2.61c1.19-.25 2.24-.89 3.01-1.77l2.43 1.04l.75-1.3l-2.12-1.55c.4-1.17.4-2.44 0-3.61l2.11-1.55l-.75-1.3l-2.41 1.04a5.42 5.42 0 0 0-3.03-1.77L12.75 4z\\"/>"},"cube-outline":{"body":"<path fill=\\"currentColor\\" d=\\"M21 16.5c0 .38-.21.71-.53.88l-7.9 4.44c-.16.12-.36.18-.57.18s-.41-.06-.57-.18l-7.9-4.44A.99.99 0 0 1 3 16.5v-9c0-.38.21-.71.53-.88l7.9-4.44c.16-.12.36-.18.57-.18s.41.06.57.18l7.9 4.44c.32.17.53.5.53.88zM12 4.15L6.04 7.5L12 10.85l5.96-3.35zM5 15.91l6 3.38v-6.71L5 9.21zm14 0v-6.7l-6 3.37v6.71z\\"/>"},"factory":{"body":"<path fill=\\"currentColor\\" d=\\"M4 18v2h4v-2zm0-4v2h10v-2zm6 4v2h4v-2zm6-4v2h4v-2zm0 4v2h4v-2zM2 22V8l5 4V8l5 4V8l5 4l1-10h3l1 10v10z\\"/>"},"format-size":{"body":"<path fill=\\"currentColor\\" d=\\"M2 4v3h5v12h3V7h5V4zm19 5h-9v3h3v7h3v-7h3z\\"/>"},"hammer-wrench":{"body":"<path fill=\\"currentColor\\" d=\\"m13.78 15.3l6 6l2.11-2.16l-6-6zm3.72-5.2c-.39 0-.81-.05-1.14-.19L4.97 21.25l-2.11-2.11l7.41-7.4L8.5 9.96l-.72.7l-1.45-1.41v2.86l-.7.7l-3.52-3.56l.7-.7h2.81l-1.4-1.41l3.56-3.56a2.976 2.976 0 0 1 4.22 0L9.89 5.74l1.41 1.4l-.71.71l1.79 1.78l1.82-1.88c-.14-.33-.2-.75-.2-1.12a3.49 3.49 0 0 1 3.5-3.52c.59 0 1.11.14 1.58.42L16.41 6.2l1.5 1.5l2.67-2.67c.28.47.42.97.42 1.6c0 1.92-1.55 3.47-3.5 3.47\\"/>"},"hand-saw":{"body":"<path fill=\\"currentColor\\" d=\\"m9.8 17l-3.9-5.4L20 2l2 3v3h-3v3h-3v3h-3v3m-3.3 1.7l-.5 2.8l-1.6 1.2c-.9.6-2.1.4-2.8-.5l-3.5-4.9c-.6-.9-.4-2.1.5-2.8l3.3-2.3zM4.6 15L3 16.1L6.5 21l1.6-1.2z\\"/>"},"package-variant":{"body":"<path fill=\\"currentColor\\" d=\\"M2 10.96a.985.985 0 0 1-.37-1.37L3.13 7c.11-.2.28-.34.47-.42l7.83-4.4c.16-.12.36-.18.57-.18s.41.06.57.18l7.9 4.44c.**********.44.46l1.45 2.52c.28.48.11 1.09-.36 1.36l-1 .58v4.96c0 .38-.21.71-.53.88l-7.9 4.44c-.16.12-.36.18-.57.18s-.41-.06-.57-.18l-7.9-4.44A.99.99 0 0 1 3 16.5v-5.54c-.3.17-.68.18-1 0m10-6.81v6.7l5.96-3.35zM5 15.91l6 3.38v-6.71L5 9.21zm14 0v-3.22l-5 2.9c-.33.18-.7.17-1 .01v3.69zm-5.15-2.55l6.28-3.63l-.58-1.01l-6.28 3.63z\\"/>"},"receipt":{"body":"<path fill=\\"currentColor\\" d=\\"m3 22l1.5-1.5L6 22l1.5-1.5L9 22l1.5-1.5L12 22l1.5-1.5L15 22l1.5-1.5L18 22l1.5-1.5L21 22V2l-1.5 1.5L18 2l-1.5 1.5L15 2l-1.5 1.5L12 2l-1.5 1.5L9 2L7.5 3.5L6 2L4.5 3.5L3 2\\"/>"},"target":{"body":"<path fill=\\"currentColor\\" d=\\"M11 2v2.07A8 8 0 0 0 4.07 11H2v2h2.07A8 8 0 0 0 11 19.93V22h2v-2.07A8 8 0 0 0 19.93 13H22v-2h-2.07A8 8 0 0 0 13 4.07V2m-2 4.08V8h2V6.09c2.5.41 4.5 2.41 4.92 4.91H16v2h1.91c-.41 2.5-2.41 4.5-4.91 4.92V16h-2v1.91C8.5 17.5 6.5 15.5 6.08 13H8v-2H6.09C6.5 8.5 8.5 6.5 11 6.08M12 11a1 1 0 0 0-1 1a1 1 0 0 0 1 1a1 1 0 0 0 1-1a1 1 0 0 0-1-1\\"/>"},"calendar-time":{"body":"<path fill=\\"currentColor\\" d=\\"M15 13h1.5v2.82l2.44 1.41l-.75 1.3L15 16.69zm4-5H5v11h4.67c-.43-.91-.67-1.93-.67-3a7 7 0 0 1 7-7c1.07 0 2.09.24 3 .67zM5 21a2 2 0 0 1-2-2V5c0-1.11.89-2 2-2h1V1h2v2h8V1h2v2h1a2 2 0 0 1 2 2v6.1c1.24 1.26 2 2.99 2 4.9a7 7 0 0 1-7 7c-1.91 0-3.64-.76-4.9-2zm11-9.85A4.85 4.85 0 0 0 11.15 16c0 2.68 2.17 4.85 4.85 4.85A4.85 4.85 0 0 0 20.85 16c0-2.68-2.17-4.85-4.85-4.85\\"/>"},"user-card-details":{"body":"<path fill=\\"currentColor\\" d=\\"M2 3h20c1.05 0 2 .95 2 2v14c0 1.05-.95 2-2 2H2c-1.05 0-2-.95-2-2V5c0-1.05.95-2 2-2m12 3v1h8V6zm0 2v1h8V8zm0 2v1h7v-1zm-6 3.91C6 13.91 2 15 2 17v1h12v-1c0-2-4-3.09-6-3.09M8 6a3 3 0 0 0-3 3a3 3 0 0 0 3 3a3 3 0 0 0 3-3a3 3 0 0 0-3-3\\"/>"}}},{"prefix":"ic","width":24,"height":24,"icons":{"round-drag-indicator":{"body":"<path fill=\\"currentColor\\" d=\\"M11 18c0 1.1-.9 2-2 2s-2-.9-2-2s.9-2 2-2s2 .9 2 2m-2-8c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2m0-6c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2m6 4c1.1 0 2-.9 2-2s-.9-2-2-2s-2 .9-2 2s.9 2 2 2m0 2c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2m0 6c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2\\"/>"},"round-view-carousel":{"body":"<path fill=\\"currentColor\\" d=\\"M3 7h2c.55 0 1 .45 1 1v8c0 .55-.45 1-1 1H3c-.55 0-1-.45-1-1V8c0-.55.45-1 1-1m5 12h8c.55 0 1-.45 1-1V6c0-.55-.45-1-1-1H8c-.55 0-1 .45-1 1v12c0 .55.45 1 1 1M19 7h2c.55 0 1 .45 1 1v8c0 .55-.45 1-1 1h-2c-.55 0-1-.45-1-1V8c0-.55.45-1 1-1\\"/>"},"delete":{"body":"<path fill=\\"currentColor\\" d=\\"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6zM19 4h-3.5l-1-1h-5l-1 1H5v2h14z\\"/>"}}},{"prefix":"map","width":50,"height":50,"icons":{}},{"prefix":"fe","width":24,"height":24,"icons":{}},{"prefix":"vs","width":1792,"height":1792,"icons":{}},{"prefix":"la","width":32,"height":32,"icons":{}},{"prefix":"ri","width":24,"height":24,"icons":{}},{"prefix":"flag","width":512,"height":512,"icons":{}},{"prefix":"prime","width":24,"height":24,"icons":{}},{"prefix":"foundation","width":100,"height":100,"icons":{}},{"prefix":"bx","width":24,"height":24,"icons":{}},{"prefix":"bpmn","width":2048,"height":2048,"icons":{}},{"prefix":"memory","width":22,"height":22,"icons":{}},{"prefix":"fa","width":1536,"height":1536,"icons":{"500px":{"body":"<path fill=\\"currentColor\\" d=\\"m1401 1547l-6 6q-113 113-259 175q-154 64-317 64q-165 0-317-64q-148-63-259-175q-113-112-175-258q-42-103-54-189q-4-28 48-36q51-8 56 20q1 1 1 4q18 90 46 159q50 124 152 226q98 98 226 152q132 56 276 56q143 0 276-56q128-55 225-152l6-6q10-10 25-6q12 3 33 22q36 37 17 58M929 932l-66 66l63 63q21 21-7 49q-17 17-32 17q-10 0-19-10l-62-61l-66 66q-5 5-15 5q-15 0-31-16l-2-2q-18-15-18-29q0-7 8-17l66-65l-66-66q-16-16 14-45q18-18 31-18q6 0 13 5l65 66l65-65q18-17 48 13q27 27 11 44m471 57q0 118-46 228q-45 105-126 186q-80 80-187 126t-228 46t-228-46t-187-126q-82-82-125-186q-15-33-15-40h-1q-9-27 43-44q50-16 60 12q37 99 97 167h1V971q3-136 102-232q105-103 253-103q147 0 251 103t104 249q0 147-104.5 251T813 1343q-58 0-112-16q-28-11-13-61q16-51 44-43l14 3q14 3 33 6t30 3q104 0 176-71.5t72-174.5q0-101-72-171q-71-71-175-71q-107 0-178 80q-64 72-64 160v413q110 67 242 67q96 0 185-36.5t156-103.5t103.5-155t36.5-183q0-198-141-339q-140-140-339-140q-200 0-340 140q-53 53-77 87l-2 2q-8 11-13 15.5t-21.5 9.5t-38.5-3q-21-5-36.5-16.5T267 718V38q0-15 10.5-26.5T305 0h877q30 0 30 55t-30 55H371v483h1q40-42 102-84t108-61q109-46 231-46q121 0 228 46t187 126q81 81 126 186q46 112 46 229m-31-581q9 8 9 18t-5.5 18t-16.5 21q-26 26-39 26q-9 0-16-7q-106-91-207-133q-128-56-276-56q-133 0-262 49q-27 10-45-37q-9-25-8-38q3-16 16-20q130-57 299-57q164 0 316 64q137 58 235 152\\"/>","width":1408,"height":1792},"align-left":{"body":"<path fill=\\"currentColor\\" d=\\"M1792 1216v128q0 26-19 45t-45 19H64q-26 0-45-19t-19-45v-128q0-26 19-45t45-19h1664q26 0 45 19t19 45m-384-384v128q0 26-19 45t-45 19H64q-26 0-45-19T0 960V832q0-26 19-45t45-19h1280q26 0 45 19t19 45m256-384v128q0 26-19 45t-45 19H64q-26 0-45-19T0 576V448q0-26 19-45t45-19h1536q26 0 45 19t19 45M1280 64v128q0 26-19 45t-45 19H64q-26 0-45-19T0 192V64q0-26 19-45T64 0h1152q26 0 45 19t19 45\\"/>","width":1792,"height":1408},"calendar-times-o":{"body":"<path fill=\\"currentColor\\" d=\\"m1111 1385l-46 46q-9 9-22 9t-23-9l-188-189l-188 189q-10 9-23 9t-22-9l-46-46q-9-9-9-22t9-23l189-188l-189-188q-9-10-9-23t9-22l46-46q9-9 22-9t23 9l188 188l188-188q10-9 23-9t22 9l46 46q9 9 9 22t-9 23l-188 188l188 188q9 10 9 23t-9 22m-983 279h1408V640H128zM512 448V160q0-14-9-23t-23-9h-64q-14 0-23 9t-9 23v288q0 14 9 23t23 9h64q14 0 23-9t9-23m768 0V160q0-14-9-23t-23-9h-64q-14 0-23 9t-9 23v288q0 14 9 23t23 9h64q14 0 23-9t9-23m384-64v1280q0 52-38 90t-90 38H128q-52 0-90-38t-38-90V384q0-52 38-90t90-38h128v-96q0-66 47-113T416 0h64q66 0 113 47t47 113v96h384v-96q0-66 47-113t113-47h64q66 0 113 47t47 113v96h128q52 0 90 38t38 90\\"/>","width":1664,"height":1792},"compass":{"body":"<path fill=\\"currentColor\\" d=\\"m640 960l256-128l-256-128zm384-591v542l-512 256V625zm288 399q0-148-73-273t-198-198t-273-73t-273 73t-198 198t-73 273t73 273t198 198t273 73t273-73t198-198t73-273m224 0q0 209-103 385.5T1153.5 1433T768 1536t-385.5-103T103 1153.5T0 768t103-385.5T382.5 103T768 0t385.5 103T1433 382.5T1536 768\\"/>"},"info-circle":{"body":"<path fill=\\"currentColor\\" d=\\"M1024 1248v-160q0-14-9-23t-23-9h-96V544q0-14-9-23t-23-9H544q-14 0-23 9t-9 23v160q0 14 9 23t23 9h96v320h-96q-14 0-23 9t-9 23v160q0 14 9 23t23 9h448q14 0 23-9t9-23M896 352V192q0-14-9-23t-23-9H672q-14 0-23 9t-9 23v160q0 14 9 23t23 9h192q14 0 23-9t9-23m640 416q0 209-103 385.5T1153.5 1433T768 1536t-385.5-103T103 1153.5T0 768t103-385.5T382.5 103T768 0t385.5 103T1433 382.5T1536 768\\"/>"},"pencil-square":{"body":"<path fill=\\"currentColor\\" d=\\"m404 980l152 152l-52 52h-56v-96h-96v-56zm414-390q14 13-3 30L524 911q-17 17-30 3q-14-13 3-30l291-291q17-17 30-3m-274 690l544-544l-288-288l-544 544v288zm608-608l92-92q28-28 28-68t-28-68l-152-152q-28-28-68-28t-68 28l-92 92zm384-384v960q0 119-84.5 203.5T1248 1536H288q-119 0-203.5-84.5T0 1248V288Q0 169 84.5 84.5T288 0h960q119 0 203.5 84.5T1536 288\\"/>"}}},{"prefix":"radix-icons","width":15,"height":15,"icons":{"enter-full-screen":{"body":"<path fill=\\"currentColor\\" fill-rule=\\"evenodd\\" d=\\"M2 2.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1H3v2.5a.5.5 0 0 1-1 0zm7 0a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v3a.5.5 0 0 1-1 0V3H9.5a.5.5 0 0 1-.5-.5M2.5 9a.5.5 0 0 1 .5.5V12h2.5a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5v-3a.5.5 0 0 1 .5-.5m10 0a.5.5 0 0 1 .5.5v3a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1 0-1H12V9.5a.5.5 0 0 1 .5-.5\\" clip-rule=\\"evenodd\\"/>"},"exit-full-screen":{"body":"<path fill=\\"currentColor\\" fill-rule=\\"evenodd\\" d=\\"M5.5 2a.5.5 0 0 1 .5.5v3a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1 0-1H5V2.5a.5.5 0 0 1 .5-.5m4 0a.5.5 0 0 1 .5.5V5h2.5a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5v-3a.5.5 0 0 1 .5-.5M2 9.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v3a.5.5 0 0 1-1 0V10H2.5a.5.5 0 0 1-.5-.5m7 0a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1H10v2.5a.5.5 0 0 1-1 0z\\" clip-rule=\\"evenodd\\"/>"}}},{"prefix":"fa-solid","width":512,"height":512,"icons":{"angle-double-right":{"body":"<path fill=\\"currentColor\\" d=\\"m224.3 273l-136 136c-9.4 9.4-24.6 9.4-33.9 0l-22.6-22.6c-9.4-9.4-9.4-24.6 0-33.9l96.4-96.4l-96.4-96.4c-9.4-9.4-9.4-24.6 0-33.9L54.3 103c9.4-9.4 24.6-9.4 33.9 0l136 136c9.5 9.4 9.5 24.6.1 34m192-34l-136-136c-9.4-9.4-24.6-9.4-33.9 0l-22.6 22.6c-9.4 9.4-9.4 24.6 0 33.9l96.4 96.4l-96.4 96.4c-9.4 9.4-9.4 24.6 0 33.9l22.6 22.6c9.4 9.4 24.6 9.4 33.9 0l136-136c9.4-9.2 9.4-24.4 0-33.8\\"/>","width":448},"list":{"body":"<path fill=\\"currentColor\\" d=\\"M80 368H16a16 16 0 0 0-16 16v64a16 16 0 0 0 16 16h64a16 16 0 0 0 16-16v-64a16 16 0 0 0-16-16m0-320H16A16 16 0 0 0 0 64v64a16 16 0 0 0 16 16h64a16 16 0 0 0 16-16V64a16 16 0 0 0-16-16m0 160H16a16 16 0 0 0-16 16v64a16 16 0 0 0 16 16h64a16 16 0 0 0 16-16v-64a16 16 0 0 0-16-16m416 176H176a16 16 0 0 0-16 16v32a16 16 0 0 0 16 16h320a16 16 0 0 0 16-16v-32a16 16 0 0 0-16-16m0-320H176a16 16 0 0 0-16 16v32a16 16 0 0 0 16 16h320a16 16 0 0 0 16-16V80a16 16 0 0 0-16-16m0 160H176a16 16 0 0 0-16 16v32a16 16 0 0 0 16 16h320a16 16 0 0 0 16-16v-32a16 16 0 0 0-16-16\\"/>"},"running":{"body":"<path fill=\\"currentColor\\" d=\\"M272 96c26.51 0 48-21.49 48-48S298.51 0 272 0s-48 21.49-48 48s21.49 48 48 48M113.69 317.47l-14.8 34.52H32c-17.67 0-32 14.33-32 32s14.33 32 32 32h77.45c19.25 0 36.58-11.44 44.11-29.09l8.79-20.52l-10.67-6.3c-17.32-10.23-30.06-25.37-37.99-42.61M384 223.99h-44.03l-26.06-53.25c-12.5-25.55-35.45-44.23-61.78-50.94l-71.08-21.14c-28.3-6.8-57.77-.55-80.84 17.14l-39.67 30.41c-14.03 10.75-16.69 30.83-5.92 44.86s30.84 16.66 44.86 5.92l39.69-30.41c7.67-5.89 17.44-8 25.27-6.14l14.7 4.37l-37.46 87.39c-12.62 29.48-1.31 64.01 26.3 80.31l84.98 50.17l-27.47 87.73c-5.28 16.86 4.11 34.81 20.97 40.09c3.19 1 6.41 1.48 9.58 1.48c13.61 0 26.23-8.77 30.52-22.45l31.64-101.06c5.91-20.77-2.89-43.08-21.64-54.39l-61.24-36.14l31.31-78.28l20.27 41.43c8 16.34 24.92 26.89 43.11 26.89H384c17.67 0 32-14.33 32-32s-14.33-31.99-32-31.99\\"/>","width":416}}},{"prefix":"emojione-monotone","width":64,"height":64,"icons":{"crescent-moon":{"body":"<path fill=\\"currentColor\\" d=\\"M43.139 2a29.9 29.9 0 0 1 5.121 16.756c0 16.701-13.686 30.24-30.57 30.24a30.66 30.66 0 0 1-15.689-4.285C7.209 54.963 17.93 62 30.318 62C47.816 62 62 47.969 62 30.66C62 17.867 54.246 6.871 43.139 2\\"/>"},"sun":{"body":"<path fill=\\"currentColor\\" d=\\"m20.52 59.717l7.027-7.2a20.9 20.9 0 0 1-6.904-2.87zM43.48 4.284l-7.025 7.199a20.9 20.9 0 0 1 6.904 2.871zm-31.996 32.17l-7.201 7.025l10.07-.122a20.9 20.9 0 0 1-2.869-6.903m41.032-8.907l7.201-7.027l-10.07.123a20.9 20.9 0 0 1 2.869 6.904m-38.162-6.905l-10.07-.123l7.201 7.027a20.8 20.8 0 0 1 2.869-6.904m35.292 22.716l10.07.122l-7.201-7.026a20.8 20.8 0 0 1-2.869 6.904M27.547 11.483l-7.027-7.2l.123 10.07a20.9 20.9 0 0 1 6.904-2.87m8.906 41.034l7.027 7.199l-.123-10.069a20.9 20.9 0 0 1-6.904 2.87m-21.701-8.555l-3.967 9.251l9.252-3.965a21.1 21.1 0 0 1-5.285-5.286m34.496-23.923l3.965-9.252l-9.25 3.965a21.1 21.1 0 0 1 5.285 5.287M11 32c0-1.278.133-2.524.352-3.741L2 31.999l9.352 3.74A21 21 0 0 1 11 32m51 0l-9.352-3.741C52.867 29.476 53 30.722 53 32s-.133 2.525-.352 3.741zM20.039 14.751l-9.252-3.965l3.965 9.252a21.2 21.2 0 0 1 5.287-5.287m23.922 34.497l9.252 3.965l-3.965-9.251a21.1 21.1 0 0 1-5.287 5.286M35.74 11.352L32 2l-3.74 9.352C29.475 11.133 30.721 11 32 11s2.525.133 3.74.352m-7.48 41.296L32 62l3.74-9.352c-1.215.219-2.461.352-3.74.352s-2.525-.133-3.74-.352\\"/><circle cx=\\"32\\" cy=\\"32\\" r=\\"19\\" fill=\\"currentColor\\"/>"}}},{"prefix":"ant-design","width":1024,"height":1024,"icons":{"align-center-outlined":{"body":"<path fill=\\"currentColor\\" d=\\"M264 230h496c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H264c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8m496 424c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H264c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm144 140H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8m0-424H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8\\"/>"},"align-left-outlined":{"body":"<path fill=\\"currentColor\\" d=\\"M120 230h496c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8m0 424h496c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8m784 140H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8m0-424H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8\\"/>"},"alipay-circle-filled":{"body":"<path fill=\\"currentColor\\" fill-rule=\\"evenodd\\" d=\\"M512 64c247.424 0 448 200.576 448 448S759.424 960 512 960S64 759.424 64 512S264.576 64 512 64m32.493 168c-69.66 0-86.056 16.843-86.709 39.079l-.02 1.426v46.623H291.45c-9.92 0-14.28 23.053-14.27 39.31c0 2.696 2.08 4.923 4.77 4.923h175.814v58.301h-116.5c-9.96 0-14.3 23.76-14.271 39.473a4.77 4.77 0 0 0 4.77 4.76l233.448.003c-4.527 41.056-15.432 77.58-30.716 109.315l-1.224 2.494l-.32-.275c-60.244-28.47-120.431-52.577-194.407-52.577l-2.61.017c-84.982 1.112-144.718 56.503-145.916 127.04l-.018 1.222l.019 2.123c1.238 70.399 63.566 126.452 148.525 126.452c61.245-.008 116.372-16.85 163.457-45.017a139 139 0 0 0 14.068-7.962c18.09-12.116 34.892-25.955 50.304-41.156l9.452 6.344l12.456 8.322c57.527 38.257 113.763 72.617 169.856 79.27a143 143 0 0 0 18.314 1.157c43.017 0 54.991-52.68 57.387-95.508l.145-2.84c.392-8.463-6.197-15.595-14.648-15.863c-75.468-2.365-136.452-22.043-192.008-46.11l-6.267-2.742c35.146-56.8 56.657-121.816 57.155-186.661l.082-1.083c.401-5.515-3.997-10.198-9.52-10.198H549.33v-58.301h165.732c9.92 0 14.28-22.117 14.27-39.311c-.01-2.686-2.089-4.922-4.779-4.922H549.32v-82.35c0-2.656-2.175-4.778-4.827-4.778m-216.5 351.847c54.627 0 107.073 22.417 158.09 52.19l5.77 3.402c-103.575 119.837-247.172 95.903-261.724 26.37a67 67 0 0 1-1.138-9.83l-.057-2.336l.013-.907c.969-40.113 45.337-68.89 99.045-68.89\\"/>"},"cloud-upload-outlined":{"body":"<path fill=\\"currentColor\\" d=\\"M518.3 459a8 8 0 0 0-12.6 0l-112 141.7a7.98 7.98 0 0 0 6.3 12.9h73.9V856c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V613.7H624c6.7 0 10.4-7.7 6.3-12.9z\\"/><path fill=\\"currentColor\\" d=\\"M811.4 366.7C765.6 245.9 648.9 160 512.2 160S258.8 245.8 213 366.6C127.3 389.1 64 467.2 64 560c0 110.5 89.5 200 199.9 200H304c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8h-40.1c-33.7 0-65.4-13.4-89-37.7c-23.5-24.2-36-56.8-34.9-90.6c.9-26.4 9.9-51.2 26.2-72.1c16.7-21.3 40.1-36.8 66.1-43.7l37.9-9.9l13.9-36.6c8.6-22.8 20.6-44.1 35.7-63.4a245.6 245.6 0 0 1 52.4-49.9c41.1-28.9 89.5-44.2 140-44.2s98.9 15.3 140 44.2c19.9 14 37.5 30.8 52.4 49.9c15.1 19.3 27.1 40.7 35.7 63.4l13.8 36.5l37.8 10C846.1 454.5 884 503.8 884 560c0 33.1-12.9 64.3-36.3 87.7a123.07 123.07 0 0 1-87.6 36.3H720c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h40.1C870.5 760 960 670.5 960 560c0-92.7-63.1-170.7-148.6-193.3\\"/>"},"dingtalk-circle-filled":{"body":"<path fill=\\"currentColor\\" d=\\"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448s448-200.6 448-448S759.4 64 512 64m227 385.3c-1 4.2-3.5 10.4-7 17.8h.1l-.4.7c-20.3 43.1-73.1 127.7-73.1 127.7s-.1-.2-.3-.5l-15.5 26.8h74.5L575.1 810l32.3-128h-58.6l20.4-84.7c-16.5 3.9-35.9 9.4-59 16.8c0 0-31.2 18.2-89.9-35c0 0-39.6-34.7-16.6-43.4c9.8-3.7 47.4-8.4 77-12.3c40-5.4 64.6-8.2 64.6-8.2S422 517 392.7 512.5c-29.3-4.6-66.4-53.1-74.3-95.8c0 0-12.2-23.4 26.3-12.3s197.9 43.2 197.9 43.2s-207.4-63.3-221.2-78.7s-40.6-84.2-37.1-126.5c0 0 1.5-10.5 12.4-7.7c0 0 153.3 69.7 258.1 107.9c104.8 37.9 195.9 57.3 184.2 106.7\\"/>"},"github-filled":{"body":"<path fill=\\"currentColor\\" d=\\"M511.6 76.3C264.3 76.2 64 276.4 64 523.5C64 718.9 189.3 885 363.8 946c23.5 5.9 19.9-10.8 19.9-22.2v-77.5c-135.7 15.9-141.2-73.9-150.3-88.9C215 726 171.5 718 184.5 703c30.9-15.9 62.4 4 98.9 57.9c26.4 39.1 77.9 32.5 104 26c5.7-23.5 17.9-44.5 34.7-60.8c-140.6-25.2-199.2-111-199.2-213c0-49.5 16.3-95 48.3-131.7c-20.4-60.5 1.9-112.3 4.9-120c58.1-5.2 118.5 41.6 123.2 45.3c33-8.9 70.7-13.6 112.9-13.6c42.4 0 80.2 4.9 113.5 13.9c11.3-8.6 67.3-48.8 121.3-43.9c2.9 7.7 24.7 58.3 5.5 118c32.4 36.8 48.9 82.7 48.9 132.3c0 102.2-59 188.1-200 212.9a127.5 127.5 0 0 1 38.1 91v112.5c.8 9 0 17.9 15 17.9c177.1-59.7 304.6-227 304.6-424.1c0-247.2-200.4-447.3-447.5-447.3\\"/>"},"reload-outlined":{"body":"<path fill=\\"currentColor\\" d=\\"m909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92C290 92 102.3 279.5 102 511.5C101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1c1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 0 0-10.1 4.8c-1.8 5-3.8 10-5.9 14.9c-17.3 41-42.1 77.8-73.7 109.4A344.8 344.8 0 0 1 655.9 829c-42.3 17.9-87.4 27-133.8 27c-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 0 1 279 755.2a342.2 342.2 0 0 1-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4s68.4-56.4 109.3-73.8c42.3-17.9 87.4-27 133.8-27c46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 0 1 109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 0 0 3 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2\\"/>"},"rotate-left-outlined":{"body":"<path fill=\\"currentColor\\" d=\\"M672 418H144c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32m-44 402H188V494h440z\\"/><path fill=\\"currentColor\\" d=\\"M819.3 328.5c-78.8-100.7-196-153.6-314.6-154.2l-.2-64c0-6.5-7.6-10.1-12.6-6.1l-128 101c-4 3.1-3.9 9.1 0 12.3L492 318.6c5.1 4 12.7.4 12.6-6.1v-63.9c12.9.1 25.9.9 38.8 2.5c42.1 5.2 82.1 18.2 119 38.7c38.1 21.2 71.2 49.7 98.4 84.3c27.1 34.7 46.7 73.7 58.1 115.8c11 40.7 14 82.7 8.9 124.8c-.7 5.4-1.4 10.8-2.4 16.1h74.9c14.8-103.6-11.3-213-81-302.3\\"/>"},"rotate-right-outlined":{"body":"<path fill=\\"currentColor\\" d=\\"M480.5 251.2c13-1.6 25.9-2.4 38.8-2.5v63.9c0 6.5 7.5 10.1 12.6 6.1L660 217.6c4-3.2 4-9.2 0-12.3l-128-101c-5.1-4-12.6-.4-12.6 6.1l-.2 64c-118.6.5-235.8 53.4-314.6 154.2c-69.6 89.2-95.7 198.6-81.1 302.4h74.9c-.9-5.3-1.7-10.7-2.4-16.1c-5.1-42.1-2.1-84.1 8.9-124.8c11.4-42.2 31-81.1 58.1-115.8c27.2-34.7 60.3-63.2 98.4-84.3c37-20.6 76.9-33.6 119.1-38.8\\"/><path fill=\\"currentColor\\" d=\\"M880 418H352c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32m-44 402H396V494h440z\\"/>"},"scan-outlined":{"body":"<path fill=\\"currentColor\\" d=\\"M136 384h56c4.4 0 8-3.6 8-8V200h176c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H196c-37.6 0-68 30.4-68 68v180c0 4.4 3.6 8 8 8m512-184h176v176c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V196c0-37.6-30.4-68-68-68H648c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8M376 824H200V648c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v180c0 37.6 30.4 68 68 68h180c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8m512-184h-56c-4.4 0-8 3.6-8 8v176H648c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h180c37.6 0 68-30.4 68-68V648c0-4.4-3.6-8-8-8m16-164H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8\\"/>"},"upload-outlined":{"body":"<path fill=\\"currentColor\\" d=\\"M400 317.7h73.9V656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V317.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 163a8 8 0 0 0-12.6 0l-112 141.7c-4.1 5.3-.4 13 6.3 13M878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8\\"/>"},"wechat-filled":{"body":"<path fill=\\"currentColor\\" d=\\"M690.1 377.4c5.9 0 11.8.2 17.6.5c-24.4-128.7-158.3-227.1-319.9-227.1C209 150.8 64 271.4 64 420.2c0 81.1 43.6 154.2 111.9 203.6a21.5 21.5 0 0 1 9.1 17.6c0 2.4-.5 4.6-1.1 6.9c-5.5 20.3-14.2 52.8-14.6 54.3c-.7 2.6-1.7 5.2-1.7 7.9c0 5.9 4.8 10.8 10.8 10.8c2.3 0 4.2-.9 6.2-2l70.9-40.9c5.3-3.1 11-5 17.2-5c3.2 0 6.4.5 9.5 1.4c33.1 9.5 68.8 14.8 105.7 14.8c6 0 11.9-.1 17.8-.4c-7.1-21-10.9-43.1-10.9-66c0-135.8 132.2-245.8 295.3-245.8m-194.3-86.5c23.8 0 43.2 19.3 43.2 43.1s-19.3 43.1-43.2 43.1c-23.8 0-43.2-19.3-43.2-43.1s19.4-43.1 43.2-43.1m-215.9 86.2c-23.8 0-43.2-19.3-43.2-43.1s19.3-43.1 43.2-43.1s43.2 19.3 43.2 43.1s-19.4 43.1-43.2 43.1m586.8 415.6c56.9-41.2 93.2-102 93.2-169.7c0-124-120.8-224.5-269.9-224.5c-149 0-269.9 100.5-269.9 224.5S540.9 847.5 690 847.5c30.8 0 60.6-4.4 88.1-12.3c2.6-.8 5.2-1.2 7.9-1.2c5.2 0 9.9 1.6 14.3 4.1l59.1 34c1.7 1 3.3 1.7 5.2 1.7a9 9 0 0 0 6.4-2.6a9 9 0 0 0 2.6-6.4c0-2.2-.9-4.4-1.4-6.6c-.3-1.2-7.6-28.3-12.2-45.3c-.5-1.9-.9-3.8-.9-5.7c.1-5.9 3.1-11.2 7.6-14.5M600.2 587.2c-19.9 0-36-16.1-36-35.9s16.1-35.9 36-35.9s36 16.1 36 35.9s-16.2 35.9-36 35.9m179.9 0c-19.9 0-36-16.1-36-35.9s16.1-35.9 36-35.9s36 16.1 36 35.9a36.08 36.08 0 0 1-36 35.9\\"/>"},"zoom-in-outlined":{"body":"<path fill=\\"currentColor\\" d=\\"M637 443H519V309c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v134H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h118v134c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V519h118c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8m284 424L775 721c122.1-148.9 113.6-369.5-26-509c-148-148.1-388.4-148.1-537 0c-148.1 148.6-148.1 389 0 537c139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11M696 696c-118.8 118.7-311.2 118.7-430 0c-118.7-118.8-118.7-311.2 0-430c118.8-118.7 311.2-118.7 430 0c118.7 118.8 118.7 311.2 0 430\\"/>"},"zoom-out-outlined":{"body":"<path fill=\\"currentColor\\" d=\\"M637 443H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h312c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8m284 424L775 721c122.1-148.9 113.6-369.5-26-509c-148-148.1-388.4-148.1-537 0c-148.1 148.6-148.1 389 0 537c139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11M696 696c-118.8 118.7-311.2 118.7-430 0c-118.7-118.8-118.7-311.2 0-430c118.8-118.7 311.2-118.7 430 0c118.7 118.8 118.7 311.2 0 430\\"/>"}}},{"prefix":"vaadin","icons":{"arrows-long-h":{"body":"<path fill=\\"currentColor\\" d=\\"m16 8l-3-3v2H3V5L0 8l3 3V9h10v2z\\"/>"},"arrows-long-v":{"body":"<path fill=\\"currentColor\\" d=\\"M9 3h2L8 0L5 3h2v10H5l3 3l3-3H9z\\"/>"},"line-h":{"body":"<path fill=\\"currentColor\\" d=\\"M0 7h16v1H0z\\"/>"},"padding":{"body":"<path fill=\\"currentColor\\" d=\\"M0 0v16h16V0zm15 3h-1v1h1v1h-1v1h1v1h-1v1h1v1h-1v1h1v1h-1v1h1v1h-1v1h1v1h-1v-1h-1v1h-1v-1h-1v1h-1v-1H9v1H8v-1H7v1H6v-1H5v1H4v-1H3v1H2v-1H1v-1h1v-1H1v-1h1v-1H1V9h1V8H1V7h1V6H1V5h1V4H1V3h1V2H1V1h1v1h1V1h1v1h1V1h1v1h1V1h1v1h1V1h1v1h1V1h1v1h1V1h1v1h1z\\"/><path fill=\\"currentColor\\" d=\\"M3 2h1v1H3zm1 1h1v1H4zm2 0h1v1H6zM5 2h1v1H5zm2 0h1v1H7zm2 0h1v1H9zM8 3h1v1H8zm2 0h1v1h-1zm2 0h1v1h-1zm-1-1h1v1h-1zm2 0h1v1h-1zm-1 3h1v1h-1zm1-1h1v1h-1zm-1 3h1v1h-1zm1-1h1v1h-1zm-1 3h1v1h-1zm1-1h1v1h-1zm-1 3h1v1h-1zm1-1h1v1h-1zm-1 3h1v1h-1zm1-1h1v1h-1zM2 3h1v1H2zm1 1h1v1H3zM2 5h1v1H2zm1 1h1v1H3zM2 7h1v1H2zm1 1h1v1H3zM2 9h1v1H2zm1 1h1v1H3zm-1 1h1v1H2zm0 2h1v1H2zm1-1h1v1H3zm1-1h1v1H4zm0 2h1v1H4zm1-1h1v1H5zm1 1h1v1H6zm1-1h1v1H7zm2 0h1v1H9zm-1 1h1v1H8zm3-1h1v1h-1zm-1 1h1v1h-1z\\"/>"}}},{"prefix":"fontisto","width":24,"height":24,"icons":{"email":{"body":"<path fill=\\"currentColor\\" d=\\"m16.484 11.976l6.151-5.344v10.627zm-7.926.905l2.16 1.875c.339.288.781.462 1.264.462h.017h-.001h.014c.484 0 .926-.175 1.269-.465l-.003.002l2.16-1.875l6.566 5.639H1.995zM1.986 5.365h20.03l-9.621 8.356a.6.6 0 0 1-.38.132h-.014h.001h-.014a.6.6 0 0 1-.381-.133l.001.001zm-.621 1.266l6.15 5.344l-6.15 5.28zm21.6-2.441c-.24-.12-.522-.19-.821-.19H1.859a1.9 1.9 0 0 0-.835.197l.011-.005A1.86 1.86 0 0 0 0 5.855v12.172a1.86 1.86 0 0 0 1.858 1.858h20.283a1.86 1.86 0 0 0 1.858-1.858V5.855c0-.727-.419-1.357-1.029-1.66l-.011-.005z\\"/>"}}},{"prefix":"carbon","width":32,"height":32,"icons":{"popup":{"body":"<path fill=\\"currentColor\\" d=\\"M28 4H10a2.006 2.006 0 0 0-2 2v14a2.006 2.006 0 0 0 2 2h18a2.006 2.006 0 0 0 2-2V6a2.006 2.006 0 0 0-2-2m0 16H10V6h18Z\\"/><path fill=\\"currentColor\\" d=\\"M18 26H4V16h2v-2H4a2.006 2.006 0 0 0-2 2v10a2.006 2.006 0 0 0 2 2h14a2.006 2.006 0 0 0 2-2v-2h-2Z\\"/>"},"tree-view-alt":{"body":"<path fill=\\"currentColor\\" d=\\"M23 9.005h6a2 2 0 0 0 2-2v-4a2 2 0 0 0-2-2h-6a2 2 0 0 0-2 2v1H11v-1a2 2 0 0 0-2-2H3a2 2 0 0 0-2 2v4a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2v-1h4v20a2 2 0 0 0 2 2h4v1a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2v-4a2 2 0 0 0-2-2h-6a2 2 0 0 0-2 2v1h-4v-9h4v1a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2v-4a2 2 0 0 0-2-2h-6a2 2 0 0 0-2 2v1h-4v-9h4v1a2 2 0 0 0 2 2m0-6h6v4h-6Zm-14 4H3v-4h6Zm14 18h6v4h-6Zm0-11h6v4h-6Z\\"/>"}}},{"prefix":"icon-park-outline","width":48,"height":48,"icons":{"peoples":{"body":"<path fill=\\"none\\" stroke=\\"currentColor\\" stroke-linecap=\\"round\\" stroke-linejoin=\\"round\\" stroke-width=\\"4\\" d=\\"M19 20a7 7 0 1 0 0-14a7 7 0 0 0 0 14M32.608 7A7 7 0 0 1 36 13a7 7 0 0 1-3.392 6M4 40.8V42h30v-1.2c0-4.48 0-6.72-.872-8.432a8 8 0 0 0-3.496-3.496C27.92 28 25.68 28 21.2 28h-4.4c-4.48 0-6.72 0-8.432.872a8 8 0 0 0-3.496 3.496C4 34.08 4 36.32 4 40.8M44 42v-1.2c0-4.48 0-6.72-.872-8.432a8 8 0 0 0-3.496-3.496\\"/>"}}},{"prefix":"ci","width":24,"height":24,"icons":{}},{"prefix":"system-uicons","width":21,"height":21,"icons":{"carousel":{"body":"<path fill=\\"none\\" stroke=\\"currentColor\\" stroke-linecap=\\"round\\" stroke-linejoin=\\"round\\" d=\\"M14.5 5.5h-8a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2v-6a2 2 0 0 0-2-2m4 0v10m-16-10v10\\"/>"},"reset-alt":{"body":"<g fill=\\"none\\" fill-rule=\\"evenodd\\" stroke=\\"currentColor\\" stroke-linecap=\\"round\\" stroke-linejoin=\\"round\\"><path d=\\"M14.5 3.5c2.414 1.377 4 4.022 4 7a8 8 0 1 1-8-8\\"/><path d=\\"M14.5 7.5v-4h4\\"/></g>"}}},{"prefix":"heroicons-outline","width":24,"height":24,"icons":{"archive-box-x-mark":{"body":"<path fill=\\"none\\" stroke=\\"currentColor\\" stroke-linecap=\\"round\\" stroke-linejoin=\\"round\\" stroke-width=\\"1.5\\" d=\\"m20.25 7.5l-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125l2.25 2.25m0 0l2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\\"/>","hidden":true}}},{"prefix":"tdesign","width":24,"height":24,"icons":{"component-divider-vertical":{"body":"<path fill=\\"currentColor\\" d=\\"M5 2h14v7H5zm2 2v3h10V4zm-5 7h20v2H2zm3 4h14v7H5zm2 2v3h10v-3z\\"/>"},"image":{"body":"<path fill=\\"currentColor\\" d=\\"M2 2h20v20H2zm2 18h13.586L9 11.414l-5 5zm16-.414V4H4v9.586l5-5zM15.547 7a1 1 0 1 0 0 2a1 1 0 0 0 0-2m-3 1a3 3 0 1 1 6 0a3 3 0 0 1-6 0\\"/>"},"qrcode":{"body":"<path fill=\\"currentColor\\" d=\\"M2 2h9v9H2zm2 2v5h5V4zm9-2h9v9h-9zm2 2v5h5V4zM5.5 5.5h2.004v2.004H5.5zm11 0h2.004v2.004H16.5zm-3.504 7.496H15V15h-2.004zm7 0H22V15h-2.004zM2 13h9v9H2zm2 2v5h5v-5zm11.996.996H18v2h2v2h2V22h-2.004v-2h-2v-2h-2zM5.5 16.5h2.004v2.004H5.5zm7.496 3.496H15V22h-2.004z\\"/>"}}},{"prefix":"fluent","width":20,"height":20,"icons":{"table-bottom-row-16-filled":{"body":"<path fill=\\"currentColor\\" d=\\"M4.5 2A2.5 2.5 0 0 0 2 4.5V10h1V4.5A1.5 1.5 0 0 1 4.5 3h7A1.5 1.5 0 0 1 13 4.5V10h1V4.5A2.5 2.5 0 0 0 11.5 2zM3 13.5a2.5 2.5 0 0 1-1-2V11h3v3h-.5a2.5 2.5 0 0 1-1.5-.5M13.95 12q.05-.243.05-.5V11h-3v3h.5a2.5 2.5 0 0 0 2.45-2M6 14h4v-3H6z\\"/>","width":16,"height":16},"text-column-one-24-filled":{"body":"<path fill=\\"currentColor\\" d=\\"M5 6a1 1 0 0 1 1-1h12a1 1 0 1 1 0 2H6a1 1 0 0 1-1-1m0 4a1 1 0 0 1 1-1h12a1 1 0 1 1 0 2H6a1 1 0 0 1-1-1m0 4a1 1 0 0 1 1-1h12a1 1 0 1 1 0 2H6a1 1 0 0 1-1-1m0 4a1 1 0 0 1 1-1h12a1 1 0 1 1 0 2H6a1 1 0 0 1-1-1\\"/>","width":24,"height":24},"text-column-three-24-filled":{"body":"<path fill=\\"currentColor\\" d=\\"M3 6a1 1 0 0 1 1-1h3a1 1 0 0 1 0 2H4a1 1 0 0 1-1-1m6.58 0a1 1 0 0 1 1-1h3a1 1 0 1 1 0 2h-3a1 1 0 0 1-1-1M16 6a1 1 0 0 1 1-1h3a1 1 0 1 1 0 2h-3a1 1 0 0 1-1-1M3 10a1 1 0 0 1 1-1h3a1 1 0 0 1 0 2H4a1 1 0 0 1-1-1m6.58 0a1 1 0 0 1 1-1h3a1 1 0 1 1 0 2h-3a1 1 0 0 1-1-1M16 10a1 1 0 0 1 1-1h3a1 1 0 1 1 0 2h-3a1 1 0 0 1-1-1M3 14a1 1 0 0 1 1-1h3a1 1 0 1 1 0 2H4a1 1 0 0 1-1-1m6.58 0a1 1 0 0 1 1-1h3a1 1 0 1 1 0 2h-3a1 1 0 0 1-1-1M16 14a1 1 0 0 1 1-1h3a1 1 0 1 1 0 2h-3a1 1 0 0 1-1-1M3 18a1 1 0 0 1 1-1h3a1 1 0 1 1 0 2H4a1 1 0 0 1-1-1m6.58 0a1 1 0 0 1 1-1h3a1 1 0 1 1 0 2h-3a1 1 0 0 1-1-1M16 18a1 1 0 0 1 1-1h3a1 1 0 1 1 0 2h-3a1 1 0 0 1-1-1\\"/>","width":24,"height":24},"text-column-two-24-filled":{"body":"<path fill=\\"currentColor\\" d=\\"M3 6a1 1 0 0 1 1-1h6a1 1 0 1 1 0 2H4a1 1 0 0 1-1-1m0 4a1 1 0 0 1 1-1h6a1 1 0 1 1 0 2H4a1 1 0 0 1-1-1m0 4a1 1 0 0 1 1-1h6a1 1 0 1 1 0 2H4a1 1 0 0 1-1-1m0 4a1 1 0 0 1 1-1h6a1 1 0 1 1 0 2H4a1 1 0 0 1-1-1M13 6a1 1 0 0 1 1-1h6a1 1 0 1 1 0 2h-6a1 1 0 0 1-1-1m0 4a1 1 0 0 1 1-1h6a1 1 0 1 1 0 2h-6a1 1 0 0 1-1-1m0 4a1 1 0 0 1 1-1h6a1 1 0 1 1 0 2h-6a1 1 0 0 1-1-1m0 4a1 1 0 0 1 1-1h6a1 1 0 1 1 0 2h-6a1 1 0 0 1-1-1\\"/>","width":24,"height":24},"text-column-two-left-24-filled":{"body":"<path fill=\\"currentColor\\" d=\\"M21 6a1 1 0 0 0-1-1h-9a1 1 0 1 0 0 2h9a1 1 0 0 0 1-1M8 6a1 1 0 0 0-1-1H4a1 1 0 0 0 0 2h3a1 1 0 0 0 1-1m13 4a1 1 0 0 0-1-1h-9a1 1 0 1 0 0 2h9a1 1 0 0 0 1-1M8 10a1 1 0 0 0-1-1H4a1 1 0 0 0 0 2h3a1 1 0 0 0 1-1m13 4a1 1 0 0 0-1-1h-9a1 1 0 1 0 0 2h9a1 1 0 0 0 1-1M8 14a1 1 0 0 0-1-1H4a1 1 0 1 0 0 2h3a1 1 0 0 0 1-1m13 4a1 1 0 0 0-1-1h-9a1 1 0 1 0 0 2h9a1 1 0 0 0 1-1M8 18a1 1 0 0 0-1-1H4a1 1 0 1 0 0 2h3a1 1 0 0 0 1-1\\"/>","width":24,"height":24}}},{"prefix":"tabler","width":24,"height":24,"icons":{"box-padding":{"body":"<path fill=\\"none\\" stroke=\\"currentColor\\" stroke-linecap=\\"round\\" stroke-linejoin=\\"round\\" stroke-width=\\"2\\" d=\\"M4 6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2zm4 10v.01M8 12v.01M8 8v.01M16 16v.01M16 12v.01M16 8v.01M12 8v.01M12 16v.01\\"/>"},"float-right":{"body":"<path fill=\\"none\\" stroke=\\"currentColor\\" stroke-linecap=\\"round\\" stroke-linejoin=\\"round\\" stroke-width=\\"2\\" d=\\"M14 6a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zM4 7h6m-6 4h6m-6 4h16M4 19h16\\"/>"},"hand-click":{"body":"<g fill=\\"none\\" stroke=\\"currentColor\\" stroke-linecap=\\"round\\" stroke-linejoin=\\"round\\" stroke-width=\\"2\\"><path d=\\"M8 13V4.5a1.5 1.5 0 0 1 3 0V12m0-.5v-2a1.5 1.5 0 0 1 3 0V12m0-1.5a1.5 1.5 0 0 1 3 0V12\\"/><path d=\\"M17 11.5a1.5 1.5 0 0 1 3 0V16a6 6 0 0 1-6 6h-2h.208a6 6 0 0 1-5.012-2.7L7 19q-.468-.718-3.286-5.728a1.5 1.5 0 0 1 .536-2.022a1.87 1.87 0 0 1 2.28.28L8 13M5 3L4 2m0 5H3m11-4l1-1m0 4h1\\"/></g>"},"input-search":{"body":"<path fill=\\"none\\" stroke=\\"currentColor\\" stroke-linecap=\\"round\\" stroke-linejoin=\\"round\\" stroke-width=\\"2\\" d=\\"M20 11V9a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h5m4 2a3 3 0 1 0 6 0a3 3 0 1 0-6 0m5.2 2.2L22 22\\"/>"},"layout-navbar":{"body":"<path fill=\\"none\\" stroke=\\"currentColor\\" stroke-linecap=\\"round\\" stroke-linejoin=\\"round\\" stroke-width=\\"2\\" d=\\"M4 6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2zm0 3h16\\"/>"},"line-dashed":{"body":"<path fill=\\"none\\" stroke=\\"currentColor\\" stroke-linecap=\\"round\\" stroke-linejoin=\\"round\\" stroke-width=\\"2\\" d=\\"M5 12h2m10 0h2m-8 0h2\\"/>"},"line-dotted":{"body":"<path fill=\\"none\\" stroke=\\"currentColor\\" stroke-linecap=\\"round\\" stroke-linejoin=\\"round\\" stroke-width=\\"2\\" d=\\"M4 12v.01M8 12v.01m4-.01v.01m4-.01v.01m4-.01v.01\\"/>"},"volume":{"body":"<path fill=\\"none\\" stroke=\\"currentColor\\" stroke-linecap=\\"round\\" stroke-linejoin=\\"round\\" stroke-width=\\"2\\" d=\\"M15 8a5 5 0 0 1 0 8m2.7-11a9 9 0 0 1 0 14M6 15H4a1 1 0 0 1-1-1v-4a1 1 0 0 1 1-1h2l3.5-4.5A.8.8 0 0 1 11 5v14a.8.8 0 0 1-1.5.5z\\"/>"},"volume-off":{"body":"<path fill=\\"none\\" stroke=\\"currentColor\\" stroke-linecap=\\"round\\" stroke-linejoin=\\"round\\" stroke-width=\\"2\\" d=\\"M15 8a5 5 0 0 1 1.912 4.934m-1.377 2.602A5 5 0 0 1 15 16m2.7-11a9 9 0 0 1 2.362 11.086m-1.676 2.299A9 9 0 0 1 17.7 19M9.069 5.054L9.5 4.5A.8.8 0 0 1 11 5v2m0 4v8a.8.8 0 0 1-1.5.5L6 15H4a1 1 0 0 1-1-1v-4a1 1 0 0 1 1-1h2l1.294-1.664M3 3l18 18\\"/>"}}},{"prefix":"entypo","width":20,"height":20,"icons":{"progress-empty":{"body":"<path fill=\\"currentColor\\" d=\\"M18 5H2C.9 5 0 5.9 0 7v6c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2m0 8H2V7h16z\\"/>"}}},{"prefix":"bi","icons":{"columns":{"body":"<path fill=\\"currentColor\\" d=\\"M0 2a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H1a1 1 0 0 1-1-1zm8.5 0v8H15V2zm0 9v3H15v-3zm-1-9H1v3h6.5zM1 14h6.5V6H1z\\"/>"},"grid-3x2-gap":{"body":"<path fill=\\"currentColor\\" d=\\"M4 4v2H2V4zm1 7V9a1 1 0 0 0-1-1H2a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1m0-5V4a1 1 0 0 0-1-1H2a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1m5 5V9a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1m0-5V4a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1M9 4v2H7V4zm5 0h-2v2h2zM4 9v2H2V9zm5 0v2H7V9zm5 0v2h-2V9zm-3-5a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1zm1 4a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1V9a1 1 0 0 0-1-1z\\"/>"},"grid-3x3-gap":{"body":"<path fill=\\"currentColor\\" d=\\"M4 2v2H2V2zm1 12v-2a1 1 0 0 0-1-1H2a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1m0-5V7a1 1 0 0 0-1-1H2a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1m0-5V2a1 1 0 0 0-1-1H2a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1m5 10v-2a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1m0-5V7a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1m0-5V2a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1M9 2v2H7V2zm5 0v2h-2V2zM4 7v2H2V7zm5 0v2H7V7zm5 0h-2v2h2zM4 12v2H2v-2zm5 0v2H7v-2zm5 0v2h-2v-2zM12 1a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1zm-1 6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1zm1 4a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1z\\"/>"}}},{"prefix":"iconoir","width":24,"height":24,"icons":{"input-search":{"body":"<path fill=\\"none\\" stroke=\\"currentColor\\" stroke-linecap=\\"round\\" stroke-linejoin=\\"round\\" stroke-width=\\"1.5\\" d=\\"M21 12v-2a5 5 0 0 0-5-5H8a5 5 0 0 0-5 5v0a5 5 0 0 0 5 5h4m8.124 4.119a3 3 0 1 0-4.248-4.237a3 3 0 0 0 4.248 4.237m0 0L22 21\\"/>"}}},{"prefix":"icon-park-twotone","width":48,"height":48,"icons":{"theme":{"body":"<defs><mask id=\\"ipTTheme0\\"><path fill=\\"#555\\" stroke=\\"#fff\\" stroke-linejoin=\\"round\\" stroke-width=\\"4\\" d=\\"M18 6a6 6 0 0 0 12 0h5.455L42 15.818l-5.727 4.91V42H11.727V20.727L6 15.818L12.546 6z\\"/></mask></defs><path fill=\\"currentColor\\" d=\\"M0 0h48v48H0z\\" mask=\\"url(#ipTTheme0)\\"/>"}}},{"prefix":"material-symbols","width":24,"height":24,"icons":{"line-start":{"body":"<path fill=\\"currentColor\\" d=\\"M4.5 14.5q-1.05 0-1.775-.725T2 12t.725-1.775T4.5 9.5q.775 0 1.4.425T6.8 11H22v2H6.8q-.275.65-.9 1.075t-1.4.425\\"/>"},"stop":{"body":"<path fill=\\"currentColor\\" d=\\"M6 18V6h12v12z\\"/>"}}},{"prefix":"majesticons","width":24,"height":24,"icons":{"back-circle":{"body":"<path fill=\\"currentColor\\" fill-rule=\\"evenodd\\" d=\\"M23 12c0-6.075-4.925-11-11-11S1 5.925 1 12s4.925 11 11 11s11-4.925 11-11m-7.496-4.868A1 1 0 0 1 17 8v8a1 1 0 0 1-1.496.868L9 13.152V16a1 1 0 1 1-2 0V8a1 1 0 1 1 2 0v2.848zM15 9.723L11.016 12L15 14.277z\\" clip-rule=\\"evenodd\\"/>"},"next-circle":{"body":"<path fill=\\"currentColor\\" fill-rule=\\"evenodd\\" d=\\"M1 12C1 5.925 5.925 1 12 1s11 4.925 11 11s-4.925 11-11 11S1 18.075 1 12m6.498-4.865a1 1 0 0 1 .998-.003L15 10.848V8a1 1 0 1 1 2 0v8a1 1 0 1 1-2 0v-2.848l-6.504 3.716A1 1 0 0 1 7 16V8a1 1 0 0 1 .498-.865\\" clip-rule=\\"evenodd\\"/>"}}},{"prefix":"solar","width":24,"height":24,"icons":{"pause-circle-bold":{"body":"<path fill=\\"currentColor\\" fill-rule=\\"evenodd\\" d=\\"M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2S2 6.477 2 12s4.477 10 10 10M8.076 8.617C8 8.801 8 9.034 8 9.5v5c0 .466 0 .699.076.883a1 1 0 0 0 .541.54c.184.077.417.077.883.077s.699 0 .883-.076a1 1 0 0 0 .54-.541c.077-.184.077-.417.077-.883v-5c0-.466 0-.699-.076-.883a1 1 0 0 0-.541-.54C10.199 8 9.966 8 9.5 8s-.699 0-.883.076a1 1 0 0 0-.54.541m5 0C13 8.801 13 9.034 13 9.5v5c0 .466 0 .699.076.883a1 1 0 0 0 .541.54c.184.077.417.077.883.077s.699 0 .883-.076a1 1 0 0 0 .54-.541c.077-.184.077-.417.077-.883v-5c0-.466 0-.699-.076-.883a1 1 0 0 0-.541-.54C15.199 8 14.966 8 14.5 8s-.699 0-.883.076a1 1 0 0 0-.54.541\\" clip-rule=\\"evenodd\\"/>"}}}]').forEach(e=>Xl.addCollection(e));let Ql,e1,t1;Ql="v",e1="el",t1={namespace:Ql,elNamespace:e1},Ja=()=>{const e=t1;return{variables:e,getPrefixCls:t=>`${e.namespace}-${t}`}};var X0=Object.defineProperty,Q0=Object.defineProperties,ei=Object.getOwnPropertyDescriptors,a1=Object.getOwnPropertySymbols,ti=Object.prototype.hasOwnProperty,ai=Object.prototype.propertyIsEnumerable,l1=(e,t,l)=>t in e?X0(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,li=(e,t)=>{for(var l in t||(t={}))ti.call(t,l)&&l1(e,l,t[l]);if(a1)for(var l of a1(t))ai.call(t,l)&&l1(e,l,t[l]);return e},ri=(e,t)=>Q0(e,ei(t)),ii=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});let r1,i1,o1;r1=["xlink:href"],i1=["data-icon"],qt=Ie(ri(li({},{name:"Icon"}),{__name:"Icon",props:{icon:Le.string,color:Le.string,size:Le.number.def(16),svgClass:Le.string.def("")},setup(e){const{getPrefixCls:t}=Ja(),l=t("icon"),n=e,s=p(null),m=Ye(()=>{var g;return(g=n.icon)==null?void 0:g.startsWith("svg-icon:")}),f=Ye(()=>r(m)?`#icon-${n.icon.split("svg-icon:")[1]}`:n.icon),c=Ye(()=>{const{color:g,size:M}=n;return{fontSize:`${M}px`,height:"1em",color:g}}),o=Ye(()=>{const{svgClass:g}=n;return`iconify ${g}`}),u=g=>ii(this,null,function*(){if(r(m))return;const M=r(s);if(!M||(yield Ue(),!g))return;const C=Xl.renderSVG(g,{});if(C)M.textContent="",M.appendChild(C);else{const W=document.createElement("span");W.className="iconify",W.dataset.icon=g,M.textContent="",M.appendChild(W)}});return Yt(()=>n.icon,g=>{u(g)}),(g,M)=>{const C=Rd;return h(),N(C,{class:ke(r(l)),color:e.color,size:e.size},{default:i(()=>[r(m)?(h(),j("svg",{key:0,class:ke(r(o)),"aria-hidden":"true"},[x("use",{"xlink:href":r(f)},null,8,r1)],2)):(h(),j("span",{key:1,ref_key:"elRef",ref:s,class:ke(g.$attrs.class),style:Ua(r(c))},[x("span",{class:ke(r(o)),"data-icon":r(f)},null,10,i1)],6))]),_:1},8,["class","color","size"])}}})),qe=(e,t)=>{const l=e.__vccOpts||e;for(const[n,s]of t)l[n]=s;return l},o1=e=>e.replace(/([A-Z])/g,"-$1").toLowerCase(),t0=e=>e?e.replace(/\-(\w)/g,(t,l)=>l.toUpperCase()):"",Kl=(e,t,l=document.documentElement)=>{l.style.setProperty(e,t)},U0=(e,t)=>{if(e.findIndex)return e.findIndex(t);let l=-1;return e.some((n,s,m)=>{const f=t(n,s,m);if(f)return l=s,f}),l},n0=()=>{if(typeof crypto=="object"){if(typeof crypto.randomUUID=="function")return crypto.randomUUID();if(typeof crypto.getRandomValues=="function"&&typeof Uint8Array=="function"){const l=n=>{const s=Number(n);return(s^crypto.getRandomValues(new Uint8Array(1))[0]&15>>s/4).toString(16)};return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,l)}}let e=new Date().getTime(),t=typeof performance<"u"&&performance.now&&performance.now()*1e3||0;return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,l=>{let n=Math.random()*16;return e>0?(n=(e+n)%16|0,e=Math.floor(e/16)):(n=(t+n)%16|0,t=Math.floor(t/16)),(l==="x"?n:n&3|8).toString(16)})},i0=function(e){try{return JSON.parse(e)}catch{return""}};let n1,ha,d1,s1,Xa,c1,Qa,el,u1,p1,f1,m1,Ze,va,Pa,tl,al,ll;n1=Object.prototype.toString,ha=(e,t)=>n1.call(e)===`[object ${t}]`,d1=e=>e!==null&&ha(e,"Object"),r0=e=>e===null?!0:Xa(e)||Ga(e)?e.length===0:e instanceof Map||e instanceof Set?e.size===0:d1(e)?Object.keys(e).length===0:!1,Ea=e=>ha(e,"Number"),Ga=e=>ha(e,"String"),Y0=e=>typeof e=="function",s1=e=>ha(e,"Boolean"),Xa=e=>e&&Array.isArray(e),c1=typeof window>"u",R0=!c1,Wa=e=>/(((^https?:(?:\/\/)?)(?:[-:&=\+\$,\w]+@)?[A-Za-z0-9.-]+(?::\d+)?|(?:www.|[-:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&%@.\w_]*)#?(?:[\w]*))?)$/.test(e),E0=()=>window.matchMedia("(prefers-color-scheme: dark)").matches,Qa={},el={base_url:Qa.env.VITE_BASE_URL+Qa.env.VITE_API_URL,result_code:200,request_timeout:12e4,default_headers:"application/json"},u1=`MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdH
nzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==`,p1=`MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAqhHyZfSsYourNxaY
7Nt+PrgrxkiA50efORdI5U5lsW79MmFnusUA355oaSXcLhu5xxB38SMSyP2KvuKN
PuH3owIDAQABAkAfoiLyL+Z4lf4Myxk6xUDgLaWGximj20CUf+5BKKnlrK+Ed8gA
kM0HqoTt2UZwA5E2MzS4EI2gjfQhz5X28uqxAiEA3wNFxfrCZlSZHb0gn2zDpWow
cSxQAgiCstxGUoOqlW8CIQDDOerGKH5OmCJ4Z21v+F25WaHYPxCFMvwxpcw99Ecv
DQIgIdhDTIqD2jfYjPTY8Jj3EDGPbH2HHuffvflECt3Ek60CIQCFRlCkHpi7hthh
YhovyloRYsM+IS9h/0BzlEAuO0ktMQIgSPT3aFAgJYwKpqRYKlLDVcflZFCKY7u3
UP8iWi1Qw0Y=`,f1=e=>{const t=new Wr;return t.setPublicKey(u1),t.encrypt(e)},m1=e=>{const t=new Wr;return t.setPrivateKey(p1),t.decrypt(e)},{wsCache:Ze}=Lt(),va="ACCESS_TOKEN",Pa="REFRESH_TOKEN",ta=()=>Ze.get(va)?Ze.get(va):Ze.get("ACCESS_TOKEN"),tl=()=>Ze.get(Pa),Yl=e=>{Ze.set(Pa,e.refreshToken),Ze.set(va,e.accessToken)},al=()=>{Ze.delete(va),Ze.delete(Pa)},B0=()=>{const e=Ze.get(Ee.LoginForm);return e&&(e.password=m1(e.password)),e},Z0=e=>{e.password=f1(e.password),Ze.set(Ee.LoginForm,e,{exp:30*24*60*60})},J0=()=>{Ze.delete(Ee.LoginForm)},Za=()=>Ze.get(Ee.TenantId),W0=e=>{Ze.set(Ee.TenantId,e)},ll={401:"\u8BA4\u8BC1\u5931\u8D25\uFF0C\u65E0\u6CD5\u8BBF\u95EE\u7CFB\u7EDF\u8D44\u6E90",403:"\u5F53\u524D\u64CD\u4F5C\u6CA1\u6709\u6743\u9650",404:"\u8BBF\u95EE\u8D44\u6E90\u4E0D\u5B58\u5728",default:"\u7CFB\u7EDF\u672A\u77E5\u9519\u8BEF\uFF0C\u8BF7\u53CD\u9988\u7ED9\u7BA1\u7406\u5458"};var oi=Object.defineProperty,ni=Object.defineProperties,di=Object.getOwnPropertyDescriptors,Sa=Object.getOwnPropertySymbols,h1=Object.prototype.hasOwnProperty,v1=Object.prototype.propertyIsEnumerable,y1=(e,t,l)=>t in e?oi(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,si=(e,t)=>{for(var l in t||(t={}))h1.call(t,l)&&y1(e,l,t[l]);if(Sa)for(var l of Sa(t))v1.call(t,l)&&y1(e,l,t[l]);return e},ci=(e,t)=>ni(e,di(t)),ui=(e,t)=>{var l={};for(var n in e)h1.call(e,n)&&t.indexOf(n)<0&&(l[n]=e[n]);if(e!=null&&Sa)for(var n of Sa(e))t.indexOf(n)<0&&v1.call(e,n)&&(l[n]=e[n]);return l};let g1,aa,Qe,rl,b1,_1,w1,il,Ia,la,C1;g1={},aa=g1.glob("../views/**/*.{vue,tsx}"),v0=e=>{for(const t in aa)if(t.includes(e))return Qd(aa[t])},Qe=()=>Ae(()=>import("./Layout-M-2S9EFf.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21])),o0=e=>{if(!e)return e;const t=e,{matched:l}=t,n=ui(t,["matched"]);return ci(si({},n),{matched:l?l.map(s=>({meta:s.meta,name:s.name,path:s.path})):void 0})},Zl=e=>{const t=[],l=Object.keys(aa);for(const n of e){const s={title:n.name,icon:n.icon,hidden:!n.visible,noCache:!n.keepAlive,alwaysShow:n.children&&n.children.length===1&&(n.alwaysShow!==void 0?n.alwaysShow:!0)};if(n.component&&n.component.indexOf("?")>-1){const f=n.component.split("?")[1];n.component=n.component.split("?")[0],s.query=Al.parse(f)}let m={path:n.path.indexOf("?")>-1?n.path.split("?")[0]:n.path,name:n.componentName&&n.componentName.length>0?n.componentName:Ia(n.path),redirect:n.redirect,meta:s};if(!n.children&&n.parentId==0&&n.component){m.component=Qe,m.meta={},m.name=Ia(n.path)+"Parent",m.redirect="",s.alwaysShow=!0;const f={path:"",name:n.componentName&&n.componentName.length>0?n.componentName:Ia(n.path),redirect:n.redirect,meta:s},c=n!=null&&n.component?l.findIndex(o=>o.includes(n.component)):l.findIndex(o=>o.includes(n.path));f.component=aa[l[c]],m.children=[f]}else{if(n.children)m.component=Qe,m.redirect=rl(n.path,n.children);else if(Wa(n.path))m={path:"/external-link",component:Qe,meta:{name:n.name},children:[m]};else{const f=n!=null&&n.component?l.findIndex(c=>c.includes(n.component)):l.findIndex(c=>c.includes(n.path));m.component=aa[l[f]]}n.children&&(m.children=Zl(n.children))}t.push(m)}return t},rl=(e,t)=>{if(!t||t.length==0)return e;const l=b1(e,t[0].path);if(t[0].children)return rl(l,t[0].children)},b1=(e,t)=>(e.endsWith("/")&&(e=e.slice(0,-1)),t.startsWith("/")||(t="/"+t),e+t),H0=(e,t)=>{if(Wa(t))return t;const l=t!=null&&t.startsWith("/")||!t?t:`/${t}`;return`${e}${l}`.replace(/\/\//g,"/")},N0=e=>{const t=us(e);for(let l=0;l<t.length;l++){const n=t[l];_1(n)&&w1(n)}return t},_1=e=>{var t,l;if(!e||!Reflect.has(e,"children")||!((t=e.children)!=null&&t.length))return!1;const n=e.children;let s=!1;for(let m=0;m<n.length;m++)if((l=n[m].children)!=null&&l.length){s=!0;break}return s},w1=e=>{var t;let l=qr({routes:[e],history:es()});const n=l.getRoutes();il(n,e.children||[],e),l=null,e.children=(t=e.children)==null?void 0:t.map(s=>ps(s,"children"))},il=(e,t,l)=>{var n,s;for(let m=0;m<t.length;m++){const f=t[m],c=e.find(o=>o.name===f.name);c&&(l.children=l.children||[],l.children.find(o=>o.name===c.name)||(n=l.children)==null||n.push(c),(s=f.children)!=null&&s.length&&il(e,f.children,l))}},Ia=(e,t)=>(e=(e||"").replace(/-(.)/g,function(l){return l.toUpperCase()}).replaceAll("-",""),e&&(e=e.charAt(0).toUpperCase()+e.slice(1)),e),{t:la}=Zt(),Jl=[{path:"/redirect",component:Qe,name:"Redirect",children:[{path:"/redirect/:path(.*)",name:"Redirect",component:()=>Ae(()=>import("./views-Redirect-nATt_sRM.js"),__vite__mapDeps([22,2,3,4,1,7,5,6])),meta:{}}],meta:{hidden:!0,noTagsView:!0}},{path:"/",component:Qe,redirect:"/index",name:"Home",meta:{},children:[{path:"index",component:()=>Ae(()=>Promise.resolve().then(()=>Dd),void 0),name:"Index",meta:{title:la("router.home"),icon:"ep:odometer",noCache:!0,affix:!0}}]},{path:"/user",component:Qe,name:"UserInfo",meta:{hidden:!0},children:[{path:"profile",component:()=>Ae(()=>import("./views-Profile-4epX3JDT.js").then(e=>e.I),__vite__mapDeps([12,1,2,3,4,5,6,7,8,9,10,13])),name:"Profile",meta:{canTo:!0,hidden:!0,noTagsView:!1,icon:"ep:user",title:la("common.profile")}},{path:"notify-message",component:()=>Ae(()=>import("./views-system-BZfQ0tqU.js").then(e=>e.i),__vite__mapDeps([15,1,2,3,4,5,6,7,11,12,8,9,10,13,14])),name:"MyNotifyMessage",meta:{canTo:!0,hidden:!0,noTagsView:!1,icon:"ep:message",title:"\u6211\u7684\u7AD9\u5185\u4FE1"}}]},{path:"/dict",component:Qe,name:"dict",meta:{hidden:!0},children:[{path:"type/data/:dictType",component:()=>Ae(()=>import("./views-system-BZfQ0tqU.js").then(e=>e.b),__vite__mapDeps([15,1,2,3,4,5,6,7,11,12,8,9,10,13,14])),name:"SystemDictData",meta:{title:"\u5B57\u5178\u6570\u636E",noCache:!0,hidden:!0,canTo:!0,icon:"",activeMenu:"/system/dict"}}]},{path:"/codegen",component:Qe,name:"CodegenEdit",meta:{hidden:!0},children:[{path:"edit",component:()=>Ae(()=>import("./views-infra-lxM1FTkM.js").then(e=>e.E),__vite__mapDeps([23,1,2,3,4,5,6,7,11,12,8,9,10,13,14])),name:"InfraCodegenEditTable",meta:{noCache:!0,hidden:!0,canTo:!0,icon:"ep:edit",title:"\u4FEE\u6539\u751F\u6210\u914D\u7F6E",activeMenu:"infra/codegen/index"}}]},{path:"/job",component:Qe,name:"JobL",meta:{hidden:!0},children:[{path:"job-log",component:()=>Ae(()=>import("./views-infra-lxM1FTkM.js").then(e=>e.i),__vite__mapDeps([23,1,2,3,4,5,6,7,11,12,8,9,10,13,14])),name:"InfraJobLog",meta:{noCache:!0,hidden:!0,canTo:!0,icon:"ep:edit",title:"\u8C03\u5EA6\u65E5\u5FD7",activeMenu:"infra/job/index"}}]},{path:"/login",component:()=>Ae(()=>import("./views-Login-BCX8kkKD.js").then(async e=>(await e.__tla,e)).then(e=>e.L),__vite__mapDeps([8,2,3,4,1,7,5,6,9,10])),name:"Login",meta:{hidden:!0,title:la("router.login"),noTagsView:!0}},{path:"/sso",component:()=>Ae(()=>import("./views-Login-BCX8kkKD.js").then(async e=>(await e.__tla,e)).then(e=>e.L),__vite__mapDeps([8,2,3,4,1,7,5,6,9,10])),name:"SSOLogin",meta:{hidden:!0,title:la("router.login"),noTagsView:!0}},{path:"/social-login",component:()=>Ae(()=>import("./views-Login-BCX8kkKD.js").then(async e=>(await e.__tla,e)).then(e=>e.S),__vite__mapDeps([8,2,3,4,1,7,5,6,9,10])),name:"SocialLogin",meta:{hidden:!0,title:la("router.socialLogin"),noTagsView:!0}},{path:"/403",component:()=>Ae(()=>import("./views-Error-Cx8xxY17.js").then(async e=>(await e.__tla,e)).then(e=>e.j),__vite__mapDeps([9,2,3,4,1,7,5,6])),name:"NoAccess",meta:{hidden:!0,title:"403",noTagsView:!0}},{path:"/404",component:()=>Ae(()=>import("./views-Error-Cx8xxY17.js").then(async e=>(await e.__tla,e)).then(e=>e.k),__vite__mapDeps([9,2,3,4,1,7,5,6])),name:"NoFound",meta:{hidden:!0,title:"404",noTagsView:!0}},{path:"/500",component:()=>Ae(()=>import("./views-Error-Cx8xxY17.js").then(async e=>(await e.__tla,e)).then(e=>e.l),__vite__mapDeps([9,2,3,4,1,7,5,6])),name:"Error",meta:{hidden:!0,title:"500",noTagsView:!0}},{path:"/bpm",component:Qe,name:"bpm",meta:{hidden:!0},children:[{path:"manager/form/edit",component:()=>Ae(()=>import("./views-bpm-BO-XbtTX.js").then(e=>e.i),__vite__mapDeps([11,2,3,4,1,7,5,6,12,8,9,10,13,14])),name:"BpmFormEditor",meta:{noCache:!0,hidden:!0,canTo:!0,title:"\u8BBE\u8BA1\u6D41\u7A0B\u8868\u5355",activeMenu:"/bpm/manager/form"}},{path:"manager/model/edit",component:()=>Ae(()=>import("./views-bpm-BO-XbtTX.js").then(e=>e.b),__vite__mapDeps([11,2,3,4,1,7,5,6,12,8,9,10,13,14])),name:"BpmModelEditor",meta:{noCache:!0,hidden:!0,canTo:!0,title:"\u8BBE\u8BA1\u6D41\u7A0B",activeMenu:"/bpm/manager/model"}},{path:"manager/simple/workflow/model/edit",component:()=>Ae(()=>import("./views-bpm-BO-XbtTX.js").then(e=>e.c),__vite__mapDeps([11,2,3,4,1,7,5,6,12,8,9,10,13,14])),name:"SimpleWorkflowDesignEditor",meta:{noCache:!0,hidden:!0,canTo:!0,title:"\u4EFF\u9489\u9489\u8BBE\u8BA1\u6D41\u7A0B",activeMenu:"/bpm/manager/model"}},{path:"manager/definition",component:()=>Ae(()=>import("./views-bpm-BO-XbtTX.js").then(e=>e.d),__vite__mapDeps([11,2,3,4,1,7,5,6,12,8,9,10,13,14])),name:"BpmProcessDefinition",meta:{noCache:!0,hidden:!0,canTo:!0,title:"\u6D41\u7A0B\u5B9A\u4E49",activeMenu:"/bpm/manager/model"}},{path:"process-instance/detail",component:()=>Ae(()=>import("./views-bpm-BO-XbtTX.js").then(e=>e.e),__vite__mapDeps([11,2,3,4,1,7,5,6,12,8,9,10,13,14])),name:"BpmProcessInstanceDetail",meta:{noCache:!0,hidden:!0,canTo:!0,title:"\u6D41\u7A0B\u8BE6\u60C5",activeMenu:"/bpm/task/my"}},{path:"oa/leave/create",component:()=>Ae(()=>import("./views-bpm-BO-XbtTX.js").then(e=>e.f),__vite__mapDeps([11,2,3,4,1,7,5,6,12,8,9,10,13,14])),name:"OALeaveCreate",meta:{noCache:!0,hidden:!0,canTo:!0,title:"\u53D1\u8D77 OA \u8BF7\u5047",activeMenu:"/bpm/oa/leave"}},{path:"oa/leave/detail",component:()=>Ae(()=>import("./views-bpm-BO-XbtTX.js").then(e=>e.h),__vite__mapDeps([11,2,3,4,1,7,5,6,12,8,9,10,13,14])),name:"OALeaveDetail",meta:{noCache:!0,hidden:!0,canTo:!0,title:"\u67E5\u770B OA \u8BF7\u5047",activeMenu:"/bpm/oa/leave"}}]},{path:"/ai",component:Qe,name:"Ai",meta:{hidden:!0},children:[{path:"image/square",component:()=>Ae(()=>import("./views-ai-f0kajhtl.js"),__vite__mapDeps([24,1,2,3,4,5,6,7,9,17,18,19,25])),name:"AiImageSquare",meta:{title:"\u7ED8\u56FE\u4F5C\u54C1",icon:"ep:home-filled",noCache:!1}}]},{path:"/management-center",component:Qe,name:"ManagementCenter",meta:{hidden:!0},children:[{path:"DrawingEncoder",component:()=>Ae(()=>import("./views-project-C9zQgjvz.js").then(e=>e.E),__vite__mapDeps([17,9,2,3,4,1,7,5,6,18])),name:"DrawingEncoder",meta:{title:"\u56FE\u53F7\u7533\u8BF7",icon:"ep:home-filled",noCache:!1}}]},{path:"/:pathMatch(.*)*",component:()=>Ae(()=>import("./views-Error-Cx8xxY17.js").then(async e=>(await e.__tla,e)).then(e=>e.k),__vite__mapDeps([9,2,3,4,1,7,5,6])),name:"",meta:{title:"404",hidden:!0,breadcrumb:!1}}],pa=qr({history:ts(),strict:!0,routes:Jl,scrollBehavior:()=>({left:0,top:0})}),C1=()=>{const e=["Redirect","Login","NoFind","Root"];pa.getRoutes().forEach(t=>{const{name:l}=t;l&&!e.includes(l)&&pa.hasRoute(l)&&pa.removeRoute(l)})},I0=e=>{e.use(pa)};var x1=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});let k1,ol,M1,nl,T1,V1;k1={},ol=k1.env.VITE_APP_TENANT_ENABLE,{result_code:M1,base_url:nl,request_timeout:T1}=el,V1=["\u65E0\u6548\u7684\u5237\u65B0\u4EE4\u724C","\u5237\u65B0\u4EE4\u724C\u5DF2\u8FC7\u671F"],La={show:!1};let ya=[],dl=!1;const pi=["/login","/refresh-token"],ga=Ol.create({baseURL:nl,timeout:T1,withCredentials:!1});ga.interceptors.request.use(e=>{var t,l;let n=(e.headers||{}).isToken===!1;if(pi.some(f=>{if(e.url)return e.url.indexOf(f)>-1,n=!1}),ta()&&!n&&(e.headers.Authorization="Bearer "+ta()),ol&&ol==="true"){const f=Za();f&&(e.headers["tenant-id"]=f)}const s=e.params||{},m=e.data||!1;if(((t=e.method)==null?void 0:t.toUpperCase())==="POST"&&e.headers["Content-Type"]==="application/x-www-form-urlencoded"&&(e.data=Al.stringify(m)),((l=e.method)==null?void 0:l.toUpperCase())==="GET"&&s){e.params={};const f=Al.stringify(s,{allowDots:!0});f&&(e.url=e.url+"?"+f)}return e},e=>Promise.reject(e)),ga.interceptors.response.use(e=>x1(void 0,null,function*(){let{data:t}=e;const l=e.config;if(!t)throw new Error;const{t:n}=Zt();if(e.request.responseType==="blob"||e.request.responseType==="arraybuffer"){if(e.data.type!=="application/json")return e.data;t=yield new Response(e.data).json()}const s=t.code||M1,m=t.msg||ll[s]||ll.default;if(V1.indexOf(m)!==-1)return Promise.reject(m);if(s===401){if(dl)return new Promise(f=>{ya.push(()=>{l.headers.Authorization="Bearer "+ta(),f(ga(l))})});if(dl=!0,!tl())return sl();try{const f=yield fi();return Yl((yield f).data.data),l.headers.Authorization="Bearer "+ta(),ya.forEach(c=>{c()}),ya=[],ga(l)}catch{return ya.forEach(f=>{f()}),sl()}finally{ya=[],dl=!1}}else return s===500?(Nt.error(n("sys.api.errMsg500")),Promise.reject(new Error(m))):s===901?(Nt.error({offset:300,dangerouslyUseHTMLString:!0,message:"<div>"+n("sys.api.errMsg901")+"</div><div> &nbsp; </div><div>\u53C2\u8003 https://doc.iocoder.cn/ \u6559\u7A0B</div><div> &nbsp; </div><div>5 \u5206\u949F\u642D\u5EFA\u672C\u5730\u73AF\u5883</div>"}),Promise.reject(new Error(m))):s!==200?m==="\u65E0\u6548\u7684\u5237\u65B0\u4EE4\u724C"?sl():(xa.error({title:m}),Promise.reject("error")):t}),e=>{let{message:t}=e;const{t:l}=Zt();return t==="Network Error"?t=l("sys.api.errorMessage"):t.includes("timeout")?t=l("sys.api.apiTimeoutMessage"):t.includes("Request failed with status code")&&(t=l("sys.api.apiRequestFailed")+t.substr(t.length-3)),Nt.error(t),Promise.reject(e)});const fi=()=>x1(void 0,null,function*(){return Ol.defaults.headers.common["tenant-id"]=Za(),yield Ol.post(nl+"/system/auth/refresh-token?refreshToken="+tl())}),sl=()=>{const{t:e}=Zt();if(!La.show){if(window.location.href.includes("login?redirect="))return;La.show=!0,Vt.confirm(e("sys.api.timeoutMessage"),e("common.confirmTitle"),{showCancelButton:!1,closeOnClickModal:!1,showClose:!1,confirmButtonText:e("login.relogin"),type:"warning"}).then(()=>{C1(),Fr(),al(),La.show=!1,window.location.href=window.location.href})}return Promise.reject(e("sys.api.timeoutMessage"))};var mi=Object.defineProperty,hi=Object.defineProperties,vi=Object.getOwnPropertyDescriptors,Aa=Object.getOwnPropertySymbols,z1=Object.prototype.hasOwnProperty,L1=Object.prototype.propertyIsEnumerable,E1=(e,t,l)=>t in e?mi(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,wt=(e,t)=>{for(var l in t||(t={}))z1.call(t,l)&&E1(e,l,t[l]);if(Aa)for(var l of Aa(t))L1.call(t,l)&&E1(e,l,t[l]);return e},yi=(e,t)=>hi(e,vi(t)),gi=(e,t)=>{var l={};for(var n in e)z1.call(e,n)&&t.indexOf(n)<0&&(l[n]=e[n]);if(e!=null&&Aa)for(var n of Aa(e))t.indexOf(n)<0&&L1.call(e,n)&&(l[n]=e[n]);return l},It=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});let P1,Ct,S1,cl;({default_headers:P1}=el),Ct=e=>{const t=e,{url:l,method:n,params:s,data:m,headersType:f,responseType:c}=t,o=gi(t,["url","method","params","data","headersType","responseType"]);return ga(yi(wt({url:l,method:n,params:s,data:m},o),{responseType:c,headers:{"Content-Type":f||P1}}))},v={get:e=>It(void 0,null,function*(){return(yield Ct(wt({method:"GET"},e))).data}),post:e=>It(void 0,null,function*(){return(yield Ct(wt({method:"POST"},e))).data}),postOriginal:e=>It(void 0,null,function*(){return yield Ct(wt({method:"POST"},e))}),delete:e=>It(void 0,null,function*(){return(yield Ct(wt({method:"DELETE"},e))).data}),put:e=>It(void 0,null,function*(){return(yield Ct(wt({method:"PUT"},e))).data}),download:e=>It(void 0,null,function*(){return yield Ct(wt({method:"GET",responseType:"blob"},e))}),downloadPost:e=>It(void 0,null,function*(){return yield Ct(wt({method:"POST",responseType:"blob"},e))}),upload:e=>It(void 0,null,function*(){return e.headersType="multipart/form-data",yield Ct(wt({method:"POST"},e))})},O0=e=>v.delete({url:"/infra/file/delete?id="+e}),S1=e=>v.get({url:"/infra/file/get?id="+e}),D0=e=>v.get({url:"/infra/file/presigned-url",params:{path:e}}),j0=e=>v.post({url:"/infra/file/create",data:e}),F0=e=>v.upload({url:"/infra/file/upload",data:e}),St=()=>{const{t:e}=Zt();return{info(t){Nt.info(t)},error(t){Nt.error(t)},success(t){Nt.success(t)},warning(t){Nt.warning(t)},alert(t){Vt.alert(t,e("common.confirmTitle"))},alertError(t){Vt.alert(t,e("common.confirmTitle"),{type:"error"})},alertSuccess(t){Vt.alert(t,e("common.confirmTitle"),{type:"success"})},alertWarning(t){Vt.alert(t,e("common.confirmTitle"),{type:"warning"})},notify(t){xa.info(t)},notifyError(t){xa.error(t)},notifySuccess(t){xa.success(t)},notifyWarning(t){xa.warning(t)},confirm(t,l){return Vt.confirm(t,l||e("common.confirmTitle"),{confirmButtonText:e("common.ok"),cancelButtonText:e("common.cancel"),type:"warning"})},delConfirm(t,l){return Vt.confirm(t||e("common.delMessage"),l||e("common.confirmTitle"),{confirmButtonText:e("common.ok"),cancelButtonText:e("common.cancel"),type:"warning"})},exportConfirm(t,l){return Vt.confirm(t||e("common.exportMessage"),l||e("common.confirmTitle"),{confirmButtonText:e("common.ok"),cancelButtonText:e("common.cancel"),type:"warning"})},prompt(t,l){return Vt.prompt(t,l,{confirmButtonText:e("common.ok"),cancelButtonText:e("common.cancel"),type:"warning"})}}},cl=()=>v.get({url:"/system/dict-data/simple-list"}),V0=e=>v.get({url:"/system/dict-data/page",params:e}),k0=e=>v.get({url:"/system/dict-data/get?id="+e}),M0=e=>v.post({url:"/system/dict-data/create",data:e}),T0=e=>v.put({url:"/system/dict-data/update",data:e}),z0=e=>v.delete({url:"/system/dict-data/delete?id="+e}),L0=e=>v.download({url:"/system/dict-data/export",params:e});var I1=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});let ra,A1;({wsCache:ra}=Lt("sessionStorage")),A1=Hl("dict",{state:()=>({dictMap:new Map,isSetDict:!1}),getters:{getDictMap(){const e=ra.get(Ee.DICT_CACHE);return e&&(this.dictMap=e),this.dictMap},getIsSetDict(){return this.isSetDict}},actions:{setDictMap(){return I1(this,null,function*(){const e=ra.get(Ee.DICT_CACHE);if(e)this.dictMap=e,this.isSetDict=!0;else{const t=yield cl(),l=new Map;t.forEach(n=>{l[n.dictType]||(l[n.dictType]=[]),l[n.dictType].push({value:n.value,label:n.label,colorType:n.colorType,cssClass:n.cssClass})}),this.dictMap=l,this.isSetDict=!0,ra.set(Ee.DICT_CACHE,l,{exp:60})}})},getDictByType(e){return this.isSetDict||this.setDictMap(),this.dictMap[e]},resetDict(){return I1(this,null,function*(){ra.delete(Ee.DICT_CACHE);const e=yield cl(),t=new Map;e.forEach(l=>{t[l.dictType]||(t[l.dictType]=[]),t[l.dictType].push({value:l.value,label:l.label,colorType:l.colorType,cssClass:l.cssClass})}),this.dictMap=t,this.isSetDict=!0,ra.set(Ee.DICT_CACHE,t,{exp:60})})}}}),Wl=()=>A1(Il);var bi=Object.defineProperty,_i=Object.defineProperties,wi=Object.getOwnPropertyDescriptors,H1=Object.getOwnPropertySymbols,Ci=Object.prototype.hasOwnProperty,xi=Object.prototype.propertyIsEnumerable,j1=(e,t,l)=>t in e?bi(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,ul=(e,t)=>{for(var l in t||(t={}))Ci.call(t,l)&&j1(e,l,t[l]);if(H1)for(var l of H1(t))xi.call(t,l)&&j1(e,l,t[l]);return e},pl=(e,t)=>_i(e,wi(t));let O1,ia,et;O1=Wl(),ia=e=>O1.getDictByType(e)||[],Ka=e=>{const t=ia(e),l=[];return t.forEach(n=>{l.push(pl(ul({},n),{value:parseInt(n.value+"")}))}),l},Pt=e=>{const t=ia(e),l=[];return t.forEach(n=>{l.push(pl(ul({},n),{value:n.value+""}))}),l},x0=e=>{const t=[];return ia(e).forEach(l=>{t.push(pl(ul({},l),{value:l.value+""=="true"}))}),t},et=(e,t)=>{const l=ia(e),n=p("");return l.forEach(s=>{s.value===t+""&&(n.value=s.label)}),n.value},Qt=(e=>(e.USER_TYPE="user_type",e.COMMON_STATUS="common_status",e.TERMINAL="terminal",e.DATE_INTERVAL="date_interval",e.SYSTEM_USER_SEX="system_user_sex",e.SYSTEM_MENU_TYPE="system_menu_type",e.SYSTEM_ROLE_TYPE="system_role_type",e.SYSTEM_DATA_SCOPE="system_data_scope",e.SYSTEM_NOTICE_TYPE="system_notice_type",e.SYSTEM_LOGIN_TYPE="system_login_type",e.SYSTEM_LOGIN_RESULT="system_login_result",e.SYSTEM_SMS_CHANNEL_CODE="system_sms_channel_code",e.SYSTEM_SMS_TEMPLATE_TYPE="system_sms_template_type",e.SYSTEM_SMS_SEND_STATUS="system_sms_send_status",e.SYSTEM_SMS_RECEIVE_STATUS="system_sms_receive_status",e.SYSTEM_OAUTH2_GRANT_TYPE="system_oauth2_grant_type",e.SYSTEM_MAIL_SEND_STATUS="system_mail_send_status",e.SYSTEM_NOTIFY_TEMPLATE_TYPE="system_notify_template_type",e.SYSTEM_SOCIAL_TYPE="system_social_type",e.SYSTEM_USER_STATUS="system_user_status",e.INFRA_BOOLEAN_STRING="infra_boolean_string",e.INFRA_JOB_STATUS="infra_job_status",e.INFRA_JOB_LOG_STATUS="infra_job_log_status",e.INFRA_API_ERROR_LOG_PROCESS_STATUS="infra_api_error_log_process_status",e.INFRA_CONFIG_TYPE="infra_config_type",e.INFRA_CODEGEN_TEMPLATE_TYPE="infra_codegen_template_type",e.INFRA_CODEGEN_FRONT_TYPE="infra_codegen_front_type",e.INFRA_CODEGEN_SCENE="infra_codegen_scene",e.INFRA_FILE_STORAGE="infra_file_storage",e.INFRA_OPERATE_TYPE="infra_operate_type",e.BPM_MODEL_FORM_TYPE="bpm_model_form_type",e.BPM_TASK_CANDIDATE_STRATEGY="bpm_task_candidate_strategy",e.BPM_PROCESS_INSTANCE_STATUS="bpm_process_instance_status",e.BPM_TASK_STATUS="bpm_task_status",e.BPM_OA_LEAVE_TYPE="bpm_oa_leave_type",e.BPM_PROCESS_LISTENER_TYPE="bpm_process_listener_type",e.BPM_PROCESS_LISTENER_VALUE_TYPE="bpm_process_listener_value_type",e.PAY_CHANNEL_CODE="pay_channel_code",e.PAY_ORDER_STATUS="pay_order_status",e.PAY_REFUND_STATUS="pay_refund_status",e.PAY_NOTIFY_STATUS="pay_notify_status",e.PAY_NOTIFY_TYPE="pay_notify_type",e.PAY_TRANSFER_STATUS="pay_transfer_status",e.PAY_TRANSFER_TYPE="pay_transfer_type",e.MP_AUTO_REPLY_REQUEST_MATCH="mp_auto_reply_request_match",e.MP_MESSAGE_TYPE="mp_message_type",e.MEMBER_POINT_BIZ_TYPE="member_point_biz_type",e.MEMBER_EXPERIENCE_BIZ_TYPE="member_experience_biz_type",e.PRODUCT_SPU_STATUS="product_spu_status",e.EXPRESS_CHARGE_MODE="trade_delivery_express_charge_mode",e.TRADE_AFTER_SALE_STATUS="trade_after_sale_status",e.TRADE_AFTER_SALE_WAY="trade_after_sale_way",e.TRADE_AFTER_SALE_TYPE="trade_after_sale_type",e.TRADE_ORDER_TYPE="trade_order_type",e.TRADE_ORDER_STATUS="trade_order_status",e.TRADE_ORDER_ITEM_AFTER_SALE_STATUS="trade_order_item_after_sale_status",e.TRADE_DELIVERY_TYPE="trade_delivery_type",e.BROKERAGE_ENABLED_CONDITION="brokerage_enabled_condition",e.BROKERAGE_BIND_MODE="brokerage_bind_mode",e.BROKERAGE_BANK_NAME="brokerage_bank_name",e.BROKERAGE_WITHDRAW_TYPE="brokerage_withdraw_type",e.BROKERAGE_RECORD_BIZ_TYPE="brokerage_record_biz_type",e.BROKERAGE_RECORD_STATUS="brokerage_record_status",e.BROKERAGE_WITHDRAW_STATUS="brokerage_withdraw_status",e.PROMOTION_DISCOUNT_TYPE="promotion_discount_type",e.PROMOTION_PRODUCT_SCOPE="promotion_product_scope",e.PROMOTION_COUPON_TEMPLATE_VALIDITY_TYPE="promotion_coupon_template_validity_type",e.PROMOTION_COUPON_STATUS="promotion_coupon_status",e.PROMOTION_COUPON_TAKE_TYPE="promotion_coupon_take_type",e.PROMOTION_ACTIVITY_STATUS="promotion_activity_status",e.PROMOTION_CONDITION_TYPE="promotion_condition_type",e.PROMOTION_BARGAIN_RECORD_STATUS="promotion_bargain_record_status",e.PROMOTION_COMBINATION_RECORD_STATUS="promotion_combination_record_status",e.PROMOTION_BANNER_POSITION="promotion_banner_position",e.CRM_AUDIT_STATUS="crm_audit_status",e.CRM_BIZ_TYPE="crm_biz_type",e.CRM_BUSINESS_END_STATUS_TYPE="crm_business_end_status_type",e.CRM_RECEIVABLE_RETURN_TYPE="crm_receivable_return_type",e.CRM_CUSTOMER_INDUSTRY="crm_customer_industry",e.CRM_CUSTOMER_LEVEL="crm_customer_level",e.CRM_CUSTOMER_SOURCE="crm_customer_source",e.CRM_PRODUCT_STATUS="crm_product_status",e.CRM_PERMISSION_LEVEL="crm_permission_level",e.CRM_PRODUCT_UNIT="crm_product_unit",e.CRM_FOLLOW_UP_TYPE="crm_follow_up_type",e.ERP_AUDIT_STATUS="erp_audit_status",e.ERP_STOCK_RECORD_BIZ_TYPE="erp_stock_record_biz_type",e.AI_PLATFORM="ai_platform",e.AI_IMAGE_STATUS="ai_image_status",e.AI_MUSIC_STATUS="ai_music_status",e.AI_GENERATE_MODE="ai_generate_mode",e.AI_WRITE_TYPE="ai_write_type",e.AI_WRITE_LENGTH="ai_write_length",e.AI_WRITE_FORMAT="ai_write_format",e.AI_WRITE_TONE="ai_write_tone",e.AI_WRITE_LANGUAGE="ai_write_language",e.PROJECT_LEVEL="project_level",e.PROJECT_TYPE="project_type",e.PROJECT_TEAM_ROLE="project_team_role",e.PROJECT_KEY_NODE="project_key_node",e.PROJECT_PROBLEM_TYPE="project_problem_type",e.PROJECT_ACTIVITIES_TARGET_TYPE="project_activities_target_type",e.PROJECT_ACTIVITIES_RELATION="project_activities_relation",e.PROJECT_ACTIVITIES_STATUS="project_activities_status",e.PROJECT_TEMPLATE_CATEGORY="project_template_category",e.PATENT_TYPE="patent_type",e.LEGAL_STATUS="legal_status",e.OPERATION_TYPE="operation_type",e.PATENT_CERTIFICATE_TYPE="patent_certificate_type",e.PATENT_MAINTENANCE_STATUS="patent_maintenance_status",e.PATENT_REMINDER_TYPE="patent_reminder_type",e))(Qt||{});let D1;G0=e=>v.post({url:"/system/auth/login",data:e}),Xr=e=>v.get({url:"/system/tenant/get-id-by-name?name="+e}),K0=e=>v.get({url:"/system/tenant/get-by-website?website="+e}),D1=()=>v.post({url:"/system/auth/logout"}),Gl=()=>v.get({url:"/system/auth/get-permission-info"}),Qr=e=>v.post({url:"/system/auth/send-sms-code",data:e}),e0=e=>v.post({url:"/system/auth/sms-login",data:e}),a0=function(e,t,l){return v.post({url:"/system/auth/social-login",data:{type:e,code:t,state:l}})},$0=e=>v.postOriginal({url:"system/captcha/get",data:e}),q0=e=>v.postOriginal({url:"system/captcha/check",data:e});var Ha=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});let At,R1,ct;({wsCache:At}=Lt()),_t=Hl("admin-user",{state:()=>({permissions:[],roles:[],isSetUser:!1,user:{id:0,avatar:"",nickname:"",deptId:0,loginCount:0}}),getters:{getPermissions(){return this.permissions},getRoles(){return this.roles},getIsSetUser(){return this.isSetUser},getUser(){return this.user}},actions:{setUserInfoAction(e){return Ha(this,null,function*(){if(!ta())return this.resetState(),null;let t=At.get(Ee.USER);(e||!t)&&(t=yield Gl()),this.permissions=t.permissions,this.roles=t.roles,this.user=t.user,this.isSetUser=!0,At.set(Ee.USER,t),At.set(Ee.ROLE_ROUTERS,t.menus)})},setUserAvatarAction(e){return Ha(this,null,function*(){const t=At.get(Ee.USER);this.user.avatar=e,t.user.avatar=e,At.set(Ee.USER,t)})},setUserNicknameAction(e){return Ha(this,null,function*(){const t=At.get(Ee.USER);this.user.nickname=e,t.user.nickname=e,At.set(Ee.USER,t)})},loginOut(){return Ha(this,null,function*(){yield D1(),al(),Fr(),this.resetState()})},resetState(){this.permissions=[],this.roles=[],this.isSetUser=!1,this.user={id:0,avatar:"",nickname:"",deptId:0,loginCount:0}}}}),S0=()=>_t(Il),R1={},{wsCache:ct}=Lt(),Va=Hl("app",{state:()=>({userInfo:"prjUserInfo",sizeMap:["default","large","small"],mobile:!1,title:R1.env.VITE_APP_TITLE,pageLoading:!1,breadcrumb:!0,breadcrumbIcon:!0,collapse:!1,uniqueOpened:!0,hamburger:!0,screenfull:!1,search:!0,size:!0,locale:!1,message:!0,tagsView:!0,tagsViewImmerse:!0,tagsViewIcon:!1,logo:!0,fixedHeader:!0,footer:!1,greyMode:!1,fixedMenu:ct.get("fixedMenu")||!1,layout:ct.get(Ee.LAYOUT)||"cutMenu",isDark:ct.get(Ee.IS_DARK)||!1,currentSize:ct.get("default")||"default",theme:ct.get(Ee.THEME)||{elColorPrimary:"#409eff",leftMenuBorderColor:"inherit",leftMenuBgColor:"#344058",leftMenuBgLightColor:"#0f2438",leftMenuBgActiveColor:"var(--el-color-primary)",leftMenuCollapseBgActiveColor:"var(--el-color-primary)",leftMenuTextColor:"#bfcbd9",leftMenuTextActiveColor:"#fff",logoTitleTextColor:"#fff",logoBorderColor:"inherit",topHeaderBgColor:"#fff",topHeaderTextColor:"#001529",topHeaderHoverColor:"#f6f6f6",topToolBorderColor:"#eee"}}),getters:{getBreadcrumb(){return this.breadcrumb},getBreadcrumbIcon(){return this.breadcrumbIcon},getCollapse(){return this.collapse},getUniqueOpened(){return this.uniqueOpened},getHamburger(){return this.hamburger},getScreenfull(){return this.screenfull},getSize(){return this.size},getLocale(){return this.locale},getMessage(){return this.message},getTagsView(){return this.tagsView},getTagsViewImmerse(){return this.tagsViewImmerse},getTagsViewIcon(){return this.tagsViewIcon},getLogo(){return this.logo},getFixedHeader(){return this.fixedHeader},getGreyMode(){return this.greyMode},getFixedMenu(){return this.fixedMenu},getPageLoading(){return this.pageLoading},getLayout(){return this.layout},getTitle(){return this.title},getUserInfo(){return this.userInfo},getIsDark(){return this.isDark},getCurrentSize(){return this.currentSize},getSizeMap(){return this.sizeMap},getMobile(){return this.mobile},getTheme(){return this.theme},getFooter(){return this.footer}},actions:{setBreadcrumb(e){this.breadcrumb=e},setBreadcrumbIcon(e){this.breadcrumbIcon=e},setCollapse(e){this.collapse=e},setUniqueOpened(e){this.uniqueOpened=e},setHamburger(e){this.hamburger=e},setScreenfull(e){this.screenfull=e},setSize(e){this.size=e},setLocale(e){this.locale=e},setMessage(e){this.message=e},setTagsView(e){this.tagsView=e},setTagsViewImmerse(e){this.tagsViewImmerse=e},setTagsViewIcon(e){this.tagsViewIcon=e},setLogo(e){this.logo=e},setFixedHeader(e){this.fixedHeader=e},setGreyMode(e){this.greyMode=e},setFixedMenu(e){ct.set("fixedMenu",e),this.fixedMenu=e},setPageLoading(e){this.pageLoading=e},setLayout(e){if(this.mobile&&e!=="classic"){Nt.warning("\u79FB\u52A8\u7AEF\u6A21\u5F0F\u4E0B\u4E0D\u652F\u6301\u5207\u6362\u5176\u4ED6\u5E03\u5C40");return}this.layout=e,ct.set(Ee.LAYOUT,this.layout)},setTitle(e){this.title=e},setIsDark(e){this.isDark=e,this.isDark?(document.documentElement.classList.add("dark"),document.documentElement.classList.remove("light")):(document.documentElement.classList.add("light"),document.documentElement.classList.remove("dark")),ct.set(Ee.IS_DARK,this.isDark)},setCurrentSize(e){this.currentSize=e,ct.set("currentSize",this.currentSize)},setMobile(e){this.mobile=e},setTheme(e){this.theme=Object.assign(this.theme,e),ct.set(Ee.THEME,this.theme)},setCssVarTheme(){for(const e in this.theme)Kl(`--${o1(e)}`,this.theme[e])},setFooter(e){this.footer=e}},persist:!1}),P0=()=>Va(Il),za=()=>v.get({url:"/system/user/simple-list"});var ki=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});$l=()=>ki(void 0,null,function*(){return yield v.get({url:"/system/dept/simple-list"})});var Mi=Object.defineProperty,N1=Object.getOwnPropertySymbols,Ti=Object.prototype.hasOwnProperty,Vi=Object.prototype.propertyIsEnumerable,Y1=(e,t,l)=>t in e?Mi(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,zi=(e,t)=>{for(var l in t||(t={}))Ti.call(t,l)&&Y1(e,l,t[l]);if(N1)for(var l of N1(t))Vi.call(t,l)&&Y1(e,l,t[l]);return e};let U1,ja;U1={id:"id",children:"children",pid:"pid"},ql={children:"children",label:"name",value:"id",isLeaf:"leaf",emitPath:!1},ja=e=>Object.assign({},U1,e),g0=(e,t={})=>{t=ja(t);const{children:l}=t,n=[...e];for(let s=0;s<n.length;s++)n[s][l]&&n.splice(s+1,0,...n[s][l]);return n},A0=(e,t,l={})=>{l=ja(l);const n=[],s=[...e],m=new Set,{children:f}=l;for(;s.length;){const c=s[0];if(m.has(c))n.pop(),s.shift();else if(m.add(c),c[f]&&s.unshift(...c[f]),n.push(c),t(c))return n}return null},y0=(e,t,l={})=>{l=ja(l);const n=l.children;function s(m){return m.map(f=>zi({},f)).filter(f=>(f[n]=f[n]&&s(f[n]),t(f)||f[n]&&f[n].length))}return s(e)},Bl=(e,t,l,n)=>{if(!Array.isArray(e))return[];const s={id:t||"id",parentId:"parentId",childrenList:"children"},m={},f={},c=[];for(const u of e){const g=u[s.parentId];m[g]==null&&(m[g]=[]),f[u[s.id]]=u,m[g].push(u)}for(const u of e){const g=u[s.parentId];f[g]==null&&c.push(u)}for(const u of c)o(u);function o(u){if(m[u[s.id]]!==null&&(u[s.childrenList]=m[u[s.id]]),u[s.childrenList])for(const g of u[s.childrenList])o(g)}return c},Ul="/assets/avatar-BejG2Ou5.gif",Xt=function(e,t){return e&&e?Dl(e).format(t??"YYYY-MM-DD HH:mm:ss"):""},Fl=function(e){const t=Math.floor(e/864e5),l=Math.floor(e/(60*60*1e3)-t*24),n=Math.floor(e/(60*1e3)-t*24*60-l*60),s=Math.floor(e/1e3-t*24*60*60-l*60*60-n*60);return t>0?t+" \u5929"+l+" \u5C0F\u65F6 "+n+" \u5206\u949F":l>0?l+" \u5C0F\u65F6 "+n+" \u5206\u949F":n>0?n+" \u5206\u949F":s>0?s+" \u79D2":"0 \u79D2"},Jr=function(e,t,l){return l?Xt(l):""};function Ht(e){return e?Xt(e.cellValue,"YYYY-MM-DD"):""}var Li=Object.defineProperty,Ei=Object.defineProperties,Pi=Object.getOwnPropertyDescriptors,q1=Object.getOwnPropertySymbols,Si=Object.prototype.hasOwnProperty,Ii=Object.prototype.propertyIsEnumerable,$1=(e,t,l)=>t in e?Li(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,fl=(e,t)=>{for(var l in t||(t={}))Si.call(t,l)&&$1(e,l,t[l]);if(q1)for(var l of q1(t))Ii.call(t,l)&&$1(e,l,t[l]);return e},Ai=(e,t)=>Ei(e,Pi(t)),Hi=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});let B1,F1,K1;B1={class:"relative h-54px flex items-center justify-between pl-15px pr-15px"},F1={class:"absolute right-15px top-[50%] h-54px flex translate-y-[-50%] items-center justify-between"},Et=Ie(Ai(fl({},{name:"Dialog"}),{__name:"Dialog",props:{modelValue:Le.bool.def(!1),title:Le.string.def("Dialog"),fullscreen:Le.bool.def(!0),isFullscreen:Le.bool.def(!1),width:Le.oneOfType([String,Number]).def("40%"),scroll:Le.bool.def(!1),maxHeight:Le.oneOfType([String,Number]).def("400px")},setup(e){const t=as(),l=e,n=Ye(()=>{const o=["fullscreen","title","maxHeight","appendToBody"],u=rs(),g=fl(fl({},u),l);for(const M in g)o.indexOf(M)!==-1&&delete g[M];return g}),s=p(l.isFullscreen),m=()=>{s.value=!r(s)},f=p(Ea(l.maxHeight)?`${l.maxHeight}px`:l.maxHeight);Yt(()=>s.value,o=>Hi(this,null,function*(){if(yield Ue(),o){const u=document.documentElement.offsetHeight;f.value=`${u-55-60-(t.footer?63:0)}px`}else f.value=Ea(l.maxHeight)?`${l.maxHeight}px`:l.maxHeight}),{immediate:!0});const c=Ye(()=>({height:r(f)}));return(o,u)=>{const g=qt,M=Yd,C=Nd;return h(),N(C,ls(r(n),{"close-on-click-modal":!0,fullscreen:r(s),width:e.width,"destroy-on-close":"","lock-scroll":"",draggable:"",class:"com-dialog","show-close":!1,"align-center":"","append-to-body":!0}),$r({header:i(({close:W})=>[x("div",B1,[ua(o.$slots,"title",{},()=>[T(w(e.title),1)]),x("div",F1,[e.fullscreen?(h(),N(g,{key:0,class:"is-hover mr-10px cursor-pointer",icon:r(s)?"radix-icons:exit-full-screen":"radix-icons:enter-full-screen",color:"var(--el-color-info)","hover-color":"var(--el-color-primary)",onClick:m},null,8,["icon"])):se("",!0),a(g,{class:"is-hover cursor-pointer",icon:"ep:close","hover-color":"var(--el-color-primary)",color:"var(--el-color-info)",onClick:W},null,8,["onClick"])])])]),default:i(()=>[e.scroll?(h(),N(M,{key:0,style:Ua(r(c))},{default:i(()=>[ua(o.$slots,"default")]),_:3},8,["style"])):ua(o.$slots,"default",{key:1})]),_:2},[r(t).footer?{name:"footer",fn:i(()=>[ua(o.$slots,"footer")]),key:"0"}:void 0]),1040,["fullscreen","width"])}}})),K1="YYYY-MM-DD HH:mm:ss",$t=function(e,t=K1){return e?Dl(e).format(t):""},Re=Dl;var ji=Object.defineProperty,Oi=Object.defineProperties,Di=Object.getOwnPropertyDescriptors,W1=Object.getOwnPropertySymbols,Ri=Object.prototype.hasOwnProperty,Ni=Object.prototype.propertyIsEnumerable,G1=(e,t,l)=>t in e?ji(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,Yi=(e,t)=>{for(var l in t||(t={}))Ri.call(t,l)&&G1(e,l,t[l]);if(W1)for(var l of W1(t))Ni.call(t,l)&&G1(e,l,t[l]);return e},Ui=(e,t)=>Oi(e,Di(t));let Z1,J1,X1,ba;fa=Ie(Ui(Yi({},{name:"Pagination"}),{__name:"index",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pagerCount:{type:Number,default:document.body.clientWidth<992?5:7}},emits:["update:page","update:limit","pagination"],setup(e,{emit:t}){const l=Va(),n=Ye(()=>l.currentSize),s=p(n.value==="small");is(()=>{s.value=n.value==="small"});const m=e,f=t,c=Ye({get(){return m.page},set(M){f("update:page",M)}}),o=Ye({get(){return m.limit},set(M){f("update:limit",M)}}),u=M=>{c.value*M>m.total&&(c.value=1),f("pagination",{page:c.value,limit:M})},g=M=>{f("pagination",{page:M,limit:o.value})};return(M,C)=>{const W=Ud;return Pe((h(),N(W,{"current-page":c.value,"onUpdate:currentPage":C[0]||(C[0]=O=>c.value=O),"page-size":o.value,"onUpdate:pageSize":C[1]||(C[1]=O=>o.value=O),background:!0,"page-sizes":[10,20,30,50,100],"pager-count":e.pagerCount,total:e.total,small:r(s),class:"float-right mb-15px mt-15px",layout:"total, sizes, prev, pager, next, jumper",onSizeChange:u,onCurrentChange:g},null,8,["current-page","page-size","pager-count","total","small"])),[[Br,e.total>0]])}}})),Z1=e=>/^#([0-9a-fA-F]{3}|[0-9a-fA-f]{6})$/.test(e),ea=Ie({name:"DictTag",props:{type:{type:String,required:!0},value:{type:[String,Number,Boolean,Array],required:!0},separator:{type:String,default:","},gutter:{type:String,default:"5px"}},setup(e){const t=Ye(()=>Ea(e.value)||s1(e.value)?[String(e.value)]:Ga(e.value)?e.value.split(e.separator):Xa(e.value)?e.value.map(String):[]),l=()=>{if(!e.type||e.value===void 0||e.value===null||e.value==="")return null;const n=ia(e.type);return a("div",{class:"dict-tag",style:{display:"inline-flex",gap:e.gutter,justifyContent:"center",alignItems:"center"}},[n.map(s=>{if(t.value.includes(s.value))return(s.colorType+""=="primary"||s.colorType+""=="default")&&(s.colorType=""),a(jr,{style:s!=null&&s.cssClass?"color: #fff":"",type:(s==null?void 0:s.colorType)||null,color:s!=null&&s.cssClass&&Z1(s==null?void 0:s.cssClass)?s==null?void 0:s.cssClass:"",disableTransitions:!0},{default:()=>[s==null?void 0:s.label]})})])};return()=>l()}}),J1={class:"card-title"},X1=Ie({__name:"CardTitle",props:{title:{type:String,required:!0}},setup(e){return(t,l)=>(h(),j("span",J1,w(e.title),1))}}),ba=qe(X1,[["__scopeId","data-v-32e4eb78"]]);var qi=Object.defineProperty,$i=Object.defineProperties,Bi=Object.getOwnPropertyDescriptors,Q1=Object.getOwnPropertySymbols,Fi=Object.prototype.hasOwnProperty,Ki=Object.prototype.propertyIsEnumerable,er=(e,t,l)=>t in e?qi(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,Wi=(e,t)=>{for(var l in t||(t={}))Fi.call(t,l)&&er(e,l,t[l]);if(Q1)for(var l of Q1(t))Ki.call(t,l)&&er(e,l,t[l]);return e},Gi=(e,t)=>$i(e,Bi(t));let tr,ar,lr,rr,ir;tr={class:"flex items-center"},ar={class:"text-16px font-700"},lr={class:"max-w-200px"},rr={class:"flex flex-grow pl-20px"},ir=Ie(Gi(Wi({},{name:"ContentWrap"}),{__name:"ContentWrap",props:{title:Le.string.def(""),message:Le.string.def(""),bodyStyle:Le.object.def({padding:"20px"})},setup(e){const{getPrefixCls:t}=Ja(),l=t("content-wrap");return(n,s)=>{const m=qt,f=Or,c=st;return h(),N(c,{"body-style":e.bodyStyle,class:ke([r(l),"mb-15px"]),shadow:"never"},$r({default:i(()=>[ua(n.$slots,"default",{},void 0,!0)]),_:2},[e.title?{name:"header",fn:i(()=>[x("div",tr,[x("span",ar,w(e.title),1),e.message?(h(),N(f,{key:0,effect:"dark",placement:"right"},{content:i(()=>[x("div",lr,w(e.message),1)]),default:i(()=>[a(m,{size:14,class:"ml-5px",icon:"ep:question-filled"})]),_:1})):se("",!0),x("div",rr,[ua(n.$slots,"header",{},void 0,!0)])])]),key:"0"}:void 0]),1032,["body-style","class"])}}})),Fa=qe(ir,[["__scopeId","data-v-96109239"]]);var Zi=Object.defineProperty,Ji=Object.defineProperties,Xi=Object.getOwnPropertyDescriptors,or=Object.getOwnPropertySymbols,Qi=Object.prototype.hasOwnProperty,eo=Object.prototype.propertyIsEnumerable,nr=(e,t,l)=>t in e?Zi(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,to=(e,t)=>{for(var l in t||(t={}))Qi.call(t,l)&&nr(e,l,t[l]);if(or)for(var l of or(t))eo.call(t,l)&&nr(e,l,t[l]);return e},ao=(e,t)=>Ji(e,Xi(t)),ml=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});const lo={class:"head-container"},ro={class:"head-container user-container"},io=Ie(ao(to({},{name:"SystemUserDeptTree"}),{__name:"DeptTree",emits:["node-click"],setup(e,{emit:t}){const l=p(""),n=p([]),s=p(),m=()=>ml(this,null,function*(){const u=yield $l();n.value=[],n.value.push(...Bl(u))}),f=(u,g)=>u?g.name.includes(u):!0,c=u=>ml(this,null,function*(){o("node-click",u)}),o=t;return Yt(l,u=>{s.value.filter(u)}),lt(()=>ml(this,null,function*(){yield m()})),(u,g)=>{const M=qt,C=ka;return h(),j(ce,null,[x("div",lo,[a(C,{modelValue:r(l),"onUpdate:modelValue":g[0]||(g[0]=W=>ze(l)?l.value=W:null),class:"mb-20px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u90E8\u95E8\u540D\u79F0"},{prefix:i(()=>[a(M,{icon:"ep:search"})]),_:1},8,["modelValue"])]),x("div",ro,[a(r(Dr),{ref_key:"treeRef",ref:s,data:r(n),"expand-on-click-node":!1,"filter-node-method":f,props:r(ql),"default-expanded-keys":[100],"highlight-current":"","node-key":"id",onNodeClick:c},null,8,["data","props"])])],64)}}})),oo=qe(io,[["__scopeId","data-v-2a461bc5"]]);var no=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});const dr=e=>(qa("data-v-3334e188"),e=e(),$a(),e),so={class:"user-header"},co=dr(()=>x("span",null,"\u7528\u6237\u4FE1\u606F",-1)),uo={class:"user-container"},po={class:"user-header"},fo=dr(()=>x("span",null,"\u5DF2\u9009\u7528\u6237",-1)),mo={class:"user-container"},ho=Ie({__name:"index",props:{userList:Le.oneOfType([Array]).isRequired},emits:["checked"],setup(e,{expose:t,emit:l}){const n=p(!1),s=p([]),m=p([]),f=p([]),c=e,o=p(""),u=St(),g=V=>{let E=[V.id];M(V,E),s.value=c.userList.filter(U=>!m.value.includes(U.id)).filter(U=>E.includes(U.deptId))},M=(V,E)=>{V.children&&V.children.forEach(U=>{E.push(U.id),M(U,E)})},C=V=>{m.value=V,s.value=c.userList.filter(E=>!V.includes(E.id)),n.value=!0},W=l,O=V=>{f.value.some(E=>E.id===V.id)||f.value.push(V)},R=V=>{const E=f.value.findIndex(U=>U.id===V.id);E!==-1&&f.value.splice(E,1)},B=()=>{n.value=!1,W("checked",f.value),f.value=[]},D=()=>no(this,null,function*(){yield u.confirm("\u786E\u5B9A\u6E05\u7A7A\u5DF2\u9009\u7528\u6237\uFF1F"),f.value=[]});return Yt(o,V=>{s.value=c.userList.filter(E=>!m.value.includes(E.id)).filter(E=>E.nickname.includes(V))}),t({showDialog:C}),(V,E)=>{const U=Fa,q=qd,ee=qt,X=ka,Q=jr,xe=st,re=vt,pe=$d,Ve=Et;return h(),N(Ve,{title:"\u7528\u6237\u9009\u62E9",modelValue:r(n),"onUpdate:modelValue":E[1]||(E[1]=b=>ze(n)?n.value=b:null),width:"60%","append-to-body":""},{footer:i(()=>[a(re,{type:"primary",onClick:B},{default:i(()=>[T("\u786E\u5B9A")]),_:1})]),default:i(()=>[a(pe,{gutter:20},{default:i(()=>[a(q,{span:6},{default:i(()=>[a(U,null,{default:i(()=>[a(oo,{onNodeClick:g})]),_:1})]),_:1}),a(q,{span:11},{default:i(()=>[a(xe,{shadow:"never"},{header:i(()=>[x("div",so,[co,a(X,{modelValue:r(o),"onUpdate:modelValue":E[0]||(E[0]=b=>ze(o)?o.value=b:null),type:"text",style:{width:"200px"},placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u540D",size:"small"},{prefix:i(()=>[a(ee,{icon:"ep:search"})]),_:1},8,["modelValue"])])]),default:i(()=>[x("div",uo,[(h(!0),j(ce,null,ve(r(s).filter(b=>b.id!=1),b=>(h(),N(Q,{key:b.id,onClick:J=>O(b),size:"large"},{default:i(()=>[T(w(b.nickname),1)]),_:2},1032,["onClick"]))),128))])]),_:1})]),_:1}),a(q,{span:7},{default:i(()=>[a(xe,{shadow:"never"},{header:i(()=>[x("div",po,[fo,a(re,{type:"danger",size:"small",plain:"",onClick:D},{default:i(()=>[T("\u6E05\u7A7A")]),_:1})])]),default:i(()=>[x("div",mo,[(h(!0),j(ce,null,ve(r(f),b=>(h(),N(Q,{key:b.id,type:"success",size:"large",closable:"",onClose:J=>R(b),class:"w-70px"},{default:i(()=>[T(w(b.nickname),1)]),_:2},1032,["onClose"]))),128))])]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"])}}}),vo=qe(ho,[["__scopeId","data-v-3334e188"]]),sr=Ie({__name:"avatar",props:{user:{type:Object,required:!0},size:{type:Number,default:40},placement:{type:String,default:"bottom"},showDelete:{type:Boolean,default:!0}},emits:["delete"],setup(e,{emit:t}){const l=t,n=s=>{l("delete",s)};return(s,m)=>{const f=Ml,c=vt,o=Tl,u=ea,g=Vl,M=zl;return h(),N(M,{placement:e.placement,width:200,trigger:"hover"},{reference:i(()=>[a(f,{size:e.size,style:Ua({backgroundColor:e.user.status===2?"#ccc":e.user.backgroundColor,fontSize:`${e.size/2-2.5}px`,textShadow:"1px 1px 2px rgba(0, 0, 0, 0.5)"})},{default:i(()=>[T(w(e.user.nickname.length>2?e.user.nickname.substring(e.user.nickname.length-2):e.user.nickname),1)]),_:1},8,["size","style"])]),default:i(()=>[e.showDelete?(h(),N(c,{key:0,type:"danger",size:"small",plain:"",style:{width:"100%","margin-bottom":"5px"},onClick:m[0]||(m[0]=C=>n(e.user))},{default:i(()=>[T(" \u79FB\u9664 ")]),_:1})):se("",!0),a(g,{"label-width":"60px",class:"custom-form",size:"small"},{default:i(()=>[a(o,{label:"\u59D3\u540D"},{default:i(()=>[T(w(e.user.nickname),1)]),_:1}),a(o,{label:"\u72B6\u6001"},{default:i(()=>[a(u,{type:r(Qt).SYSTEM_USER_STATUS,value:e.user.status},null,8,["type","value"])]),_:1}),a(o,{label:"\u5DE5\u53F7"},{default:i(()=>[T(w(e.user.username),1)]),_:1}),a(o,{label:"\u90E8\u95E8"},{default:i(()=>[T(w(e.user.deptName),1)]),_:1})]),_:1})]),_:1},8,["placement"])}}});var yo=Object.defineProperty,go=Object.defineProperties,bo=Object.getOwnPropertyDescriptors,cr=Object.getOwnPropertySymbols,_o=Object.prototype.hasOwnProperty,wo=Object.prototype.propertyIsEnumerable,ur=(e,t,l)=>t in e?yo(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,pr=(e,t)=>{for(var l in t||(t={}))_o.call(t,l)&&ur(e,l,t[l]);if(cr)for(var l of cr(t))wo.call(t,l)&&ur(e,l,t[l]);return e},fr=(e,t)=>go(e,bo(t)),Co=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});const xo={class:"avatar-group"},ko={key:0,class:"avatar-item"},Mo={class:"avatar-group"},To=Ie({__name:"index",props:{userList:Le.oneOfType([Array]).isRequired,modelValue:Le.oneOfType([Array]).isRequired,size:Le.oneOfType([Number]).isRequired,limit:Le.number.def(10),add:Le.bool.def(!0),visiableUserList:Le.oneOfType([Array]).def([])},emits:["update:modelValue","change:msg"],setup(e,{emit:t}){const l=p(),n=p([]),s=e,m=t,f=Ye(()=>s.userList.filter(M=>s.visiableUserList.length===0||s.visiableUserList.includes(M.id))),c=()=>{r(l).showDialog(n.value.map(M=>M.id))},o=M=>{M.forEach(C=>n.value.push(fr(pr({},C),{backgroundColor:g()}))),m("update:modelValue",n.value.map(C=>C.id)),m("change:msg","\u65B0\u589E\u4E86\u7528\u6237"+M.map(C=>C.nickname).join(","))},u=M=>{n.value=n.value.filter(C=>C.id!==M.id),m("update:modelValue",n.value.map(C=>C.id)),m("change:msg","\u79FB\u9664\u4E86\u7528\u6237"+M.nickname)},g=()=>{const M=()=>Math.floor(Math.random()*128+50).toString(16).padStart(2,"0");return`#${M()}${M()}${M()}`};return Yt([()=>s.modelValue,()=>s.userList],M=>Co(this,[M],function*([C,W]){if(C&&W.length>0){const O=s.userList.filter(R=>C.includes(R.id));n.value=[],O==null||O.forEach(R=>n.value.push(fr(pr({},R),{backgroundColor:g()})))}C||(n.value=[])}),{immediate:!0,deep:!0}),(M,C)=>{const W=Ml,O=zl,R=vt;return h(),j("div",xo,[(h(!0),j(ce,null,ve(r(n).slice(0,e.limit),B=>(h(),j("div",{key:B.id,class:"avatar-item"},[a(sr,{user:B,size:e.size,"show-delete":e.add,onDelete:D=>u(B)},null,8,["user","size","show-delete","onDelete"])]))),128)),r(n).length>e.limit?(h(),j("div",ko,[a(O,{placement:"bottom",width:200,trigger:"click"},{reference:i(()=>[a(W,{size:e.size},{default:i(()=>[T(" +"+w(r(n).slice(e.limit,r(n).length).length),1)]),_:1},8,["size"])]),default:i(()=>[x("div",Mo,[(h(!0),j(ce,null,ve(r(n).slice(e.limit,r(n).length),B=>(h(),j("div",{key:B.id,class:"avatar-item"},[a(sr,{user:B,size:e.size,"show-delete":e.add,placement:"top",onDelete:D=>u(B)},null,8,["user","size","show-delete","onDelete"])]))),128))])]),_:1})])):se("",!0),e.add?(h(),N(R,{key:1,icon:r(Bd),circle:"",style:Ua({height:typeof e.size=="number"?e.size+"px":e.size,width:typeof e.size=="number"?e.size+"px":e.size}),onClick:c},null,8,["icon","style"])):se("",!0),e.add?(h(),N(vo,{key:2,userList:r(f),ref_key:"userSelectedRef",ref:l,onChecked:o},null,8,["userList"])):se("",!0)])}}}),Vo=qe(To,[["__scopeId","data-v-9bd63079"]]),Oa={getCalendarCountList:(e,t,l)=>v.get({url:`/project/instrument/get-calendar-count-list?startDate=${e}&endDate=${t}&userId=${l}`}),getCalendarTodoList:(e,t,l)=>v.get({url:`/project/instrument/get-calendar-todo-list?date=${e}&type=${t}&userId=${l}`}),getStatusCountList:e=>v.get({url:`/project/instrument/get-status-count-list?userId=${e}`}),getViewUserList:()=>v.get({url:"/project/instrument/get-view-user-list"})};var ut=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});let mr,Da,hr;mr=e=>ut(void 0,null,function*(){return yield v.get({url:"/bpm/task/todo-page",params:e})}),_0=e=>ut(void 0,null,function*(){return yield v.put({url:"/bpm/task/approve",data:e})}),w0=e=>ut(void 0,null,function*(){return yield v.put({url:"/bpm/task/reject",data:e})}),b0=e=>ut(void 0,null,function*(){return yield v.get({url:"/bpm/task/list-by-process-instance-id?processInstanceId="+e})}),c0=e=>ut(void 0,null,function*(){return yield v.get({url:"/bpm/task/list-by-return",params:{id:e}})}),u0=e=>ut(void 0,null,function*(){return yield v.put({url:"/bpm/task/return",data:e})}),p0=e=>ut(void 0,null,function*(){return yield v.put({url:"/bpm/task/delegate",data:e})}),f0=e=>ut(void 0,null,function*(){return yield v.put({url:"/bpm/task/transfer",data:e})}),m0=e=>ut(void 0,null,function*(){return yield v.put({url:"/bpm/task/create-sign",data:e})}),s0=e=>ut(void 0,null,function*(){return yield v.delete({url:"/bpm/task/delete-sign",data:e})}),d0=e=>ut(void 0,null,function*(){return yield v.get({url:"/bpm/task/list-by-parent-task-id?parentTaskId="+e})}),Da={createBasics:e=>v.post({url:"/project/basics/create",data:e}),updateBasics:e=>v.post({url:"/project/basics/update",data:e}),getBasics:e=>v.get({url:`/project/basics/get/${e}`}),getSimpleBasics:e=>v.get({url:`/project/basics/get-simple/${e}`}),getBasicsList:e=>v.post({url:"/project/basics/list",data:e}),getBasicsPage:e=>v.post({url:"/project/basics/page",data:e}),getQueryCategoryList:e=>v.post({url:"/project/basics/list-category",data:e}),getBasicsLogPage:e=>v.get({url:"/project/basics/page-log",params:e}),pauseRestartBasics:e=>v.get({url:`/project/basics/pause-restart/${e}`}),getPermission:e=>v.get({url:`/project/basics/get-permission/${e}`}),getSimpleList:()=>v.get({url:"/project/basics/get-simple-list"}),getIdAndNameList:()=>v.get({url:"/project/basics/get-simple-id-name-list"}),updateErpCode:e=>v.get({url:"/project/basics/update-erp-code",params:e}),updatePlanExpense:e=>v.get({url:"/project/basics/update-plan-expense",params:e}),updateActualOrderDate:e=>v.get({url:"/project/basics/update-actual-date",params:e}),export:e=>v.downloadPost({url:"/project/basics/export-excel",data:e}),getBasicsChangeApplicationList:()=>v.get({url:"/project/basics/get-change-application-list"}),getParentOrChildRelation:e=>v.get({url:"/project/basics/get-parent-or-child-relation/"+e}),getChildRelation:e=>v.get({url:"/project/basics/get-child-relation/"+e}),sendComment:e=>v.post({url:"/project/basics/send-comment",data:e}),completeBasics:e=>v.get({url:"/project/basics/complete",params:{id:e}})},hr={getCommentPage:e=>v.post({url:"/infra/comment/list",params:e}),createComment:e=>v.post({url:"/infra/comment/add",data:e}),deleteComment:e=>v.delete({url:`/infra/comment/delete/${e}`}),getMentionPage:e=>v.post({url:"/infra/comment/list-mention",params:e}),viewMention:e=>v.get({url:"/infra/comment/view-mention",params:e})};var Bt=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});const zo={getSupportLibraryPage:e=>Bt(void 0,null,function*(){return yield v.get({url:"/project/support-library/page",params:e})}),getSupportLibrary:e=>Bt(void 0,null,function*(){return yield v.get({url:"/project/support-library/get?id="+e})}),createSupportLibrary:e=>Bt(void 0,null,function*(){return yield v.post({url:"/project/support-library/create",data:e})}),updateSupportLibrary:e=>Bt(void 0,null,function*(){return yield v.put({url:"/project/support-library/update",data:e})}),deleteSupportLibrary:e=>Bt(void 0,null,function*(){return yield v.delete({url:"/project/support-library/delete?id="+e})}),exportSupportLibrary:e=>Bt(void 0,null,function*(){return yield v.download({url:"/project/support-library/export-excel",params:e})}),getSimpleList:()=>Bt(void 0,null,function*(){return yield v.get({url:"/project/support-library/simple-list"})})};Zt(),C0=function(e){if(e&&e instanceof Array&&e.length>0){const{wsCache:t}=Lt(),l=e,n="*:*:*";return!!t.get(Ee.USER).permissions.some(s=>n===s||l.includes(s))}else return!1};function Lo(e){if(e&&e instanceof Array&&e.length>0){const{wsCache:t}=Lt(),l=e,n="admin";return!!t.get(Ee.USER).roles.some(s=>n===s||l.includes(s))}else return!1}var pt=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});const xt=e=>(qa("data-v-6bd9b5b5"),e=e(),$a(),e),Eo={key:0,class:"w-10% rounded-5px bg-white ml-5px p-5px h-100% person-selector-panel"},Po=["id"],So=xt(()=>x("div",{class:"w-16px h-16px bg-[var(--el-color-primary)] m-2px"},null,-1)),Io=xt(()=>x("span",null,"\u6D3B\u52A8",-1)),Ao=[So,Io],Ho=xt(()=>x("div",{class:"w-16px h-16px bg-[var(--el-color-danger)] m-2px"},null,-1)),jo=xt(()=>x("span",null,"\u95EE\u9898",-1)),Oo=[Ho,jo],Do=xt(()=>x("div",{class:"w-16px h-16px bg-[var(--el-color-warning)] m-2px"},null,-1)),Ro=xt(()=>x("span",null,"\u9884\u5BA1\u8BB0\u5F55",-1)),No=[Do,Ro],Yo={key:0,class:"calendar-todo-list"},Uo=["onClick"],qo={class:"flex w-100% position-relative !p-l-30px"},$o={class:"w-50% whitespace-nowrap overflow-hidden text-ellipsis"},Bo={class:"w-100%"},Fo={class:"w-100% text-.9rem text-#888888"},Ko={class:"flex justify-between items-center w-[calc(50%-120px)]"},Wo={class:"position-relative"},Go=["onClick"],Zo=xt(()=>x("div",{class:"w-10px h-10px rounded-50% bg-[var(--el-color-primary)]"},null,-1)),Jo={class:ke(["whitespace-nowrap overflow-hidden text-ellipsis w-90%"])},Xo={key:0},Qo=["onClick"],en={class:"whitespace-nowrap overflow-hidden text-ellipsis"},tn={class:"whitespace-nowrap overflow-hidden text-ellipsis"},an={class:"whitespace-nowrap overflow-hidden text-ellipsis"},ln={class:"el-timeline-item__timestamp"},rn={class:"el-timeline-item__user"},on={key:0},nn={key:1},dn={class:"flex items-center"},sn=xt(()=>x("img",{src:Ul,alt:""},null,-1)),cn=xt(()=>x("div",{class:"text-1rem color-[var(--el-color-info)]"}," \u6B22\u8FCE\u56DE\u6765\uFF01 ",-1)),un={class:"h-[calc(100%-78px)] bg-white rounded-5px border-1px border-solid border-[var(--el-border-color)] p-5px w-full"},pn={class:"p-10px"},fn={class:"bg-white h-[calc(100%-40px)] w-full"},mn={class:"p-10px h-20% w-full"},hn=Ie({__name:"Workbench",setup(e){const t=p([]),l=()=>pt(this,null,function*(){t.value=yield zo.getSimpleList()}),n=_t(),s=Va(),m=n.getUser.avatar,f=n.getUser.nickname,c=p([]),o=Pl(),{wsCache:u}=Lt(),g=p({type:"my",personId:_t().getUser.id,personName:_t().getUser.nickname}),M=p([]),C=Ye(()=>s.getMobile),W=p(),O=p(new Date),R=p("all"),B=p("all"),D=p([]),V=p([]),E=p([]),U=p([]),q=p([]),ee=p([]),X=p(0),Q=p({pageNo:1,pageSize:10,creator:n.getUser.id}),xe=()=>{B.value==="all"?E.value=V.value:E.value=V.value.filter(le=>le.type===B.value)},re=le=>{u.set("project_page_show_form",le),o.push({name:"ProjectCenter"})},pe=le=>{switch(le){case 1:return"fa-solid:running";case 2:return"ep:warning-filled";case 3:return"ep:success-filled";case 4:return"fa:compass";case 10:return"fa:pencil-square";case 11:return"fa:calendar-times-o"}},Ve=()=>pt(this,null,function*(){Q.value.creator=g.value.personId;const le=yield Da.getBasicsLogPage(Q.value);ee.value=ee.value.concat(le.list),X.value=le.total}),b=()=>{Q.value.pageNo++,!(Q.value.pageNo>Math.ceil(X.value/Q.value.pageSize))&&Ve()},J=p({pageNo:1,pageSize:10,userId:n.getUser.id,status:0}),de=p([]),ye=p(0),S=()=>pt(this,null,function*(){J.value.userId=g.value.personId;const le=yield hr.getMentionPage(J.value);de.value=de.value.concat(le.list),ye.value=le.total}),be=()=>{J.value.pageNo++,!(J.value.pageNo>Math.ceil(ye.value/J.value.pageSize))&&S()},L=le=>{switch(le){case"activities":return"\u6D3B\u52A8";case"problem":return"\u95EE\u9898";case"risk":return"\u98CE\u9669";case"technical":return"\u6280\u672F\u8BC4\u5BA1";default:return""}},Y=le=>pt(this,null,function*(){var ue;const Se=yield Da.getSimpleBasics(le.basicsId);u.set("project_page_show_form",{categoryId:(ue=Se.categoryIds)==null?void 0:ue[0],basicsId:le.basicsId,page:le.type,stage:le.stage,id:le.itemId}),o.push({name:"ProjectCenter"})}),ie=()=>pt(this,null,function*(){const le=yield mr({pageNo:1,pageSize:-1,userId:g.value.personId});q.value=le.list}),Te=le=>{var ue;return(ue=D.value.find(Se=>Se.date===le))==null?void 0:ue.total},He=()=>pt(this,null,function*(){const le=Re(O.value),ue=le.startOf("month"),Se=le.endOf("month"),at=ue.clone().subtract(ue.day(),"day"),ot=(6-Se.day()+7)%7,kt=Se.clone().add(ot,"day"),Oe=yield Oa.getCalendarCountList(at.format("YYYY-MM-DD"),kt.format("YYYY-MM-DD"),g.value.personId);D.value=Oe}),tt=()=>pt(this,null,function*(){const le=yield Oa.getCalendarTodoList(Re(O.value).format("YYYY-MM-DD"),R.value,g.value.personId);V.value=le,E.value=le}),ft=()=>pt(this,null,function*(){const le=yield za();c.value=le}),Je=()=>pt(this,null,function*(){const le=yield Oa.getStatusCountList(g.value.personId);U.value=le}),Ke=le=>{var ue;return((ue=U.value.find(Se=>Se.status==le))==null?void 0:ue.total)||0},rt=le=>{o.push({name:"BpmProcessInstanceDetail",query:{id:le.processInstance.id}})};Yt(()=>g.value.type,()=>{g.value.type==="my"&&(g.value.personId=_t().getUser.id,g.value.personName=_t().getUser.nickname,u.set("workbench_view_type",g.value),He(),tt(),Je(),ie(),ee.value=[],Ve(),S())});const it=()=>{g.value.type="other",M.value.length===0&&Dt()},Dt=()=>pt(this,null,function*(){const le=yield Oa.getViewUserList();!le||(le==null?void 0:le.length)===0?M.value=c.value.filter(ue=>ue.id!==1):M.value=c.value.filter(ue=>le.includes(ue.id)||t.value.includes(ue.id))}),mt=p(""),Xe=p(),he=(le,ue)=>le?ue.nickname.includes(le):!0;Yt(mt,le=>{Xe.value.filter(le)});const Rt=le=>{const ue=c.value.find(Se=>Se.id===le.id);g.value.personId=ue==null?void 0:ue.id,g.value.personName=ue==null?void 0:ue.nickname,u.set("workbench_view_type",g.value),He(),tt(),Je(),ie(),ee.value=[],Ve(),S()};Yt(()=>O.value,(le,ue)=>{R.value="day",tt();const Se=Re(le),at=Re(ue);(Se.year()!==at.year()||Se.month()!==at.month())&&He()});function Kt(){const le=document.getElementById(`user-${g.value.personId}`);setTimeout(()=>{le&&Ue(()=>{le.scrollIntoView({block:"center"})})},100)}return lt(()=>pt(this,null,function*(){const le=u.get("workbench_view_type");Lo(["r&d_manager","super_admin","porject_manager"])&&le&&(g.value=le),yield ft(),yield l(),yield Dt(),Kt(),He(),tt(),Je(),ie(),Ve(),S()})),(le,ue)=>{const Se=Na,at=Ya,ot=qt,kt=ka,Oe=Dr,Mt=vt,nt=Or,dt=ba,We=Fd,Tt=Vo,gt=ea,Wt=Ma,bt=Rr,Gt=sa,ht=Kd,da=Wd,te=ca,k=Gd,G=Zd,oe=Ml,Ce=Fa,A=Sl("hasRole"),me=Jd;return h(),j("div",{class:ke(["h-full",r(C)?"flex flex-col":"flex"])},[r(g).type==="other"?(h(),j("div",Eo,[x("div",null,[a(at,{size:"small",modelValue:r(g).type,"onUpdate:modelValue":ue[0]||(ue[0]=z=>r(g).type=z)},{default:i(()=>[a(Se,{label:"\u4E2A\u4EBA\u6570\u636E",value:"my"}),Pe(a(Se,{label:"\u4ED6\u4EBA\u6570\u636E",value:"other"},null,512),[[A,["r&d_manager","super_admin","porject_manager"]]])]),_:1},8,["modelValue"])]),a(kt,{modelValue:r(mt),"onUpdate:modelValue":ue[1]||(ue[1]=z=>ze(mt)?mt.value=z:null),class:"mt-10px mb-10px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u540D\u79F0",size:"small"},{prefix:i(()=>[a(ot,{icon:"ep:search"})]),_:1},8,["modelValue"]),a(Oe,{ref_key:"treeRef",ref:Xe,data:r(M),props:{label:"nickname"},"current-node-key":r(g).personId,class:"h-[calc(100%-100px)] overflow-auto","filter-node-method":he,"highlight-current":"","node-key":"id",onNodeClick:Rt},{default:i(({node:z,data:I})=>[x("span",{id:`user-${I.id}`},w(z.label),9,Po)]),_:1},8,["data","current-node-key"])])):se("",!0),r(g).type==="my"?Pe((h(),j("div",{key:1,class:"floating-expand-btn",onClick:it},[a(nt,{content:"\u67E5\u770B\u4ED6\u4EBA\u6570\u636E",placement:"right"},{default:i(()=>[a(Mt,{type:"primary",size:"small",class:"w-24px !h-80px expand-button"},{default:i(()=>[T(" \u4ED6\u4EBA\u6570\u636E "),a(ot,{icon:"ep:right"})]),_:1})]),_:1})])),[[A,["r&d_manager","super_admin","porject_manager"]]]):se("",!0),x("div",{class:ke(["h-full",r(C)?"w-full":"w-[calc(100%-20%)] p-l-10px p-r-10px"])},[x("div",{class:ke(["w-100% rounded-5px bg-white p-10px",r(C)?"h-auto mb-10px":"h-50% flex"])},[x("div",{class:ke(["calendar-list",r(C)?"w-full":"w-70%"])},[x("div",{class:ke(["flex items-center",r(C)?"flex-col gap-2":"justify-between"])},[a(dt,{title:`${r(g).type==="my"?"\u6211":r(g).personName}\u7684\u4E8B\u9879`},null,8,["title"]),a(at,{size:"small",modelValue:r(B),"onUpdate:modelValue":ue[2]||(ue[2]=z=>ze(B)?B.value=z:null),onChange:xe,class:ke(r(C)?"flex-wrap":"")},{default:i(()=>[a(We,{class:ke(r(C)?"text-sm":"text-1rem"),label:"\u6240\u6709",value:"all"},null,8,["class"]),a(We,{value:"activities"},{default:i(()=>[x("div",{class:ke(["flex items-center",r(C)?"text-sm":"text-1rem"])},Ao,2)]),_:1}),a(We,{value:"problem"},{default:i(()=>[a(nt,{content:`A\u7C7B\u95EE\u9898\u662F\u5F71\u4E61\u54CD\u4EA7\u54C1\u6838\u5FC3\u529F\u80FD\u3001\u5B89\u89C4\u6216\u7528\u6237\u4F53\u9A8C\u7684\u4E25\u91CD\u95EE\u9898\uFF0C\u5FC5\u987B\u7ACB\u5373\u89E3\u51B3\u3002\r
                              B\u7C7B\u95EE\u9898\u5BF9\u7528\u6237\u4F53\u9A8C\u6709\u4E00\u5B9A\u5F71\u54CD\uFF0C\u4F46\u4E0D\u4F1A\u5BFC\u81F4\u4EA7\u54C1\u5B8C\u5168\u65E0\u6CD5\u4F7F\u7528\uFF0C\u53EF\u4EE5\u5728A\u7C7B\u95EE\u9898\u89E3\u51B3\u540E\u9010\u6B65\u5904\u7406\u3002\r
                              C\u7C7B\u95EE\u9898\u5BF9\u4EA7\u54C1\u6838\u5FC3\u529F\u80FD\u548C\u7528\u6237\u4F53\u9A8C\u5F71\u54CD\u8F83\u5C0F\uFF0C\u901A\u5E38\u662F\u4F18\u5316\u548C\u6539\u8FDB\u7C7B\u95EE\u9898\uFF0C\u53EF\u4EE5\u5728\u9879\u76EE\u540E\u671F\u6216\u8D44\u6E90\u5145\u8DB3\u65F6\u5904\u7406\u3002`},{default:i(()=>[x("div",{class:ke(["flex items-center",r(C)?"text-sm":"text-1rem"])},Oo,2)]),_:1})]),_:1}),a(We,{value:"technical"},{default:i(()=>[x("div",{class:ke(["flex items-center",r(C)?"text-sm":"text-1rem"])},No,2)]),_:1})]),_:1},8,["modelValue","class"]),a(at,{modelValue:r(R),"onUpdate:modelValue":ue[3]||(ue[3]=z=>ze(R)?R.value=z:null),class:ke(r(C)?"":"m-l-10px"),onChange:tt},{default:i(()=>[a(We,{label:"\u6240\u6709",value:"all"}),a(We,{label:"\u6309\u5929",value:"day"})]),_:1},8,["modelValue","class"])],2),r(E).length>0?(h(),j("div",Yo,[(h(!0),j(ce,null,ve(r(E),z=>(h(),j("div",{key:z.id,class:ke(["calendar-todo-item","text-1rem","cursor-pointer"]),onClick:I=>re({categoryId:z.categoryIds[0],basicsId:z.basicsId,page:z.type,stage:z.stage,id:z.id})},[x("div",qo,[x("div",{class:ke([z.type=="activities"&&"!bg-[var(--el-color-primary)]",z.type=="problem"&&"!bg-[var(--el-color-danger)]",z.type=="technical"&&"!bg-[var(--el-color-warning)]","position-absolute","left-[-5px]","top-[-5px]","text-.9rem","text-white","w-2.5rem","tag"])},w(z.type=="activities"?"\u6D3B\u52A8":z.type=="problem"?"\u95EE\u9898":"\u9884\u5BA1"),3),a(Tt,{modelValue:z.director,"onUpdate:modelValue":I=>z.director=I,size:30,limit:3,add:!1,"user-list":r(c),class:"w-120px"},null,8,["modelValue","onUpdate:modelValue","user-list"]),x("div",$o,[x("div",Bo,w(z.name),1),x("div",Fo,"\u9879\u76EE\uFF1A"+w(z.basicsName),1)]),x("div",Ko,[x("div",null,w(r(Xt)(z.endDate,"YYYY-MM-DD")),1)]),a(gt,{size:"small",type:"project_activities_status",value:z.status},null,8,["value"])]),a(Wt,{percentage:z.progress,class:"w-100%"},null,8,["percentage"])],8,Uo))),128))])):(h(),N(bt,{key:1,description:"\u5F53\u524D\u9009\u62E9\u65E5\u671F\u6682\u65E0\u4E8B\u9879"}))],2),a(da,{ref_key:"calendar",ref:W,class:"h-100% w-30% rounded-5px",modelValue:r(O),"onUpdate:modelValue":ue[5]||(ue[5]=z=>ze(O)?O.value=z:null)},{header:i(()=>[a(dt,{title:"\u65E5\u5386"}),x("span",null,[a(Gt,{modelValue:r(O),"onUpdate:modelValue":ue[4]||(ue[4]=z=>ze(O)?O.value=z:null),type:"month",clearable:!1,class:"!w-100px",size:"small"},null,8,["modelValue"])])]),"date-cell":i(({data:z})=>[x("div",Wo,[a(ht,{value:Te(z.day),class:"w-full","show-zero":!1,offset:[-4,8]},{default:i(()=>[x("div",{class:ke([z.isSelected&&"is-selected","p-t-5px","p-b-5px","w-36px"])},w(z.day.split("-").slice(2).join("-")),3)]),_:2},1032,["value"])])]),_:1},8,["modelValue"])],2),x("div",{class:ke([r(C)?"flex flex-col gap-10px":"h-[calc(50%-10px)] m-t-10px flex"])},[x("div",{class:ke(["bg-white rounded-5px border-1px border-solid border-[var(--el-border-color)] p-10px",r(C)?"w-full":"w-33%"])},[x("div",{class:ke(r(C)?"h-auto mb-10px":"h-40px")},[a(dt,{title:`${r(g).type==="my"?"\u6211":r(g).personName}\u7684\u6D41\u7A0B\u5F85\u529E`},null,8,["title"])],2),r(q).length>0?(h(),j("div",{key:0,class:ke(["overflow-auto",r(C)?"max-h-300px":"h-[calc(100%-60px)]"])},[(h(!0),j(ce,null,ve(r(q),z=>{var I,$,F;return h(),j("div",{key:z.id,class:ke(["todo-item",r(C)?"mobile-todo-item":""]),onClick:K=>rt(z)},[Zo,a(nt,{content:((I=z.formVariables)==null?void 0:I.approve_title)||z.processInstance.name},{default:i(()=>{var K;return[x("div",Jo,w(((K=z.formVariables)==null?void 0:K.approve_title)||z.processInstance.name),1)]}),_:2},1032,["content"]),x("div",{class:ke([r(C)?"w-full text-xs text-gray-500":"w-50%"])},"\u63D0\u4EA4\u4EBA:"+w((F=($=z==null?void 0:z.processInstance)==null?void 0:$.startUser)==null?void 0:F.nickname),3),x("div",{class:ke(["overflow-hidden whitespace-nowrap text-ellipsis",r(C)?"w-full text-xs text-gray-400":"w-50%"])},w(r(Xt)(z.createTime)),3)],10,Go)}),128))],2)):(h(),N(bt,{key:1,description:"\u6682\u65E0\u5F85\u529E"}))],2),x("div",{class:ke(["bg-white rounded-5px border-1px border-solid border-[var(--el-border-color)] p-10px",r(C)?"w-full":"w-33% m-l-10px"])},[x("div",{class:ke(r(C)?"h-auto mb-10px":"h-40px")},[a(dt,{title:`@${r(g).type==="my"?"\u6211":r(g).personName}\u7684\u4E8B\u9879`},null,8,["title"])],2),r(de).length>0?Pe((h(),j("div",Xo,[(h(!0),j(ce,null,ve(r(de),z=>(h(),j("div",{class:"mention-item",key:z.id,onClick:I=>Y(z)},[x("div",en,"\u9879\u76EE\uFF1A"+w(z.basicsName),1),x("div",tn,w(L(z.type))+"\uFF1A"+w(z.itemName),1),a(nt,{content:z.mentionContent},{default:i(()=>[x("div",an,w(z.mentionContent),1)]),_:2},1032,["content"]),x("div",null,w(r($t)(z.createTime)),1)],8,Qo))),128))])),[[me,be]]):(h(),N(bt,{key:1,description:`\u5F53\u524D\u65E0@${r(g).type==="my"?"\u6211":r(g).personName}\u7684\u4E8B\u9879`},null,8,["description"]))],2),x("div",{class:ke(["bg-white rounded-5px border-1px border-solid border-[var(--el-border-color)] p-10px",r(C)?"w-full":"w-[calc(33%-10px)] m-l-10px"])},[x("div",{class:ke(r(C)?"h-auto mb-10px":"h-40px")},[a(dt,{title:`${r(g).type==="my"?"\u6211":r(g).personName}\u7684\u64CD\u4F5C\u8BB0\u5F55`},null,8,["title"])],2),r(ee).length>0?Pe((h(),j("div",{key:0,class:ke(["overflow-auto",r(C)?"max-h-300px":"h-[calc(100%-40px)]"])},[a(G,null,{default:i(()=>[(h(!0),j(ce,null,ve(r(ee),z=>(h(),N(k,{key:z.id},{default:i(()=>{var I;return[x("div",ln,[T(w(r(Xt)(z.createTime))+"-",1),x("span",rn,w(z.createName),1)]),T(" "+w(z.content)+" ",1),z.modifyFieldName?(h(),j("div",on,[r(fs)(z.modifyField)?(h(),j(ce,{key:0},[T(" \u5B57\u6BB5:"+w(z.modifyFieldName)+" \u4ECE:"+w(r(Gr)(z.modifyField,z.beforeValue))+" \u4FEE\u6539\u4E3A:"+w(r(Gr)(z.modifyField,z.afterValue)),1)],64)):["managers","userIds","director","coordinate"].includes(z.modifyField)||(I=r(Pt)(r(Qt).PROJECT_TEAM_ROLE).find($=>$.value===z.modifyField))!=null&&I.label?(h(),j(ce,{key:1},[T(" \u5B57\u6BB5:"+w(z.modifyFieldName)+" \u4ECE:"+w(r(Rl)(r(c),z.beforeValue))+" \u4FEE\u6539\u4E3A:"+w(r(Rl)(r(c),z.afterValue)),1)],64)):(h(),j(ce,{key:2},[T(" \u5B57\u6BB5:"+w(z.modifyFieldName)+" \u4ECE:"+w(z.beforeValue)+" \u4FEE\u6539\u4E3A:"+w(z.afterValue),1)],64))])):se("",!0),z.processInstanceId?(h(),j("div",nn,[a(te,{type:"primary",onClick:$=>re(z.processInstanceId)},{default:i(()=>[T("\u8DF3\u8F6C\u6D41\u7A0B")]),_:2},1032,["onClick"])])):se("",!0)]}),_:2},1024))),128))]),_:1})],2)),[[me,b]]):(h(),N(bt,{key:1,description:"\u5F53\u524D\u65E0\u64CD\u4F5C\u8BB0\u5F55"}))],2)],2)],2),x("div",{class:ke(r(g).type==="my"?"w-20%":"w-10%")},[a(Ce,null,{default:i(()=>[x("div",dn,[a(oe,{src:r(m),size:50,class:"mr-16px"},{default:i(()=>[sn]),_:1},8,["src"]),x("div",null,[x("div",null,w(r(f)),1),cn])])]),_:1}),x("div",un,[x("div",pn,[a(dt,{title:`${r(g).type==="my"?"\u6211":r(g).personName}\u7684\u6570\u636E`},null,8,["title"])]),x("div",fn,[x("div",mn,[(h(!0),j(ce,null,ve(r(Ka)("project_activities_status"),z=>Pe((h(),j("div",{key:z.value,class:ke(["data-item",`type-${z.colorType||"default"}`])},[x("div",null,w(z.label),1),x("div",null,w(Ke(z.value)),1),x("div",null,[a(ot,{icon:pe(z.value),class:"!text-1.8rem"},null,8,["icon"])])],2)),[[Br,z.label!=="\u6682\u505C"]])),128))])])])],2)],2)}}}),vn=qe(hn,[["__scopeId","data-v-6bd9b5b5"]]),je={getBasicsDistribution:()=>v.get({url:"/project/kanban/get-basics-distribution"}),getBasicsProgressDeviation:e=>v.get({url:"/project/kanban/get-basics-progress-deviation",params:e}),getBasicsProblemCount:e=>v.get({url:"/project/kanban/get-basics-problem-count",params:e}),getBasicsOfficeLevel:()=>v.get({url:"/project/kanban/get-team-level"}),getBasicsDelivery:e=>v.get({url:"/project/kanban/get-basics-delivery",params:e}),getReanalyzeList:()=>v.get({url:"/project/kanban/get-reanalyze"}),getPersonalAscension:e=>v.get({url:"/project/kanban/get-personal-ascension",params:e}),getWorkingHours:e=>v.get({url:"/project/kanban/get-working-hours",params:e}),getProblemDistribution:e=>v.get({url:"/project/kanban/get-problem-distribution",params:e}),getDeviation:e=>v.get({url:"/project/kanban/get-deviation",params:e}),getAdcpAchieved:e=>v.get({url:"/project/kanban/get-adcp-achieved/"+e}),getCruxAchieved:(e,t)=>v.get({url:"/project/kanban/get-crux-achieved/"+e+"/"+t}),getChangeProject:e=>v.get({url:"/project/kanban/get-change-project/"+e}),getProblemAchieved:e=>v.post({url:"/project/kanban/get-problem-achieved",data:e}),getWorkHoursNew:e=>v.post({url:"/project/kanban/get-working-hours-new",data:e}),getCruxNode:e=>v.get({url:"/project/kanban/get-crux-node",params:e}),getWorkHoursDetail:e=>v.get({url:"/project/kanban/get-work-hours-detail",params:e}),getProblemInfo:e=>v.get({url:"/project/kanban/get-problem-content",params:e}),getDataBoardList:e=>v.get({url:"/project/kanban/get-data-board",params:e}),getCruxDataBoardList:e=>v.get({url:"/project/kanban/get-crux-data-board",params:e}),getPersonDataBoardList:e=>v.get({url:"/project/kanban/get-person-data-board",params:e}),getProductProjectList:e=>v.get({url:"/project/kanban/get-product-project",params:e}),exportProductProject:e=>v.download({url:"/project/kanban/export-product-project",params:e})};var jt=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});const yn={getCategoryPage:e=>jt(void 0,null,function*(){return yield v.get({url:"/project/category/page",params:e})}),getCategoryList:e=>jt(void 0,null,function*(){return yield v.get({url:"/project/category/list",params:e})}),getCategoryListByUserRange:()=>jt(void 0,null,function*(){return yield v.get({url:"/project/category/list-range"})}),getCategory:e=>jt(void 0,null,function*(){return yield v.get({url:"/project/category/get?id="+e})}),createCategory:e=>jt(void 0,null,function*(){return yield v.post({url:"/project/category/create",data:e})}),updateCategory:e=>jt(void 0,null,function*(){return yield v.put({url:"/project/category/update",data:e})}),deleteCategory:e=>jt(void 0,null,function*(){return yield v.delete({url:"/project/category/delete?id="+e})}),exportCategory:e=>jt(void 0,null,function*(){return yield v.download({url:"/project/category/export-excel",params:e})})},_a={title:{text:"Referer of a Website",left:"center",textStyle:{fontSize:".9rem"}},tooltip:{trigger:"item"},legend:{orient:"vertical",left:"left",icon:"circle",itemHeight:5,itemGap:10,textStyle:{fontSize:".8rem",padding:[0,0,0,-8]}},series:[{name:"Access From",type:"pie",radius:"50%",data:[],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},label:{formatter:function(e){return`${e.name}
          [${e.percent.toFixed(1)}%]`},textStyle:{fontSize:".8rem"}}}],grid:{left:"10%",right:"4%",bottom:"3%",containLabel:!0}},hl=(e,t,l)=>{_a.title.text=l,_a.series[0].name=l,_a.series[0].data=e,_a&&t.setOption(_a)};let wa={tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{data:[]},xAxis:{},yAxis:[{type:"value"}],series:[],grid:{left:"2%",right:"2%",bottom:"2%",containLabel:!0}};const Ra=(e,t,l,n)=>{wa.legend={data:[e]},wa.xAxis=[{type:"category",axisTick:{show:!1},data:t}];const s=[];s.push({name:e,type:"bar",emphasis:{focus:"series"},label:{show:!0,position:"top"},data:l}),wa.series=s,wa&&n.setOption(wa,!0)};var Ft=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});const gn={getOfficeLevelPage:e=>Ft(void 0,null,function*(){return yield v.post({url:"/project/office-level/page",data:e})}),getOfficeLevel:e=>Ft(void 0,null,function*(){return yield v.get({url:"/project/office-level/get?id="+e})}),createOfficeLevel:e=>Ft(void 0,null,function*(){return yield v.post({url:"/project/office-level/create",data:e})}),updateOfficeLevel:e=>Ft(void 0,null,function*(){return yield v.put({url:"/project/office-level/update",data:e})}),deleteOfficeLevel:e=>Ft(void 0,null,function*(){return yield v.delete({url:"/project/office-level/delete?id="+e})}),exportOfficeLevel:e=>Ft(void 0,null,function*(){return yield v.download({url:"/project/office-level/export-excel",params:e})}),getOfficeLevelList:()=>Ft(void 0,null,function*(){return yield v.get({url:"/project/office-level/list"})})},bn={getErpList:e=>v.get({url:"/docking/erp-expense/list",params:e}),getEkuaibaoList:e=>v.get({url:"/docking/ekuaibao-expense/list",params:e}),getExpenseList:e=>v.get({url:"/docking/project-expense/list",params:e})};var _n=Object.defineProperty,vr=Object.getOwnPropertySymbols,wn=Object.prototype.hasOwnProperty,Cn=Object.prototype.propertyIsEnumerable,yr=(e,t,l)=>t in e?_n(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,xn=(e,t)=>{for(var l in t||(t={}))wn.call(t,l)&&yr(e,l,t[l]);if(vr)for(var l of vr(t))Cn.call(t,l)&&yr(e,l,t[l]);return e},$e=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});const Ot=e=>(qa("data-v-8ab3a057"),e=e(),$a(),e),kn={class:"p-l-10px h-full overflow-auto",ref:"containerRef"},Mn={class:"flex w-full justify-between"},Tn=Ot(()=>x("div",{id:"distribution-type",class:"w-30% h-220px"},null,-1)),Vn=Ot(()=>x("div",{id:"distribution-level",class:"w-30% h-220px"},null,-1)),zn={class:"flex items-center"},Ln={class:"flex"},En=Ot(()=>x("div",{class:"flex h-220px w-50% text-.8rem color-[var(--secondary-text-color)]"},[x("div",{class:"w-120px text-center bg-#f8f8f9",style:{height:"220px","line-height":"220px"}},"\u516C\u5F0F\u8BF4\u660E"),x("div",{class:"h-100%"},[x("div",{class:"h-33% border-1px border-solid border-#f1f1f1 p-10px"}," ADCP\u504F\u5DEE\u5929\u6570=ADCP\u5B9E\u9645\u5B8C\u6210\u65F6\u95F4-ADCP\u8BA1\u5212\u5B8C\u6210\u65F6\u95F4\u3002\u672A\u5B8C\u7ED3\u5219\u548C\u5F53\u524D\u65E5\u671F\u8BA1\u7B97 "),x("div",{class:"h-33% border-1px border-solid border-#f1f1f1 p-10px"}," \u81EA\u7814\u7C7BA1\u7B49\u7EA7\u9879\u76EEADCP\u504F\u5DEE\u5929\u6570=\uFF08\u81EA\u7814\u7C7BA1\u7B49\u7EA7\u9879\u76EE1ADCP\u504F\u5DEE\u5929\u6570+\u81EA\u7814\u7C7BA1\u7B49\u7EA7\u9879\u76EE2ADCP\u504F\u5DEE\u5929\u6570+...\uFF09/\u81EA\u7814\u7C7BA1\u7B49\u7EA7\u9879\u76EE\u603B\u6570\u5176\u4ED6\u540C\u6837\u7C7B\u578B\u4EE5\u6B64\u7C7B\u63A8 "),x("div",{class:"h-33% border-1px border-solid border-#f1f1f1 p-10px"}," \u81EA\u7814\u7C7B\u9879\u76EE\u7C7B\u578BADCP\u5E73\u5747\u504F\u5DEE\u5929\u6570=\u81EA\u7814\u7C7B\u9879\u76EEADCP\u504F\u5DEE\u5929\u6570\u603B\u548C/\u81EA\u7814\u7C7B\u9879\u76EE\u603B\u6570\u9879\u76EE\u7B49\u7EA7ADCP\u5E73\u5747\u504F\u5DEE\u5929\u6570\u4EE5\u6B64\u7C7B\u63A8 ")])],-1)),Pn={class:"flex h-200px w-100%"},Sn=Ot(()=>x("div",{id:"deviation-type",class:"w-50% h-200px"},null,-1)),In=Ot(()=>x("div",{id:"deviation-level",class:"w-50% h-200px"},null,-1)),An=[Sn,In],Hn={class:"flex items-center"},jn={class:"flex"},On=Ot(()=>x("div",{class:"flex h-220px w-50% text-.8rem color-[var(--secondary-text-color)]"},[x("div",{class:"w-120px text-center bg-#f8f8f9",style:{height:"220px","line-height":"220px"}}," \u516C\u5F0F\u8BF4\u660E "),x("div",{class:"h-100%"},[x("div",{class:"h-33% border-1px border-solid border-#f1f1f1 p-10px"}," \u5355\u4E2A\u9879\u76EE\u95EE\u9898\u6570\u91CF=\u5176\u4ED6\u95EE\u9898\u5217\u8868\u95EE\u9898\u6570\u91CF+\u5C0F\u8BD5\u95EE\u9898\u5217\u8868\u95EE\u9898\u6570\u91CF+\u4E2D\u8BD5\u95EE\u9898\u5217\u8868\u95EE\u9898\u6570\u91CF+TR\u8BC4\u5BA1\u95EE\u9898\u6570\u91CF "),x("div",{class:"h-33% border-1px border-solid border-#f1f1f1 p-10px"}," \u81EA\u7814\u7C7BA1\u7B49\u7EA7\u9879\u76EE\u5E73\u5747\u95EE\u9898\u6570=\uFF08\u81EA\u7814\u7C7BA1\u7B49\u7EA7\u9879\u76EE1\u95EE\u9898\u6570+\u81EA\u7814\u7C7BA1\u7B49\u7EA7\u9879\u76EE2\u5E73\u5747\u95EE\u9898\u6570+...\uFF09/\u81EA\u7814\u7C7BA1\u7B49\u7EA7\u9879\u76EE\u603B\u6570\u5176\u4ED6\u540C\u6837\u7C7B\u578B\u4EE5\u6B64\u7C7B\u63A8 "),x("div",{class:"h-33% border-1px border-solid border-#f1f1f1 p-10px"}," \u81EA\u7814\u7C7B\u9879\u76EE\u7C7B\u578B\u5E73\u5747\u95EE\u9898\u6570=\u81EA\u7814\u7C7B\u9879\u76EE\u95EE\u9898\u6570\u603B\u548C/\u81EA\u7814\u7C7B\u9879\u76EE\u603B\u6570\u9879\u76EE\u7B49\u7EA7\u95EE\u9898\u6570\uFF0C\u9879\u76EE\u7B49\u7EA7\u4EE5\u6B64\u7C7B\u63A8 ")])],-1)),Dn={class:"flex h-200px w-100%"},Rn=Ot(()=>x("div",{id:"problem-type",class:"w-50% h-200px"},null,-1)),Nn=Ot(()=>x("div",{id:"problem-level",class:"w-50% h-200px"},null,-1)),Yn=[Rn,Nn],Un={class:"flex items-center"},qn={class:"flex flex-wrap"},$n=["id"],Bn=["onUpdate:modelValue","onInput","onKeyup"],Fn=["onUpdate:modelValue","onInput","onKeyup"],Kn=["onUpdate:modelValue","onInput","onKeyup"],Wn=["onUpdate:modelValue","onInput","onKeyup"],Gn=["onUpdate:modelValue","onInput","onKeyup"],Zn=["onUpdate:modelValue","onInput","onKeyup"],Jn=["onUpdate:modelValue","onInput","onKeyup"],Xn=["onUpdate:modelValue","onInput","onKeyup"],Qn=Ie({__name:"ProjectCollection",setup(e){const t=p([]),l=()=>$e(this,null,function*(){t.value=Pt("project_team_role").map(I=>({label:I.label,value:I.value}))}),n=p([]),s=p([]),m=()=>$e(this,null,function*(){n.value=Pt("project_level").map(I=>({label:I.label,value:I.label})),s.value=Pt("project_level").map(I=>({label:I.label,value:I.value}))}),f=({option:I,row:$},F)=>$[F].toLowerCase().includes(I.data.toLowerCase()),c=p([{data:""}]),o=p([]),u=()=>$e(this,null,function*(){const I=yield yn.getCategoryList({});o.value=I}),g=p(!1),M=p([]),C=p([]),W=p(),O=p(),R=()=>$e(this,null,function*(){g.value=!0;try{const I=yield je.getBasicsDistribution();M.value=I,B()}finally{g.value=!1}}),B=()=>$e(this,null,function*(){const I=yield Pt("project_level");C.value=[],I.forEach(ae=>{C.value.push({level:ae.label,value:ae.value})}),C.value.forEach(ae=>{let fe=0;o.value.forEach(d=>{if(d.templateCategory){const y=M.value.find(H=>H.category==d.id&&H.level===ae.value);ae[d.id]=(y==null?void 0:y.total)||0,fe+=(y==null?void 0:y.total)||0}}),ae.total=fe}),yield Ue();const $=k(M.value,ae=>{const fe=o.value.find(d=>d.id===ae.category);return(fe==null?void 0:fe.name.replace("\u9879\u76EE",""))||""}),F=k(M.value,ae=>{var fe;return((fe=I.find(d=>d.value===ae.level))==null?void 0:fe.label)||""}),K=Object.entries($).map(([ae,fe])=>({name:ae,value:fe})),P=Object.entries(F).map(([ae,fe])=>({name:ae,value:fe}));var Z=document.getElementById("distribution-type");hl(K,Jt(Z),"\u9879\u76EE\u7C7B\u578B\u5360\u6BD4");var ne=document.getElementById("distribution-level");hl(P,Jt(ne),"\u9879\u76EE\u7B49\u7EA7\u5360\u6BD4")}),D=p(!1),V=p("year"),E=p(new Date),U=p([]),q=p([]),ee=p(),X=p(),Q=()=>$e(this,null,function*(){D.value=!0;try{const I=Re(E.value).startOf(V.value).format("YYYY-MM-DD"),$=Re(E.value).endOf(V.value).format("YYYY-MM-DD"),F=yield je.getBasicsProgressDeviation({startDate:I,endDate:$});U.value=F,xe()}finally{D.value=!1}}),xe=()=>$e(this,null,function*(){const I=yield Pt("project_level");q.value=[],I.forEach(Z=>{q.value.push({level:Z.label,value:Z.value})}),q.value.forEach(Z=>{let ne=0,ae=0;o.value.forEach(fe=>{if(fe.templateCategory){const d=U.value.find(y=>y.category==fe.id&&y.level===Z.value);Z[fe.id]=(d==null?void 0:d.total)||0,ne+=(d==null?void 0:d.total)||0,ae++}}),Z.total=Number((ne/ae).toFixed(2))}),yield Ue();const $=G(U.value,Z=>{const ne=o.value.find(ae=>ae.id===Z.category);return(ne==null?void 0:ne.name.replace("\u9879\u76EE",""))||""}),F=G(U.value,Z=>{var ne;return((ne=I.find(ae=>ae.value===Z.level))==null?void 0:ne.label)||""});var K=document.getElementById("deviation-type");Ra("\u9879\u76EE\u7B49\u7EA7ADCP\u5E73\u5747\u504F\u5DEE\u5929\u6570",Object.entries(F).map(([Z])=>Z),Object.entries(F).map(([,Z])=>Z),Jt(K));var P=document.getElementById("deviation-level");Ra("\u9879\u76EE\u7C7B\u578BADCP\u5E73\u5747\u504F\u5DEE\u5929\u6570",Object.entries($).map(([Z])=>Z),Object.entries($).map(([,Z])=>Z),Jt(P))}),re=p(!1),pe=p(new Date),Ve=p("year"),b=p([]),J=p([]),de=p(),ye=p(),S=()=>$e(this,null,function*(){re.value=!0;try{const I=Re(pe.value).startOf(Ve.value).format("YYYY-MM-DD"),$=Re(pe.value).endOf(Ve.value).format("YYYY-MM-DD"),F=yield je.getBasicsProblemCount({startDate:I,endDate:$});b.value=F,be()}finally{re.value=!1}}),be=()=>$e(this,null,function*(){const I=yield Pt("project_level");J.value=[],I.forEach(Z=>{J.value.push({level:Z.label,value:Z.value})}),J.value.forEach(Z=>{let ne=0,ae=0;o.value.forEach(fe=>{if(fe.templateCategory){const d=b.value.find(y=>y.category==fe.id&&y.level===Z.value);Z[fe.id]=(d==null?void 0:d.total)||0,ne+=(d==null?void 0:d.total)||0,ae++}}),Z.total=Number((ne/ae).toFixed(2))}),yield Ue();const $=G(b.value,Z=>{const ne=o.value.find(ae=>ae.id===Z.category);return(ne==null?void 0:ne.name.replace("\u9879\u76EE",""))||""}),F=G(b.value,Z=>{var ne;return((ne=I.find(ae=>ae.value===Z.level))==null?void 0:ne.label)||""});var K=document.getElementById("problem-type");Ra("\u9879\u76EE\u7B49\u7EA7\u5E73\u5747\u95EE\u9898\u6570\u91CF",Object.entries(F).map(([Z])=>Z),Object.entries(F).map(([,Z])=>Z),Jt(K));var P=document.getElementById("problem-level");Ra("\u9879\u76EE\u7C7B\u578B\u7C7B\u578B\u5E73\u5747\u95EE\u9898\u6570\u91CF",Object.entries($).map(([Z])=>Z),Object.entries($).map(([,Z])=>Z),Jt(P))}),L=p(!1),Y=p([]),ie=p([]),Te=p(),He=p(),tt=()=>$e(this,null,function*(){L.value=!0;try{const I=yield je.getBasicsOfficeLevel();Y.value=I,ie.value=yield gn.getOfficeLevelList()}finally{L.value=!1}}),ft=(I,$)=>{const F=new Set($),K=I.filter(P=>F.has(P)).length;return K==0?"":K},Je=p(!1),Ke=p([]),rt=p(new Date),it=p("year"),Dt=p([]),mt=p(),Xe=p(),he=()=>$e(this,null,function*(){Je.value=!0;try{const I=Re(rt.value).startOf(it.value).format("YYYY-MM-DD"),$=Re(rt.value).endOf(it.value).format("YYYY-MM-DD"),F=yield je.getBasicsDelivery({startDate:I,endDate:$});Ke.value=F,Dt.value=Rt(Ke.value),yield Ue(),o.value.filter(K=>K.templateCategory).forEach(K=>{const P=Ke.value.find(ae=>K.id==ae.categoryId);let Z=[];for(let ae in P)switch(ae){case"alarm":Z.push({name:"\u62A5\u8B66",value:P[ae]});break;case"normal":Z.push({name:"\u6B63\u5E38",value:P[ae]});break;case"overdue":Z.push({name:"\u5EF6\u671F",value:P[ae]});break;case"suspended":Z.push({name:"\u6682\u505C",value:P[ae]});break}var ne=document.getElementById("delivery"+K.id);hl(Z,Jt(ne),K.name.replace("\u9879\u76EE",""))})}finally{Je.value=!1}}),Rt=I=>["alarm","normal","overdue","suspended"].map($=>{let F="";switch($){case"alarm":F="\u91CC\u7A0B\u7891\u8FDB\u5EA6\u62A5\u8B66\uFF08\u8D85\u671F\u4E14\u672A\u5B8C\u6210\uFF09";break;case"normal":F="\u91CC\u7A0B\u7891\u8FDB\u5EA6\u6B63\u5E38\uFF08\u671F\u9650\u5185\u5B8C\u6210\uFF09";break;case"overdue":F="\u91CC\u7A0B\u7891\u8FDB\u5EA6\u5EF6\u671F\uFF08\u8D85\u671F\u4F46\u5B8C\u6210\uFF09";break;case"suspended":F="\u91CC\u7A0B\u7891\u8FDB\u5EA6\u6682\u505C";break}const K={title:F};let P=0;return o.value.forEach(Z=>{var ne;if(Z.templateCategory){const ae=((ne=I.find(fe=>fe.categoryId===Z.id))==null?void 0:ne[$])||0;K[Z.id]=ae,P+=ae}}),K.total=P,K}),Kt=p(!1),le=p([]),ue=p(),Se=p(),at=()=>$e(this,null,function*(){Kt.value=!0;try{const I=yield je.getReanalyzeList();le.value=I}finally{Kt.value=!1}}),ot=p(!1),kt=p([]),Oe=p(),Mt=p(),nt=p(new Date),dt=()=>$e(this,null,function*(){ot.value=!0;try{const I=yield bn.getExpenseList({endDate:Re(nt.value).format("YYYY-MM-DD")});kt.value=I}finally{ot.value=!1}}),We=p(!1),Tt=p([]),gt=p([]),Wt=p(),bt=p(),Gt=p(),ht=p(),da=()=>$e(this,null,function*(){We.value=!0;try{const I=yield je.getPersonalAscension({roles:"mould,attestation,iot,packing,testing,electronic,structure,pm",isCrux:!1});Tt.value=I}finally{We.value=!1}}),te=()=>$e(this,null,function*(){We.value=!0;try{const I=yield je.getPersonalAscension({roles:"se",isCrux:!0});gt.value=I}finally{We.value=!1}});function k(I,$){return I.reduce((F,K)=>{const P=$(K);return F[P]=(F[P]||0)+K.total,F},{})}function G(I,$){const F={};return I.forEach(K=>{const P=$(K);F[P]||(F[P]={total:0,count:0}),F[P].total+=K.total,F[P].count+=1}),Object.fromEntries(Object.entries(F).map(([K,{total:P,count:Z}])=>[K,Z>0?Number((P/Z).toFixed(2)):0]))}function oe({columns:I,data:$}){return[I.map((F,K)=>K===0?"\u5408\u8BA1":A($,F.field))]}function Ce({columns:I,data:$}){return[I.map((F,K)=>K===0?"\u5747\u503C":me($,F.field).toFixed(2))]}function A(I,$){let F=0;for(let K=0;K<I.length;K++)F+=I[K][$];return F}function me(I,$){let F=0;for(let K=0;K<I.length;K++)F+=I[K][$];return F/I.length}const z=(I,$)=>{if(!I||I.length==0)return[];let F="",K=0,P=0,Z=[];for(let ae=0;ae<I.length;ae++)for(let fe in I[ae]){I[ae][fe]!=F?(P>0&&Z.push({row:K,col:0,rowspan:P+1,colspan:0}),K=ae,P=0):P++,F=I[ae][fe];break}P>0&&Z.push({row:K,col:0,rowspan:P,colSpan:0});let ne=[];for(let ae of Z){ne.push(ae);for(let fe=1;fe<$;fe++){let d=xn({},ae);d.col=fe,ne.push(d)}}return ne};return lt(()=>$e(this,null,function*(){yield u(),yield tt(),yield l(),yield m(),setTimeout(()=>$e(this,null,function*(){const I=[R(),Q(),S()];yield Promise.all(I),yield Ue(),setTimeout(()=>{Promise.all([tt(),he(),at(),da(),te(),dt()]),setTimeout(()=>{var $,F,K,P,Z,ne,ae,fe,d;Promise.all([($=r(bt))==null?void 0:$.connect(r(Wt)),(F=r(ht))==null?void 0:F.connect(r(Gt)),(K=r(Se))==null?void 0:K.connect(r(ue)),(P=r(Xe))==null?void 0:P.connect(r(mt)),(Z=r(He))==null?void 0:Z.connect(r(Te)),(ne=r(ye))==null?void 0:ne.connect(r(de)),(ae=r(X))==null?void 0:ae.connect(r(ee)),(fe=r(O))==null?void 0:fe.connect(r(W)),(d=r(Mt))==null?void 0:d.connect(r(Oe))])},500)},1e3)}),0)})),(I,$)=>{const F=ba,K=Me("vxe-toolbar"),P=Me("vxe-column"),Z=Me("vxe-table"),ne=st,ae=Na,fe=Ya,d=sa,y=Me("vxe-colgroup"),H=Nr;return h(),j("div",kn,[a(ne,{shadow:"never"},{header:i(()=>[a(K,{size:"mini",custom:"",export:"",ref_key:"distributionToolbarRef",ref:W},{buttons:i(()=>[a(F,{title:"\u9879\u76EE\u5206\u5E03\u4E0E\u5360\u6BD4"})]),_:1},512)]),default:i(()=>[Pe((h(),j("div",Mn,[a(Z,{ref_key:"distributionTableRef",ref:O,"header-cell-style":{padding:0,fontSize:".8rem"},"cell-style":{padding:0,fontSize:".8rem"},"footer-cell-style":{padding:0,fontSize:".8rem"},data:r(C),"export-config":{type:"xlsx",isMerge:!0},"virtual-y-config":{enabled:!0,gt:0},class:"w-40%",align:"center",height:"220px",footerMethod:oe,"show-footer":""},{default:i(()=>[a(P,{field:"level",title:"\u7B49\u7EA7",width:"80"}),(h(!0),j(ce,null,ve(r(o).filter(_=>_.templateCategory),_=>(h(),N(P,{key:_.id,field:_.id,title:_.name.replace("\u9879\u76EE","")},null,8,["field","title"]))),128)),a(P,{field:"total",title:"\u5408\u8BA1",width:"60"})]),_:1},8,["data"]),Tn,Vn])),[[H,r(g)]])]),_:1}),a(ne,{shadow:"never",class:"m-t-10px"},{header:i(()=>[a(K,{size:"mini",custom:"",export:"",ref_key:"progressToolbarRef",ref:ee},{buttons:i(()=>[a(F,{title:"\u9879\u76EE\u6574\u4F53\u8FDB\u5EA6"})]),tools:i(()=>[x("div",zn,[a(fe,{size:"small",modelValue:r(V),"onUpdate:modelValue":$[0]||($[0]=_=>ze(V)?V.value=_:null),onChange:Q},{default:i(()=>[a(ae,{label:"\u6708",value:"month"}),a(ae,{label:"\u5E74",value:"year"})]),_:1},8,["modelValue"]),a(d,{modelValue:r(E),"onUpdate:modelValue":$[1]||($[1]=_=>ze(E)?E.value=_:null),type:r(V),placeholder:"\u9009\u62E9\u6708",size:"small",clearable:!1,onChange:Q},null,8,["modelValue","type"])])]),_:1},512)]),default:i(()=>[Pe((h(),j("div",Ln,[a(Z,{ref_key:"progressTableRef",ref:X,"header-cell-style":{padding:0,fontSize:".8rem"},"cell-style":{padding:0,fontSize:".8rem"},"footer-cell-style":{padding:0,fontSize:".8rem"},data:r(q),"export-config":{type:"xlsx",isMerge:!0},"virtual-y-config":{enabled:!0,gt:0},class:"w-50%",align:"center",height:"220px","show-footer":"","footer-method":Ce},{default:i(()=>[a(P,{field:"level",title:"\u7B49\u7EA7",width:"60"}),(h(!0),j(ce,null,ve(r(o).filter(_=>_.templateCategory),_=>(h(),N(P,{key:_.id,field:_.id,title:_.name.replace("\u9879\u76EE","")},null,8,["field","title"]))),128)),a(P,{field:"total",title:"\u5747\u503C",width:"60"})]),_:1},8,["data"]),En])),[[H,r(D)]]),Pe((h(),j("div",Pn,An)),[[H,r(D)]])]),_:1}),a(ne,{shadow:"never",class:"m-t-10px"},{header:i(()=>[a(K,{size:"mini",custom:"",export:"",ref_key:"problemToolbarRef",ref:de},{buttons:i(()=>[a(F,{title:"\u9879\u76EE\u8D28\u91CF"})]),tools:i(()=>[x("div",Hn,[a(fe,{size:"small",modelValue:r(Ve),"onUpdate:modelValue":$[2]||($[2]=_=>ze(Ve)?Ve.value=_:null),onChange:S},{default:i(()=>[a(ae,{label:"\u6708",value:"month"}),a(ae,{label:"\u5E74",value:"year"})]),_:1},8,["modelValue"]),a(d,{modelValue:r(pe),"onUpdate:modelValue":$[3]||($[3]=_=>ze(pe)?pe.value=_:null),type:r(Ve),placeholder:"\u9009\u62E9\u6708",size:"small",clearable:!1,onChange:S},null,8,["modelValue","type"])])]),_:1},512)]),default:i(()=>[Pe((h(),j("div",jn,[a(Z,{ref_key:"problemTableRef",ref:ye,"header-cell-style":{padding:0,fontSize:".8rem"},"cell-style":{padding:0,fontSize:".8rem"},"footer-cell-style":{padding:0,fontSize:".8rem"},data:r(J),"export-config":{type:"xlsx",isMerge:!0},"virtual-y-config":{enabled:!0,gt:0},class:"w-50%",align:"center",height:"220px","show-footer":"","footer-method":Ce},{default:i(()=>[a(P,{field:"level",title:"\u7B49\u7EA7",width:"60"}),(h(!0),j(ce,null,ve(r(o).filter(_=>_.templateCategory),_=>(h(),N(P,{key:_.id,field:_.id,title:_.name.replace("\u9879\u76EE","")},null,8,["field","title"]))),128)),a(P,{field:"total",title:"\u5747\u503C",width:"60"})]),_:1},8,["data"]),On])),[[H,r(re)]]),Pe((h(),j("div",Dn,Yn)),[[H,r(re)]])]),_:1}),a(ne,{shadow:"never",class:"m-t-10px"},{header:i(()=>[a(K,{custom:"",export:"",size:"mini",ref_key:"officeToolbarRef",ref:Te},{buttons:i(()=>[a(F,{title:"\u9879\u76EE\u5F00\u53D1\u4EBA\u5458\u7ED3\u6784"})]),_:1},512)]),default:i(()=>[a(Z,{ref_key:"officeTableRef",ref:He,"header-cell-style":{padding:0,fontSize:".8rem"},"cell-style":{padding:0,fontSize:".8rem"},"footer-cell-style":{padding:0,fontSize:".8rem"},data:r(Y),"export-config":{type:"xlsx",isMerge:!0},"merge-cells":z(r(Y),2),"virtual-y-config":{enabled:!0,gt:0},class:"w-100%",align:"center",height:"400px",border:"",loading:r(L)},{default:i(()=>[a(P,{field:"level",title:"\u9879\u76EE\u7B49\u7EA7",filters:r(n)},null,8,["filters"]),a(P,{field:"projectCount",title:"\u9879\u76EE\u6570\u91CF"}),a(P,{field:"role",title:"\u6D89\u53CA\u89D2\u8272",filters:r(t)},{default:i(({row:_})=>[T(w(r(et)("project_team_role",_.role)),1)]),_:1},8,["filters"]),a(P,{field:"userCount",title:"\u4EBA\u5458\u6570\u91CF"},{default:i(({row:_})=>{var Ne;return[T(w(((Ne=_.userIds)==null?void 0:Ne.length)||0),1)]}),_:1}),(h(!0),j(ce,null,ve(r(ie),_=>(h(),N(P,{key:_.id,title:r(et)("rank_of_officer",_.level)},{default:i(({row:Ne})=>[T(w(ft(_.userIds||[],Ne.userIds||[])),1)]),_:2},1032,["title"]))),128))]),_:1},8,["data","merge-cells","loading"])]),_:1}),a(ne,{shadow:"never",class:"m-t-10px"},{header:i(()=>[a(K,{size:"mini",custom:"",export:"",ref_key:"deliveryToolbarRef",ref:mt},{buttons:i(()=>[a(F,{title:"\u9879\u76EE\u4EA4\u4ED8"})]),tools:i(()=>[x("div",Un,[a(fe,{size:"small",modelValue:r(it),"onUpdate:modelValue":$[4]||($[4]=_=>ze(it)?it.value=_:null),onChange:he},{default:i(()=>[a(ae,{label:"\u6708",value:"month"}),a(ae,{label:"\u5E74",value:"year"})]),_:1},8,["modelValue"]),a(d,{modelValue:r(rt),"onUpdate:modelValue":$[5]||($[5]=_=>ze(rt)?rt.value=_:null),type:r(it),placeholder:"\u9009\u62E9\u6708",size:"small",clearable:!1,onChange:he},null,8,["modelValue","type"])])]),_:1},512)]),default:i(()=>[Pe((h(),j("div",qn,[a(Z,{ref_key:"deliveryTableRef",ref:Xe,border:"",data:r(Dt),height:"220px",class:"w-50%","scrollbar-always-on":"","header-cell-style":{padding:0,fontSize:".8rem"},"cell-style":{padding:0,fontSize:".8rem",height:"2rem"},"footer-cell-style":{padding:0,fontSize:".8rem"},"export-config":{type:"xlsx",isMerge:!0},"virtual-y-config":{enabled:!0,gt:0},align:"center","show-overflow":"","show-footer":"","footer-method":oe},{default:i(()=>[a(P,{title:"\u5B8C\u6210\u60C5\u51B5",width:"120",field:"title"}),(h(!0),j(ce,null,ve(r(o).filter(_=>_.templateCategory),_=>(h(),N(P,{key:_.id,field:_.id,title:_.name.replace("\u9879\u76EE","")},null,8,["field","title"]))),128)),a(P,{title:"\u5408\u8BA1",field:"total"})]),_:1},8,["data"]),(h(!0),j(ce,null,ve(r(o).filter(_=>_.templateCategory),_=>(h(),j("div",{key:_.id,id:`delivery${_.id}`,class:"w-25% h-220px"},null,8,$n))),128))])),[[H,r(Je)]])]),_:1}),a(ne,{shadow:"never",class:"m-t-10px"},{header:i(()=>[a(K,{custom:"",export:"",size:"small",ref_key:"reanalyzeToolbarRef",ref:ue},{buttons:i(()=>[a(F,{title:"\u9879\u76EE\u590D\u76D8"})]),_:1},512)]),default:i(()=>[a(Z,{ref_key:"reanalyzeTableRef",ref:Se,border:"",data:r(le),height:"400px",class:"w-100%","scrollbar-always-on":"","header-cell-style":{padding:0,fontSize:".8rem"},"cell-style":{padding:0,fontSize:".8rem",height:"2rem"},"footer-cell-style":{padding:0,fontSize:".8rem"},"export-config":{type:"xlsx",isMerge:!0,isColgroup:!0},"virtual-y-config":{enabled:!0,gt:0},align:"center","show-overflow":"",loading:r(Kt)},{default:i(()=>[a(y,{title:"\u9879\u76EE\u62A5\u8B66/\u6682\u505C\u4FE1\u606F",filed:"group1"},{default:i(()=>[a(P,{title:"\u9879\u76EE\u540D\u79F0",field:"name",filters:r(c),"filter-method":_=>f(_,"name")},{filter:i(({$panel:_,column:Ne})=>[(h(!0),j(ce,null,ve(Ne.filters,(_e,Ge)=>Pe((h(),j("input",{class:"my-input",type:"type",key:Ge,"onUpdate:modelValue":we=>_e.data=we,onInput:we=>_.changeOption(we,!!_e.data,_e),onKeyup:zt(we=>_.confirmFilter(),["enter"]),placeholder:"\u6309\u56DE\u8F66\u786E\u8BA4\u7B5B\u9009"},null,40,Bn)),[[Ut,_e.data]])),128))]),_:1},8,["filters","filter-method"]),a(P,{title:"\u9879\u76EE\u7C7B\u578B",field:"mold"},{default:i(({row:_})=>[T(w(r(et)("project_type",_.mold)),1)]),_:1}),a(P,{title:"\u9879\u76EE\u5E73\u53F0",field:"platform"},{default:i(({row:_})=>[T(w(r(et)("project_platform",_.platform)),1)]),_:1}),a(P,{title:"\u9879\u76EE\u7B49\u7EA7",field:"level",filters:r(s)},{default:i(({row:_})=>[T(w(r(et)("project_level",_.level)),1)]),_:1},8,["filters"]),a(P,{title:"\u4EA7\u54C1\u7ECF\u7406",field:"productUser",filters:r(c),"filter-method":_=>f(_,"productUser")},{filter:i(({$panel:_,column:Ne})=>[(h(!0),j(ce,null,ve(Ne.filters,(_e,Ge)=>Pe((h(),j("input",{class:"my-input",type:"type",key:Ge,"onUpdate:modelValue":we=>_e.data=we,onInput:we=>_.changeOption(we,!!_e.data,_e),onKeyup:zt(we=>_.confirmFilter(),["enter"]),placeholder:"\u6309\u56DE\u8F66\u786E\u8BA4\u7B5B\u9009"},null,40,Fn)),[[Ut,_e.data]])),128))]),_:1},8,["filters","filter-method"]),a(P,{title:"\u9879\u76EE\u7ECF\u7406",field:"pmUser",filters:r(c),"filter-method":_=>f(_,"pmUser")},{filter:i(({$panel:_,column:Ne})=>[(h(!0),j(ce,null,ve(Ne.filters,(_e,Ge)=>Pe((h(),j("input",{class:"my-input",type:"type",key:Ge,"onUpdate:modelValue":we=>_e.data=we,onInput:we=>_.changeOption(we,!!_e.data,_e),onKeyup:zt(we=>_.confirmFilter(),["enter"]),placeholder:"\u6309\u56DE\u8F66\u786E\u8BA4\u7B5B\u9009"},null,40,Kn)),[[Ut,_e.data]])),128))]),_:1},8,["filters","filter-method"]),a(P,{title:"SE",field:"seUser",filters:r(c),"filter-method":_=>f(_,"seUser")},{filter:i(({$panel:_,column:Ne})=>[(h(!0),j(ce,null,ve(Ne.filters,(_e,Ge)=>Pe((h(),j("input",{class:"my-input",type:"type",key:Ge,"onUpdate:modelValue":we=>_e.data=we,onInput:we=>_.changeOption(we,!!_e.data,_e),onKeyup:zt(we=>_.confirmFilter(),["enter"]),placeholder:"\u6309\u56DE\u8F66\u786E\u8BA4\u7B5B\u9009"},null,40,Wn)),[[Ut,_e.data]])),128))]),_:1},8,["filters","filter-method"]),a(P,{title:"\u72B6\u6001",field:"status"}),a(P,{title:"\u62A5\u8B66/\u6682\u505C\u65F6\u95F4",field:"endDate",formatter:r(Ht)},null,8,["formatter"]),a(P,{title:"\u5EF6\u8FDF\u5929\u6570",field:"extensionDays"})]),_:1}),a(y,{title:"\u62A5\u8B66\u5206\u6790",filed:"group2"},{default:i(()=>[a(P,{title:"\u539F\u56E0\u5206\u6790",field:"reason"}),a(P,{title:"\u91C7\u53D6\u63AA\u65BD",field:"measures"}),a(P,{title:"\u540E\u7EED\u8BA1\u5212",field:"plan"})]),_:1})]),_:1},8,["data","loading"])]),_:1}),a(ne,{shadow:"never",class:"m-t-10px"},{header:i(()=>[a(K,{custom:"",export:"",size:"small",ref_key:"costToolbarRef",ref:Oe},{buttons:i(()=>[a(F,{title:"\u9879\u76EE\u6210\u672C"})]),tools:i(()=>[a(d,{type:"month",modelValue:r(nt),"onUpdate:modelValue":$[6]||($[6]=_=>ze(nt)?nt.value=_:null),onChange:dt,clearable:!1},null,8,["modelValue"])]),_:1},512)]),default:i(()=>[a(Z,{ref_key:"costTableRef",ref:Mt,border:"",data:r(kt),height:"400px",class:"w-100%","scrollbar-always-on":"","header-cell-style":{padding:0,fontSize:".8rem"},"cell-style":{padding:0,fontSize:".8rem",height:"2rem"},"footer-cell-style":{padding:0,fontSize:".8rem"},"export-config":{type:"xlsx",isMerge:!0,isColgroup:!0},"virtual-y-config":{enabled:!0,gt:0},align:"center","show-overflow":"",loading:r(ot)},{default:i(()=>[a(P,{title:"\u9879\u76EE\u540D\u79F0",field:"name"}),a(P,{title:"\u9879\u76EE\u5F00\u59CB\u65F6\u95F4",field:"startDate",formatter:r(Ht)},null,8,["formatter"]),a(P,{title:"ADCP\u65F6\u95F4",field:"adcpDate",formatter:r(Ht)},null,8,["formatter"]),a(P,{title:"\u8BA1\u5212\u6210\u672C",field:"planCost"}),a(P,{title:"\u5B9E\u9645\u6210\u672C",field:"actualCost"}),a(P,{title:"\u8BA1\u5212\u8D39\u7528",field:"planExpense"}),a(P,{title:"\u5B9E\u9645\u8D39\u7528",field:"actualExpense"})]),_:1},8,["data","loading"])]),_:1}),a(ne,{shadow:"never",class:"m-t-10px"},{header:i(()=>[a(K,{size:"small",ref_key:"ascensionToolbarRef",ref:Gt,custom:"",export:""},{buttons:i(()=>[a(F,{title:"\u5DE5\u7A0B\u5E08\u4E2A\u4EBA\u6548\u80FD"})]),_:1},512)]),default:i(()=>[a(Z,{ref_key:"ascensionTableRef",ref:ht,border:"",data:r(Tt),height:"400px",class:"w-100%","scrollbar-always-on":"","header-cell-style":{padding:0,fontSize:".8rem"},"cell-style":{padding:0,fontSize:".8rem",height:"1rem"},"footer-cell-style":{padding:0,fontSize:".8rem"},"export-config":{type:"xlsx",isMerge:!0},"merge-cells":z(r(Tt),3),"virtual-y-config":{enabled:!0,gt:0},align:"center","show-overflow":"",loading:r(We)},{default:i(()=>[a(P,{title:"\u59D3\u540D",field:"nickname",filters:r(c),"filter-method":_=>f(_,"nickname")},{filter:i(({$panel:_,column:Ne})=>[(h(!0),j(ce,null,ve(Ne.filters,(_e,Ge)=>Pe((h(),j("input",{class:"my-input",type:"type",key:Ge,"onUpdate:modelValue":we=>_e.data=we,onInput:we=>_.changeOption(we,!!_e.data,_e),onKeyup:zt(we=>_.confirmFilter(),["enter"]),placeholder:"\u6309\u56DE\u8F66\u786E\u8BA4\u7B5B\u9009"},null,40,Gn)),[[Ut,_e.data]])),128))]),_:1},8,["filters","filter-method"]),a(P,{title:"\u804C\u7EA7",field:"officeLevel"}),a(P,{title:"\u9879\u76EE\u6570\u91CF",field:"projectCount"}),a(P,{title:"\u9879\u76EE\u7B49\u7EA7",field:"level",filters:r(s)},{default:i(({row:_})=>[T(w(r(et)("project_level",_.level)),1)]),_:1},8,["filters"]),a(P,{title:"\u9879\u76EE\u540D\u79F0",field:"name",filters:r(c),"filter-method":_=>f(_,"name")},{filter:i(({$panel:_,column:Ne})=>[(h(!0),j(ce,null,ve(Ne.filters,(_e,Ge)=>Pe((h(),j("input",{class:"my-input",type:"type",key:Ge,"onUpdate:modelValue":we=>_e.data=we,onInput:we=>_.changeOption(we,!!_e.data,_e),onKeyup:zt(we=>_.confirmFilter(),["enter"]),placeholder:"\u6309\u56DE\u8F66\u786E\u8BA4\u7B5B\u9009"},null,40,Zn)),[[Ut,_e.data]])),128))]),_:1},8,["filters","filter-method"]),a(P,{title:"\u9879\u76EE\u5F00\u59CB\u65F6\u95F4",field:"startDate",formatter:r(Ht)},null,8,["formatter"]),a(P,{title:"\u9879\u76EE\u9884\u8BA1\u7ED3\u675F\u65F6\u95F4",field:"endDate",formatter:r(Ht)},null,8,["formatter"]),a(P,{title:"\u8BA1\u5212\u9879\u76EE\u5468\u671F\u5929\u6570",field:"planDays"}),a(P,{title:"\u9879\u76EE\u504F\u5DEE\u5929\u6570",field:"deviationDays"}),a(P,{title:"\u9879\u76EE\u89D2\u8272",field:"role",filters:r(t)},{default:i(({row:_})=>[T(w(r(et)("project_team_role",_.role)),1)]),_:1},8,["filters"]),a(P,{title:"\u5B9E\u9645\u5DE5\u65F6(\u5929)",field:"sumDays"}),a(P,{title:"\u6D3B\u52A8\u5E73\u5747\u504F\u5DEE\u5929\u6570",field:"avgDays"}),a(P,{title:"\u95EE\u9898\u6570\u91CF",field:"problemTotal"})]),_:1},8,["data","merge-cells","loading"])]),_:1}),a(ne,{shadow:"never",class:"m-t-10px"},{header:i(()=>[a(K,{size:"small",custom:"",export:"",ref_key:"ascensionSeToolbarRef",ref:Wt},{buttons:i(()=>[a(F,{title:"SE\u4E2A\u4EBA\u6548\u80FD"})]),_:1},512)]),default:i(()=>[a(Z,{ref_key:"ascensionSeTableRef",ref:bt,border:"",data:r(gt),height:"400px",class:"w-100%","scrollbar-always-on":"","header-cell-style":{padding:0,fontSize:".8rem"},"cell-style":{padding:0,fontSize:".8rem",height:"2rem"},"footer-cell-style":{padding:0,fontSize:".8rem"},"export-config":{type:"xlsx",isMerge:!0},"merge-cells":z(r(gt),3),"virtual-y-config":{enabled:!0,gt:0},align:"center","show-overflow":"",loading:r(We)},{default:i(()=>[a(P,{title:"\u59D3\u540D",field:"nickname",filters:r(c),"filter-method":_=>f(_,"nickname")},{filter:i(({$panel:_,column:Ne})=>[(h(!0),j(ce,null,ve(Ne.filters,(_e,Ge)=>Pe((h(),j("input",{class:"my-input",type:"type",key:Ge,"onUpdate:modelValue":we=>_e.data=we,onInput:we=>_.changeOption(we,!!_e.data,_e),onKeyup:zt(we=>_.confirmFilter(),["enter"]),placeholder:"\u6309\u56DE\u8F66\u786E\u8BA4\u7B5B\u9009"},null,40,Jn)),[[Ut,_e.data]])),128))]),_:1},8,["filters","filter-method"]),a(P,{title:"\u804C\u7EA7",field:"officeLevel"}),a(P,{title:"\u9879\u76EE\u6570\u91CF",field:"projectCount"}),a(P,{title:"\u9879\u76EE\u7B49\u7EA7",field:"level",filters:r(s)},{default:i(({row:_})=>[T(w(r(et)("project_level",_.level)),1)]),_:1},8,["filters"]),a(P,{title:"\u9879\u76EE\u540D\u79F0",field:"name",filters:r(c),"filter-method":_=>f(_,"name")},{filter:i(({$panel:_,column:Ne})=>[(h(!0),j(ce,null,ve(Ne.filters,(_e,Ge)=>Pe((h(),j("input",{class:"my-input",type:"type",key:Ge,"onUpdate:modelValue":we=>_e.data=we,onInput:we=>_.changeOption(we,!!_e.data,_e),onKeyup:zt(we=>_.confirmFilter(),["enter"]),placeholder:"\u6309\u56DE\u8F66\u786E\u8BA4\u7B5B\u9009"},null,40,Xn)),[[Ut,_e.data]])),128))]),_:1},8,["filters","filter-method"]),a(P,{title:"\u9879\u76EE\u5F00\u59CB\u65F6\u95F4",field:"startDate",formatter:r(Ht)},null,8,["formatter"]),a(P,{title:"\u9879\u76EE\u9884\u8BA1\u7ED3\u675F\u65F6\u95F4",field:"endDate",formatter:r(Ht)},null,8,["formatter"]),a(P,{title:"\u8BA1\u5212\u9879\u76EE\u5468\u671F\u5929\u6570",field:"planDays"}),a(P,{title:"\u9879\u76EE\u504F\u5DEE\u5929\u6570",field:"deviationDays"}),a(P,{title:"\u9879\u76EE\u89D2\u8272",field:"role",filters:r(t)},{default:i(({row:_})=>[T(w(r(et)("project_team_role",_.role)),1)]),_:1},8,["filters"]),a(P,{title:"\u5B9E\u9645\u5DE5\u65F6(\u5929)",field:"sumDays"}),a(P,{title:"\u9879\u76EE\u91CC\u7A0B\u7891\u5E73\u5747\u504F\u5DEE\u5929\u6570",field:"avgDays"}),a(P,{title:"\u95EE\u9898\u6570\u91CF",field:"problemTotal"})]),_:1},8,["data","merge-cells","loading"])]),_:1})],512)}}}),e2=qe(Qn,[["__scopeId","data-v-8ab3a057"]]),gr={saveReanalyze:e=>v.post({url:"/project/reanalyze/save",data:e}),getReanalyze:()=>v.get({url:"/project/reanalyze/list"})};var br=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});const t2={class:"h-full p-10px"},a2=Ie({__name:"Reanalyze",setup(e){const t=p([]),l=p(!1),n=p(),s=St(),m=p({id:void 0,activitiesId:void 0,reason:void 0,measures:void 0,plan:void 0}),f=p(!1),c=()=>br(this,null,function*(){l.value=!0;try{const g=yield gr.getReanalyze();t.value=g}finally{l.value=!1}}),o=g=>{f.value=!0,m.value={id:g.id,activitiesId:g.nodeId,reason:g.reason,measures:g.measures,plan:g.plan}},u=()=>br(this,null,function*(){try{yield gr.saveReanalyze(m.value),s.success("\u4FDD\u5B58\u6210\u529F"),f.value=!1,c()}finally{}});return lt(()=>{c()}),(g,M)=>{const C=Me("vxe-column"),W=Me("vxe-colgroup"),O=vt,R=Me("vxe-table"),B=Rr,D=ka,V=Tl,E=Vl,U=Et;return h(),j("div",t2,[r(t).length>0?(h(),N(R,{key:0,border:"",data:r(t),height:"100%",class:"w-100%","scrollbar-always-on":"","header-cell-style":{padding:0,fontSize:".8rem"},"cell-style":{padding:0,fontSize:".8rem",height:"2rem"},"footer-cell-style":{padding:0,fontSize:".8rem"},"scroll-y":{enabled:!0,gt:0},align:"center","show-overflow":"",loading:r(l),"edit-config":{trigger:"manual",mode:"row"},ref_key:"tableRef",ref:n},{default:i(()=>[a(W,{title:"\u9879\u76EE\u62A5\u8B66/\u6682\u505C\u4FE1\u606F"},{default:i(()=>[a(C,{title:"\u9879\u76EE\u540D\u79F0",field:"name"}),a(C,{title:"\u9879\u76EE\u7C7B\u578B",field:"mold"},{default:i(({row:q})=>[T(w(r(et)("project_type",q.mold)),1)]),_:1}),a(C,{title:"\u9879\u76EE\u5E73\u53F0",field:"platform"},{default:i(({row:q})=>[T(w(r(et)("project_platform",q.platform)),1)]),_:1}),a(C,{title:"\u9879\u76EE\u7B49\u7EA7",field:"level"},{default:i(({row:q})=>[T(w(r(et)("project_level",q.level)),1)]),_:1}),a(C,{title:"\u4EA7\u54C1\u7ECF\u7406",field:"productUser"}),a(C,{title:"\u9879\u76EE\u7ECF\u7406",field:"pmUser"}),a(C,{title:"SE",field:"seUser"}),a(C,{title:"\u72B6\u6001",field:"status"}),a(C,{title:"\u62A5\u8B66/\u6682\u505C\u65F6\u95F4",field:"endDate",formatter:r(Ht)},null,8,["formatter"]),a(C,{title:"\u5EF6\u8FDF\u5929\u6570",field:"extensionDays"})]),_:1}),a(W,{title:"\u62A5\u8B66\u5206\u6790"},{default:i(()=>[a(C,{title:"\u539F\u56E0\u5206\u6790",field:"reason"}),a(C,{title:"\u91C7\u53D6\u63AA\u65BD",field:"measures"}),a(C,{title:"\u540E\u7EED\u8BA1\u5212",field:"plan"}),a(C,{title:"\u64CD\u4F5C",width:"160"},{default:i(({row:q})=>[a(O,{type:"primary",size:"small",plain:"",onClick:ee=>o(q)},{default:i(()=>[T("\u586B\u5199")]),_:2},1032,["onClick"])]),_:1})]),_:1})]),_:1},8,["data","loading"])):(h(),N(B,{key:1,class:"w-full h-full bg-white",description:"\u60A8\u8D1F\u8D23\u7684\u9879\u76EE\u4E0B\u6682\u65E0\u9700\u8981\u590D\u76D8\u7684\u5173\u952E\u6D3B\u52A8"})),a(U,{modelValue:r(f),"onUpdate:modelValue":M[3]||(M[3]=q=>ze(f)?f.value=q:null),title:"\u4FEE\u6539",width:"50%"},{footer:i(()=>[a(O,{type:"primary",onClick:u},{default:i(()=>[T("\u4FDD\u5B58")]),_:1})]),default:i(()=>[a(E,{"label-width":"100px"},{default:i(()=>[a(V,{label:"\u539F\u56E0\u5206\u6790"},{default:i(()=>[a(D,{type:"textarea",rows:4,modelValue:r(m).reason,"onUpdate:modelValue":M[0]||(M[0]=q=>r(m).reason=q)},null,8,["modelValue"])]),_:1}),a(V,{label:"\u91C7\u53D6\u63AA\u65BD"},{default:i(()=>[a(D,{type:"textarea",rows:4,modelValue:r(m).measures,"onUpdate:modelValue":M[1]||(M[1]=q=>r(m).measures=q)},null,8,["modelValue"])]),_:1}),a(V,{label:"\u540E\u7EED\u8BA1\u5212"},{default:i(()=>[a(D,{type:"textarea",rows:4,modelValue:r(m).plan,"onUpdate:modelValue":M[2]||(M[2]=q=>r(m).plan=q)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"])])}}});var Fe=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});const l2={getProblemPage:e=>Fe(void 0,null,function*(){return yield v.get({url:"/project/problem/page",params:e})}),getProblemExportPage:e=>Fe(void 0,null,function*(){return yield v.post({url:"/project/problem/page-export",data:e})}),getProblemList:e=>Fe(void 0,null,function*(){return yield v.get({url:"/project/problem/list",params:e})}),getProblem:e=>Fe(void 0,null,function*(){return yield v.get({url:"/project/problem/get?id="+e})}),createProblem:e=>Fe(void 0,null,function*(){return yield v.post({url:"/project/problem/create",data:e})}),updateProblem:e=>Fe(void 0,null,function*(){return yield v.put({url:"/project/problem/update",data:e})}),updateProblemBatch:e=>Fe(void 0,null,function*(){return yield v.put({url:"/project/problem/update-batch",data:e})}),deleteProblem:e=>Fe(void 0,null,function*(){return yield v.delete({url:"/project/problem/delete?id="+e})}),exportProblem:e=>Fe(void 0,null,function*(){return yield v.downloadPost({url:"/project/problem/export-excel",data:e})}),exportProblemMulti:e=>Fe(void 0,null,function*(){return yield v.downloadPost({url:"/project/problem/export-multi",data:e})}),updateProblemFeedback:e=>Fe(void 0,null,function*(){return yield v.post({url:"/project/problem/feedback",data:e})}),updateProblemCoordinate:e=>Fe(void 0,null,function*(){return yield v.post({url:"/project/problem/coordinate",data:e})}),getStageCount:e=>Fe(void 0,null,function*(){return yield v.get({url:`/project/problem/get-stage-count/${e}`})}),updateProblemShare:e=>Fe(void 0,null,function*(){return yield v.get({url:"/project/problem/update-problem-share",params:e})}),getProblemBank:e=>Fe(void 0,null,function*(){return yield v.post({url:"/project/problem/get-problem-bank",data:e})}),exportProblemTemplate:()=>Fe(void 0,null,function*(){return yield v.download({url:"/project/problem/export-template"})})},r2={getActivitiesList:e=>v.post({url:"/project/activities/list",data:e}),getActivitiesSimpleList:e=>v.post({url:"/project/activities/simple-list",data:e}),getActivitiesPage:e=>v.post({url:"/project/activities/page",data:e}),getActivities:e=>v.get({url:`/project/activities/get/${e}`}),getActivitiesDisassembleList:e=>v.post({url:"/project/activities/list-disassemble",data:e}),createActivitiesBatch:e=>v.post({url:"/project/activities/create-batch",data:e}),createActivities:e=>v.post({url:"/project/activities/create",data:e}),updateActivitiesCoordinate:e=>v.post({url:"/project/activities/update-coordinate",data:e}),exportActivities:e=>v.downloadPost({url:"/project/activities/export-excel",data:e}),exportActivitiesMulti:e=>v.downloadPost({url:"/project/activities/export-multi",data:e}),getStageCount:e=>v.get({url:`/project/activities/get-stage-count/${e}`}),getCruxInfoPage:e=>v.get({url:"/project/activities/get-crux-info-page",params:e}),getWorkingHoursTotal:e=>v.get({url:`/project/activities/get-working-hours-total/${e}`}),exportWorkingHours:e=>v.download({url:"/project/activities/export-crux-and-hours",params:e}),checkActivitiesStatus:e=>v.get({url:`/project/activities/check-activities-status/${e}`}),getDateChangeLog:e=>v.get({url:`/project/activities/get-date-change-log/${e}`}),updateWorkHours:e=>v.post({url:"/project/activities/update-work-hours",data:e}),updateAuditWrokHours:e=>v.get({url:"/project/activities/update-audit-work-hours",params:{id:e}})};var i2=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});const o2={class:"flex p-5px justify-between"},n2=Ie({__name:"ToProjectCenter",props:{name:Le.string.isRequired,basicsId:Le.number.isRequired,activitiesId:Le.number,problemId:Le.number,itemId:Le.string},setup(e){const t=e,l=Pl(),{wsCache:n}=Lt(),{getUser:s}=_t(),m=n.get(Ee.USER).permissions,f=St(),c=o=>i2(this,null,function*(){var u,g,M,C;if(!t.basicsId){f.alertError("\u8BF7\u5237\u65B0\u540E\u91CD\u8BD5");return}const W=yield Da.getSimpleBasics(t.basicsId);if(t.activitiesId&&o==="activities"){const O=yield r2.getActivities(t.activitiesId);n.set("project_page_show_form",{categoryId:(u=W.categoryIds)==null?void 0:u[0],basicsId:t.basicsId,page:o,stage:O.stage,id:O.id,status:W.status})}else if(t.problemId&&o==="problem"){const O=yield l2.getProblem(t.problemId);n.set("project_page_show_form",{categoryId:(g=W.categoryIds)==null?void 0:g[0],basicsId:t.basicsId,page:o,stage:O.type,id:O.id,status:W.status})}else t.itemId&&o==="material"?n.set("project_page_show_form",{categoryId:(M=W.categoryIds)==null?void 0:M[0],basicsId:t.basicsId,page:o,status:W.status,id:t.itemId}):n.set("project_page_show_form",{categoryId:(C=W.categoryIds)==null?void 0:C[0],basicsId:t.basicsId,page:o,status:W.status});l.push({name:"ProjectCenter"})});return(o,u)=>{const g=vt,M=zl;return!t.activitiesId&&!t.problemId&&!t.itemId?(h(),N(M,{key:0,trigger:"click",width:"700px"},{reference:i(()=>[a(g,{type:"primary",link:""},{default:i(()=>[T(w(t.name),1)]),_:1})]),default:i(()=>[x("div",o2,[x("div",{class:"path-item",onClick:u[0]||(u[0]=C=>c("activities"))},"\u6D3B\u52A8"),x("div",{class:"path-item",onClick:u[1]||(u[1]=C=>c("problem"))},"\u95EE\u9898"),x("div",{class:"path-item",onClick:u[2]||(u[2]=C=>c("risk"))},"\u98CE\u9669"),x("div",{class:"path-item",onClick:u[3]||(u[3]=C=>c("technical"))},"\u6280\u672F\u8BC4\u5BA1"),x("div",{class:"path-item",onClick:u[4]||(u[4]=C=>c("tcp"))},"\u51B3\u7B56\u8BC4\u5BA1"),x("div",{class:"path-item",onClick:u[5]||(u[5]=C=>c("conference"))},"\u4F1A\u8BAE\u7EAA\u8981"),x("div",{class:"path-item",onClick:u[6]||(u[6]=C=>c("document"))},"\u8F93\u51FA\u7269"),x("div",{class:"path-item",onClick:u[7]||(u[7]=C=>c("gantt"))},"\u7518\u7279\u56FE"),r(Nl)("pm",r(s).id)||r(Nl)("financial",r(s).id)||r(s).id==1||r(m).includes("project:expense:view")?(h(),j("div",{key:0,class:"path-item",onClick:u[8]||(u[8]=C=>c("expense"))}," \u8D39\u7528 ")):se("",!0),r(Nl)("pm",r(s).id)||r(s).id==1?(h(),j("div",{key:1,class:"path-item",onClick:u[9]||(u[9]=C=>c("kanban"))}," \u770B\u677F ")):se("",!0),x("div",{class:"path-item",onClick:u[10]||(u[10]=C=>c("material"))},"\u7269\u6599")])]),_:1})):(h(),N(g,{key:1,type:"primary",link:"",onClick:u[11]||(u[11]=C=>c(t.activitiesId?"activities":t.problemId?"problem":"material"))},{default:i(()=>[T(w(t.name),1)]),_:1}))}}}),Be=qe(n2,[["__scopeId","data-v-e7d607ff"]]);var d2=Object.defineProperty,_r=Object.getOwnPropertySymbols,s2=Object.prototype.hasOwnProperty,c2=Object.prototype.propertyIsEnumerable,wr=(e,t,l)=>t in e?d2(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,vl=(e,t)=>{for(var l in t||(t={}))s2.call(t,l)&&wr(e,l,t[l]);if(_r)for(var l of _r(t))c2.call(t,l)&&wr(e,l,t[l]);return e},yt=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});const Ca=e=>(qa("data-v-80c9e991"),e=e(),$a(),e),u2={class:"p-l-10px h-full overflow-auto",ref:"containerRef"},p2=Ca(()=>x("div",{class:"over-tip"},[x("span",null," \u7EDF\u8BA1\u516C\u5F0F\uFF1AADCP\u6309\u671F\u8FBE\u6210\u7387=\u5468\u671F\u5185\u5B9E\u9645\u6309\u671F\u8FBE\u6210ADCP\u4E2A\u6570\xF7\u5468\u671F\u5185\u8BA1\u5212\u8FBE\u6210ADCP\u4E2A\u6570x100% \u5907\u6CE8\uFF1A\u5E74\u5EA6\u4E0E\u6708\u5EA6\u5747\u4F53\u73B0ADCP\u6309\u671F\u8FBE\u6210\u7684\u60C5\u51B5\uFF0C\u6708\u5EA6\u672A\u6309\u671F\u5B8C\u6210\u7684ADCP\u4E0D\u4F1A\u8BA1\u5165\u4E0B\u4E00\u4E2A\u6708\u5EA6\u7684\u6570\u636E\u7EDF\u8BA1\u4E2D\u3002 ")],-1)),f2=Ca(()=>x("div",{class:"over-tip"},[x("span",null," \u7EDF\u8BA1\u516C\u5F0F\uFF1A\u91CC\u7A0B\u7891\u6309\u671F\u8FBE\u6210\u7387=\u5468\u671F\u5185\u5B9E\u9645\u6309\u671F\u8FBE\u6210\u91CC\u7A0B\u7891\u4E2A\u6570\xF7\u5468\u671F\u5185\u8BA1\u5212\u8FBE\u6210\u91CC\u7A0B\u7891\u4E2A\u6570x100% ")],-1)),m2=Ca(()=>x("div",{class:"over-tip"},[x("span",null," \u7EDF\u8BA1\u516C\u5F0F\uFF1A\u6D3B\u52A8\u5DE5\u65F6\u8FBE\u6210\u7387=\u5468\u671F\u5185\u8FBE\u6210\u7684\u6D3B\u52A8\u7684\u6807\u51C6\u5DE5\u65F6\uFF08\u5373\u5B9E\u9645\u5DE5\u65F6\uFF09\xF7\u5468\u671F\u5185\u516C\u53F8\u89C4\u5B9A\u51FA\u52E4\u5DE5\u65F6\uFF08\u5373\u8BA1\u5212\u5DE5\u65F6\uFF09x100% ")],-1)),h2=Ca(()=>x("div",{class:"over-tip"},[x("span",null," \u7EDF\u8BA1\u516C\u5F0F\uFF1A\u95EE\u9898\u6309\u671F\u5173\u95ED\u7387=\u5468\u671F\u5185\u5B9E\u9645\u6309\u671F\u5173\u95ED\u95EE\u9898\u4E2A\u6570\xF7\u5468\u671F\u5185\u8BA1\u5212\u5173\u95ED\u95EE\u9898\u4E2A\u6570x100% \u5907\u6CE8\uFF1A\u5E74\u5EA6/\u6708\u5EA6/\u5468\u5EA6\u5747\u4F53\u73B0\u95EE\u9898\u6309\u671F\u5173\u95ED\u7684\u60C5\u51B5\uFF0C\u672C\u5468\u671F\u672A\u6309\u671F\u5B8C\u6210\u7684\u95EE\u9898\u6570\u4E0D\u4F1A\u8BA1\u5165\u4E0B\u4E00\u4E2A\u5468\u671F\u7684\u6570\u636E\u4E2D\u3002 ")],-1)),v2=Ca(()=>x("div",{class:"over-tip"},[x("span",null," \u7EDF\u8BA1\u516C\u5F0F\uFF1A\u9879\u76EE\u53D8\u66F4\u6B21\u6570=\u9879\u76EE\u7ECF\u7406\u63D0\u4EA4\u7684\u5173\u4E8E\u9879\u76EE\u4FE1\u606F\u3001\u6D3B\u52A8\u4FE1\u606F\u53D8\u66F4\u7684\u6B21\u6570 \u5907\u6CE8\uFF1A\u56E01-4\u6708\u672A\u5728\u7CFB\u7EDF\u4E0A\u56FA\u5316\u9879\u76EE\u53D8\u66F4\u6D41\u7A0B\uFF0C\u6545\u65E0\u53D6\u6570\u30025\u6708\u4EFD\u6570\u636E\u4E3A\u65B0\u7CFB\u7EDF\u4E0A\u7EBF\u4E0E\u6A21\u5757\u6D4B\u8BD5\u6545\u6B64\u6570\u636E\u65E0\u53C2\u8003\u6027\uFF0C6\u6708\u4E4B\u540E\u7684\u6570\u636E\u65B9\u53EF\u4F7F\u7528\u8FDB\u884C\u5206\u6790\u3002 ")],-1)),y2={key:0},g2={key:1},b2=Ie({__name:"Workhours",setup(e){const t=p([{data:[]}]),l=p([{data:[]}]);ge.locale("zh-cn",{week:{dow:1,doy:1}}),ge.locale("zh-cn");const n=p(Re().format("YYYY")),s=p(!1),m=p([]),f=p(),c=p(),o=()=>yt(this,null,function*(){s.value=!0;try{const te=yield je.getAdcpAchieved(Number(n.value));m.value=te.reduce((k,G)=>{if(!G||!G.manager||!G.month)return k;const{manager:oe,month:Ce,planCount:A,actualCount:me,rate:z}=G,I=Ce.toString(),$=k.find(F=>F.manager===oe);return $?$.data[I]={planCount:A,actualCount:me,rate:z}:k.push({manager:oe,data:{[I]:{planCount:A,actualCount:me,rate:z}}}),k},[]).map(k=>{const G=new Date().getFullYear(),oe=new Date().getMonth()+1,Ce=Number(n.value)===G?oe:12;let A=0,me=0;for(let $=1;$<=Ce;$++){const F=$.toString();k.data[F]&&(A+=k.data[F].planCount,me+=k.data[F].actualCount)}k.data[99]={planCount:A,actualCount:me,rate:A?(me/A*100).toFixed(1):"0.0"};let z=0,I=0;for(let $=1;$<=12;$++){const F=$.toString();k.data[F]&&(z+=k.data[F].planCount,I+=k.data[F].actualCount)}return k.data.annual={planCount:z,actualCount:I,rate:z?(I/z*100).toFixed(1):"0.0"},k}).sort((k,G)=>k.manager.localeCompare(G.manager))}finally{s.value=!1}});function u({columns:te,data:k}){return[te.map((G,oe)=>oe===0?"\u9879\u76EE\u96C6":G.field.includes("rate")?((C(k,te[oe-1].field)/C(k,te[oe-2].field)||0)*100).toFixed(1)+"%":C(k,G.field))]}function g({columns:te,data:k}){return[te.map((G,oe)=>oe===0?"\u9879\u76EE\u96C6":G.field.includes("percent")?((W(k,te[oe-1].field)/W(k,te[oe-2].field)||0)*100).toFixed(1)+"%":W(k,G.field))]}function M({columns:te,data:k}){return[te.map((G,oe)=>oe===0?"\u5408\u8BA1":G.field.includes("avgHours")?O(k,G.field).toFixed(2):"")]}function C(te,k){var G;let oe=0;for(let Ce=0;Ce<te.length;Ce++){const A=k.split("-");oe+=((G=te[Ce].data[A[1]])==null?void 0:G[A[0]])||0}return oe}function W(te,k){var G;let oe=0;for(let Ce=0;Ce<te.length;Ce++){const A=k.split("-");oe+=((G=te[Ce].data[A[1]])==null?void 0:G[A[0]])||0}return oe}function O(te,k){var G;let oe=0;for(let Ce=0;Ce<te.length;Ce++)oe+=((G=te[Ce])==null?void 0:G[k])||0;return oe}const R=p(Re().format("YYYY")),B=p(!1),D=p([]),V=p(),E=p(),U=p("year"),q=Ye(()=>Array.from({length:ge().year(Number(R.value)).isoWeeksInYear()+2},(te,k)=>k+1)),ee=Ye(()=>Array.from({length:ge().year(Number(Ke.value)).isoWeeksInYear()+2},(te,k)=>k+1)),X=te=>{let k=ge().year(Number(R.value)).week(te).startOf("week"),G=ge().year(Number(R.value)).week(te).endOf("week");if(k.format("YYYY-MM-DD")!=`${Number(R.value)}-01-01`&&(k=ge().year(Number(R.value)).week(te-1).startOf("week"),G=ge().year(Number(R.value)).week(te-1).endOf("week")),te==54&&k.month()==0)return{label:"\u5F53\u524D\u5E74\u5EA6\u65E054\u5468"};const oe=ge(`${Number(R.value)}-01-01`).startOf("day"),Ce=ge(`${Number(R.value)}-12-31`).endOf("day"),A=k.isBefore(oe)?oe:k,me=G.isAfter(Ce)?Ce:G;return{start:A.format("YYYY-MM-DD"),end:me.format("YYYY-MM-DD"),label:`${A.format("M\u6708D\u65E5")} - ${me.format("M\u6708D\u65E5")}`}},Q=()=>yt(this,null,function*(){B.value=!0;try{const te=yield je.getCruxAchieved(Number(R.value),U.value);D.value=Object.entries(te).map(([k,G])=>({manager:k,data:le(G)})).sort((k,G)=>k.manager.localeCompare(G.manager))}finally{B.value=!1}}),xe=p(Re().format("YYYY")),re=p(!1),pe=p([]),Ve=p(),b=p(),J=()=>yt(this,null,function*(){re.value=!0;try{const te=(yield je.getChangeProject(Number(xe.value))).reduce((k,G)=>{const{manager:oe,month:Ce,total:A}=G;return k[oe]||(k[oe]={manager:oe,data:vl({},Array.from({length:12},(me,z)=>({[z+1]:0})).reduce((me,z)=>vl(vl({},me),z),{})),annual:0}),k[oe].data[Ce]=A,k[oe].annual+=A,k},{});pe.value=Object.values(te)}finally{re.value=!1}}),de=p("year"),ye=p(Re().format("YYYY")),S=p(!0),be=p(!1),L=p([]),Y=p(),ie=p(),Te=Ye(()=>Array.from({length:ge().year(Number(ye.value)).isoWeeksInYear()+2},(te,k)=>k+1)),He=p({year:0,type:"",filterType:"",filterIds:[]}),tt=({filterList:te})=>{const k=te[0].datas[0].type;He.value.filterType=k,k==="user"?He.value.filterIds=te[0].datas[0].userList:He.value.filterIds=te[0].datas[0].deptList,ft()},ft=()=>yt(this,null,function*(){be.value=!0;try{He.value.year=Number(ye.value),He.value.type=de.value;const te=yield je.getProblemAchieved(He.value);L.value=Object.entries(te).map(([k,G])=>({manager:k,data:le(G)}))}finally{be.value=!1}}),Je=p("year"),Ke=p(Re().format("YYYY")),rt=p(!1),it=p([]),Dt=p(),mt=p(),Xe=p({year:0,type:"",filterType:"",filterIds:[]}),he=(te,k,G)=>{var oe;return((oe=te.data[k])==null?void 0:oe[G])||0},Rt=()=>yt(this,null,function*(){rt.value=!0;try{Xe.value.year=Number(Ke.value),Xe.value.type=Je.value;const te=yield je.getWorkHoursNew(Xe.value);it.value=Object.entries(te).map(([k,G])=>({manager:k,data:le(G)}))}finally{rt.value=!1}}),Kt=({filterList:te})=>{const k=te[0].datas[0].type;Xe.value.filterType=k,k==="user"?Xe.value.filterIds=te[0].datas[0].userList:Xe.value.filterIds=te[0].datas[0].deptList,Rt()};function le(te){return te.reduce((k,G)=>(k[G.serialNumber]={planCount:G.planCount,actualCount:G.actualCount,percent:G.percent},k),{})}function ue(te,k,G,oe,Ce){return yt(this,null,function*(){try{const A=[],me=["\u4EBA\u5458",`${G}\u5E74\u5EA6\u8BA1\u5212`,"\u5B9E\u9645","\u8FBE\u6210\u7387"],z=oe==="year"?[1,2,3,4,5,6,7,8,9,10,11,12]:Array.from({length:54},(K,P)=>P+1);z.forEach(K=>{me.push(`${K}${oe==="year"?"\u6708":"\u5468"}\u8BA1\u5212`,`${K}${oe==="year"?"\u6708":"\u5468"}\u5B9E\u9645`,`${K}${oe==="year"?"\u6708":"\u5468"}\u8FBE\u6210\u7387`)}),A.push(me),k.forEach(K=>{var P,Z,ne;const ae=[K.manager];ae.push(((P=K.data[100])==null?void 0:P.planCount)||0,((Z=K.data[100])==null?void 0:Z.actualCount)||0,`${((ne=K.data[100])==null?void 0:ne.percent)||0}%`),z.forEach(fe=>{var d,y,H;ae.push(((d=K.data[fe])==null?void 0:d.planCount)||0,((y=K.data[fe])==null?void 0:y.actualCount)||0,`${((H=K.data[fe])==null?void 0:H.percent)||0}%`)}),A.push(ae)});const I=jl.aoa_to_sheet(A);I["!cols"]=[{wpx:120},...Array(3+z.length*3).fill({wpx:100})];const $=jl.book_new();jl.book_append_sheet($,I,"\u8BE6\u60C5");const F=`${te}_${G}_${Date.now()}.xlsx`;ss($,F)}catch(A){throw A}finally{}})}const Se=p(!1),at=p([]),ot=p(),kt=p(),Oe=te=>yt(this,null,function*(){var k;const G=yield je.getCruxNode(te);at.value=G,Se.value=!0,yield Ue(),(k=r(ot))==null||k.connect(r(kt))}),Mt=p(!1),nt=p([]),dt=p(),We=p(),Tt=te=>yt(this,null,function*(){var k;const G=yield je.getWorkHoursDetail(te);nt.value=G,Mt.value=!0,yield Ue(),(k=r(dt))==null||k.connect(r(We))}),gt=p(!1),Wt=p([]),bt=p(),Gt=p(),ht=te=>yt(this,null,function*(){var k;const G=yield je.getProblemInfo(te);Wt.value=G,gt.value=!0,yield Ue(),(k=r(bt))==null||k.connect(r(Gt))}),da=te=>{var k;const G=(k=r(te).getColumns().find(oe=>oe.field==="manager"))==null?void 0:k.filters[0];G.data.type="dept",G.data.deptList=[3246,3472,3544,3545,3546,3547,3548,3549],r(te).updateFilterOptionStatus(G,!0)};return lt(()=>yt(this,null,function*(){var te,k,G,oe,Ce;yield Ue(),(te=r(c))==null||te.connect(r(f)),(k=r(E))==null||k.connect(r(V)),(G=r(b))==null||G.connect(r(Ve)),(oe=r(ie))==null||oe.connect(r(Y)),(Ce=r(mt))==null||Ce.connect(r(Dt)),requestAnimationFrame(()=>{da(mt),Xe.value.filterType="dept",Xe.value.filterIds=[3246,3472,3544,3545,3546,3547,3548,3549],da(ie),He.value.filterType="dept",He.value.filterIds=[3246,3472,3544,3545,3546,3547,3548,3549],o(),Q(),J(),ft(),Rt()})})),(te,k)=>{const G=ba,oe=sa,Ce=Me("vxe-toolbar"),A=Me("vxe-column"),me=ca,z=Ma,I=Me("vxe-colgroup"),$=Me("vxe-table"),F=st,K=Na,P=Ya,Z=qt,ne=vt,ae=Et,fe=Nr;return h(),j("div",u2,[a(F,{shadow:"never"},{header:i(()=>[a(Ce,{size:"mini",custom:"",export:"",ref_key:"adcpToolbarRef",ref:f},{buttons:i(()=>[a(G,{title:"ADCP\u6309\u671F\u8FBE\u6210\u60C5\u51B5"}),p2]),tools:i(()=>[a(oe,{modelValue:r(n),"onUpdate:modelValue":k[0]||(k[0]=d=>ze(n)?n.value=d:null),"value-format":"YYYY",type:"year",onChange:o,clearable:!1},null,8,["modelValue"])]),_:1},512)]),default:i(()=>[Pe((h(),j("div",null,[a($,{ref_key:"adcpTableRef",ref:c,"header-cell-style":{padding:0,fontSize:".8rem",background:"#eee"},"cell-style":{padding:"5px",fontSize:".8rem"},"cell-config":{height:24},"header-cell-config":{height:24},"footer-cell-config":{height:24},"footer-cell-style":{padding:0,fontSize:".8rem"},data:r(m),"export-config":{type:"xlsx",isMerge:!0},align:"center",height:"258px",footerMethod:u,"show-footer":"",border:"",stripe:""},{default:i(()=>[a(A,{field:"manager",title:"\u9879\u76EE\u7ECF\u7406",width:"120",fixed:"left"}),a(I,{field:"total",title:"\u5408\u8BA1",width:"60",fixed:"left"},{default:i(()=>[a(A,{field:"planCount-annual",title:"\u8BA1\u5212",width:"100",sortable:"","sort-by":d=>he(d==null?void 0:d.row,"annual","planCount")},{default:i(({row:d})=>[a(me,{type:"primary",onClick:y=>Oe({type:"ADCP",dateType:"month",year:Number(r(n)),serialNumber:100,manager:d.manager})},{default:i(()=>{var y;return[T(w((y=d.data.annual)==null?void 0:y.planCount),1)]}),_:2},1032,["onClick"])]),_:1},8,["sort-by"]),a(A,{field:"actualCount-annual",title:"\u5B9E\u9645",width:"100",sortable:"","sort-by":d=>he(d==null?void 0:d.row,"annual","actualCount")},{default:i(({row:d})=>[a(me,{type:"primary",onClick:y=>Oe({type:"ADCP",dateType:"month",year:Number(r(n)),serialNumber:100,manager:d.manager,completed:!0})},{default:i(()=>{var y;return[T(w((y=d.data.annual)==null?void 0:y.actualCount),1)]}),_:2},1032,["onClick"])]),_:1},8,["sort-by"]),a(A,{field:"rate-annual",title:"\u8FBE\u6210\u7387",width:"100",sortable:"","sort-by":d=>he(d==null?void 0:d.row,"annual","rate")},{default:i(({row:d})=>{var y;return[a(z,{"text-inside":!0,"stroke-width":18,percentage:(y=d.data.annual)==null?void 0:y.rate,status:"success",class:"no-radius"},null,8,["percentage"])]}),_:1},8,["sort-by"])]),_:1}),a(I,{field:"total",title:"\u5F53\u671F\u6570\u636E",width:"60"},{default:i(()=>[a(A,{field:"planCount-99",title:"\u8BA1\u5212",width:"100",sortable:"","sort-by":d=>he(d==null?void 0:d.row,"99","planCount")},{default:i(({row:d})=>[a(me,{type:"primary",onClick:y=>Oe({type:"ADCP",dateType:"month",year:Number(r(n)),serialNumber:99,manager:d.manager})},{default:i(()=>{var y;return[T(w((y=d.data[99])==null?void 0:y.planCount),1)]}),_:2},1032,["onClick"])]),_:1},8,["sort-by"]),a(A,{field:"actualCount-99",title:"\u5B9E\u9645",width:"100",sortable:"","sort-by":d=>he(d==null?void 0:d.row,"99","actualCount")},{default:i(({row:d})=>[a(me,{type:"primary",onClick:y=>Oe({type:"ADCP",dateType:"month",year:Number(r(n)),serialNumber:99,manager:d.manager,completed:!0})},{default:i(()=>{var y;return[T(w((y=d.data[99])==null?void 0:y.actualCount),1)]}),_:2},1032,["onClick"])]),_:1},8,["sort-by"]),a(A,{field:"rate-99",title:"\u8FBE\u6210\u7387",width:"100",sortable:"","sort-by":d=>he(d==null?void 0:d.row,"99","rate")},{default:i(({row:d})=>{var y;return[a(z,{"text-inside":!0,"stroke-width":18,percentage:(y=d.data[99])==null?void 0:y.rate,status:"success",class:"no-radius"},null,8,["percentage"])]}),_:1},8,["sort-by"])]),_:1}),(h(),j(ce,null,ve([1,2,3,4,5,6,7,8,9,10,11,12],d=>a(I,{key:d,field:`${d}-data`,title:`${d}\u6708`},{default:i(()=>[a(A,{field:`planCount-${d}`,title:"\u8BA1\u5212",width:"100",sortable:"","sort-by":y=>he(y==null?void 0:y.row,d,"planCount")},{default:i(({row:y})=>[a(me,{type:"primary",onClick:H=>Oe({type:"ADCP",dateType:"month",year:Number(r(n)),serialNumber:d,manager:y.manager})},{default:i(()=>{var H;return[T(w((H=y.data[d])==null?void 0:H.planCount),1)]}),_:2},1032,["onClick"])]),_:2},1032,["field","sort-by"]),a(A,{field:`actualCount-${d}`,title:"\u5B9E\u9645",width:"100",sortable:"","sort-by":y=>he(y==null?void 0:y.row,d,"actualCount")},{default:i(({row:y})=>[a(me,{type:"primary",onClick:H=>Oe({type:"ADCP",dateType:"month",year:Number(r(n)),serialNumber:d,manager:y.manager,completed:!0})},{default:i(()=>{var H;return[T(w((H=y.data[d])==null?void 0:H.actualCount),1)]}),_:2},1032,["onClick"])]),_:2},1032,["field","sort-by"]),a(A,{field:`rate-${d}`,title:"\u8FBE\u6210\u7387",width:"100",sortable:"","sort-by":y=>he(y==null?void 0:y.row,d,"rate")},{default:i(({row:y})=>{var H;return[a(z,{"text-inside":!0,"stroke-width":18,percentage:(H=y.data[d])==null?void 0:H.rate,status:"success",class:"no-radius"},null,8,["percentage"])]}),_:2},1032,["field","sort-by"])]),_:2},1032,["field","title"])),64))]),_:1},8,["data"])])),[[fe,r(s)]])]),_:1}),a(F,{shadow:"never",class:"m-t-10px"},{header:i(()=>[a(Ce,{size:"mini",custom:"",export:"",ref_key:"cruxToolbarRef",ref:V},{buttons:i(()=>[a(G,{title:"\u91CC\u7A0B\u7891\u6309\u671F\u8FBE\u6210\u60C5\u51B5"}),f2]),tools:i(()=>[a(P,{modelValue:r(U),"onUpdate:modelValue":k[1]||(k[1]=d=>ze(U)?U.value=d:null),onChange:Q},{default:i(()=>[a(K,{label:"\u6708",value:"year"}),a(K,{label:"\u5468",value:"week"})]),_:1},8,["modelValue"]),a(oe,{modelValue:r(R),"onUpdate:modelValue":k[2]||(k[2]=d=>ze(R)?R.value=d:null),"value-format":"YYYY",type:"year",onChange:Q,clearable:!1},null,8,["modelValue"])]),_:1},512)]),default:i(()=>[Pe((h(),j("div",null,[a($,{ref_key:"cruxTableRef",ref:E,"header-cell-style":{padding:0,fontSize:".8rem",background:"#eee"},"cell-style":{padding:"5px",fontSize:".8rem"},"cell-config":{height:24},"header-cell-config":{height:24},"footer-cell-config":{height:24},"footer-cell-style":{padding:0,fontSize:".8rem"},data:r(D),"export-config":{type:"xlsx",isMerge:!0},align:"center",height:"258px","show-footer":"",footerMethod:g,border:"",stripe:""},{default:i(()=>[a(A,{field:"manager",title:"\u9879\u76EE\u7ECF\u7406",width:"120",fixed:"left"}),a(I,{field:"total",title:`${r(Re)(r(R)).format("YYYY")}\u5E74\u5EA6`,width:"60",fixed:"left"},{default:i(()=>[a(A,{field:"planCount-100",title:"\u8BA1\u5212",width:"100",sortable:"","sort-by":d=>he(d==null?void 0:d.row,100,"planCount")},{default:i(({row:d})=>[a(me,{type:"primary",onClick:y=>Oe({type:"other",dateType:"month",year:Number(r(R)),serialNumber:100,manager:d.manager})},{default:i(()=>{var y;return[T(w((y=d.data[100])==null?void 0:y.planCount),1)]}),_:2},1032,["onClick"])]),_:1},8,["sort-by"]),a(A,{field:"actualCount-100",title:"\u5B9E\u9645",width:"100",sortable:"","sort-by":d=>he(d==null?void 0:d.row,100,"actualCount")},{default:i(({row:d})=>[a(me,{type:"primary",onClick:y=>Oe({type:"other",dateType:"month",year:Number(r(R)),serialNumber:100,manager:d.manager,completed:!0})},{default:i(()=>{var y;return[T(w((y=d.data[100])==null?void 0:y.actualCount),1)]}),_:2},1032,["onClick"])]),_:1},8,["sort-by"]),a(A,{field:"percent-100",title:"\u8FBE\u6210\u7387",width:"100",sortable:"","sort-by":d=>he(d==null?void 0:d.row,100,"percent")},{default:i(({row:d})=>{var y;return[a(z,{"text-inside":!0,"stroke-width":18,percentage:((y=d.data[100])==null?void 0:y.percent)||0,status:"success",class:"no-radius"},null,8,["percentage"])]}),_:1},8,["sort-by"])]),_:1},8,["title"]),a(I,{field:"currentTotal",title:"\u5F53\u671F\u6570\u636E"},{default:i(()=>[a(A,{field:"planCount-99",title:"\u8BA1\u5212",width:"100",sortable:"","sort-by":d=>he(d==null?void 0:d.row,99,"planCount")},{default:i(({row:d})=>[a(me,{type:"primary",onClick:y=>Oe({type:"other",dateType:"month",year:Number(r(R)),serialNumber:99,manager:d.manager})},{default:i(()=>{var y;return[T(w((y=d.data[99])==null?void 0:y.planCount),1)]}),_:2},1032,["onClick"])]),_:1},8,["sort-by"]),a(A,{field:"actualCount-99",title:"\u5B9E\u9645",width:"100",sortable:"","sort-by":d=>he(d==null?void 0:d.row,99,"actualCount")},{default:i(({row:d})=>[a(me,{type:"primary",onClick:y=>Oe({type:"other",dateType:"month",year:Number(r(R)),serialNumber:99,manager:d.manager,completed:!0})},{default:i(()=>{var y;return[T(w((y=d.data[99])==null?void 0:y.actualCount),1)]}),_:2},1032,["onClick"])]),_:1},8,["sort-by"]),a(A,{field:"percent-99",title:"\u5F53\u671F\u8FBE\u6210\u7387",width:"100",sortable:"","sort-by":d=>he(d==null?void 0:d.row,99,"percent")},{default:i(({row:d})=>{var y;return[a(z,{"text-inside":!0,"stroke-width":18,percentage:((y=d.data[99])==null?void 0:y.percent)||0,status:"success",class:"no-radius"},null,8,["percentage"])]}),_:1},8,["sort-by"])]),_:1}),r(U)==="year"?(h(),j(ce,{key:0},ve([1,2,3,4,5,6,7,8,9,10,11,12],d=>a(I,{key:d,field:`month${d}`,title:`${d}\u6708\u60C5\u51B5`},{default:i(()=>[a(A,{field:`planCount-${d}`,title:"\u8BA1\u5212",width:"100",sortable:"","sort-by":y=>he(y==null?void 0:y.row,d,"planCount")},{default:i(({row:y})=>[a(me,{type:"primary",onClick:H=>Oe({type:"other",dateType:"month",year:Number(r(R)),serialNumber:d,manager:y.manager})},{default:i(()=>{var H;return[T(w(((H=y.data[d])==null?void 0:H.planCount)||0),1)]}),_:2},1032,["onClick"])]),_:2},1032,["field","sort-by"]),a(A,{field:`actualCount-${d}`,title:"\u5B9E\u9645",width:"100",sortable:"","sort-by":y=>he(y==null?void 0:y.row,d,"actualCount")},{default:i(({row:y})=>[a(me,{type:"primary",onClick:H=>Oe({type:"other",dateType:"month",year:Number(r(R)),serialNumber:d,manager:y.manager,completed:!0})},{default:i(()=>{var H;return[T(w(((H=y.data[d])==null?void 0:H.actualCount)||0),1)]}),_:2},1032,["onClick"])]),_:2},1032,["field","sort-by"]),a(A,{field:`percent-${d}`,title:"\u8FBE\u6210\u7387",width:"100",sortable:"","sort-by":y=>he(y==null?void 0:y.row,d,"percent")},{default:i(({row:y})=>{var H;return[a(z,{"text-inside":!0,"stroke-width":18,percentage:((H=y.data[d])==null?void 0:H.percent)||0,status:"success",class:"no-radius"},null,8,["percentage"])]}),_:2},1032,["field","sort-by"])]),_:2},1032,["field","title"])),64)):(h(!0),j(ce,{key:1},ve(r(q),d=>{var y;return h(),N(I,{key:d,field:`month${d}`,title:`\u7B2C${d}\u5468\u60C5\u51B5-${(y=X(d))==null?void 0:y.label}`},{default:i(()=>[a(A,{field:`planCount-${d}`,title:"\u8BA1\u5212",width:"100"},{default:i(({row:H})=>[a(me,{type:"primary",onClick:_=>Oe({type:"other",dateType:"week",year:Number(r(R)),serialNumber:d,manager:H.manager})},{default:i(()=>{var _;return[T(w(((_=H.data[d])==null?void 0:_.planCount)||0),1)]}),_:2},1032,["onClick"])]),_:2},1032,["field"]),a(A,{field:`actualCount-${d}`,title:"\u5B9E\u9645",width:"100"},{default:i(({row:H})=>[a(me,{type:"primary",onClick:_=>Oe({type:"other",dateType:"week",year:Number(r(R)),serialNumber:d,manager:H.manager,completed:!0})},{default:i(()=>{var _;return[T(w(((_=H.data[d])==null?void 0:_.actualCount)||0),1)]}),_:2},1032,["onClick"])]),_:2},1032,["field"]),a(A,{field:`percent-${d}`,title:"\u8FBE\u6210\u7387",width:"100"},{default:i(({row:H})=>{var _;return[a(z,{"text-inside":!0,"stroke-width":18,percentage:((_=H.data[d])==null?void 0:_.percent)||0,status:"success",class:"no-radius"},null,8,["percentage"])]}),_:2},1032,["field"])]),_:2},1032,["field","title"])}),128))]),_:1},8,["data"])])),[[fe,r(B)]])]),_:1}),a(F,{shadow:"never",class:"m-t-10px"},{header:i(()=>[a(Ce,{size:"mini",custom:"",ref_key:"workingHoursToolbarRef",ref:Dt},{buttons:i(()=>[a(G,{title:"\u5DE5\u65F6\u60C5\u51B5"}),m2]),tools:i(()=>[a(P,{modelValue:r(Je),"onUpdate:modelValue":k[3]||(k[3]=d=>ze(Je)?Je.value=d:null),onChange:Rt},{default:i(()=>[a(K,{label:"\u6708",value:"year"}),a(K,{label:"\u5468",value:"week"})]),_:1},8,["modelValue"]),a(oe,{modelValue:r(Ke),"onUpdate:modelValue":k[4]||(k[4]=d=>ze(Ke)?Ke.value=d:null),"value-format":"YYYY",type:"year",onChange:Rt,clearable:!1},null,8,["modelValue"]),a(ne,{onClick:k[5]||(k[5]=d=>ue("\u5DE5\u65F6\u60C5\u51B5",r(it),r(Ke),r(Je),r(rt))),circle:""},{default:i(()=>[a(Z,{icon:"ep:download"})]),_:1})]),_:1},512)]),default:i(()=>[x("div",null,[a($,{ref_key:"workingHoursTableRef",ref:mt,"header-cell-style":{padding:0,fontSize:".8rem",background:"#eee"},"cell-style":{padding:0,fontSize:".8rem"},"cell-config":{height:24},"header-cell-config":{height:24},"footer-cell-config":{height:24},"footer-cell-style":{padding:0,fontSize:".8rem"},data:r(it),align:"center",border:"","max-height":"600px","min-height":"400px",loading:r(rt),stripe:"","filter-config":{remote:!0},onFilterChange:Kt},{default:i(()=>[a(A,{field:"manager",title:"\u4EBA\u5458",width:"120",fixed:"left",filters:r(l),"filter-render":Zr},null,8,["filters","filter-render"]),a(I,{field:"total",title:`${r(Re)(r(Ke)).format("YYYY")}\u5E74\u5EA6`,width:"60",fixed:"left"},{default:i(()=>[a(A,{field:"planCount-100",title:"\u8BA1\u5212",width:"100",sortable:"","sort-by":d=>he(d==null?void 0:d.row,100,"planCount")},{default:i(({row:d})=>{var y;return[T(w(((y=d.data[100])==null?void 0:y.planCount.toFixed(0))||0),1)]}),_:1},8,["sort-by"]),a(A,{field:"actualCount-100",title:"\u5B9E\u9645",width:"100",sortable:"","sort-by":d=>he(d==null?void 0:d.row,100,"actualCount")},{default:i(({row:d})=>[a(me,{type:"primary",onClick:y=>Tt({dateType:"month",year:Number(r(Ke)),serialNumber:100,manager:d.manager})},{default:i(()=>{var y;return[T(w(((y=d.data[100])==null?void 0:y.actualCount.toFixed(0))||0),1)]}),_:2},1032,["onClick"])]),_:1},8,["sort-by"]),a(A,{field:"percent-100",title:"\u8FBE\u6210\u7387",width:"100",sortable:"","sort-by":d=>he(d==null?void 0:d.row,100,"percent")},{default:i(({row:d})=>{var y;return[T(w(((y=d.data[100])==null?void 0:y.percent)||0)+"% ",1)]}),_:1},8,["sort-by"])]),_:1},8,["title"]),r(Je)==="year"?(h(),j(ce,{key:0},ve([1,2,3,4,5,6,7,8,9,10,11,12],d=>a(I,{key:d,field:`month${d}`,title:`${d}\u6708\u60C5\u51B5`},{default:i(()=>[a(A,{field:`planCount-${d}`,title:"\u8BA1\u5212",width:"100",sortable:"","sort-by":y=>he(y==null?void 0:y.row,d,"planCount")},{default:i(({row:y})=>{var H;return[T(w(((H=y.data[d])==null?void 0:H.planCount.toFixed(0))||0),1)]}),_:2},1032,["field","sort-by"]),a(A,{field:`actualCount-${d}`,title:"\u5B9E\u9645",width:"100",sortable:"","sort-by":y=>he(y==null?void 0:y.row,d,"actualCount")},{default:i(({row:y})=>[a(me,{type:"primary",onClick:H=>Tt({dateType:"month",year:Number(r(Ke)),serialNumber:d,manager:y.manager})},{default:i(()=>{var H;return[T(w(((H=y.data[d])==null?void 0:H.actualCount.toFixed(0))||0),1)]}),_:2},1032,["onClick"])]),_:2},1032,["field","sort-by"]),a(A,{field:`percent-${d}`,title:"\u8FBE\u6210\u7387",width:"100",sortable:"","sort-by":y=>he(y==null?void 0:y.row,d,"percent")},{default:i(({row:y})=>{var H;return[T(w(((H=y.data[d])==null?void 0:H.percent)||0)+"% ",1)]}),_:2},1032,["field","sort-by"])]),_:2},1032,["field","title"])),64)):(h(!0),j(ce,{key:1},ve(r(ee),d=>{var y;return h(),N(I,{key:d,field:`month${d}`,title:`\u7B2C${d}\u5468\u60C5\u51B5-${(y=X(d))==null?void 0:y.label}`},{default:i(()=>[a(A,{field:`planCount-${d}`,title:"\u8BA1\u5212",width:"100",sortable:"","sort-by":H=>he(H==null?void 0:H.row,d,"planCount")},{default:i(({row:H})=>{var _;return[T(w(((_=H.data[d])==null?void 0:_.planCount)||0),1)]}),_:2},1032,["field","sort-by"]),a(A,{field:`actualCount-${d}`,title:"\u5B9E\u9645",width:"100",sortable:"","sort-by":H=>he(H==null?void 0:H.row,d,"actualCount")},{default:i(({row:H})=>[a(me,{type:"primary",onClick:_=>Tt({dateType:"week",year:Number(r(Ke)),serialNumber:d,manager:H.manager})},{default:i(()=>{var _;return[T(w(((_=H.data[d])==null?void 0:_.actualCount)||0),1)]}),_:2},1032,["onClick"])]),_:2},1032,["field","sort-by"]),a(A,{field:`percent-${d}`,title:"\u8FBE\u6210\u7387",width:"100",sortable:"","sort-by":H=>he(H==null?void 0:H.row,d,"percent")},{default:i(({row:H})=>{var _;return[T(w(((_=H.data[d])==null?void 0:_.percent)||0)+"% ",1)]}),_:2},1032,["field","sort-by"])]),_:2},1032,["field","title"])}),128))]),_:1},8,["data","loading"])])]),_:1}),a(F,{shadow:"never",class:"m-t-10px"},{header:i(()=>[a(Ce,{size:"mini",custom:"",ref_key:"problemToolbarRef",ref:Y},{buttons:i(()=>[a(G,{title:"\u95EE\u9898\u6309\u671F\u5173\u95ED\u60C5\u51B5"}),h2]),tools:i(()=>[a(P,{modelValue:r(de),"onUpdate:modelValue":k[6]||(k[6]=d=>ze(de)?de.value=d:null),onChange:ft},{default:i(()=>[a(K,{label:"\u6708",value:"year"}),a(K,{label:"\u5468",value:"week"})]),_:1},8,["modelValue"]),a(oe,{modelValue:r(ye),"onUpdate:modelValue":k[7]||(k[7]=d=>ze(ye)?ye.value=d:null),"value-format":"YYYY",type:"year",onChange:ft,clearable:!1},null,8,["modelValue"]),a(ne,{onClick:k[8]||(k[8]=d=>ue("\u95EE\u9898\u6309\u671F\u5173\u95ED",r(L),r(ye),r(de),r(be))),circle:""},{default:i(()=>[a(Z,{icon:"ep:download"})]),_:1})]),_:1},512)]),default:i(()=>[x("div",null,[a($,{ref_key:"problemTableRef",ref:ie,"header-cell-style":{padding:0,fontSize:".8rem",background:"#eee"},"cell-style":{padding:0,fontSize:".8rem"},"cell-config":{height:24},"header-cell-config":{height:24},"footer-cell-config":{height:24},"footer-cell-style":{padding:0,fontSize:".8rem"},data:r(L),"virtual-y-config":{enabled:r(S),gt:0},"virtual-x-config":{enabled:r(S),gt:0},align:"center",border:"",stripe:"","max-height":"600px","min-height":"400px",loading:r(be),"filter-config":{remote:!0},onFilterChange:tt},{default:i(()=>[a(A,{field:"manager",title:"\u4EBA\u5458",width:"120",fixed:"left",filters:r(t),"filter-render":Zr},null,8,["filters","filter-render"]),a(I,{field:"total",title:`${r(Re)(r(ye)).format("YYYY")}\u5E74\u5EA6`,width:"60",fixed:"left"},{default:i(()=>[a(A,{field:"planCount-100",title:"\u8BA1\u5212",width:"100",sortable:"","sort-by":d=>he(d==null?void 0:d.row,100,"planCount")},{default:i(({row:d})=>[a(me,{type:"primary",onClick:y=>ht({dateType:"month",year:Number(r(ye)),serialNumber:100,manager:d.manager})},{default:i(()=>{var y;return[T(w(((y=d.data[100])==null?void 0:y.planCount)||0),1)]}),_:2},1032,["onClick"])]),_:1},8,["sort-by"]),a(A,{field:"actualCount-100",title:"\u5B9E\u9645",width:"100",sortable:"","sort-by":d=>he(d==null?void 0:d.row,100,"actualCount")},{default:i(({row:d})=>[a(me,{type:"primary",onClick:y=>ht({dateType:"month",year:Number(r(ye)),serialNumber:100,manager:d.manager,completed:!0})},{default:i(()=>{var y;return[T(w(((y=d.data[100])==null?void 0:y.actualCount)||0),1)]}),_:2},1032,["onClick"])]),_:1},8,["sort-by"]),a(A,{field:"percent-100",title:"\u8FBE\u6210\u7387",width:"100",sortable:"","sort-by":d=>he(d==null?void 0:d.row,100,"percent")},{default:i(({row:d})=>{var y;return[T(w(((y=d.data[100])==null?void 0:y.percent)||0)+"% ",1)]}),_:1},8,["sort-by"])]),_:1},8,["title"]),r(de)==="year"?(h(),j(ce,{key:0},ve([1,2,3,4,5,6,7,8,9,10,11,12],d=>a(I,{key:d,field:`month${d}`,title:`${d}\u6708\u60C5\u51B5`},{default:i(()=>[a(A,{field:`planCount-${d}`,title:"\u8BA1\u5212",width:"100",sortable:"","sort-by":y=>he(y==null?void 0:y.row,d,"planCount")},{default:i(({row:y})=>[a(me,{type:"primary",onClick:H=>ht({dateType:"month",year:Number(r(ye)),serialNumber:d,manager:y.manager})},{default:i(()=>{var H;return[T(w(((H=y.data[d])==null?void 0:H.planCount)||0),1)]}),_:2},1032,["onClick"])]),_:2},1032,["field","sort-by"]),a(A,{field:`actualCount-${d}`,title:"\u5B9E\u9645",width:"100",sortable:"","sort-by":y=>he(y==null?void 0:y.row,d,"actualCount")},{default:i(({row:y})=>[a(me,{type:"primary",onClick:H=>ht({dateType:"month",year:Number(r(ye)),serialNumber:d,manager:y.manager,completed:!0})},{default:i(()=>{var H;return[T(w(((H=y.data[d])==null?void 0:H.actualCount)||0),1)]}),_:2},1032,["onClick"])]),_:2},1032,["field","sort-by"]),a(A,{field:`percent-${d}`,title:"\u8FBE\u6210\u7387",width:"100",sortable:"","sort-by":y=>he(y==null?void 0:y.row,d,"percent")},{default:i(({row:y})=>{var H;return[T(w(((H=y.data[d])==null?void 0:H.percent)||0)+"% ",1)]}),_:2},1032,["field","sort-by"])]),_:2},1032,["field","title"])),64)):(h(!0),j(ce,{key:1},ve(r(Te),d=>{var y;return h(),N(I,{key:d,field:`month${d}`,title:`\u7B2C${d}\u5468\u60C5\u51B5-${(y=X(d))==null?void 0:y.label}`},{default:i(()=>[a(A,{field:`planCount-${d}`,title:"\u8BA1\u5212",width:"100",sortable:"","sort-by":H=>he(H==null?void 0:H.row,d,"planCount")},{default:i(({row:H})=>[a(me,{type:"primary",onClick:_=>ht({dateType:"week",year:Number(r(ye)),serialNumber:d,manager:H.manager})},{default:i(()=>{var _;return[T(w(((_=H.data[d])==null?void 0:_.planCount)||0),1)]}),_:2},1032,["onClick"])]),_:2},1032,["field","sort-by"]),a(A,{field:`actualCount-${d}`,title:"\u5B9E\u9645",width:"100",sortable:"","sort-by":H=>he(H==null?void 0:H.row,d,"actualCount")},{default:i(({row:H})=>[a(me,{type:"primary",onClick:_=>ht({dateType:"week",year:Number(r(ye)),serialNumber:d,manager:H.manager,completed:!0})},{default:i(()=>{var _;return[T(w(((_=H.data[d])==null?void 0:_.actualCount)||0),1)]}),_:2},1032,["onClick"])]),_:2},1032,["field","sort-by"]),a(A,{field:`percent-${d}`,title:"\u8FBE\u6210\u7387",width:"100",sortable:"","sort-by":H=>he(H==null?void 0:H.row,d,"percent")},{default:i(({row:H})=>{var _;return[T(w(((_=H.data[d])==null?void 0:_.percent)||0)+"% ",1)]}),_:2},1032,["field","sort-by"])]),_:2},1032,["field","title"])}),128))]),_:1},8,["data","virtual-y-config","virtual-x-config","loading"])])]),_:1}),a(F,{shadow:"never",class:"m-t-10px"},{header:i(()=>[a(Ce,{size:"mini",custom:"",export:"",ref_key:"changeToolbarRef",ref:Ve},{buttons:i(()=>[a(G,{title:"\u9879\u76EE\u53D8\u66F4\u6B21\u6570"}),v2]),tools:i(()=>[a(oe,{modelValue:r(xe),"onUpdate:modelValue":k[9]||(k[9]=d=>ze(xe)?xe.value=d:null),"value-format":"YYYY",type:"year",onChange:J,clearable:!1},null,8,["modelValue"])]),_:1},512)]),default:i(()=>[Pe((h(),j("div",null,[a($,{ref_key:"changeTableRef",ref:b,"header-cell-style":{padding:0,fontSize:".8rem",background:"#eee"},"cell-style":{padding:0,fontSize:".8rem"},"cell-config":{height:24},"header-cell-config":{height:24},"footer-cell-config":{height:24},"footer-cell-style":{padding:0,fontSize:".8rem"},data:r(pe),"export-config":{type:"xlsx",isMerge:!0},align:"center",border:"",stripe:""},{default:i(()=>[a(A,{field:"manager",title:"\u9879\u76EE\u7ECF\u7406",width:"120",fixed:"left"}),(h(),j(ce,null,ve([1,2,3,4,5,6,7,8,9,10,11,12],d=>a(A,{key:d,field:`data.${d}`,title:`${d}\u6708`,width:"100"},{default:i(({row:y})=>[r(xe)=="2025"&&![1,2,3,4].includes(d)?(h(),j("span",y2,w(y.data[d]),1)):r(xe)=="2025"&&[1,2,3,4].includes(d)||Number(r(xe))<2025?(h(),j("span",g2,"/")):se("",!0)]),_:2},1032,["field","title"])),64)),a(A,{field:"annual",title:"\u5E74\u5EA6\u603B\u6570",width:"100"})]),_:1},8,["data"])])),[[fe,r(re)]])]),_:1}),a(ae,{title:"\u5173\u952E\u8282\u70B9\u6570\u636E\u5207\u7247",modelValue:r(Se),"onUpdate:modelValue":k[10]||(k[10]=d=>ze(Se)?Se.value=d:null),width:"80%"},{default:i(()=>[a(Ce,{ref_key:"cruxNodeToolbarRef",ref:kt,export:"",size:"mini"},null,512),a($,{ref_key:"cruxNodeTableRef",ref:ot,data:r(at),"export-config":{type:"xlsx"},"show-overflow":"",align:"center","cell-config":{height:"34px"},"max-height":"600"},{default:i(()=>[a(A,{title:"\u5E8F\u53F7",type:"seq",width:"50"}),a(A,{title:"\u9879\u76EE\u7ECF\u7406",field:"manager",width:"80"}),a(A,{title:"\u9879\u76EE\u540D\u79F0",field:"projectName"},{default:i(({row:d})=>[a(Be,{name:d.projectName,"basics-id":d.projectId},null,8,["name","basics-id"])]),_:1}),a(A,{title:"\u6D3B\u52A8\u540D\u79F0",field:"activitiesName"},{default:i(({row:d})=>[a(Be,{name:d.activitiesName,"basics-id":d.projectId,"activities-id":d.activitiesId},null,8,["name","basics-id","activities-id"])]),_:1}),a(A,{title:"\u8BA1\u5212\u5F00\u59CB\u65F6\u95F4",field:"startDate"}),a(A,{title:"\u8BA1\u5212\u5B8C\u6210\u65F6\u95F4",field:"endDate"}),a(A,{title:"\u5B9E\u9645\u5B8C\u6210\u65F6\u95F4",field:"completedDate"})]),_:1},8,["data"])]),_:1},8,["modelValue"]),a(ae,{title:"\u5DE5\u65F6\u6570\u636E\u5207\u7247",modelValue:r(Mt),"onUpdate:modelValue":k[11]||(k[11]=d=>ze(Mt)?Mt.value=d:null),width:"80%"},{default:i(()=>[a(Ce,{ref_key:"workHoursDetailToolbarRef",ref:We,export:"",size:"mini"},null,512),a($,{ref_key:"workHoursDetailTableRef",ref:dt,data:r(nt),"export-config":{type:"xlsx"},"show-overflow":"",align:"center","cell-config":{height:"34px"},"max-height":"600","footer-cell-style":{padding:0},"footer-method":M,"show-footer":""},{default:i(()=>[a(A,{title:"\u5E8F\u53F7",type:"seq",width:"50"}),a(A,{title:"\u4EBA\u5458",field:"manager",width:"80"}),a(A,{title:"\u9879\u76EE\u540D\u79F0",field:"basicsName"},{default:i(({row:d})=>[a(Be,{name:d.basicsName,"basics-id":d.projectId},null,8,["name","basics-id"])]),_:1}),a(A,{title:"\u9879\u76EE\u7B49\u7EA7",field:"basicsLevel"}),a(A,{title:"\u6D3B\u52A8\u540D\u79F0",field:"activitiesName"},{default:i(({row:d})=>[a(Be,{name:d.activitiesName,"basics-id":d.projectId,"activities-id":d.activitiesId},null,8,["name","basics-id","activities-id"])]),_:1}),a(A,{title:"\u8BA1\u5212\u5F00\u59CB\u65F6\u95F4",field:"startDate"}),a(A,{title:"\u8BA1\u5212\u5B8C\u6210\u65F6\u95F4",field:"endDate"}),a(A,{title:"\u5B9E\u9645\u5B8C\u6210\u65F6\u95F4",field:"completedDate"}),a(A,{title:"\u6807\u51C6\u5DE5\u65F6",field:"hours",width:"80"}),a(A,{title:"\u8D1F\u8D23\u4EBA\u6570",field:"personCount",width:"80"}),a(A,{title:"\u5206\u644A\u5DE5\u65F6",field:"avgHours",width:"80"})]),_:1},8,["data"])]),_:1},8,["modelValue"]),a(ae,{title:"\u95EE\u9898\u6570\u636E\u5207\u7247",modelValue:r(gt),"onUpdate:modelValue":k[12]||(k[12]=d=>ze(gt)?gt.value=d:null),width:"80%"},{default:i(()=>[a(Ce,{ref_key:"problemInfoToolbarRef",ref:Gt,export:"",size:"mini"},null,512),a($,{ref_key:"problemInfoTableRef",ref:bt,data:r(Wt),"export-config":{type:"xlsx"},"show-overflow":"",align:"center","cell-config":{height:"34px"},"max-height":"600"},{default:i(()=>[a(A,{title:"\u5E8F\u53F7",type:"seq",width:"50"}),a(A,{title:"\u4EBA\u5458",field:"manager",width:"80"}),a(A,{title:"\u9879\u76EE\u540D\u79F0",field:"projectName"},{default:i(({row:d})=>[a(Be,{name:d.projectName,"basics-id":d.projectId},null,8,["name","basics-id"])]),_:1}),a(A,{title:"\u95EE\u9898\u5185\u5BB9",field:"activitiesName"},{default:i(({row:d})=>[a(Be,{name:d.activitiesName,"basics-id":d.projectId,"problem-id":d.activitiesId},null,8,["name","basics-id","problem-id"])]),_:1}),a(A,{title:"\u63D0\u51FA\u65F6\u95F4",field:"startDate"}),a(A,{title:"\u8BA1\u5212\u5B8C\u6210\u65F6\u95F4",field:"endDate"}),a(A,{title:"\u5B9E\u9645\u5B8C\u6210\u65F6\u95F4",field:"completedDate"})]),_:1},8,["data"])]),_:1},8,["modelValue"])],512)}}}),_2=qe(b2,[["__scopeId","data-v-80c9e991"]]);var yl=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});let Cr,xr;Cr=e=>yl(void 0,null,function*(){return yield v.post({url:"/bpm/process-instance/manager-page",data:e})}),xr=e=>yl(void 0,null,function*(){return yield v.downloadPost({url:"/bpm/process-instance/export",data:e})}),h0=e=>yl(void 0,null,function*(){return yield v.get({url:"/bpm/process-instance/get?id="+e})});var oa=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});let kr,na;kr={getCategoryPage:e=>oa(void 0,null,function*(){return yield v.get({url:"/bpm/category/page",params:e})}),getCategorySimpleList:()=>oa(void 0,null,function*(){return yield v.get({url:"/bpm/category/simple-list"})}),getCategory:e=>oa(void 0,null,function*(){return yield v.get({url:"/bpm/category/get?id="+e})}),createCategory:e=>oa(void 0,null,function*(){return yield v.post({url:"/bpm/category/create",data:e})}),updateCategory:e=>oa(void 0,null,function*(){return yield v.put({url:"/bpm/category/update",data:e})}),deleteCategory:e=>oa(void 0,null,function*(){return yield v.delete({url:"/bpm/category/delete?id="+e})})},na=(e,t,l)=>{const n=new Blob([e],{type:l});window.URL=window.URL||window.webkitURL;const s=URL.createObjectURL(n),m=document.createElement("a");m.href=s,m.download=t,m.click(),window.URL.revokeObjectURL(s)},ma={excel:(e,t)=>{na(e,t,"application/vnd.ms-excel")},word:(e,t)=>{na(e,t,"application/msword")},zip:(e,t)=>{na(e,t,"application/zip")},html:(e,t)=>{na(e,t,"text/html")},markdown:(e,t)=>{na(e,t,"text/markdown")},image:({url:e,canvasWidth:t,canvasHeight:l,drawWithImageSize:n=!0})=>{const s=new Image;s.src=e,s.onload=()=>{const m=document.createElement("canvas");m.width=t||s.width,m.height=l||s.height;const f=m.getContext("2d");f==null||f.clearRect(0,0,m.width,m.height),n?f.drawImage(s,0,0,s.width,s.height):f.drawImage(s,0,0);const c=m.toDataURL("image/png"),o=document.createElement("a");o.href=c,o.download="image.png",o.click()}}};var w2=Object.defineProperty,C2=Object.defineProperties,x2=Object.getOwnPropertyDescriptors,Mr=Object.getOwnPropertySymbols,k2=Object.prototype.hasOwnProperty,M2=Object.prototype.propertyIsEnumerable,Tr=(e,t,l)=>t in e?w2(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,T2=(e,t)=>{for(var l in t||(t={}))k2.call(t,l)&&Tr(e,l,t[l]);if(Mr)for(var l of Mr(t))M2.call(t,l)&&Tr(e,l,t[l]);return e},V2=(e,t)=>C2(e,x2(t)),gl=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});const z2={class:"p-l-10px h-full overflow-auto",ref:"containerRef"},L2={class:"h-[calc(100vh-150px)]"},E2={class:"h-[calc(100%-52px)]"},P2=Ie(V2(T2({},{name:"BpmProcessInstanceManager"}),{__name:"ChangeFlow",setup(e){const t=Pl(),l=St();Zt();const n=p(!0),s=p(0),m=p([]),f=Ta({pageNo:1,pageSize:20,startUserId:void 0,name:"",processDefinitionId:void 0,category:void 0,status:void 0,variables:{event:["modify","delete"]},createTime:[]}),c=p(),o=p([]),u=p([]),g=p(!1),M=()=>gl(this,null,function*(){n.value=!0;try{const R=yield Cr(f);m.value=R.list,s.value=R.total}finally{n.value=!1}}),C=()=>gl(this,null,function*(){try{yield l.exportConfirm(),g.value=!0;const R=yield xr(f);ma.excel(R,"\u6D41\u7A0B\u5B9E\u4F8B.xlsx")}catch{}finally{g.value=!1}}),W=()=>{f.pageNo=1,M()},O=R=>{t.push({name:"BpmProcessInstanceDetail",query:{id:R.id}})};return os(()=>{M()}),lt(()=>gl(this,null,function*(){yield M(),o.value=yield kr.getCategorySimpleList(),u.value=yield za()})),(R,B)=>{const D=Ll,V=El,E=Tl,U=ka,q=vt,ee=Vl,X=ca,Q=Me("vxe-column"),xe=ea,re=Me("vxe-table"),pe=fa,Ve=st;return h(),j("div",z2,[a(Ve,{shadow:"never"},{header:i(()=>[a(ee,{class:"-mb-15px",model:r(f),ref_key:"queryFormRef",ref:c,inline:!0,"label-width":"100px",size:"small"},{default:i(()=>[a(E,{label:"\u53D1\u8D77\u4EBA",prop:"startUserId"},{default:i(()=>[a(V,{modelValue:r(f).startUserId,"onUpdate:modelValue":B[0]||(B[0]=b=>r(f).startUserId=b),placeholder:"\u8BF7\u9009\u62E9\u53D1\u8D77\u4EBA",class:"!w-120px",filterable:"",clearable:""},{default:i(()=>[(h(!0),j(ce,null,ve(r(u),b=>(h(),N(D,{key:b.id,label:b.nickname,value:b.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(E,{label:"\u6D41\u7A0B\u540D\u79F0",prop:"name"},{default:i(()=>[a(U,{modelValue:r(f).name,"onUpdate:modelValue":B[1]||(B[1]=b=>r(f).name=b),placeholder:"\u8BF7\u8F93\u5165\u6D41\u7A0B\u540D\u79F0",clearable:"",onKeyup:zt(W,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(E,{label:"\u6D41\u7A0B\u72B6\u6001",prop:"status"},{default:i(()=>[a(V,{modelValue:r(f).status,"onUpdate:modelValue":B[2]||(B[2]=b=>r(f).status=b),placeholder:"\u8BF7\u9009\u62E9\u6D41\u7A0B\u72B6\u6001",clearable:"",class:"!w-120px"},{default:i(()=>[(h(!0),j(ce,null,ve(r(Ka)(r(Qt).BPM_PROCESS_INSTANCE_STATUS),b=>(h(),N(D,{key:b.value,label:b.label,value:b.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(E,null,{default:i(()=>[a(q,{type:"primary",onClick:W},{default:i(()=>[T("\u67E5\u8BE2")]),_:1}),a(q,{type:"warning",onClick:C,plain:"",loading:r(g)},{default:i(()=>[T("\u5BFC\u51FA")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),default:i(()=>[x("div",L2,[x("div",E2,[a(re,{loading:r(n),data:r(m),"column-config":{resizable:!0},"cell-style":{fontSize:"0.9rem"},"show-overflow":"",height:"100%",border:"",stripe:"","header-cell-config":{height:40},"cell-config":{height:40}},{default:i(()=>[a(Q,{title:"\u6D41\u7A0B\u540D\u79F0",align:"center",field:"name","min-width":"250px",fixed:"left"},{default:i(({row:b})=>[a(X,{type:"primary",onClick:J=>O(b)},{default:i(()=>{var J;return[T(w(((J=b.formVariables)==null?void 0:J.approve_title)||b.name),1)]}),_:2},1032,["onClick"])]),_:1}),a(Q,{title:"\u6D41\u7A0B\u5206\u7C7B",align:"center",field:"categoryName","min-width":"100",fixed:"left"},{default:i(({row:b})=>{var J;return[T(w(((J=b.formVariables)==null?void 0:J.typeName)||b.categoryName),1)]}),_:1}),a(Q,{title:"\u6D41\u7A0B\u53D1\u8D77\u4EBA",align:"center",field:"startUser.nickname",width:"120"}),a(Q,{title:"\u53D1\u8D77\u90E8\u95E8",align:"center",field:"startUser.deptName",width:"120"}),a(Q,{title:"\u6D41\u7A0B\u72B6\u6001",align:"center",field:"status",width:"100"},{default:i(b=>[a(xe,{type:r(Qt).BPM_PROCESS_INSTANCE_STATUS,value:b.row.status},null,8,["type","value"])]),_:1}),a(Q,{title:"\u53D1\u8D77\u65F6\u95F4",align:"center",field:"startTime",width:"180"},{default:i(({row:b})=>[T(w(r($t)(b.startTime)),1)]),_:1}),a(Q,{title:"\u7ED3\u675F\u65F6\u95F4",align:"center",field:"endTime",width:"180"},{default:i(({row:b})=>[T(w(r($t)(b.endTime)),1)]),_:1}),a(Q,{align:"center",title:"\u8017\u65F6",field:"durationInMillis",width:"80"},{default:i(b=>[T(w(b.row.durationInMillis>0?r(Fl)(b.row.durationInMillis):"-"),1)]),_:1}),a(Q,{title:"\u5F53\u524D\u5BA1\u6279\u4EFB\u52A1",align:"center",field:"tasks","min-width":"120px"},{default:i(b=>[(h(!0),j(ce,null,ve(b.row.tasks,J=>(h(),N(q,{type:"primary",key:J.id,link:""},{default:i(()=>[x("span",null,w(J.name),1)]),_:2},1024))),128))]),_:1}),a(Q,{title:"\u53D8\u66F4\u5185\u5BB9",align:"left",field:"content",width:"200"},{default:i(({row:b})=>{var J;return[T(w((J=b.formVariables)==null?void 0:J.content),1)]}),_:1}),a(Q,{title:"\u53D8\u66F4\u539F\u56E0",align:"left",field:"reason",width:"200"},{default:i(({row:b})=>{var J;return[T(w((J=b.formVariables)==null?void 0:J.reason),1)]}),_:1}),a(Q,{title:"\u6D41\u7A0B\u7F16\u53F7",align:"center",field:"id","min-width":"320px"})]),_:1},8,["loading","data"])]),a(pe,{total:r(s),page:r(f).pageNo,"onUpdate:page":B[3]||(B[3]=b=>r(f).pageNo=b),limit:r(f).pageSize,"onUpdate:limit":B[4]||(B[4]=b=>r(f).pageSize=b),onPagination:M,size:"small"},null,8,["total","page","limit"])])]),_:1})],512)}}})),S2=e=>new Promise(t=>{setTimeout(()=>{try{localStorage.getItem(e)?t(JSON.parse(localStorage.getItem(e)||"")):t({})}catch{t({})}},300)}),I2=(e,t)=>new Promise(l=>{setTimeout(()=>{localStorage.setItem(e,JSON.stringify(t)),cs.modal.message({status:"success",content:"\u4FDD\u5B58\u4E2A\u6027\u5316\u5217\u8BBE\u7F6E\u6210\u529F"}),l({})},200)}),bl=Ta({storage:!0,restoreStore({id:e}){return S2(e)},updateStore({id:e,storeData:t}){return I2(e,t)},checkMethod({column:e}){var t;return!((t=e.field)!=null&&t.startsWith("dynamic_"))},icon:"\u5217\u8BBE\u7F6E"});var A2=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});const H2={class:"p-l-10px h-full overflow-auto",ref:"containerRef"},j2={class:"h-[calc(100vh-150px)]"},O2=Ie({__name:"DataBoard",setup(e){const t=p([{data:""}]),l=p([{data:""}]),n=p([{data:""}]),s=p([{data:{condition:"10",value:void 0}}]),m=p({}),f=p({date:ge().format("YYYY-MM-DD")}),c=p([]),o=p(!1),u=p(),g=p(),M=p([]),C=p(ge().format("YYYY-MM-DD")),W=p({visible:!1,basicsName:"",basicsId:void 0,list:[]}),O=(U,q,ee)=>{W.value.visible=!0,W.value.basicsName=q,W.value.basicsId=U,W.value.list=ee},R=U=>{const q=ge(U).startOf("month"),ee=ge(U).endOf("month"),X=[];let Q=q.clone(),xe=1;const re=ge();let pe=[];for(;Q.isSameOrBefore(ee);){const Ve=Q.clone(),b=ge.min(Q.clone().endOf("isoWeek"),ee.clone()),J=`${Ve.format("YYYY-MM-DD")}\u5230${b.format("YYYY-MM-DD")}`,de=re.isSameOrAfter(Ve,"days"),ye=re.isSameOrBefore(b,"days"),S=re.isSameOrAfter(q,"days"),be=re.isSameOrBefore(ee,"days");de&&ye&&S&&be&&(pe=[xe]),X.push({label:J,value:xe}),Q=b.clone().add(1,"day"),xe++}return{weeks:X,currentWeek:pe}},B=Ye(()=>{const U=R(f.value.date);return M.value=U.currentWeek.length===0?U.weeks.map(q=>q.value):U.currentWeek,m.value=U.weeks.reduce((q,ee)=>(q[ee.value-1]=[{data:{condition:"10",value:void 0}}],q),{}),U.weeks}),D=Ye(()=>{const U=[],q=ge(f.value.date),ee=q.daysInMonth();for(let X=1;X<=ee;X++){const Q=q.date(X);U.push(Q.format("YYYY-MM-DD"))}return U}),V=()=>A2(this,null,function*(){o.value=!0;try{const U=ge(f.value.date),q=ge(C.value);U.year()!=q.year()||U.month()!=q.month()?C.value=f.value.date:C.value=ge().format("YYYY-MM-DD");const ee=yield je.getDataBoardList(f.value);c.value=ee}finally{o.value=!1}}),E=({column:U})=>{if(["basics-info","basics-month-total","basics-week-1","basics-week-3","basics-week-5"].includes(U.field))return{backgroundColor:"rgb(241, 241, 241)"}};return lt(()=>{V(),Ue(()=>{var U;(U=r(g))==null||U.connect(r(u))})}),(U,q)=>{const ee=sa,X=Yr,Q=Ur,xe=Me("vxe-toolbar"),re=Me("vxe-column"),pe=Me("vxe-colgroup"),Ve=Ma,b=ca,J=Ll,de=El,ye=Me("vxe-table"),S=st,be=Et;return h(),j("div",H2,[a(S,{shadow:"never"},{header:i(()=>[a(xe,{size:"mini",ref_key:"toolbarRef",ref:u,export:""},{buttons:i(()=>[a(ee,{modelValue:r(f).date,"onUpdate:modelValue":q[0]||(q[0]=L=>r(f).date=L),"value-format":"YYYY-MM-DD",type:"month",clearable:!1,onChange:V},null,8,["modelValue"]),a(Q,{modelValue:r(M),"onUpdate:modelValue":q[1]||(q[1]=L=>ze(M)?M.value=L:null),class:"ml-30px"},{default:i(()=>[(h(!0),j(ce,null,ve(r(B),(L,Y)=>(h(),N(X,{key:Y,label:`\u7B2C${Y+1}\u5468`,value:L.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},512)]),default:i(()=>[x("div",j2,[a(ye,{ref_key:"tableRef",ref:g,height:"100%",align:"center",border:"",id:"dataBoardTable","show-overflow":"",stripe:"","header-cell-config":{height:30},"cell-config":{height:30},"cell-style":{height:30},"header-cell-style":E,data:r(c),loading:r(o),"sort-config":{multiple:!0},"custom-config":r(bl),"row-config":{isHover:!0,isCurrent:!0},"export-config":{type:"xlsx"}},{default:i(()=>[a(pe,{title:"\u9879\u76EE\u4FE1\u606F",field:"basics-info",fixed:"left"},{default:i(()=>[a(re,{title:"\u9879\u76EE\u540D\u79F0","min-width":"100",field:"basicsName",align:"left",filters:r(t),"filter-render":De,"header-class-name":"basics-info","class-name":"border-left"},{default:i(({row:L})=>[a(Be,{name:L.basicsName,"basics-id":L.id},null,8,["name","basics-id"])]),_:1},8,["filters","filter-render"]),a(re,{title:"SE\u4EE3\u8868",width:"60",field:"seName",filters:r(l),"filter-render":De,"header-class-name":"basics-info"},null,8,["filters","filter-render"]),a(re,{title:"\u9879\u76EE\u7ECF\u7406",width:"60",field:"managerName",filters:r(n),"filter-render":De,"header-class-name":"basics-info","class-name":"border-right"},null,8,["filters","filter-render"])]),_:1}),(h(),N(pe,{title:"\u6240\u6709\u9879\u76EE\u5468\u671F\u8FDB\u5EA6\u60C5\u51B5\u6C47\u603B",field:"basics-progress",key:-1},{default:i(()=>[a(re,{title:"\u8282\u70B9\u603B\u6570",width:"60",field:"totalCount",sortable:"","class-name":"border-left"}),a(re,{title:"\u5B9E\u9645\u5B8C\u6210\u6570",width:"60",field:"completedCount",sortable:""}),a(re,{title:"\u9879\u76EE\u8FDB\u5EA6",width:"60",field:"progress",sortable:""},{default:i(({row:L})=>[a(Ve,{"text-inside":!0,"stroke-width":22,percentage:L.progress,status:"success",class:"no-radius"},null,8,["percentage"])]),_:1}),a(re,{title:"\u672A\u5B8C\u6210\u6570",width:"60",field:"extensionCount",sortable:""},{default:i(({row:L})=>[L.extensionCount?(h(),N(b,{key:0,type:"primary",onClick:Y=>O(L.id,L.basicsName,L.extensionActivitiesList)},{default:i(()=>[T(w(L.extensionCount),1)]),_:2},1032,["onClick"])):se("",!0)]),_:1}),a(re,{title:"ADCP\u504F\u5DEE\u5929\u6570",width:"70",field:"delayDays","class-name":"border-right"})]),_:1})),(h(),N(pe,{title:`${r(ge)(r(f).date).format("M\u6708")}\u6C47\u603B`,field:"basics-month-total",key:0},{default:i(()=>[a(re,{title:"\u8BA1\u5212\u8282\u70B9",width:"60",field:"data.99.planCount",sortable:"","header-class-name":"basics-info","class-name":"border-left"},{default:i(({row:L})=>{var Y;return[(Y=L.data[99])!=null&&Y.planCount?(h(),N(b,{key:0,type:"primary",onClick:ie=>O(L.id,L.basicsName,L.data[99].planActivitiesList)},{default:i(()=>[T(w(L.data[99].planCount),1)]),_:2},1032,["onClick"])):se("",!0)]}),_:1}),a(re,{title:"\u5B8C\u6210\u8282\u70B9",width:"60",field:"data.99.actualCount",sortable:"","header-class-name":"basics-info"},{default:i(({row:L})=>{var Y;return[(Y=L.data[99])!=null&&Y.actualCount?(h(),N(b,{key:0,type:"primary",onClick:ie=>O(L.id,L.basicsName,L.data[99].actualActivitiesList)},{default:i(()=>[T(w(L.data[99].actualCount),1)]),_:2},1032,["onClick"])):se("",!0)]}),_:1}),a(re,{title:"\u8D85\u671F\u672A\u5B8C\u6210",width:"60",field:"data.99.extensionCount",sortable:"","header-class-name":"basics-info"},{default:i(({row:L})=>{var Y;return[(Y=L.data[99])!=null&&Y.extensionCount?(h(),N(b,{key:0,type:"primary",onClick:ie=>O(L.id,L.basicsName,L.data[99].extensionActivitiesList)},{default:i(()=>[T(w(L.data[99].extensionCount),1)]),_:2},1032,["onClick"])):se("",!0)]}),_:1}),a(re,{title:"\u5B8C\u6210\u7387",width:"60",field:"data.99.completionRate",sortable:"","header-class-name":"basics-info",filters:r(s),"filter-render":Ba},{default:i(({row:L})=>{var Y;return[a(Ve,{"text-inside":!0,"stroke-width":22,percentage:(Y=L.data[99])==null?void 0:Y.completionRate,status:"success",class:"no-radius"},null,8,["percentage"])]}),_:1},8,["filters","filter-render"]),a(re,{title:"\u5F53\u524D\u91CC\u7A0B\u7891\u504F\u5DEE\u5929\u6570","min-width":"100",field:"data.99.currentCrux","header-class-name":"basics-info","class-name":"border-right"},{default:i(({row:L})=>{var Y,ie,Te;return[L.data[99].currentCrux?(h(),N(Be,{key:0,name:`\u5EF6${r(ge)(((Y=L.data[99].currentCrux)==null?void 0:Y.completedDate)||r(ge)()).diff(r(ge)((ie=L.data[99].currentCrux)==null?void 0:ie.endDate),"days")}\u5929 ${(Te=L.data[99].currentCrux)==null?void 0:Te.name}`,"basics-id":L.id,"activities-id":L.data[99].currentCrux.id},null,8,["name","basics-id","activities-id"])):se("",!0)]}),_:1})]),_:1},8,["title"])),(h(!0),j(ce,null,ve(r(B).filter(L=>r(M).includes(L.value)),(L,Y)=>(h(),N(pe,{key:Y+1,title:`${L.label}\u7B2C${Y+1}\u5468`,field:`basics-week-${Y}`},{default:i(()=>[a(re,{title:"\u8BA1\u5212\u8282\u70B9",width:"60",field:`data.${Y}.planCount`,sortable:"","header-class-name":Y%2==1&&"basics-info","class-name":"border-left"},{default:i(({row:ie})=>{var Te;return[(Te=ie.data[Y])!=null&&Te.planCount?(h(),N(b,{key:0,type:"primary",onClick:He=>O(ie.id,ie.basicsName,ie.data[Y].planActivitiesList)},{default:i(()=>[T(w(ie.data[Y].planCount),1)]),_:2},1032,["onClick"])):se("",!0)]}),_:2},1032,["field","header-class-name"]),a(re,{title:"\u5B8C\u6210\u8282\u70B9",width:"60",field:`data.${Y}.actualCount`,sortable:"","header-class-name":Y%2==1&&"basics-info"},{default:i(({row:ie})=>{var Te;return[(Te=ie.data[Y])!=null&&Te.actualCount?(h(),N(b,{key:0,type:"primary",onClick:He=>O(ie.id,ie.basicsName,ie.data[Y].actualActivitiesList)},{default:i(()=>[T(w(ie.data[Y].actualCount),1)]),_:2},1032,["onClick"])):se("",!0)]}),_:2},1032,["field","header-class-name"]),a(re,{title:"\u8D85\u671F\u672A\u5B8C\u6210",width:"60",field:`data.${Y}.extensionCount`,sortable:"","header-class-name":"basics-info"},{default:i(({row:ie})=>{var Te;return[(Te=ie.data[Y])!=null&&Te.extensionCount?(h(),N(b,{key:0,type:"primary",onClick:He=>O(ie.id,ie.basicsName,ie.data[Y].extensionActivitiesList)},{default:i(()=>[T(w(ie.data[Y].extensionCount),1)]),_:2},1032,["onClick"])):se("",!0)]}),_:2},1032,["field"]),a(re,{title:"\u5B8C\u6210\u7387",width:"60",field:`data.${Y}.completionRate`,sortable:"","header-class-name":Y%2==1&&"basics-info",filters:r(m)[Y],"filter-render":Ba},{default:i(({row:ie})=>{var Te;return[a(Ve,{"text-inside":!0,"stroke-width":22,percentage:(Te=ie.data[Y])==null?void 0:Te.completionRate,status:"success",class:"no-radius"},null,8,["percentage"])]}),_:2},1032,["field","header-class-name","filters","filter-render"]),a(re,{title:"\u5F53\u524D\u91CC\u7A0B\u7891\u504F\u5DEE\u5929\u6570","min-width":"100",field:`data.${L.value}.currentCrux`,"header-class-name":Y%2==1&&"basics-info","class-name":"border-right"},{default:i(({row:ie})=>{var Te,He,tt,ft;return[(Te=ie.data[Y])!=null&&Te.currentCrux?(h(),N(Be,{key:0,name:`\u5EF6${r(ge)(((He=ie.data[Y].currentCrux)==null?void 0:He.completedDate)||r(ge)()).diff(r(ge)((tt=ie.data[Y].currentCrux)==null?void 0:tt.endDate),"days")}\u5929 ${(ft=ie.data[Y].currentCrux)==null?void 0:ft.name}`,"basics-id":ie.id,"activities-id":ie.data[Y].currentCrux.id},null,8,["name","basics-id","activities-id"])):se("",!0)]}),_:2},1032,["field","header-class-name"])]),_:2},1032,["title","field"]))),128)),(h(),N(pe,{field:"day-total",title:r(C),key:9999},{header:i(()=>[a(de,{size:"small",modelValue:r(C),"onUpdate:modelValue":q[2]||(q[2]=L=>ze(C)?C.value=L:null),clearable:!1},{default:i(()=>[(h(!0),j(ce,null,ve(r(D),(L,Y)=>(h(),N(J,{key:Y,label:L,value:L},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),default:i(()=>[a(re,{title:"\u8BA1\u5212\u8282\u70B9",width:"60",field:`dayData.${r(C)}.planCount`,sortable:""},{default:i(({row:L})=>{var Y;return[(Y=L.dayData[r(C)])!=null&&Y.planCount?(h(),N(b,{key:0,type:"primary",onClick:ie=>O(L.id,L.basicsName,L.dayData[r(C)].planActivitiesList)},{default:i(()=>[T(w(L.dayData[r(C)].planCount),1)]),_:2},1032,["onClick"])):se("",!0)]}),_:1},8,["field"]),a(re,{title:"\u5B8C\u6210\u8282\u70B9",width:"60",field:`dayData.${r(C)}.actualCount`,sortable:""},{default:i(({row:L})=>{var Y;return[(Y=L.dayData[r(C)])!=null&&Y.actualCount?(h(),N(b,{key:0,type:"primary",onClick:ie=>O(L.id,L.basicsName,L.dayData[r(C)].actualActivitiesList)},{default:i(()=>[T(w(L.dayData[r(C)].actualCount),1)]),_:2},1032,["onClick"])):se("",!0)]}),_:1},8,["field"])]),_:1},8,["title"]))]),_:1},8,["data","loading","custom-config"])])]),_:1}),a(be,{title:"\u8BE6\u60C5",modelValue:r(W).visible,"onUpdate:modelValue":q[3]||(q[3]=L=>r(W).visible=L),width:"80%"},{default:i(()=>[a(ye,{"header-cell-config":{height:30},"cell-config":{height:30},data:r(W).list,height:"500",align:"center","show-overflow":""},{default:i(()=>[a(re,{type:"seq",width:"60"}),a(re,{title:"\u9879\u76EE"},{default:i(()=>[T(w(r(W).basicsName),1)]),_:1}),a(re,{title:"\u6D3B\u52A8",field:"name"},{default:i(({row:L})=>[a(Be,{name:L.name,"basics-id":r(W).basicsId,"activities-id":L.id},null,8,["name","basics-id","activities-id"])]),_:1}),a(re,{title:"\u5F00\u59CB\u65F6\u95F4",field:"startDate"}),a(re,{title:"\u7ED3\u675F\u65F6\u95F4",field:"endDate"}),a(re,{title:"\u5B8C\u6210\u65F6\u95F4",field:"completedDate"})]),_:1},8,["data"])]),_:1},8,["modelValue"])],512)}}}),D2=qe(O2,[["__scopeId","data-v-e857836a"]]);var R2=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});const N2={class:"p-l-10px h-full overflow-auto",ref:"containerRef"},Y2={class:"h-[calc(100vh-150px)]"},U2=Ie({__name:"CruxDataBoard",setup(e){const t=p([{data:""}]),l=p([{data:""}]),n=p([{data:""}]),s=p({date:ge().format("YYYY-MM-DD"),type:"activities"}),m=p([]),f=p(!1),c=p(),o=p(),u=p({visible:!1,basicsName:"",basicsId:void 0,list:[]}),g=(W,O,R)=>{u.value.visible=!0,u.value.basicsName=O,u.value.basicsId=W,u.value.list=R},M=()=>R2(this,null,function*(){f.value=!0;try{const W=yield je.getCruxDataBoardList(s.value);m.value=W}finally{f.value=!1}}),C=({column:W})=>{if(["basics-info","basics-month-total","basics-week-1","basics-week-3","basics-week-5"].includes(W.field))return{backgroundColor:"rgb(241, 241, 241)"}};return lt(()=>{M(),Ue(()=>{var W;(W=r(o))==null||W.connect(r(c))})}),(W,O)=>{const R=sa,B=Na,D=Ya,V=Me("vxe-toolbar"),E=Me("vxe-column"),U=Me("vxe-colgroup"),q=ca,ee=Ma,X=ea,Q=Me("vxe-table"),xe=st,re=Et;return h(),j("div",N2,[a(xe,{shadow:"never"},{header:i(()=>[a(V,{size:"mini",ref_key:"toolbarRef",ref:c,custom:"",export:""},{buttons:i(()=>[a(R,{modelValue:r(s).date,"onUpdate:modelValue":O[0]||(O[0]=pe=>r(s).date=pe),"value-format":"YYYY-MM-DD",type:"month",clearable:!1,onChange:M},null,8,["modelValue"]),a(D,{modelValue:r(s).type,"onUpdate:modelValue":O[1]||(O[1]=pe=>r(s).type=pe),class:"ml-30px",onChange:M},{default:i(()=>[a(B,{label:"\u91CC\u7A0B\u7891\u6D3B\u52A8",value:"activities"}),a(B,{label:"\u95EE\u9898",value:"problem"})]),_:1},8,["modelValue"])]),_:1},512)]),default:i(()=>[x("div",Y2,[a(Q,{ref_key:"tableRef",ref:o,height:"100%",align:"center",border:"",id:"dataBoardTable","show-overflow":"",stripe:"","header-cell-config":{height:30},"cell-config":{height:30},"cell-style":{height:30},"header-cell-style":C,data:r(m),loading:r(f),"sort-config":{multiple:!0},"custom-config":r(bl),"export-config":{type:"xlsx"}},{default:i(()=>{var pe,Ve;return[a(U,{title:"\u9879\u76EE\u4FE1\u606F",field:"basics-info",fixed:"left"},{default:i(()=>[a(E,{title:"\u9879\u76EE\u540D\u79F0","min-width":"100",field:"basicsName",align:"left",filters:r(t),"filter-render":De,"header-class-name":"basics-info"},{default:i(({row:b})=>[a(Be,{name:b.basicsName,"basics-id":b.id},null,8,["name","basics-id"])]),_:1},8,["filters","filter-render"]),a(E,{title:"SE\u4EE3\u8868",width:"60",field:"seName",filters:r(l),"filter-render":De,"header-class-name":"basics-info"},null,8,["filters","filter-render"]),a(E,{title:"\u9879\u76EE\u7ECF\u7406",width:"60",field:"managerName",filters:r(n),"filter-render":De,"header-class-name":"basics-info"},null,8,["filters","filter-render"])]),_:1}),a(U,{title:r(s).type=="activities"?"\u9879\u76EE\u91CC\u7A0B\u7891\u8FDB\u5EA6\u60C5\u51B5":"\u9879\u76EE\u95EE\u9898\u70B9\u8FDB\u5EA6\u95ED\u73AF\u60C5\u51B5",field:"basics-progress"},{default:i(()=>[a(E,{title:"\u8282\u70B9\u603B\u6570",width:"60",field:"totalCount",sortable:""},{default:i(({row:b})=>[b!=null&&b.totalCount?(h(),N(q,{key:0,type:"primary",onClick:J=>g(b.id,b.basicsName,b.totalList)},{default:i(()=>[T(w(b.totalCount),1)]),_:2},1032,["onClick"])):se("",!0)]),_:1}),a(E,{title:"\u51C6\u65F6\u5B8C\u6210\u6570",width:"60",field:"punctualityCount",sortable:""},{default:i(({row:b})=>[b!=null&&b.punctualityCount?(h(),N(q,{key:0,type:"primary",onClick:J=>g(b.id,b.basicsName,b.punctualityList)},{default:i(()=>[T(w(b.punctualityCount),1)]),_:2},1032,["onClick"])):se("",!0)]),_:1}),a(E,{title:"\u51C6\u65F6\u5B8C\u6210\u7387",width:"60",field:"punctualityRate",sortable:""},{default:i(({row:b})=>[a(ee,{"text-inside":!0,"stroke-width":22,percentage:b.punctualityRate,status:"success",class:"no-radius"},null,8,["percentage"])]),_:1}),a(E,{title:"\u8D85\u671F\u5B8C\u6210\u6570",width:"60",field:"extensionCount",sortable:""},{default:i(({row:b})=>[b!=null&&b.extensionCount?(h(),N(q,{key:0,type:"primary",onClick:J=>g(b.id,b.basicsName,b.extensionList)},{default:i(()=>[T(w(b.extensionCount),1)]),_:2},1032,["onClick"])):se("",!0)]),_:1}),a(E,{title:"\u8D85\u671F\u672A\u5B8C\u6210\u6570",width:"70",field:"overdueCount",sortable:""},{default:i(({row:b})=>[b!=null&&b.overdueCount?(h(),N(q,{key:0,type:"primary",onClick:J=>g(b.id,b.basicsName,b.overdueList)},{default:i(()=>[T(w(b.overdueCount),1)]),_:2},1032,["onClick"])):se("",!0)]),_:1}),a(E,{title:"\u672A\u5230\u671F\u6570",width:"60",field:"todoCount",sortable:""},{default:i(({row:b})=>[b!=null&&b.todoCount?(h(),N(q,{key:0,type:"primary",onClick:J=>g(b.id,b.basicsName,b.todoList)},{default:i(()=>[T(w(b.todoCount),1)]),_:2},1032,["onClick"])):se("",!0)]),_:1}),r(s).type==="activities"?(h(),j(ce,{key:0},[a(E,{title:"ADCP\u65F6\u95F4",width:"90",field:"adcpDate",sortable:""}),a(E,{title:"ADCP\u8D85\u671F",width:"60",field:"adcpStatus"},{default:i(({row:b})=>[a(X,{type:"infra_boolean_string",value:b.adcpStatus},null,8,["value"])]),_:1})],64)):(h(),N(E,{key:1,title:"\u662F\u5426\u5EF6\u671F",width:"60",field:"overdueStatus"},{default:i(({row:b})=>[a(X,{type:"infra_boolean_string",value:b.overdueStatus},null,8,["value"])]),_:1})),a(E,{title:"\u5EF6\u671F\u5929\u6570",width:"60",field:"overdueDays",sortable:""})]),_:1},8,["title"]),(h(!0),j(ce,null,ve((Ve=(pe=r(m))==null?void 0:pe[0])==null?void 0:Ve.dataList,(b,J)=>(h(),N(U,{key:J,title:b.label,field:`${b.type}${J}`,"header-class-name":J%2==0?"back-1":""},{default:i(()=>[a(E,{title:"\u8BA1\u5212\u8282\u70B9\u6570",field:`dataList.${J}.totalCount`,width:"60","header-class-name":J%2==0?"back-1":"",sortable:""},{default:i(({row:de})=>{var ye;return[(ye=de.dataList[J])!=null&&ye.totalCount?(h(),N(q,{key:0,type:"primary",onClick:S=>g(de.id,de.basicsName,de.dataList[J].totalList)},{default:i(()=>{var S;return[T(w((S=de.dataList[J])==null?void 0:S.totalCount),1)]}),_:2},1032,["onClick"])):se("",!0)]}),_:2},1032,["field","header-class-name"]),a(E,{title:["month","week"].includes(b.type)?"\u6309\u671F\u5B8C\u6210\u6570":"\u5B9E\u9645\u5B8C\u6210\u6570",field:`dataList.${J}.punctualityCount`,width:"60","header-class-name":J%2==0?"back-1":"",sortable:""},{default:i(({row:de})=>{var ye;return[(ye=de.dataList[J])!=null&&ye.punctualityCount?(h(),N(q,{key:0,type:"primary",onClick:S=>g(de.id,de.basicsName,de.dataList[J].punctualityList)},{default:i(()=>{var S;return[T(w((S=de.dataList[J])==null?void 0:S.punctualityCount),1)]}),_:2},1032,["onClick"])):se("",!0)]}),_:2},1032,["title","field","header-class-name"]),r(s).type!="activities"||["month","week"].includes(b.type)?(h(),N(E,{key:0,title:r(s).type=="activities"?"\u6309\u671F\u5B8C\u6210\u7387":"\u95ED\u73AF\u7387",field:`dataList.${J}.punctualityRate`,width:"60","header-class-name":J%2==0?"back-1":"",sortable:""},{default:i(({row:de})=>[a(ee,{"text-inside":!0,"stroke-width":22,percentage:de.dataList[J].punctualityRate,status:"success",class:"no-radius"},null,8,["percentage"])]),_:2},1032,["title","field","header-class-name"])):se("",!0),["month","week"].includes(b.type)?(h(),j(ce,{key:1},[a(E,{title:"\u8D85\u671F\u5B8C\u6210\u6570",field:`dataList.${J}.extensionCount`,width:"60","header-class-name":J%2==0?"back-1":"",sortable:""},{default:i(({row:de})=>{var ye;return[(ye=de.dataList[J])!=null&&ye.extensionCount?(h(),N(q,{key:0,type:"primary",onClick:S=>g(de.id,de.basicsName,de.dataList[J].extensionList)},{default:i(()=>{var S;return[T(w((S=de.dataList[J])==null?void 0:S.extensionCount),1)]}),_:2},1032,["onClick"])):se("",!0)]}),_:2},1032,["field","header-class-name"]),a(E,{title:"\u8D85\u671F\u672A\u5B8C\u6210\u6570",field:`dataList.${J}.overdueCount`,width:"70","header-class-name":J%2==0?"back-1":"",sortable:""},{default:i(({row:de})=>{var ye;return[(ye=de.dataList[J])!=null&&ye.overdueCount?(h(),N(q,{key:0,type:"primary",onClick:S=>g(de.id,de.basicsName,de.dataList[J].overdueList)},{default:i(()=>{var S;return[T(w((S=de.dataList[J])==null?void 0:S.overdueCount),1)]}),_:2},1032,["onClick"])):se("",!0)]}),_:2},1032,["field","header-class-name"])],64)):se("",!0)]),_:2},1032,["title","field","header-class-name"]))),128))]}),_:1},8,["data","loading","custom-config"])])]),_:1}),a(re,{title:"\u8BE6\u60C5",modelValue:r(u).visible,"onUpdate:modelValue":O[2]||(O[2]=pe=>r(u).visible=pe),width:"80%"},{default:i(()=>[a(Q,{"header-cell-config":{height:30},"cell-config":{height:30},data:r(u).list,height:"500",align:"center","show-overflow":""},{default:i(()=>[a(E,{title:"\u9879\u76EE"},{default:i(()=>[T(w(r(u).basicsName),1)]),_:1}),a(E,{title:r(s).type==="activities"?"\u6D3B\u52A8":"\u95EE\u9898",field:"name"},{default:i(({row:pe})=>[a(Be,{name:pe.name,"basics-id":r(u).basicsId,"activities-id":r(s).type==="activities"?pe.id:void 0,"problem-id":r(s).type==="problem"?pe.id:void 0},null,8,["name","basics-id","activities-id","problem-id"])]),_:1},8,["title"]),a(E,{title:"\u5F00\u59CB\u65F6\u95F4",field:"startDate"}),a(E,{title:"\u7ED3\u675F\u65F6\u95F4",field:"endDate"}),a(E,{title:"\u5B8C\u6210\u65F6\u95F4",field:"completedDate"})]),_:1},8,["data"])]),_:1},8,["modelValue"])],512)}}}),q2=qe(U2,[["__scopeId","data-v-1bd4d4f2"]]);var $2=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});const B2={class:"p-l-10px h-full overflow-auto",ref:"containerRef"},F2={class:"h-[calc(100vh-150px)]"},K2=Ie({__name:"PersonDataBoard",setup(e){const t=p([{data:""}]),l=p([{data:""}]);p([{data:""}]);const n=p([{data:{condition:"10",value:void 0}}]),s=p({}),m=p({date:ge().format("YYYY-MM-DD")}),f=p([]),c=p(!1),o=p(),u=p(),g=p([]),M=p(ge().format("YYYY-MM-DD")),C=p({visible:!1,basicsName:"",basicsId:void 0,list:[]}),W=V=>{C.value.visible=!0,C.value.list=V},O=V=>{const E=ge(V).startOf("month"),U=ge(V).endOf("month"),q=[];let ee=E.clone(),X=1;const Q=ge();let xe=[];for(;ee.isSameOrBefore(U);){const re=ee.clone(),pe=ge.min(ee.clone().endOf("isoWeek"),U.clone()),Ve=`${re.format("YYYY-MM-DD")}\u5230${pe.format("YYYY-MM-DD")}`,b=Q.isSameOrAfter(re,"days"),J=Q.isSameOrBefore(pe,"days"),de=Q.isSameOrAfter(E,"days"),ye=Q.isSameOrBefore(U,"days");b&&J&&de&&ye&&(xe=[X]),q.push({label:Ve,value:X}),ee=pe.clone().add(1,"day"),X++}return{weeks:q,currentWeek:xe}},R=Ye(()=>{const V=O(m.value.date);return g.value=V.currentWeek.length===0?V.weeks.map(E=>E.value):V.currentWeek,s.value=V.weeks.reduce((E,U)=>(E[U.value-1]=[{data:{condition:"10",value:void 0}}],E),{}),V.weeks}),B=Ye(()=>{const V=[],E=ge(m.value.date),U=E.daysInMonth();for(let q=1;q<=U;q++){const ee=E.date(q);V.push(ee.format("YYYY-MM-DD"))}return V}),D=()=>$2(this,null,function*(){c.value=!0;try{const V=ge(m.value.date),E=ge(M.value);V.year()!=E.year()||V.month()!=E.month()?M.value=m.value.date:M.value=ge().format("YYYY-MM-DD");const U=yield je.getPersonDataBoardList(m.value);f.value=U}finally{c.value=!1}});return lt(()=>{D(),Ue(()=>{var V;(V=r(u))==null||V.connect(r(o))})}),(V,E)=>{const U=sa,q=Yr,ee=Ur,X=Me("vxe-toolbar"),Q=Me("vxe-column"),xe=Ma,re=ca,pe=Me("vxe-colgroup"),Ve=Ll,b=El,J=Me("vxe-table"),de=st,ye=Et;return h(),j("div",B2,[a(de,{shadow:"never"},{header:i(()=>[a(X,{size:"mini",ref_key:"toolbarRef",ref:o,export:""},{buttons:i(()=>[a(U,{modelValue:r(m).date,"onUpdate:modelValue":E[0]||(E[0]=S=>r(m).date=S),"value-format":"YYYY-MM-DD",type:"month",clearable:!1,onChange:D},null,8,["modelValue"]),a(ee,{modelValue:r(g),"onUpdate:modelValue":E[1]||(E[1]=S=>ze(g)?g.value=S:null),class:"ml-30px"},{default:i(()=>[(h(!0),j(ce,null,ve(r(R),(S,be)=>(h(),N(q,{key:be,label:`\u7B2C${be+1}\u5468`,value:S.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},512)]),default:i(()=>[x("div",F2,[a(J,{ref_key:"tableRef",ref:u,height:"100%",align:"center",border:"",id:"dataBoardTable","show-overflow":"",stripe:"","header-cell-config":{height:30},"cell-config":{height:30},"cell-style":{height:30},data:r(f),loading:r(c),"sort-config":{multiple:!0},"custom-config":r(bl),"row-config":{isHover:!0,isCurrent:!0},"export-config":{type:"xlsx"}},{default:i(()=>[(h(),N(pe,{title:"\u4EBA\u5458\u6240\u6709\u9879\u76EE\u6C47\u603B",field:"basics-info",fixed:"left","header-class-name":"basics-info",key:-1},{default:i(()=>[a(Q,{title:"\u4EBA\u5458",width:"100",field:"name",filters:r(t),"filter-render":De,"header-class-name":"basics-info"},null,8,["filters","filter-render"]),a(Q,{title:"\u5206\u7EC4",width:"100",field:"deptName",filters:r(l),"filter-render":De,"header-class-name":"basics-info"},null,8,["filters","filter-render"]),a(Q,{title:"\u8282\u70B9\u603B\u6570","min-width":"80",field:"totalCount",sortable:"","header-class-name":"basics-info"}),a(Q,{title:"\u5B9E\u9645\u5B8C\u6210\u6570","min-width":"80",field:"completedCount",sortable:"","header-class-name":"basics-info"}),a(Q,{title:"\u5B8C\u6210\u7387","min-width":"80",field:"completedRate",sortable:"","header-class-name":"basics-info"},{default:i(({row:S})=>[a(xe,{"text-inside":!0,"stroke-width":22,percentage:S.completedRate,status:"success",class:"no-radius"},null,8,["percentage"])]),_:1}),a(Q,{title:"\u8D85\u671F\u672A\u5B8C\u6210","min-width":"80",field:"incompleteCount",sortable:"","header-class-name":"basics-info","class-name":"border-right"},{default:i(({row:S})=>[S!=null&&S.incompleteCount?(h(),N(re,{key:0,type:"primary",onClick:be=>W(S.incompleteList)},{default:i(()=>[T(w(S.incompleteCount),1)]),_:2},1032,["onClick"])):se("",!0)]),_:1})]),_:1})),(h(),N(pe,{title:`${r(ge)(r(m).date).format("M\u6708")}\u6C47\u603B`,field:"basics-month-total",key:0},{default:i(()=>[a(Q,{title:"\u8BA1\u5212\u8282\u70B9","min-width":"80",field:"data.99.totalCount",sortable:""},{default:i(({row:S})=>{var be,L;return[(L=(be=S.data)==null?void 0:be[99])!=null&&L.totalCount?(h(),N(re,{key:0,type:"primary",onClick:Y=>W(S.data[99].totalList)},{default:i(()=>[T(w(S.data[99].totalCount),1)]),_:2},1032,["onClick"])):se("",!0)]}),_:1}),a(Q,{title:"\u5B8C\u6210\u8282\u70B9","min-width":"80",field:"data.99.completedCount",sortable:""},{default:i(({row:S})=>{var be,L;return[(L=(be=S.data)==null?void 0:be[99])!=null&&L.completedCount?(h(),N(re,{key:0,type:"primary",onClick:Y=>W(S.data[99].completedList)},{default:i(()=>{var Y;return[T(w((Y=S.data)==null?void 0:Y[99].completedCount),1)]}),_:2},1032,["onClick"])):se("",!0)]}),_:1}),a(Q,{title:"\u5B8C\u6210\u7387","min-width":"80",field:"data.99.completedRate",sortable:"",filters:r(n),"filter-render":Ba},{default:i(({row:S})=>{var be,L;return[a(xe,{"text-inside":!0,"stroke-width":22,percentage:(L=(be=S.data)==null?void 0:be[99])==null?void 0:L.completedRate,status:"success",class:"no-radius"},null,8,["percentage"])]}),_:1},8,["filters","filter-render"]),a(Q,{title:"\u8D85\u671F\u672A\u5B8C\u6210","min-width":"80",field:"data.99.incompleteCount",sortable:"","class-name":"border-right"},{default:i(({row:S})=>{var be,L;return[(L=(be=S.data)==null?void 0:be[99])!=null&&L.incompleteCount?(h(),N(re,{key:0,type:"primary",onClick:Y=>W(S.data[99].incompleteList)},{default:i(()=>{var Y;return[T(w((Y=S.data)==null?void 0:Y[99].incompleteCount),1)]}),_:2},1032,["onClick"])):se("",!0)]}),_:1})]),_:1},8,["title"])),(h(!0),j(ce,null,ve(r(R).filter(S=>r(g).includes(S.value)),(S,be)=>(h(),N(pe,{title:`${S.label}\u7B2C${S.value}\u5468`,field:`week-${be}`,key:S.value+be,"header-class-name":(S.value-1)%2==1&&"basics-info"},{default:i(()=>[a(Q,{title:"\u8BA1\u5212\u8282\u70B9","min-width":"80",field:`data.${S.value-1}.totalCount`,sortable:"","header-class-name":(S.value-1)%2==1&&"basics-info"},{default:i(({row:L})=>{var Y,ie;return[(ie=(Y=L.data)==null?void 0:Y[S.value-1])!=null&&ie.totalCount?(h(),N(re,{key:0,type:"primary",onClick:Te=>W(L.data[S.value-1].totalList)},{default:i(()=>[T(w(L.data[S.value-1].totalCount),1)]),_:2},1032,["onClick"])):se("",!0)]}),_:2},1032,["field","header-class-name"]),a(Q,{title:"\u5B8C\u6210\u8282\u70B9","min-width":"80",field:`data.${S.value-1}.completedCount`,sortable:"","header-class-name":(S.value-1)%2==1&&"basics-info"},{default:i(({row:L})=>{var Y,ie;return[(ie=(Y=L.data)==null?void 0:Y[S.value-1])!=null&&ie.completedCount?(h(),N(re,{key:0,type:"primary",onClick:Te=>W(L.data[S.value-1].completedList)},{default:i(()=>{var Te;return[T(w((Te=L.data)==null?void 0:Te[S.value-1].completedCount),1)]}),_:2},1032,["onClick"])):se("",!0)]}),_:2},1032,["field","header-class-name"]),a(Q,{title:"\u5B8C\u6210\u7387","min-width":"80",field:`data.${S.value-1}.completedRate`,sortable:"","header-class-name":(S.value-1)%2==1&&"basics-info",filters:r(n),"filter-render":Ba},{default:i(({row:L})=>{var Y,ie;return[a(xe,{"text-inside":!0,"stroke-width":22,percentage:(ie=(Y=L.data)==null?void 0:Y[S.value-1])==null?void 0:ie.completedRate,status:"success",class:"no-radius"},null,8,["percentage"])]}),_:2},1032,["field","header-class-name","filters","filter-render"]),a(Q,{title:"\u8D85\u671F\u672A\u5B8C\u6210","min-width":"80",field:`data.${S.value-1}.incompleteCount`,sortable:"","header-class-name":(S.value-1)%2==1&&"basics-info","class-name":"border-right"},{default:i(({row:L})=>{var Y,ie;return[(ie=(Y=L.data)==null?void 0:Y[S.value-1])!=null&&ie.incompleteCount?(h(),N(re,{key:0,type:"primary",onClick:Te=>W(L.data[S.value-1].incompleteList)},{default:i(()=>{var Te;return[T(w((Te=L.data)==null?void 0:Te[S.value-1].incompleteCount),1)]}),_:2},1032,["onClick"])):se("",!0)]}),_:2},1032,["field","header-class-name"])]),_:2},1032,["title","field","header-class-name"]))),128)),r(R).length>0&&r(g).length>0?(h(),N(pe,{field:"day-total",key:"99999999",title:r(M)},{header:i(()=>[a(b,{size:"small",modelValue:r(M),"onUpdate:modelValue":E[2]||(E[2]=S=>ze(M)?M.value=S:null),clearable:!1},{default:i(()=>[(h(!0),j(ce,null,ve(r(B),(S,be)=>(h(),N(Ve,{key:be,label:S,value:S},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),default:i(()=>[a(Q,{title:"\u8BA1\u5212\u8282\u70B9","min-width":"80",field:`dayData.${r(M)}.totalCount`,sortable:""},{default:i(({row:S})=>{var be,L;return[(L=(be=S.dayData)==null?void 0:be[r(M)])!=null&&L.totalCount?(h(),N(re,{key:0,type:"primary",onClick:Y=>W(S.dayData[r(M)].totalList)},{default:i(()=>[T(w(S.dayData[r(M)].totalCount),1)]),_:2},1032,["onClick"])):se("",!0)]}),_:1},8,["field"]),a(Q,{title:"\u5B8C\u6210\u8282\u70B9","min-width":"80",field:`dayData.${r(M)}.completedCount`,sortable:""},{default:i(({row:S})=>{var be,L;return[(L=(be=S.dayData)==null?void 0:be[r(M)])!=null&&L.completedCount?(h(),N(re,{key:0,type:"primary",onClick:Y=>W(S.dayData[r(M)].completedList)},{default:i(()=>[T(w(S.dayData[r(M)].completedCount),1)]),_:2},1032,["onClick"])):se("",!0)]}),_:1},8,["field"])]),_:1},8,["title"])):se("",!0)]),_:1},8,["data","loading","custom-config"])])]),_:1}),a(ye,{title:"\u8BE6\u60C5",modelValue:r(C).visible,"onUpdate:modelValue":E[3]||(E[3]=S=>r(C).visible=S),width:"80%"},{default:i(()=>[a(J,{"header-cell-config":{height:30},"cell-config":{height:30},data:r(C).list,height:"500",align:"center","show-overflow":""},{default:i(()=>[a(Q,{title:"\u9879\u76EE"},{default:i(({row:S})=>[T(w(S.basicsName),1)]),_:1}),a(Q,{title:"\u6D3B\u52A8",field:"name"},{default:i(({row:S})=>[a(Be,{name:S.name,"basics-id":S.basicsId,"activities-id":S.id},null,8,["name","basics-id","activities-id"])]),_:1}),a(Q,{title:"\u5F00\u59CB\u65F6\u95F4",field:"startDate"}),a(Q,{title:"\u7ED3\u675F\u65F6\u95F4",field:"endDate"}),a(Q,{title:"\u5B8C\u6210\u65F6\u95F4",field:"completedDate"})]),_:1},8,["data"])]),_:1},8,["modelValue"])],512)}}}),W2=qe(K2,[["__scopeId","data-v-6ae1317c"]]);var G2=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});let Vr,_l,wl;l0=e=>{const t=e.split(","),l=t[0].match(/:(.*?);/)[1],n=window.atob(t[1]);let s=n.length;const m=new Uint8Array(s);for(;s--;)m[s]=n.charCodeAt(s);return new Blob([m],{type:l})},Vr=e=>G2(void 0,[e],function*({url:t,fileName:l}){const n=yield(yield fetch(t)).blob(),s=document.createElement("a");s.href=URL.createObjectURL(n),s.download=l||t.substring(t.lastIndexOf("/")+1),document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(s.href)}),_l=Ie({__name:"index",props:{download:Le.bool.def(!1),hasDialog:Le.bool.def(!0)},setup(e,{expose:t}){const{getUser:l}=_t(),n=e,s=p(""),m=p(""),f="http://oa.iaa360.cn:13133/office-preview",c=p(),o=p(""),u=p(!1),g=p({document:{fileType:"",key:"",title:"",url:"",permissions:{chat:!1,comment:!1,copy:!1,print:!1,download:n.download}},documentType:"",editorConfig:{callbackUrl:`${window.location.origin}/callback`,lang:"zh",mode:"view",user:{group:l.deptId,id:l.id,image:l.avatar,name:l.nickname}}}),M=Ta({color:"rgba(0, 0, 0, .15)"}),C=async(D,V)=>{try{if(c.value=await S1(D),!c.value||!c.value.url)return;u.value=!0,o.value=V,W()}catch{}},W=()=>{const D=c.value.url,V=D.split(".").pop()||"";["docx","doc","xlsx","xls","pptx","ppt","pdf"].includes(V.toLowerCase())?(g.value.document={fileType:V,key:`file-${c.value.id}`,title:o.value,url:D,permissions:{chat:!1,comment:!1,copy:!1,print:!1,download:n.download}},g.value.editorConfig.callbackUrl=`${window.location.origin}/callback`,s.value="office"):m.value="http://oa.iaa360.cn:13135/onlinePreview?url="+encodeURIComponent(btoa(D)),g.value.documentType=O(V)},O=D=>{switch(D.toLowerCase()){case"docx":case"doc":return"word";case"xlsx":case"xls":return"cell";case"pptx":case"ppt":return"slide";case"pdf":return"pdf";default:return"word"}},R=()=>a(Xd,{font:M,content:`IAA-\u56FD\u9645\u9999\u6C1B-${l.nickname}`},{default:()=>[a("div",{class:"file-viewer-container"},[s.value==="office"?a(ns,{id:"docEditor",documentServerUrl:f,config:g.value,onDocumentReady:B},null):a("iframe",{id:"myIframe",src:m.value,class:"h-full w-full"},null)]),Pe(a("div",{class:"text-right"},[a(vt,{class:"w-full mt-10px",type:"primary",plain:!0,onClick:()=>Vr({url:c.value.url,fileName:o.value})},{default:()=>[T("\u4E0B\u8F7D")]})]),[[Sl("if"),n.download]])]});t({open:C});const B=()=>{};return(D,V)=>{const E=Et;return e.hasDialog?(h(),N(E,{key:0,title:r(o),modelValue:r(u),"onUpdate:modelValue":V[0]||(V[0]=U=>ze(u)?u.value=U:null),width:"80%",class:"file-viewer-dialog"},{default:i(()=>[r(g).documentType?(h(),N(R,{key:0})):se("",!0)]),_:1},8,["title","modelValue"])):!e.hasDialog&&r(g).documentType?(h(),N(R,{key:1})):se("",!0)}}}),wl={getMaterialCruxNode:e=>v.get({url:"/project/material/get-crux-node",params:{basicsId:e}}),syncMaterial:(e,t)=>v.get({url:"/docking/erp-material/sync",params:{basicsId:e,erpCode:t}}),getMaterialList:e=>v.get({url:"/project/material/get-list",params:{basicsId:e}}),saveAttachment:e=>v.get({url:"/project/material/save-attachment",params:e}),deleteAttachment:e=>v.get({url:"/project/material/delete-attachment",params:{id:e}}),getAttachmentList:(e,t)=>v.get({url:"/project/material/get-attachment-list",params:{materialId:e,type:t}}),saveMaterial:e=>v.post({url:"/project/material/save-material",data:e}),createMaterialSample:e=>v.post({url:"/bpm/material/create",data:e}),saveDirector:e=>v.post({url:"/project/material/save-director",data:e}),getMaterialFlow:e=>v.get({url:"/bpm/material/get/"+e}),getKanbanList:e=>v.get({url:"/docking/erp-material/get-kanban-list/"+e}),exportKanbanList:e=>v.download({url:"/docking/erp-material/export-kanban-list/"+e}),getKanbanPage:e=>v.post({url:"/docking/erp-material/get-kanban-page",data:e}),exportKanbanPage:e=>v.downloadPost({url:"/docking/erp-material/export-kanban-page",data:e})};var Z2=Object.defineProperty,J2=Object.defineProperties,X2=Object.getOwnPropertyDescriptors,zr=Object.getOwnPropertySymbols,Q2=Object.prototype.hasOwnProperty,ed=Object.prototype.propertyIsEnumerable,Lr=(e,t,l)=>t in e?Z2(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,td=(e,t)=>{for(var l in t||(t={}))Q2.call(t,l)&&Lr(e,l,t[l]);if(zr)for(var l of zr(t))ed.call(t,l)&&Lr(e,l,t[l]);return e},ad=(e,t)=>J2(e,X2(t)),Er=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});const ld={class:"p-l-10px h-full overflow-auto",ref:"containerRef"},rd={class:"h-[calc(100vh-150px)]"},id={class:"h-[calc(100%-50px)]"},od=Ie({__name:"MaterialKanban",setup(e){const t=Ta({basicsName:[{data:""}],erpCode:[{data:""}],code:[{data:""}],name:[{data:""}],spec:[{data:""}]}),l=p({pageNo:1,pageSize:10}),n=p(0),s=p([]),m=p(),f=p(),c=p(!1),o=p(),u=()=>Er(this,null,function*(){c.value=!0;try{const O=yield wl.getKanbanPage(l.value);s.value=O.list,n.value=O.total}finally{c.value=!1}}),g=()=>{l.value.pageNo=1,u()},M=O=>{const R=[""],B={};O.filterList.forEach(D=>{const{field:V,values:E,datas:U}=D;R.includes(V)&&E.length>0?B[V]=E:U.length>0&&(B[V]=U[0])}),Object.keys(l.value).forEach(D=>{!["pageNo","pageSize"].includes(D)&&!O.filterList.some(V=>V.field===D)&&(l.value[D]=void 0)}),Object.assign(l.value,B),g()},C=St(),W=O=>Er(this,[O],function*({options:R}){try{yield C.exportConfirm();const B=R.columns.map(V=>V.field);if(B.length===0){C.warning("\u672A\u9009\u62E9\u9700\u8981\u5BFC\u51FA\u7684\u5217");return}B.includes("date")&&B.splice(B.indexOf("date"),1,"startDate","endDate");const D=yield wl.exportKanbanPage(ad(td({},l.value),{columns:B}));ma.excel(D,`${R.filename}.xlsx`)}catch{}});return lt(()=>{u(),Ue(()=>{var O;(O=r(f))==null||O.connect(r(m))})}),(O,R)=>{const B=Me("vxe-toolbar"),D=Me("vxe-column"),V=vt,E=Me("vxe-table"),U=fa,q=st,ee=_l;return h(),j("div",ld,[a(q,{shadow:"never"},{header:i(()=>[a(B,{size:"mini",ref_key:"toolbarRef",ref:m,custom:"",export:""},null,512)]),default:i(()=>[x("div",rd,[x("div",id,[a(E,{height:"100%",ref_key:"tableRef",ref:f,data:r(s),border:"",stripe:"","show-overflow":"","header-cell-config":{height:30},"column-config":{resizable:!0,isHover:!0},"cell-config":{height:36},"row-config":{isHover:!0,isCurrent:!0},loading:r(c),align:"center",onFilterChange:M,"filter-config":{remote:!0},"export-config":{remote:!0,message:!1,exportMethod:W}},{default:i(()=>[a(D,{title:"\u9879\u76EE",field:"basicsName",width:"120",filters:r(t).basicsName,"filter-render":De},{default:i(({row:X})=>[a(Be,{name:X.basicsName,"basics-id":X.basicsId},null,8,["name","basics-id"])]),_:1},8,["filters","filter-render"]),a(D,{title:"ERP\u9879\u76EE\u7F16\u7801",field:"erpCode",width:"120",filters:r(t).erpCode,"filter-render":De},null,8,["filters","filter-render"]),a(D,{title:"\u54C1\u53F7",field:"code",width:"90",filters:r(t).code,"filter-render":De},{default:i(({row:X})=>[a(Be,{name:X.code,"basics-id":X.basicsId,"item-id":X.itemId},null,8,["name","basics-id","item-id"])]),_:1},8,["filters","filter-render"]),a(D,{title:"\u54C1\u540D",field:"name",width:"100",filters:r(t).name,"filter-render":De},null,8,["filters","filter-render"]),a(D,{title:"\u89C4\u683C",field:"spec","min-width":"150",filters:r(t).spec,"filter-render":De},null,8,["filters","filter-render"]),a(D,{title:"\u5C0F\u8BD5\u56FE\u7EB8",field:"xiaoshiFileName",width:"100"},{default:i(({row:X})=>[a(V,{type:"primary",link:"",onClick:Q=>{var xe;return(xe=r(o))==null?void 0:xe.open(X==null?void 0:X.xiaoshiFileId,X==null?void 0:X.xiaoshiFileName)}},{default:i(()=>[T(w(X.xiaoshiFileName),1)]),_:2},1032,["onClick"])]),_:1}),a(D,{title:"\u5C0F\u8BD5\u8BA1\u5212\u65F6\u95F4",field:"xiaoshiPlanDate",width:"120"}),a(D,{title:"\u5C0F\u8BD5\u5B8C\u6210\u65F6\u95F4",field:"xiaoshiDate",width:"120"}),a(D,{title:"\u5C0F\u8BD5\u5916\u53D1\u6570\u91CF",field:"xiaoshiSendQty",width:"110"}),a(D,{title:"\u5C0F\u8BD5\u91C7\u8D2D\u6570\u91CF",field:"xiaoshiPurchaseQty",width:"110"}),a(D,{title:"\u5C0F\u8BD5\u6536\u8D27\u6570\u91CF",field:"xiaoshiReceiveQty",width:"110"}),a(D,{title:"\u7269\u6599\u627F\u8BA4\u6587\u4EF6",field:"sampleFileName",width:"120"},{default:i(({row:X})=>[a(V,{type:"primary",link:"",onClick:Q=>{var xe;return(xe=r(o))==null?void 0:xe.open(X==null?void 0:X.sampleFileId,X==null?void 0:X.sampleFileName)}},{default:i(()=>[T(w(X.sampleFileName),1)]),_:2},1032,["onClick"])]),_:1}),a(D,{title:"\u7269\u6599\u627F\u8BA4\u8BA1\u5212\u65F6\u95F4",field:"samplePlanDate",width:"140"}),a(D,{title:"\u7269\u6599\u627F\u8BA4\u65F6\u95F4",field:"sampleDate",width:"120"}),a(D,{title:"\u7B7E\u6837\u56FE\u7247",field:"approvalSampleImgs","cell-render":{name:"VxeImage",props:{width:36,height:36}},width:"80"}),a(D,{title:"\u7B7E\u6837\u65F6\u95F4",field:"approvalSampleTime",width:"140"},{default:i(({row:X})=>[T(w(X!=null&&X.approvalSampleTime?r($t)(X.approvalSampleTime):""),1)]),_:1}),a(D,{title:"\u4E2D\u8BD5\u8BA1\u5212\u65F6\u95F4",field:"zhongshiPlanDate",width:"110"}),a(D,{title:"\u4E2D\u8BD5\u5B8C\u6210\u65F6\u95F4",field:"zhongshiDate",width:"110"}),a(D,{title:"\u4E2D\u8BD5\u5916\u53D1\u6570\u91CF",field:"zhongshiSendQty",width:"110"}),a(D,{title:"\u4E2D\u8BD5\u91C7\u8D2D\u6570\u91CF",field:"zhongshiPurchaseQty",width:"110"}),a(D,{title:"\u4E2D\u8BD5\u6536\u8D27\u6570\u91CF",field:"zhongshiReceiveQty",width:"110"}),a(D,{title:"\u7269\u6599\u4E0B\u53D1\u65F6\u95F4",field:"distributionTime",width:"110"})]),_:1},8,["data","loading","export-config"])]),a(U,{limit:r(l).pageSize,"onUpdate:limit":R[0]||(R[0]=X=>r(l).pageSize=X),page:r(l).pageNo,"onUpdate:page":R[1]||(R[1]=X=>r(l).pageNo=X),total:r(n),size:"small"},null,8,["limit","page","total"])])]),_:1}),a(ee,{ref_key:"officeEditorRef",ref:o,"has-dialog":"",download:!0},null,512)],512)}}}),nd=qe(od,[["__scopeId","data-v-fb506dbd"]]);var Pr=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});const Cl={getDrawingUploadPage:e=>Pr(void 0,null,function*(){return yield v.post({url:"/project/drawing-upload/page",data:e})}),addDrawingUpload:e=>Pr(void 0,null,function*(){return yield v.post({url:"/project/drawing-upload/add",data:e})}),exportDrawingUpload:e=>v.downloadPost({url:"/project/drawing-upload/export-drawing-page",data:e})};var dd=Object.defineProperty,sd=Object.defineProperties,cd=Object.getOwnPropertyDescriptors,Sr=Object.getOwnPropertySymbols,ud=Object.prototype.hasOwnProperty,pd=Object.prototype.propertyIsEnumerable,Ir=(e,t,l)=>t in e?dd(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,fd=(e,t)=>{for(var l in t||(t={}))ud.call(t,l)&&Ir(e,l,t[l]);if(Sr)for(var l of Sr(t))pd.call(t,l)&&Ir(e,l,t[l]);return e},md=(e,t)=>sd(e,cd(t)),xl=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});const hd={class:"p-l-10px h-full overflow-auto",ref:"containerRef"},vd={class:"flex justify-between items-center"},yd={class:"h-[calc(100vh-150px)]"},gd={class:"h-[calc(100%-50px)]"},bd=Ie({__name:"DrawinguploadStatus",setup(e){const t=Ta({materialName:[{data:""}],materialCode:[{data:""}],materialVersion:[{data:""}],specifications:[{data:""}],createName:[{data:""}],createTime:[{data:{}}],updateName:[{data:""}],updateTime:[{data:{}}]}),l=p({pageNo:1,pageSize:100}),n=p(0),s=p([]),m=p(),f=p(),c=p(!1),o=p(),u=O=>{const R=[""],B={};O.filterList.forEach(D=>{const{field:V,values:E,datas:U}=D;R.includes(V)&&E.length>0?B[V]=E:U.length>0&&(B[V]=U[0])}),Object.keys(l.value).forEach(D=>{!["pageNo","pageSize"].includes(D)&&!O.filterList.some(V=>V.field===D)&&(l.value[D]=void 0)}),Object.assign(l.value,B),g()},g=()=>xl(this,null,function*(){try{const O=yield Cl.getDrawingUploadPage(l.value);s.value=O.list,n.value=O.total}catch{}}),M=St(),C=()=>xl(this,null,function*(){try{const O=f.value;if(O){const R=O.getUpdateRecords();if(R.length===0){M.warning("\u6CA1\u6709\u9700\u8981\u4FDD\u5B58\u7684\u6570\u636E");return}c.value=!0,yield Cl.addDrawingUpload(R),M.success("\u4FDD\u5B58\u6210\u529F"),yield g()}}catch{M.success("\u4FDD\u5B58\u5931\u8D25")}finally{c.value=!1}}),W=O=>xl(this,[O],function*({options:R}){try{yield M.exportConfirm();const B=R.columns.map(V=>V.field);if(B.length===0){M.warning("\u672A\u9009\u62E9\u9700\u8981\u5BFC\u51FA\u7684\u5217");return}B.includes("date")&&B.splice(B.indexOf("date"),1,"startDate","endDate");const D=yield Cl.exportDrawingUpload(md(fd({},l.value),{columns:B}));ma.excel(D,`${R.filename}.xlsx`)}catch{}});return lt(()=>{g(),Ue(()=>{var O;(O=r(f))==null||O.connect(r(m))})}),(O,R)=>{const B=vt,D=Me("vxe-toolbar"),V=Me("vxe-column"),E=Me("vxe-table"),U=fa,q=st,ee=_l;return h(),j("div",hd,[a(q,{shadow:"never"},{header:i(()=>[x("div",vd,[a(B,{type:"primary",onClick:C},{default:i(()=>[T("\u4FDD\u5B58")]),_:1}),a(D,{size:"mini",ref_key:"toolbarRef",ref:m,custom:"",export:""},null,512)])]),default:i(()=>[x("div",yd,[x("div",gd,[a(E,{height:"100%",ref_key:"tableRef",ref:f,data:r(s),border:"",stripe:"","show-overflow":"","header-cell-config":{height:30},"column-config":{resizable:!0,isHover:!0},"cell-config":{height:36},"row-config":{isHover:!0,isCurrent:!0},loading:r(c),align:"center",onFilterChange:u,"filter-config":{remote:!0},"edit-config":{trigger:"click",mode:"row",showStatus:!0},"keep-source":!0,"export-config":{remote:!0,message:!1,exportMethod:W}},{default:i(()=>[a(V,{title:"\u7269\u6599\u7F16\u7801",field:"materialCode",width:"120",filters:r(t).materialCode,"filter-render":De},null,8,["filters","filter-render"]),a(V,{title:"\u7269\u6599\u7248\u672C",field:"materialVersion",width:"120",filters:r(t).materialVersion,"filter-render":De},null,8,["filters","filter-render"]),a(V,{title:"\u7269\u6599\u540D\u79F0",field:"materialName",width:"300",filters:r(t).materialName,"filter-render":De},null,8,["filters","filter-render"]),a(V,{title:"\u89C4\u683C\u578B\u53F7",field:"specifications",width:"400",filters:r(t).specifications,"filter-render":De},null,8,["filters","filter-render"]),a(V,{title:"\u8BA1\u5212\u5B8C\u6210\u65F6\u95F4",field:"plannedCompletionDate",width:"140","edit-render":{name:"$input",props:{type:"date",format:"yyyy-MM-dd",valueFormat:"yyyy-MM-dd"}}}),a(V,{title:"\u5B9E\u9645\u5B8C\u6210\u65F6\u95F4",field:"actualCompletionDate",width:"140"}),a(V,{title:"\u5907\u6CE8",field:"remark","min-width":"110","edit-render":{name:"input"}}),a(V,{title:"\u7269\u6599\u521B\u5EFA\u4EBA",field:"createName",width:"120",filters:r(t).createName,"filter-render":De},null,8,["filters","filter-render"]),a(V,{title:"\u7269\u6599\u521B\u5EFA\u65E5\u671F",field:"createTime",width:"150"},{default:i(({row:X})=>[T(w(r($t)(X.createTime)),1)]),_:1}),a(V,{title:"\u7269\u6599\u4FEE\u6539\u4EBA",field:"updateName",width:"120",filters:r(t).updateName,"filter-render":De},null,8,["filters","filter-render"]),a(V,{title:"\u7269\u6599\u4FEE\u6539\u65E5\u671F",field:"updateTime",width:"150"},{default:i(({row:X})=>[T(w(r($t)(X.updateTime)),1)]),_:1}),a(V,{title:"\u6587\u6863\u540D\u79F0",field:"docName",width:"100"})]),_:1},8,["data","loading","export-config"])]),a(U,{limit:r(l).pageSize,"onUpdate:limit":R[0]||(R[0]=X=>r(l).pageSize=X),page:r(l).pageNo,"onUpdate:page":R[1]||(R[1]=X=>r(l).pageNo=X),total:r(n),size:"small",onPagination:g},null,8,["limit","page","total"])])]),_:1}),a(ee,{ref_key:"officeEditorRef",ref:o,"has-dialog":"",download:!0},null,512)],512)}}}),_d=qe(bd,[["__scopeId","data-v-f7e57c6b"]]);var wd=Object.defineProperty,Cd=Object.defineProperties,xd=Object.getOwnPropertyDescriptors,Ar=Object.getOwnPropertySymbols,kd=Object.prototype.hasOwnProperty,Md=Object.prototype.propertyIsEnumerable,Hr=(e,t,l)=>t in e?wd(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,Td=(e,t)=>{for(var l in t||(t={}))kd.call(t,l)&&Hr(e,l,t[l]);if(Ar)for(var l of Ar(t))Md.call(t,l)&&Hr(e,l,t[l]);return e},Vd=(e,t)=>Cd(e,xd(t)),kl=(e,t,l)=>new Promise((n,s)=>{var m=o=>{try{c(l.next(o))}catch(u){s(u)}},f=o=>{try{c(l.throw(o))}catch(u){s(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(m,f);c((l=l.apply(e,t)).next())});const zd={class:"p-l-10px h-full overflow-auto",ref:"containerRef"},Ld={class:"h-[calc(100vh-150px)]"},Ed={class:"h-[calc(100%-50px)]"},Pd=Ie({__name:"ProductProject",setup(e){const t={basicsName:[{data:""}],activityName:[{data:""}]},l=p({pageNo:1,pageSize:30}),n=p([]),s=p(0),m=p(!1),f=p(),c=p(),o=St(),u=p([]),g=()=>kl(this,null,function*(){m.value=!0;try{const O=yield je.getProductProjectList(l.value);n.value=O.list,s.value=O.total}finally{m.value=!1}}),M=O=>{const R=[""],B={};O.filterList.forEach(D=>{const{field:V,values:E,datas:U}=D;R.includes(V)&&E.length>0?B[V]=E:U.length>0&&(B[V]=U[0])}),Object.keys(l.value).forEach(D=>{!["pageNo","pageSize"].includes(D)&&!O.filterList.some(V=>V.field===D)&&(l.value[D]=void 0)}),Object.assign(l.value,B),g()},C=O=>kl(this,[O],function*({options:R}){yield o.exportConfirm();const B=R.columns.map(V=>V.field);if(B.length===0){o.warning("\u672A\u9009\u62E9\u9700\u8981\u5BFC\u51FA\u7684\u5217");return}B.includes("date")&&B.splice(B.indexOf("date"),1,"startDate","endDate");const D=yield je.exportProductProject(Vd(Td({},l.value),{columns:B}));ma.excel(D,`${R.filename}.xlsx`)}),W=()=>kl(this,null,function*(){u.value=yield za()});return lt(()=>{g(),W(),Ue(()=>{var O;(O=r(c))==null||O.connect(r(f))})}),(O,R)=>{const B=Me("vxe-toolbar"),D=Me("vxe-column"),V=ea,E=Me("vxe-table"),U=fa,q=st;return h(),j("div",zd,[a(q,{shadow:"never"},{header:i(()=>[a(B,{size:"mini",ref_key:"toolbarRef",ref:f,custom:"",export:""},null,512)]),default:i(()=>[x("div",Ld,[x("div",Ed,[a(E,{height:"100%",ref_key:"tableRef",ref:c,data:r(n),border:"",stripe:"","show-overflow":"","header-cell-config":{height:30},"column-config":{resizable:!0,isHover:!0},"cell-config":{height:36},"row-config":{isHover:!0,isCurrent:!0},loading:r(m),align:"center",onFilterChange:M,"filter-config":{remote:!0},"edit-config":{trigger:"click",mode:"row",showStatus:!0},"keep-source":!0,"export-config":{remote:!0,message:!1,exportMethod:C}},{default:i(()=>[a(D,{title:"\u9879\u76EE",field:"basicsName",filters:t.basicsName,"filter-render":De},{default:i(({row:ee})=>[a(Be,{name:ee.basicsName,"basics-id":ee.basicsId},null,8,["name","basics-id"])]),_:1},8,["filters","filter-render"]),a(D,{title:"\u6D3B\u52A8",field:"activityName",filters:t.activityName,"filter-render":De},{default:i(({row:ee})=>[a(Be,{name:ee.activityName,"basics-id":ee.basicsId,"activities-id":ee.activityId},null,8,["name","basics-id","activities-id"])]),_:1},8,["filters","filter-render"]),a(D,{title:"\u8D1F\u8D23\u4EBA",field:"diretor"},{default:i(({row:ee})=>[T(w(r(Rl)(r(u),ee.director)),1)]),_:1}),a(D,{title:"\u65F6\u95F4",field:"date"},{default:i(({row:ee})=>[T(w(`${ee.startDate} - ${ee.endDate}`),1)]),_:1}),a(D,{title:"\u53D1\u751F\u6B21\u6570",field:"occurrenceNumber"},{default:i(({row:ee})=>[T(w(`\u7B2C${ee.occurrenceNumber}\u6B21`),1)]),_:1}),a(D,{title:"\u5165\u5E93\u786E\u8BA4",field:"productArchiving"},{default:i(({row:ee})=>[a(V,{type:"infra_boolean_string",value:ee.productArchiving},null,8,["value"])]),_:1}),a(D,{title:"\u9500\u552E\u786E\u8BA4",field:"salesConfirm"},{default:i(({row:ee})=>[a(V,{type:"infra_boolean_string",value:ee.salesConfirm},null,8,["value"])]),_:1})]),_:1},8,["data","loading","export-config"])]),a(U,{limit:r(l).pageSize,"onUpdate:limit":R[0]||(R[0]=ee=>r(l).pageSize=ee),page:r(l).pageNo,"onUpdate:page":R[1]||(R[1]=ee=>r(l).pageNo=ee),total:r(s),size:"small",onPagination:g},null,8,["limit","page","total"])])]),_:1})],512)}}}),Sd=qe(Pd,[["__scopeId","data-v-313d1b57"]]),Id={class:"flex h-[calc(100vh-90px)]"},Ad=["onClick"],Hd={class:"w-[calc(100%-100px)] h-full"},jd=Ie({__name:"Index",setup(e){const{wsCache:t}=Lt(),l=m=>{t.set("kanban_current_node",m),n.value=m},n=p("workbench"),s=p([{label:"\u5DE5\u4F5C\u53F0",value:"workbench",role:[""]},{label:"\u7814\u53D1\u7BA1\u7406\u770B\u677F",value:"workhours",role:["r&d_manager","super_admin","project_view","porject_manager"]},{label:"\u7814\u53D1\u9879\u76EE\u770B\u677F",value:"databoard",role:["r&d_manager","super_admin","project_view","porject_manager"]},{label:"\u7814\u53D1\u4EBA\u5458\u770B\u677F",value:"persondataboard",role:["r&d_manager","super_admin","project_view","porject_manager"]},{label:"\u590D\u76D8\u5DE5\u4F5C\u53F0",value:"reanalyze",role:["porject_manager"]},{label:"\u53D8\u66F4\u8BB0\u5F55",value:"change",role:["r&d_manager","super_admin","project_view"]},{label:"PQA\u9879\u76EE\u770B\u677F",value:"cruxdataboard",role:["super_admin","pqa","r&d_manager","project_view","porject_manager"]},{label:"\u7269\u6599\u770B\u677F",value:"material",role:["super_admin","pqa","r&d_manager","project_view","porject_manager"]},{label:"\u56FE\u7EB8\u4E0A\u4F20\u60C5\u51B5",value:"drawingupload",role:["super_admin","pqa","r&d_manager","project_view","porject_manager"]},{label:"\u4EA7\u54C1\u90E8\u9879\u76EE\u770B\u677F",value:"product_project",role:["super_admin","project_view","r&d_manager","product_kanban_view"]}]);return lt(()=>{const m=t.get("kanban_current_node");m&&(n.value=m)}),(m,f)=>{const c=ba,o=st,u=Sl("hasRole");return h(),j("div",Id,[a(o,{class:"w-100px bg-white text-center rounded-5px p-5px",shadow:"never"},{header:i(()=>[a(c,{title:"\u4EEA\u8868\u5206\u7C7B"})]),default:i(()=>[(h(!0),j(ce,null,ve(r(s),g=>Pe((h(),j("div",{key:g.value,class:ke([r(n)==g.value&&"is-active","category-item"]),onClick:M=>l(g.value)},[T(w(g.label),1)],10,Ad)),[[u,g.role]])),128))]),_:1}),x("div",Hd,[r(n)=="workbench"?(h(),N(vn,{key:0})):r(n)=="project-collection"?(h(),N(e2,{key:1})):r(n)=="reanalyze"?(h(),N(a2,{key:2})):r(n)=="workhours"?(h(),N(_2,{key:3})):r(n)=="change"?(h(),N(P2,{key:4})):r(n)=="databoard"?(h(),N(D2,{key:5})):r(n)=="cruxdataboard"?(h(),N(q2,{key:6})):r(n)=="persondataboard"?(h(),N(W2,{key:7})):r(n)=="material"?(h(),N(nd,{key:8})):r(n)=="drawingupload"?(h(),N(_d,{key:9})):r(n)=="product_project"?(h(),N(Sd,{key:10})):se("",!0)])])}}}),Od=qe(jd,[["__scopeId","data-v-e279ad99"]]),Dd=Object.freeze(Object.defineProperty({__proto__:null,default:Od},Symbol.toStringTag,{value:"Module"}))});export{Jr as $,Yl as A,Xr as B,Qr as C,e0 as D,v as E,Va as F,t0 as G,a0 as H,pa as I,_t as J,Et as K,l0 as L,Ul as M,Xt as N,fa as O,r0 as P,i0 as Q,o0 as R,Fa as S,Ka as T,Qt as U,ql as V,$l as W,Bl as X,za as Y,ea as Z,qt as _,ms as __tla,n0 as a,d0 as a0,s0 as a1,Fl as a2,c0 as a3,u0 as a4,p0 as a5,f0 as a6,m0 as a7,h0 as a8,v0 as a9,y0 as aA,g0 as aB,b0 as aa,_0 as ab,w0 as ac,ma as ad,Pt as ae,C0 as af,$t as ag,x0 as ah,k0 as ai,M0 as aj,T0 as ak,V0 as al,z0 as am,L0 as an,Kl as ao,E0 as ap,P0 as aq,Wl as ar,S0 as as,La as at,I0 as au,A0 as av,Wa as aw,H0 as ax,Re as ay,Gl as az,St as b,j0 as c,qe as d,Ga as e,O0 as f,D0 as g,ta as h,R0 as i,Za as j,Ea as k,N0 as l,Zl as m,Ja as n,Y0 as o,U0 as p,q0 as q,Jl as r,$0 as s,B0 as t,F0 as u,K0 as v,W0 as w,G0 as x,Z0 as y,J0 as z};
function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["js/Layout-M-2S9EFf.js","js/element-plus-DgaixBsQ.js","js/vue-vendor-BbSoq9WN.js","js/utils-vendor-Vtb-rlR8.js","js/vendor-DLCNhz7G.js","assets/vendor-Bmh3dkrl.css","assets/vue-vendor-BHIrdaQr.css","assets/element-plus-BT83m8I2.css","js/views-Login-BCX8kkKD.js","js/views-Error-Cx8xxY17.js","assets/views-Login-CwelOi5-.css","js/views-bpm-BO-XbtTX.js","js/views-Profile-4epX3JDT.js","assets/views-Profile-CsC9MOg3.css","assets/views-bpm-Y3mtzbnl.css","js/views-system-BZfQ0tqU.js","assets/index-DRUx63iF.js","js/views-project-C9zQgjvz.js","assets/views-project-DYgOH47U.css","js/echarts-D356XqSJ.js","assets/index-C3kKJ3Kb.css","assets/Layout-CfEZVlbF.css","js/views-Redirect-nATt_sRM.js","js/views-infra-lxM1FTkM.js","js/views-ai-f0kajhtl.js","assets/views-ai-BGnU5J7Z.css"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
