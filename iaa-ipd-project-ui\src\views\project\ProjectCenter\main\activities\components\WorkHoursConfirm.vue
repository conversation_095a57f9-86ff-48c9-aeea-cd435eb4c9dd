<template>
  <el-collapse-item title="工时确认" name="4" v-if="formData.workHoursType != '99'">
    <div class="flex justify-between mb-5px">
      <span class="text-1rem font-bold">已选工时：{{ formData.workHoursTotal || 0 }}H</span>
      <el-button
        type="primary"
        plain
        size="small"
        v-if="attachmentList?.length === 0 && !formData?.workHoursAudit"
        @click="onAduitWork"
      >
        确认工时
      </el-button>
    </div>
    <vxe-table
      ref="workHoursRef"
      :header-cell-style="{ padding: '5px', backgroundColor: '#fff' }"
      :cell-style="{ padding: '5px' }"
      :header-cell-config="{ height: 24 }"
      :cell-config="{ height: 24 }"
      :row-config="{ keyField: 'id' }"
      :checkbox-config="{
        checkRowKeys: formData.workHoursIds,
        highlight: true,
        visibleMethod: visibleMethod,
        checkMethod: checkMethod
      }"
      show-overflow
      border
      align="center"
      :data="workHoursList"
      size="mini"
      :max-height="500"
      @checkbox-change="onCheckboxChange"
      @checkbox-all="onCheckboxChange"
    >
      <vxe-column type="checkbox" width="60" />
      <vxe-column title="分类" width="150" field="secondaryType">
        <template #default="{ row }">
          <DictTag type="project_testing_secondary_type" :value="row.secondaryType" />
        </template>
      </vxe-column>
      <vxe-column
        title="测试项"
        width="150"
        align="left"
        field="name"
        :filters="nameOptions"
        :filter-render="FilterValue.textFilterRender"
      />
      <vxe-column title="测试要求" min-width="200" align="left" field="demand" />
      <vxe-column title="测试说明" width="200" align="left" field="accountFor" />
      <vxe-column title="工时" width="100" field="workHours" />
    </vxe-table>
  </el-collapse-item>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ActivitiesVO, ActivitiesApi } from '@/api/project/activities'
import { TestingTemplateApi } from '@/api/project/testingtemplate'
import { useUserStore } from '@/store/modules/user'
import { useMessage } from '@/hooks/web/useMessage'
import * as FilterValue from '@/views/project/ProjectCenter/main/components/Filter'

const props = defineProps<{
  data: ActivitiesVO
  formData: ActivitiesVO
  attachmentList: any[]
}>()

const emits = defineEmits(['audit-work-hours', 'change'])

const { getUser } = useUserStore()
const message = useMessage()

// 响应式数据
const workHoursList = ref<any[]>([])
const workHoursRef = ref()
const nameOptions = ref([{ data: '' }])

// 方法
const onListWorkHoursTemplate = async () => {
  if (!props.formData.workHoursType) return
  if (props.formData.workHoursType == '99') return
  const res = await TestingTemplateApi.getTestingTemplateList(props.formData.workHoursType!)
  workHoursList.value = res
}

const onAduitWork = async () => {
  await message.confirm('确定工时已确认完成？确认完成将工时不可修改')
  emits('audit-work-hours')
}

const onCheckboxChange = async () => {
  const data = workHoursRef.value?.getCheckboxRecords(true)
  const workHoursIds = data.map((item: any) => item.id)
  const workHoursTotal = parseFloat(
    data
      .reduce((acc: number, curr: any) => {
        return acc + (curr.workHours || 0)
      }, 0)
      .toFixed(2)
  )
  await emits('change', { ids: workHoursIds, total: workHoursTotal })
  await ActivitiesApi.updateWorkHours({
    id: props.formData.id,
    workHoursIds: workHoursIds,
    workHoursTotal: workHoursTotal
  })
  message.success('确认成功')
}

const visibleMethod = ({ row }: { row: any }) => {
  if (
    props.formData.status !== 10 &&
    props.formData.progress !== 100 &&
    !props.formData?.workHoursAudit
  ) {
    return true
  }
  return props.formData.workHoursIds?.includes(row.id)
}

const checkMethod = ({ row }: { row: any }) => {
  if (
    props.formData.status !== 10 &&
    props.formData.progress !== 100 &&
    (props.formData.director?.includes(getUser.id) ||
      props.formData.coordinate?.includes(getUser.id)) &&
    !props.formData?.workHoursAudit
  ) {
    return true
  } else {
    return false
  }
}

// 初始化方法，由父组件调用
const init = async () => {
  await onListWorkHoursTemplate()
}

// 暴露方法给父组件
const auditWorkHours = () => {
  onAduitWork()
}

defineExpose({
  init,
  auditWorkHours,
  onListWorkHoursTemplate
})
</script>
