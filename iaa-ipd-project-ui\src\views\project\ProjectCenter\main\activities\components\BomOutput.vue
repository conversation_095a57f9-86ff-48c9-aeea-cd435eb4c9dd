<template>
  <template v-if="formData.targetDockingId == 0">
    <el-button
      type="primary"
      plain
      class="w-full mb-10px"
      size="small"
      v-if="allowPermission && data.status !== 10 && data.progress !== 100 && allowTheOutput"
      @click="openBomSelectedForm"
    >
      选取BOM
    </el-button>
    <vxe-table
      :data="dockingList"
      :header-cell-config="{ height: 30 }"
      :cell-config="{ height: 30 }"
      :cell-style="{ padding: '0 5px' }"
      :header-cell-style="{ padding: '0 5px' }"
      show-overflow
      border
      stripe
    >
      <vxe-column title="BOM" field="code" />
      <vxe-column title="操作" field="opereation" width="200" align="center">
        <template #default="{ row }">
          <el-button type="primary" link size="small" @click="openBomDetailsForm(row.code)">
            查看详情
          </el-button>
          <el-button
            type="danger"
            link
            size="small"
            v-if="allowPermission && data.status !== 10 && data.progress !== 100 && allowTheOutput"
            @click="deleteDocking(row.id)"
          >
            删除
          </el-button>
        </template>
      </vxe-column>
    </vxe-table>
  </template>
  <template v-else>
    <el-empty description="开发中，暂不支持" />
  </template>

  <!-- BOM选择对话框 -->
  <Dialog title="选择BOM作为输出对象" v-model="bomSelectedVisble">
    <el-input
      v-model="bomQueryName"
      size="small"
      placeholder="输入内容发起BOM查询"
      @keydown.enter="onListBom"
    >
      <template #append>
        <el-button type="primary" :icon="Search" size="small" @click="onListBom" />
      </template>
    </el-input>
    <vxe-table
      class="mt-10px"
      :data="bomList"
      :header-cell-config="{ height: 30 }"
      :cell-config="{ height: 30 }"
      :cell-style="{ padding: '0 5px' }"
      :header-cell-style="{ padding: '0 5px' }"
      height="250px"
      border
      stripe
      show-overflow
    >
      <vxe-column title="BOM" field="key" />
      <vxe-column title="BOM版本" field="value" width="100px" align="center" />
      <vxe-column title="操作" align="center" width="100px">
        <template #default="{ row }">
          <el-button type="primary" link size="small" @click="onBomSelected(row)">选择</el-button>
        </template>
      </vxe-column>
    </vxe-table>
    <el-divider>已选BOM</el-divider>
    <vxe-table
      :data="bomSelectedList"
      :header-cell-config="{ height: 30 }"
      :cell-config="{ height: 30 }"
      :cell-style="{ padding: '0 5px' }"
      :header-cell-style="{ padding: '0 5px' }"
      height="250px"
      border
      stripe
    >
      <vxe-column title="BOM" field="key" />
      <vxe-column title="BOM版本" field="value" width="100px" align="center" />
      <vxe-column title="操作" align="center" width="100px">
        <template #default="{ row }">
          <el-button type="danger" link size="small" @click="onBomUnselected(row)">
            取消选择
          </el-button>
        </template>
      </vxe-column>
    </vxe-table>
    <template #footer>
      <el-button type="primary" @click="onSubmitBomList" :loading="bomSelectedLoading">
        保存
      </el-button>
    </template>
  </Dialog>

  <!-- BOM详情对话框 -->
  <Dialog :title="bomTreeTitle" v-model="bomTreeVisible" width="70%">
    <vxe-table
      :data="bomTreeList"
      :header-cell-config="{ height: 30 }"
      :cell-config="{ height: 30 }"
      :cell-style="{ padding: '0 5px' }"
      :header-cell-style="{ padding: '0 5px' }"
      :tree-config="{ rowField: 'id', parentField: 'parentId', transform: true, expandAll: true }"
      border
      show-overflow
      align="center"
    >
      <vxe-column title="序号" type="seq" width="150" tree-node />
      <vxe-column title="品号" field="id" width="120" />
      <vxe-column title="版本" field="partVar" width="80" />
      <vxe-column title="品名" field="partName" align="left" width="250" />
      <vxe-column title="单位" field="unit" width="80" />
      <vxe-column title="规格" field="spec" align="left" />
      <vxe-column title="用量" field="counts" width="80" />
    </vxe-table>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { ActivitiesVO } from '@/api/project/activities'
import { DockingApi } from '@/api/project/activitiestargetdocking'
import { BomApi } from '@/api/docking/plm/bom'
import { useMessage } from '@/hooks/web/useMessage'

const props = defineProps<{
  data: ActivitiesVO
  formData: ActivitiesVO
  allowPermission: boolean
  allowTheOutput: boolean
}>()

const emits = defineEmits(['completed', 'refresh', 'docking-updated'])

const message = useMessage()

// 响应式数据
const dockingList = ref<any[]>([])
const bomList = ref<any[]>([])
const bomQueryName = ref('')
const bomSelectedVisble = ref(false)
const bomSelectedList = ref<any[]>([])
const bomSelectedLoading = ref(false)
const bomTreeList = ref<any[]>([])
const bomTreeVisible = ref(false)
const bomTreeTitle = ref('')

// 方法
const onListDocking = async () => {
  const res = await DockingApi.getDockingList(props.formData.id!)
  dockingList.value = res
  emits('docking-updated')
}

const onListBom = async () => {
  const res = await BomApi.getBomList(bomQueryName.value)
  bomList.value = res
}

const openBomSelectedForm = () => {
  bomSelectedVisble.value = true
  onListBom()
}

const onBomSelected = (row: any) => {
  const exists = bomSelectedList.value.some(
    (item) => item.key === row.key && item.value == row.value
  )
  if (exists) {
    message.alertError('当前BOM已选择')
    return
  }
  bomSelectedList.value.push(row)
}

const onBomUnselected = (row: any) => {
  bomSelectedList.value = bomSelectedList.value.filter(
    (item) => !(item.key === row.key && item.value == row.value)
  )
}

const onSubmitBomList = async () => {
  if (!(bomSelectedList.value && bomSelectedList.value.length > 0)) {
    message.alertError('未选择任何BOM')
    return
  }
  bomSelectedLoading.value = true
  try {
    const data = bomSelectedList.value.map((item) => {
      return {
        activitiesId: props.formData.id,
        targetType: props.formData.targetType,
        targetDockingId: props.formData.targetDockingId,
        code: item.key + item.value,
        name: item.key + item.value
      }
    })
    await DockingApi.createBatch(data)
    bomQueryName.value = ''
    bomSelectedList.value = []
    bomList.value = []
    bomSelectedVisble.value = false
    onListDocking()
    message
      .confirm('保存成功，请确认是否发起活动完成流程')
      .then(() => {
        emits('completed')
      })
      .catch(() => {
        emits('refresh')
      })
  } finally {
    bomSelectedLoading.value = false
  }
}

const openBomDetailsForm = async (bomName: string) => {
  const res = await BomApi.getBomTreeList(bomName)
  bomTreeList.value = res
  bomTreeVisible.value = true
  bomTreeTitle.value = bomName
}

const deleteDocking = async (id: number) => {
  await message.confirm('确定删除该输出对象？')
  await DockingApi.delDocking(id)
  message.success('删除成功')
  onListDocking()
}

// 初始化方法，由父组件调用
const init = async () => {
  if (props.formData.targetDockingId == 0) {
    await onListDocking()
  }
}

// 暴露方法给父组件
defineExpose({
  init,
  onListDocking
})
</script>
