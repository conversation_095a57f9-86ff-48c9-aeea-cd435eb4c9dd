import request from '@/config/axios'


export const DrawingUploadApi = {
  getDrawingUploadPage: async (params: any) => {
    return await request.post({ url: `/project/drawing-upload/page`, data: params })
  },

  addDrawingUpload: async (params: any) => {
    return await request.post({ url: `/project/drawing-upload/add`, data: params })
  },
    /** 导出上传看板分页 */
    exportDrawingUpload: (data: any) => {
      return request.downloadPost({ url: '/project/drawing-upload/export-drawing-page', data })
    }
}