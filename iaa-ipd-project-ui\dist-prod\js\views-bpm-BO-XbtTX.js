import{a as Ye,E as ie,P as Ie,Q as Dl,R as Ul,I as Ml,p as Ot,S as Le,K as Ce,T as rt,U as Oe,_ as Me,b as je,O as At,V as Nl,W as Bl,X as Fl,Y as He,Z as ct,N as ke,d as Tt,$ as Vt,a0 as Ll,a1 as $l,J as It,a2 as Rl,a3 as zl,a4 as ql,a5 as Jl,a6 as Gl,a7 as Kl,a8 as Yl,a9 as Hl,aa as Zl,ab as ya,ac as Ql}from"./views-Home-ewBLhuw2.js";import{r as m,d as we,n as ce,l as te,a0 as Wl,S as i,F as W,ah as _e,H as $e,y as Xl,B as ne,aE as mt,aN as ot,X as Re,m as h,p as R,K as o,u as n,Q as N,a7 as se,J as I,R as ee,E as yt,e as De,D as ei,z as Ae,ap as va,q as F,G as ti,P as J,L as me,A as Ne,w as re,ad as ae,T as jt,ab as ai,ac as ni,O as li,_ as fa,az as ba,aA as ga,c as ha,ak as wa,M as Sa}from"./vue-vendor-BbSoq9WN.js";import{b as pe,e as de,M as ue,o as ii,m as xa,a as ye,E as ri,G as ze,i as oi,ad as si,W as Se,r as xe,aa as Be,ab as Fe,V as Pe,a6 as pi,p as vt,q as ka,Y as Dt,ae as Ut,af as ui,ag as di,h as ci,J as Ca,K as ft,n as mi,S as yi,T as vi,d as Mt,Z as fi,_ as bi,c as gi,y as hi,ah as wi,ai as Si}from"./element-plus-DgaixBsQ.js";import{C as Nt,_ as xi}from"./views-Profile-4epX3JDT.js";import{bB as _a,bO as ki,bP as Ci,bQ as _i,bR as Ze,bS as Pi,bT as Ei,bU as Oi,bV as Ai,bW as Ti,bX as Vi,bY as Bt,bZ as bt,b_ as fe,b$ as Ii,c0 as ji,c1 as Di,c2 as Ui,c3 as Mi,c4 as Ni,c5 as Pa}from"./vendor-DLCNhz7G.js";import{m as Ea,e as gt}from"./utils-vendor-Vtb-rlR8.js";import{a as Bi,s as Fi,p as Te}from"./views-Error-Cx8xxY17.js";import{_ as Qe}from"./views-Login-BCX8kkKD.js";function We(){return{type:"Required",field:"formCreate$required",title:"\u662F\u5426\u5FC5\u586B"}}const Xe=(e,a,t)=>t.map(r=>(r.field==="formCreate$required"?r.title=e("props.required")||r.title:r.field&&r.field!=="_optionType"&&(r.title=e("components."+a+"."+r.field)||r.title),r)),Li=()=>{const e="\u6587\u4EF6\u4E0A\u4F20",a="UploadFile";return{icon:"icon-upload",label:e,name:a,rule(){return{type:a,field:Ye(),title:e,info:"",$required:!1}},props(t,{t:r}){return Xe(r,a+".props",[We(),{type:"select",field:"fileType",title:"\u6587\u4EF6\u7C7B\u578B",value:["doc","xls","ppt","txt","pdf"],options:[{label:"doc",value:"doc"},{label:"xls",value:"xls"},{label:"ppt",value:"ppt"},{label:"txt",value:"txt"},{label:"pdf",value:"pdf"}],props:{multiple:!0}},{type:"switch",field:"autoUpload",title:"\u662F\u5426\u5728\u9009\u53D6\u6587\u4EF6\u540E\u7ACB\u5373\u8FDB\u884C\u4E0A\u4F20",value:!0},{type:"switch",field:"drag",title:"\u62D6\u62FD\u4E0A\u4F20",value:!1},{type:"switch",field:"isShowTip",title:"\u662F\u5426\u663E\u793A\u63D0\u793A",value:!0},{type:"inputNumber",field:"fileSize",title:"\u5927\u5C0F\u9650\u5236(MB)",value:5,props:{min:0}},{type:"inputNumber",field:"limit",title:"\u6570\u91CF\u9650\u5236",value:5,props:{min:0}},{type:"switch",field:"disabled",title:"\u662F\u5426\u7981\u7528",value:!1}])}}},$i=()=>{const e="\u5355\u56FE\u4E0A\u4F20",a="UploadImg";return{icon:"icon-upload",label:e,name:a,rule(){return{type:a,field:Ye(),title:e,info:"",$required:!1}},props(t,{t:r}){return Xe(r,a+".props",[We(),{type:"switch",field:"drag",title:"\u62D6\u62FD\u4E0A\u4F20",value:!1},{type:"select",field:"fileType",title:"\u56FE\u7247\u7C7B\u578B\u9650\u5236",value:["image/jpeg","image/png","image/gif"],options:[{label:"image/apng",value:"image/apng"},{label:"image/bmp",value:"image/bmp"},{label:"image/gif",value:"image/gif"},{label:"image/jpeg",value:"image/jpeg"},{label:"image/pjpeg",value:"image/pjpeg"},{label:"image/svg+xml",value:"image/svg+xml"},{label:"image/tiff",value:"image/tiff"},{label:"image/webp",value:"image/webp"},{label:"image/x-icon",value:"image/x-icon"}],props:{multiple:!0}},{type:"inputNumber",field:"fileSize",title:"\u5927\u5C0F\u9650\u5236(MB)",value:5,props:{min:0}},{type:"input",field:"height",title:"\u7EC4\u4EF6\u9AD8\u5EA6",value:"150px"},{type:"input",field:"width",title:"\u7EC4\u4EF6\u5BBD\u5EA6",value:"150px"},{type:"input",field:"borderradius",title:"\u7EC4\u4EF6\u8FB9\u6846\u5706\u89D2",value:"8px"},{type:"switch",field:"disabled",title:"\u662F\u5426\u663E\u793A\u5220\u9664\u6309\u94AE",value:!0},{type:"switch",field:"showBtnText",title:"\u662F\u5426\u663E\u793A\u6309\u94AE\u6587\u5B57",value:!0}])}}},Ri=()=>{const e="\u591A\u56FE\u4E0A\u4F20",a="UploadImgs";return{icon:"icon-upload",label:e,name:a,rule(){return{type:a,field:Ye(),title:e,info:"",$required:!1}},props(t,{t:r}){return Xe(r,a+".props",[We(),{type:"switch",field:"drag",title:"\u62D6\u62FD\u4E0A\u4F20",value:!1},{type:"select",field:"fileType",title:"\u56FE\u7247\u7C7B\u578B\u9650\u5236",value:["image/jpeg","image/png","image/gif"],options:[{label:"image/apng",value:"image/apng"},{label:"image/bmp",value:"image/bmp"},{label:"image/gif",value:"image/gif"},{label:"image/jpeg",value:"image/jpeg"},{label:"image/pjpeg",value:"image/pjpeg"},{label:"image/svg+xml",value:"image/svg+xml"},{label:"image/tiff",value:"image/tiff"},{label:"image/webp",value:"image/webp"},{label:"image/x-icon",value:"image/x-icon"}],props:{multiple:!0}},{type:"inputNumber",field:"fileSize",title:"\u5927\u5C0F\u9650\u5236(MB)",value:5,props:{min:0}},{type:"inputNumber",field:"limit",title:"\u6570\u91CF\u9650\u5236",value:5,props:{min:0}},{type:"input",field:"height",title:"\u7EC4\u4EF6\u9AD8\u5EA6",value:"150px"},{type:"input",field:"width",title:"\u7EC4\u4EF6\u5BBD\u5EA6",value:"150px"},{type:"input",field:"borderradius",title:"\u7EC4\u4EF6\u8FB9\u6846\u5706\u89D2",value:"8px"}])}}},Oa=()=>ie.get({url:"/system/dict-type/list-all-simple"}),Aa=[{type:"select",field:"selectType",title:"\u9009\u62E9\u5668\u7C7B\u578B",value:"select",options:[{label:"\u4E0B\u62C9\u6846",value:"select"},{label:"\u5355\u9009\u6846",value:"radio"},{label:"\u591A\u9009\u6846",value:"checkbox"}],control:[{value:"select",condition:"==",method:"hidden",rule:["multiple","clearable","collapseTags","multipleLimit","allowCreate","filterable","noMatchText","remote","remoteMethod","reserveKeyword","defaultFirstOption","automaticDropdown"]}]},{type:"switch",field:"filterable",title:"\u662F\u5426\u53EF\u641C\u7D22"},{type:"switch",field:"multiple",title:"\u662F\u5426\u591A\u9009"},{type:"switch",field:"disabled",title:"\u662F\u5426\u7981\u7528"},{type:"switch",field:"clearable",title:"\u662F\u5426\u53EF\u4EE5\u6E05\u7A7A\u9009\u9879"},{type:"switch",field:"collapseTags",title:"\u591A\u9009\u65F6\u662F\u5426\u5C06\u9009\u4E2D\u503C\u6309\u6587\u5B57\u7684\u5F62\u5F0F\u5C55\u793A"},{type:"inputNumber",field:"multipleLimit",title:"\u591A\u9009\u65F6\u7528\u6237\u6700\u591A\u53EF\u4EE5\u9009\u62E9\u7684\u9879\u76EE\u6570\uFF0C\u4E3A 0 \u5219\u4E0D\u9650\u5236",props:{min:0}},{type:"input",field:"autocomplete",title:"autocomplete \u5C5E\u6027"},{type:"input",field:"placeholder",title:"\u5360\u4F4D\u7B26"},{type:"switch",field:"allowCreate",title:"\u662F\u5426\u5141\u8BB8\u7528\u6237\u521B\u5EFA\u65B0\u6761\u76EE"},{type:"input",field:"noMatchText",title:"\u641C\u7D22\u6761\u4EF6\u65E0\u5339\u914D\u65F6\u663E\u793A\u7684\u6587\u5B57"},{type:"input",field:"noDataText",title:"\u9009\u9879\u4E3A\u7A7A\u65F6\u663E\u793A\u7684\u6587\u5B57"},{type:"switch",field:"reserveKeyword",title:"\u591A\u9009\u4E14\u53EF\u641C\u7D22\u65F6\uFF0C\u662F\u5426\u5728\u9009\u4E2D\u4E00\u4E2A\u9009\u9879\u540E\u4FDD\u7559\u5F53\u524D\u7684\u641C\u7D22\u5173\u952E\u8BCD"},{type:"switch",field:"defaultFirstOption",title:"\u5728\u8F93\u5165\u6846\u6309\u4E0B\u56DE\u8F66\uFF0C\u9009\u62E9\u7B2C\u4E00\u4E2A\u5339\u914D\u9879"},{type:"switch",field:"popperAppendToBody",title:"\u662F\u5426\u5C06\u5F39\u51FA\u6846\u63D2\u5165\u81F3 body \u5143\u7D20",value:!0},{type:"switch",field:"automaticDropdown",title:"\u5BF9\u4E8E\u4E0D\u53EF\u641C\u7D22\u7684 Select\uFF0C\u662F\u5426\u5728\u8F93\u5165\u6846\u83B7\u5F97\u7126\u70B9\u540E\u81EA\u52A8\u5F39\u51FA\u9009\u9879\u83DC\u5355"}],zi=[{type:"input",field:"url",title:"url \u5730\u5740",props:{placeholder:"/system/user/simple-list"}},{type:"select",field:"method",title:"\u8BF7\u6C42\u7C7B\u578B",value:"GET",options:[{label:"GET",value:"GET"},{label:"POST",value:"POST"}],control:[{value:"GET",condition:"!=",method:"hidden",rule:[{type:"input",field:"data",title:"\u8BF7\u6C42\u53C2\u6570 JSON \u683C\u5F0F",props:{autosize:!0,type:"textarea",placeholder:'{"type": 1}'}}]}]},{type:"input",field:"labelField",title:"label \u5C5E\u6027",info:"\u53EF\u4EE5\u4F7F\u7528 el \u8868\u8FBE\u5F0F\uFF1A${\u5C5E\u6027}\uFF0C\u6765\u5B9E\u73B0\u590D\u6742\u6570\u636E\u7EC4\u5408\u3002\u5982\uFF1A${nickname}-${id}",props:{placeholder:"nickname"}},{type:"input",field:"valueField",title:"value \u5C5E\u6027",info:"\u53EF\u4EE5\u4F7F\u7528 el \u8868\u8FBE\u5F0F\uFF1A${\u5C5E\u6027}\uFF0C\u6765\u5B9E\u73B0\u590D\u6742\u6570\u636E\u7EC4\u5408\u3002\u5982\uFF1A${nickname}-${id}",props:{placeholder:"id"}},{type:"input",field:"parseFunc",title:"\u9009\u9879\u89E3\u6790\u51FD\u6570",info:`data \u4E3A\u63A5\u53E3\u8FD4\u56DE\u503C,\u9700\u8981\u5199\u4E00\u4E2A\u533F\u540D\u51FD\u6570\u89E3\u6790\u8FD4\u56DE\u503C\u4E3A\u9009\u62E9\u5668 options \u5217\u8868
    (data: any)=>{ label: string; value: any }[]`,props:{autosize:!0,rows:{minRows:2,maxRows:6},type:"textarea",placeholder:`
        function (data) {
            console.log(data)
            return data.list.map(item=> ({label: item.nickname,value: item.id}))
        }`}},{type:"switch",field:"remote",info:"\u662F\u5426\u53EF\u641C\u7D22",title:"\u5176\u4E2D\u7684\u9009\u9879\u662F\u5426\u4ECE\u670D\u52A1\u5668\u8FDC\u7A0B\u52A0\u8F7D"},{type:"input",field:"remoteField",title:"\u8BF7\u6C42\u53C2\u6570",info:"\u8FDC\u7A0B\u8BF7\u6C42\u65F6\u8BF7\u6C42\u643A\u5E26\u7684\u53C2\u6570\u540D\u79F0\uFF0C\u5982\uFF1Aname"}];var qi=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const Ji=()=>{const e="\u5B57\u5178\u9009\u62E9\u5668",a="DictSelect",t=Ea(Aa),r=m([]);return we(()=>qi(void 0,null,function*(){var s;const p=yield Oa();!p||p.length===0||(r.value=(s=p==null?void 0:p.map(c=>({label:c.name,value:c.type})))!=null?s:[])})),{icon:"icon-doc-text",label:e,name:a,rule(){return{type:a,field:Ye(),title:e,info:"",$required:!1}},props(s,{t:p}){return Xe(p,a+".props",[We(),{type:"select",field:"dictType",title:"\u5B57\u5178\u7C7B\u578B",value:"",options:r.value},{type:"select",field:"dictValueType",title:"\u5B57\u5178\u503C\u7C7B\u578B",value:"str",options:[{label:"\u6570\u5B57",value:"int"},{label:"\u5B57\u7B26\u4E32",value:"str"},{label:"\u5E03\u5C14\u503C",value:"bool"}]},...t])}}},Gi=()=>{const e="\u5BCC\u6587\u672C",a="Editor";return{icon:"icon-editor",label:e,name:a,rule(){return{type:a,field:Ye(),title:e,info:"",$required:!1}},props(t,{t:r}){return Xe(r,a+".props",[We(),{type:"input",field:"height",title:"\u9AD8\u5EA6"},{type:"switch",field:"readonly",title:"\u662F\u5426\u53EA\u8BFB"}])}}},Ft=e=>{const a=e.label,t=e.name,r=Ea(Aa);return{icon:e.icon,label:a,name:t,rule(){return{type:t,field:Ye(),title:a,info:"",$required:!1}},props(s,{t:p}){return e.props||(e.props=[]),Xe(p,t+".props",[We(),...e.props,...r])}}};var Ta=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const Ki=e=>Ta(void 0,null,function*(){const a=Gi(),t=Li(),r=$i(),s=Ri(),p=()=>{var b,f;(b=e.value)==null||b.removeMenuItem("upload"),(f=e.value)==null||f.removeMenuItem("fc-editor"),[a,t,r,s].forEach(w=>{var S,v;(S=e.value)==null||S.addComponent(w),(v=e.value)==null||v.appendMenuItem("main",{icon:w.icon,name:w.name,label:w.label})})},c=Ft({name:"UserSelect",label:"\u7528\u6237\u9009\u62E9\u5668",icon:"icon-user-o"}),u=Ft({name:"DeptSelect",label:"\u90E8\u95E8\u9009\u62E9\u5668",icon:"icon-address-card-o"}),l=Ji(),d=Ft({name:"ApiSelect",label:"\u63A5\u53E3\u9009\u62E9\u5668",icon:"icon-server",props:[...zi]}),g=()=>{var b;const f={name:"system",title:"\u7CFB\u7EDF\u5B57\u6BB5",list:[c,u,l,d].map(w=>{var S;return(S=e.value)==null||S.addComponent(w),{icon:w.icon,name:w.name,label:w.label}})};(b=e.value)==null||b.addMenu(f)};we(()=>Ta(void 0,null,function*(){yield ce(),p(),g()}))});function ht(e){return typeof e=="function"||Object.prototype.toString.call(e)==="[object Object]"&&!Xl(e)}const Yi=e=>te({name:e.name,props:{labelField:{type:String,default:()=>e.labelField??"label"},valueField:{type:String,default:()=>e.valueField??"value"},url:{type:String,default:()=>e.url??""},method:{type:String,default:"GET"},parseFunc:{type:String,default:""},data:{type:String,default:""},selectType:{type:String,default:"select"},multiple:{type:Boolean,default:!1},remote:{type:Boolean,default:!1},remoteField:{type:String,default:"label"}},setup(a){const t=Wl(),r=m([]),s=m(!1),p=m(),c=async()=>{if(r.value=[],!Ie(a.url))switch(a.method){case"GET":let v=a.url;a.remote&&(v=`${v}?${a.remoteField}=${p.value}`),u(await ie.get({url:v}));break;case"POST":const y=Dl(a.data);a.remote&&(y[a.remoteField]=p.value),u(await ie.post({url:a.url,data:y}));break}};function u(v){var y;if(!Ie(a.parseFunc)){r.value=(y=d())==null?void 0:y(v);return}if(Array.isArray(v)){l(v);return}if(v=v.list,v&&Array.isArray(v)){l(v);return}}function l(v){if(Array.isArray(v)){r.value=v.map(y=>({label:g(y,a.labelField),value:g(y,a.valueField)}));return}}function d(){let v=null;return a.parseFunc&&(v=new Function(`return ${a.parseFunc}`)()),v}function g(v,y){if(y.indexOf("${")===-1)return v[y];const x=/\$\{([^}]*)}/g;return y.replace(x,(k,C)=>v[C.trim()])}const b=async v=>{if(v){s.value=!0;try{p.value=v,await c()}finally{s.value=!1}}};we(async()=>{await c()});const f=()=>{let v;if(a.multiple){let y;return i(_e("el-select"),$e({class:"w-1/1",multiple:!0,loading:s.value},t,{remote:a.remote},a.remote&&{remoteMethod:b}),ht(y=r.value.map((x,k)=>i(_e("el-option"),{key:k,label:x.label,value:x.value},null)))?y:{default:()=>[y]})}return i(_e("el-select"),$e({class:"w-1/1",loading:s.value},t,{remote:a.remote},a.remote&&{remoteMethod:b}),ht(v=r.value.map((y,x)=>i(_e("el-option"),{key:x,label:y.label,value:y.value},null)))?v:{default:()=>[v]})},w=()=>{let v;return Ie(r.value)&&(r.value=[{label:"\u9009\u98791",value:"\u9009\u98791"},{label:"\u9009\u98792",value:"\u9009\u98792"}]),i(_e("el-checkbox-group"),$e({class:"w-1/1"},t),ht(v=r.value.map((y,x)=>i(_e("el-checkbox"),{key:x,label:y.label,value:y.value},null)))?v:{default:()=>[v]})},S=()=>{let v;return Ie(r.value)&&(r.value=[{label:"\u9009\u98791",value:"\u9009\u98791"},{label:"\u9009\u98792",value:"\u9009\u98792"}]),i(_e("el-radio-group"),$e({class:"w-1/1"},t),ht(v=r.value.map((y,x)=>i(_e("el-radio"),{key:x,value:y.value},{default:()=>[y.label]})))?v:{default:()=>[v]})};return()=>i(W,null,[a.selectType==="select"?f():a.selectType==="radio"?S():a.selectType==="checkbox"?w():f()])}}),Lt=_a("tagsView",{state:()=>({visitedViews:[],cachedViews:new Set}),getters:{getVisitedViews(){return this.visitedViews},getCachedViews(){return Array.from(this.cachedViews)}},actions:{addView(e){this.addVisitedView(e),this.addCachedView()},addVisitedView(e){var a,t;this.visitedViews.some(r=>r.path===e.path)||(a=e.meta)!=null&&a.noTagsView||this.visitedViews.push(Object.assign({},e,{title:((t=e.meta)==null?void 0:t.title)||"no-name"}))},addCachedView(){var e;const a=new Set;for(const t of this.visitedViews){const r=Ul(t);if((e=r.meta)!=null&&e.noCache)continue;const s=r.name;a.add(s)}Array.from(this.cachedViews).sort().toString()!==Array.from(a).sort().toString()&&(this.cachedViews=a)},delView(e){this.delVisitedView(e),this.delCachedView()},delVisitedView(e){for(const[a,t]of this.visitedViews.entries())if(t.path===e.path){this.visitedViews.splice(a,1);break}},delCachedView(){const e=Ml.currentRoute.value,a=Ot(this.getCachedViews,t=>t===e.name);a>-1&&this.cachedViews.delete(this.getCachedViews[a])},delAllViews(){this.delAllVisitedViews(),this.delCachedView()},delAllVisitedViews(){this.visitedViews=[]},delOthersViews(e){this.delOthersVisitedViews(e),this.addCachedView()},delOthersVisitedViews(e){this.visitedViews=this.visitedViews.filter(a=>{var t;return((t=a==null?void 0:a.meta)==null?void 0:t.affix)||a.path===e.path})},delLeftViews(e){const a=Ot(this.visitedViews,t=>t.path===e.path);a>-1&&(this.visitedViews=this.visitedViews.filter((t,r)=>{var s;return((s=t==null?void 0:t.meta)==null?void 0:s.affix)||t.path===e.path||r>a}),this.addCachedView())},delRightViews(e){const a=Ot(this.visitedViews,t=>t.path===e.path);a>-1&&(this.visitedViews=this.visitedViews.filter((t,r)=>{var s;return((s=t==null?void 0:t.meta)==null?void 0:s.affix)||t.path===e.path||r<a}),this.addCachedView())},updateVisitedView(e){for(let a of this.visitedViews)if(a.path===e.path){a=Object.assign(a,e);break}}},persist:!1});var wt=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const Hi=e=>wt(void 0,null,function*(){return yield ie.post({url:"/bpm/form/create",data:e})}),Zi=e=>wt(void 0,null,function*(){return yield ie.put({url:"/bpm/form/update",data:e})}),Qi=e=>wt(void 0,null,function*(){return yield ie.get({url:"/bpm/form/get?id="+e})}),Wi=()=>wt(void 0,null,function*(){return yield ie.get({url:"/bpm/form/simple-list"})}),Xi=e=>JSON.stringify(e.value.getOption()),er=e=>{const a=e.value.getRule(),t=[];return a.forEach(r=>{t.push(JSON.stringify(r))}),t},Va=e=>{const a=[];return e.forEach(t=>{a.push(JSON.parse(t))}),a},tr=(e,a,t)=>{e.value.setOption(JSON.parse(a)),e.value.setRule(Va(t))},St=(e,a,t,r)=>{ne(e)&&(e=e.value),e.option=JSON.parse(a),e.rule=Va(t),r&&setTimeout(()=>{e.values=r},100)};var ar=Object.defineProperty,nr=Object.defineProperties,lr=Object.getOwnPropertyDescriptors,Ia=Object.getOwnPropertySymbols,ir=Object.prototype.hasOwnProperty,rr=Object.prototype.propertyIsEnumerable,ja=(e,a,t)=>a in e?ar(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,or=(e,a)=>{for(var t in a||(a={}))ir.call(a,t)&&ja(e,t,a[t]);if(Ia)for(var t of Ia(a))rr.call(a,t)&&ja(e,t,a[t]);return e},sr=(e,a)=>nr(e,lr(a)),Da=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const pr=te(sr(or({},{name:"BpmFormEditor"}),{__name:"index",setup(e){const{t:a}=Bi(),t=je(),{push:r,currentRoute:s}=mt(),{query:p}=ot(),{delView:c}=Lt(),u=m();Ki(u);const l=m(!1),d=m(!1),g=m({name:"",status:Nt.ENABLE,remark:""}),b=Re({name:[{required:!0,message:"\u8868\u5355\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u5F00\u542F\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),f=m(),w=()=>{l.value=!0},S=()=>Da(this,null,function*(){if(!(!f||!(yield f.value.validate()))){d.value=!0;try{const y=g.value;y.conf=Xi(u),y.fields=er(u),y.id?(yield Zi(y),t.success(a("common.updateSuccess"))):(yield Hi(y),t.success(a("common.createSuccess"))),l.value=!1,v()}finally{d.value=!1}}}),v=()=>{c(n(s)),r("/bpm/manager/form")};return we(()=>Da(this,null,function*(){const y=p.id;if(!y)return;const x=yield Qi(y);g.value=x,tr(u,x.conf,x.fields)})),(y,x)=>{const k=Me,C=pe,E=Le,j=de,P=ue,D=ii,G=xa,O=ye,U=Ce;return h(),R(W,null,[i(E,null,{default:o(()=>[i(n(ki),{ref_key:"designer",ref:u,height:"780px"},{handle:o(()=>[i(C,{round:"",size:"small",type:"primary",onClick:w},{default:o(()=>[i(k,{class:"mr-5px",icon:"ep:plus"}),N(" \u4FDD\u5B58 ")]),_:1})]),_:1},512)]),_:1}),i(U,{modelValue:n(l),"onUpdate:modelValue":x[4]||(x[4]=Y=>ne(l)?l.value=Y:null),title:"\u4FDD\u5B58\u8868\u5355",width:"600"},{footer:o(()=>[i(C,{disabled:n(d),type:"primary",onClick:S},{default:o(()=>[N("\u786E \u5B9A")]),_:1},8,["disabled"]),i(C,{onClick:x[3]||(x[3]=Y=>l.value=!1)},{default:o(()=>[N("\u53D6 \u6D88")]),_:1})]),default:o(()=>[i(O,{ref_key:"formRef",ref:f,model:n(g),rules:n(b),"label-width":"80px"},{default:o(()=>[i(P,{label:"\u8868\u5355\u540D",prop:"name"},{default:o(()=>[i(j,{modelValue:n(g).name,"onUpdate:modelValue":x[0]||(x[0]=Y=>n(g).name=Y),placeholder:"\u8BF7\u8F93\u5165\u8868\u5355\u540D"},null,8,["modelValue"])]),_:1}),i(P,{label:"\u72B6\u6001",prop:"status"},{default:o(()=>[i(G,{modelValue:n(g).status,"onUpdate:modelValue":x[1]||(x[1]=Y=>n(g).status=Y)},{default:o(()=>[(h(!0),R(W,null,se(n(rt)(n(Oe).COMMON_STATUS),Y=>(h(),I(D,{key:Y.value,label:Y.value},{default:o(()=>[N(ee(Y.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),i(P,{label:"\u5907\u6CE8",prop:"remark"},{default:o(()=>[i(j,{modelValue:n(g).remark,"onUpdate:modelValue":x[2]||(x[2]=Y=>n(g).remark=Y),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])],64)}}})),ur=Object.freeze(Object.defineProperty({__proto__:null,default:pr},Symbol.toStringTag,{value:"Module"})),Ua=(e,a,t)=>(t||(t="camunda"),`<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions 
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL"
  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC"
  xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
  id="diagram_${e}"
  targetNamespace="${{activiti:"http://activiti.org/bpmn",camunda:"http://bpmn.io/schema/bpmn",flowable:"http://flowable.org/bpmn"}[t]}">
  <bpmn2:process id="${e}" name="${a}" isExecutable="true">
  </bpmn2:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="${e}">
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn2:definitions>`);function dr(e){return function(a,t){return t=t||{},a=e[a]||a,a.replace(/{([^}]+)}/g,function(r,s){let p=t[s];return e[t[s]]!==null&&e[t[s]]!==void 0&&(p=e[t[s]]),p||"{"+s+"}"})}}const cr={"Append EndEvent":"\u8FFD\u52A0\u7ED3\u675F\u4E8B\u4EF6","Append Gateway":"\u8FFD\u52A0\u7F51\u5173","Append Task":"\u8FFD\u52A0\u4EFB\u52A1","Append Intermediate/Boundary Event":"\u8FFD\u52A0\u4E2D\u95F4\u629B\u51FA\u4E8B\u4EF6/\u8FB9\u754C\u4E8B\u4EF6","Activate the global connect tool":"\u6FC0\u6D3B\u5168\u5C40\u8FDE\u63A5\u5DE5\u5177","Append {type}":"\u6DFB\u52A0 {type}","Add Lane above":"\u5728\u4E0A\u9762\u6DFB\u52A0\u9053","Divide into two Lanes":"\u5206\u5272\u6210\u4E24\u4E2A\u9053","Divide into three Lanes":"\u5206\u5272\u6210\u4E09\u4E2A\u9053","Add Lane below":"\u5728\u4E0B\u9762\u6DFB\u52A0\u9053","Append compensation activity":"\u8FFD\u52A0\u8865\u507F\u6D3B\u52A8","Change type":"\u4FEE\u6539\u7C7B\u578B","Connect using Association":"\u4F7F\u7528\u5173\u8054\u8FDE\u63A5","Connect using Sequence/MessageFlow or Association":"\u4F7F\u7528\u987A\u5E8F/\u6D88\u606F\u6D41\u6216\u8005\u5173\u8054\u8FDE\u63A5","Connect using DataInputAssociation":"\u4F7F\u7528\u6570\u636E\u8F93\u5165\u5173\u8054\u8FDE\u63A5",Remove:"\u79FB\u9664","Activate the hand tool":"\u6FC0\u6D3B\u6293\u624B\u5DE5\u5177","Activate the lasso tool":"\u6FC0\u6D3B\u5957\u7D22\u5DE5\u5177","Activate the create/remove space tool":"\u6FC0\u6D3B\u521B\u5EFA/\u5220\u9664\u7A7A\u95F4\u5DE5\u5177","Create expanded SubProcess":"\u521B\u5EFA\u6269\u5C55\u5B50\u8FC7\u7A0B","Create IntermediateThrowEvent/BoundaryEvent":"\u521B\u5EFA\u4E2D\u95F4\u629B\u51FA\u4E8B\u4EF6/\u8FB9\u754C\u4E8B\u4EF6","Create Pool/Participant":"\u521B\u5EFA\u6C60/\u53C2\u4E0E\u8005","Parallel Multi Instance":"\u5E76\u884C\u591A\u91CD\u4E8B\u4EF6","Sequential Multi Instance":"\u65F6\u5E8F\u591A\u91CD\u4E8B\u4EF6",DataObjectReference:"\u6570\u636E\u5BF9\u8C61\u53C2\u8003",DataStoreReference:"\u6570\u636E\u5B58\u50A8\u53C2\u8003",Loop:"\u5FAA\u73AF","Ad-hoc":"\u5373\u5E2D","Create {type}":"\u521B\u5EFA {type}",Task:"\u4EFB\u52A1","Send Task":"\u53D1\u9001\u4EFB\u52A1","Receive Task":"\u63A5\u6536\u4EFB\u52A1","User Task":"\u7528\u6237\u4EFB\u52A1","Manual Task":"\u624B\u5DE5\u4EFB\u52A1","Business Rule Task":"\u4E1A\u52A1\u89C4\u5219\u4EFB\u52A1","Service Task":"\u670D\u52A1\u4EFB\u52A1","Script Task":"\u811A\u672C\u4EFB\u52A1","Call Activity":"\u8C03\u7528\u6D3B\u52A8","Sub Process (collapsed)":"\u5B50\u6D41\u7A0B\uFF08\u6298\u53E0\u7684\uFF09","Sub Process (expanded)":"\u5B50\u6D41\u7A0B\uFF08\u5C55\u5F00\u7684\uFF09","Start Event":"\u5F00\u59CB\u4E8B\u4EF6",StartEvent:"\u5F00\u59CB\u4E8B\u4EF6","Intermediate Throw Event":"\u4E2D\u95F4\u4E8B\u4EF6","End Event":"\u7ED3\u675F\u4E8B\u4EF6",EndEvent:"\u7ED3\u675F\u4E8B\u4EF6","Create StartEvent":"\u521B\u5EFA\u5F00\u59CB\u4E8B\u4EF6","Create EndEvent":"\u521B\u5EFA\u7ED3\u675F\u4E8B\u4EF6","Create Task":"\u521B\u5EFA\u4EFB\u52A1","Create User Task":"\u521B\u5EFA\u7528\u6237\u4EFB\u52A1","Create Service Task":"\u521B\u5EFA\u670D\u52A1\u4EFB\u52A1","Create Gateway":"\u521B\u5EFA\u7F51\u5173","Create DataObjectReference":"\u521B\u5EFA\u6570\u636E\u5BF9\u8C61","Create DataStoreReference":"\u521B\u5EFA\u6570\u636E\u5B58\u50A8","Create Group":"\u521B\u5EFA\u5206\u7EC4","Create Intermediate/Boundary Event":"\u521B\u5EFA\u4E2D\u95F4/\u8FB9\u754C\u4E8B\u4EF6","Message Start Event":"\u6D88\u606F\u5F00\u59CB\u4E8B\u4EF6","Timer Start Event":"\u5B9A\u65F6\u5F00\u59CB\u4E8B\u4EF6","Conditional Start Event":"\u6761\u4EF6\u5F00\u59CB\u4E8B\u4EF6","Signal Start Event":"\u4FE1\u53F7\u5F00\u59CB\u4E8B\u4EF6","Error Start Event":"\u9519\u8BEF\u5F00\u59CB\u4E8B\u4EF6","Escalation Start Event":"\u5347\u7EA7\u5F00\u59CB\u4E8B\u4EF6","Compensation Start Event":"\u8865\u507F\u5F00\u59CB\u4E8B\u4EF6","Message Start Event (non-interrupting)":"\u6D88\u606F\u5F00\u59CB\u4E8B\u4EF6\uFF08\u975E\u4E2D\u65AD\uFF09","Timer Start Event (non-interrupting)":"\u5B9A\u65F6\u5F00\u59CB\u4E8B\u4EF6\uFF08\u975E\u4E2D\u65AD\uFF09","Conditional Start Event (non-interrupting)":"\u6761\u4EF6\u5F00\u59CB\u4E8B\u4EF6\uFF08\u975E\u4E2D\u65AD\uFF09","Signal Start Event (non-interrupting)":"\u4FE1\u53F7\u5F00\u59CB\u4E8B\u4EF6\uFF08\u975E\u4E2D\u65AD\uFF09","Escalation Start Event (non-interrupting)":"\u5347\u7EA7\u5F00\u59CB\u4E8B\u4EF6\uFF08\u975E\u4E2D\u65AD\uFF09","Message Intermediate Catch Event":"\u6D88\u606F\u4E2D\u95F4\u6355\u83B7\u4E8B\u4EF6","Message Intermediate Throw Event":"\u6D88\u606F\u4E2D\u95F4\u629B\u51FA\u4E8B\u4EF6","Timer Intermediate Catch Event":"\u5B9A\u65F6\u4E2D\u95F4\u6355\u83B7\u4E8B\u4EF6","Escalation Intermediate Throw Event":"\u5347\u7EA7\u4E2D\u95F4\u629B\u51FA\u4E8B\u4EF6","Conditional Intermediate Catch Event":"\u6761\u4EF6\u4E2D\u95F4\u6355\u83B7\u4E8B\u4EF6","Link Intermediate Catch Event":"\u94FE\u63A5\u4E2D\u95F4\u6355\u83B7\u4E8B\u4EF6","Link Intermediate Throw Event":"\u94FE\u63A5\u4E2D\u95F4\u629B\u51FA\u4E8B\u4EF6","Compensation Intermediate Throw Event":"\u8865\u507F\u4E2D\u95F4\u629B\u51FA\u4E8B\u4EF6","Signal Intermediate Catch Event":"\u4FE1\u53F7\u4E2D\u95F4\u6355\u83B7\u4E8B\u4EF6","Signal Intermediate Throw Event":"\u4FE1\u53F7\u4E2D\u95F4\u629B\u51FA\u4E8B\u4EF6","Message End Event":"\u6D88\u606F\u7ED3\u675F\u4E8B\u4EF6","Escalation End Event":"\u5B9A\u65F6\u7ED3\u675F\u4E8B\u4EF6","Error End Event":"\u9519\u8BEF\u7ED3\u675F\u4E8B\u4EF6","Cancel End Event":"\u53D6\u6D88\u7ED3\u675F\u4E8B\u4EF6","Compensation End Event":"\u8865\u507F\u7ED3\u675F\u4E8B\u4EF6","Signal End Event":"\u4FE1\u53F7\u7ED3\u675F\u4E8B\u4EF6","Terminate End Event":"\u7EC8\u6B62\u7ED3\u675F\u4E8B\u4EF6","Message Boundary Event":"\u6D88\u606F\u8FB9\u754C\u4E8B\u4EF6","Message Boundary Event (non-interrupting)":"\u6D88\u606F\u8FB9\u754C\u4E8B\u4EF6\uFF08\u975E\u4E2D\u65AD\uFF09","Timer Boundary Event":"\u5B9A\u65F6\u8FB9\u754C\u4E8B\u4EF6","Timer Boundary Event (non-interrupting)":"\u5B9A\u65F6\u8FB9\u754C\u4E8B\u4EF6\uFF08\u975E\u4E2D\u65AD\uFF09","Escalation Boundary Event":"\u5347\u7EA7\u8FB9\u754C\u4E8B\u4EF6","Escalation Boundary Event (non-interrupting)":"\u5347\u7EA7\u8FB9\u754C\u4E8B\u4EF6\uFF08\u975E\u4E2D\u65AD\uFF09","Conditional Boundary Event":"\u6761\u4EF6\u8FB9\u754C\u4E8B\u4EF6","Conditional Boundary Event (non-interrupting)":"\u6761\u4EF6\u8FB9\u754C\u4E8B\u4EF6\uFF08\u975E\u4E2D\u65AD\uFF09","Error Boundary Event":"\u9519\u8BEF\u8FB9\u754C\u4E8B\u4EF6","Cancel Boundary Event":"\u53D6\u6D88\u8FB9\u754C\u4E8B\u4EF6","Signal Boundary Event":"\u4FE1\u53F7\u8FB9\u754C\u4E8B\u4EF6","Signal Boundary Event (non-interrupting)":"\u4FE1\u53F7\u8FB9\u754C\u4E8B\u4EF6\uFF08\u975E\u4E2D\u65AD\uFF09","Compensation Boundary Event":"\u8865\u507F\u8FB9\u754C\u4E8B\u4EF6","Exclusive Gateway":"\u4E92\u65A5\u7F51\u5173","Parallel Gateway":"\u5E76\u884C\u7F51\u5173","Inclusive Gateway":"\u76F8\u5BB9\u7F51\u5173","Complex Gateway":"\u590D\u6742\u7F51\u5173","Event based Gateway":"\u4E8B\u4EF6\u7F51\u5173",Transaction:"\u8F6C\u8FD0","Sub Process":"\u5B50\u6D41\u7A0B","Event Sub Process":"\u4E8B\u4EF6\u5B50\u6D41\u7A0B","Collapsed Pool":"\u6298\u53E0\u6C60","Expanded Pool":"\u5C55\u5F00\u6C60","no parent for {element} in {parent}":"\u5728{parent}\u91CC\uFF0C{element}\u6CA1\u6709\u7236\u7C7B","no shape type specified":"\u6CA1\u6709\u6307\u5B9A\u7684\u5F62\u72B6\u7C7B\u578B","flow elements must be children of pools/participants":"\u6D41\u5143\u7D20\u5FC5\u987B\u662F\u6C60/\u53C2\u4E0E\u8005\u7684\u5B50\u7C7B","out of bounds release":"out of bounds release","more than {count} child lanes":"\u5B50\u9053\u5927\u4E8E{count} ","element required":"\u5143\u7D20\u4E0D\u80FD\u4E3A\u7A7A","diagram not part of bpmn:Definitions":"\u6D41\u7A0B\u56FE\u4E0D\u7B26\u5408bpmn\u89C4\u8303","no diagram to display":"\u6CA1\u6709\u53EF\u5C55\u793A\u7684\u6D41\u7A0B\u56FE","no process or collaboration to display":"\u6CA1\u6709\u53EF\u5C55\u793A\u7684\u6D41\u7A0B/\u534F\u4F5C","element {element} referenced by {referenced}#{property} not yet drawn":"\u7531{referenced}#{property}\u5F15\u7528\u7684{element}\u5143\u7D20\u4ECD\u672A\u7ED8\u5236","already rendered {element}":"{element} \u5DF2\u88AB\u6E32\u67D3","failed to import {element}":"\u5BFC\u5165{element}\u5931\u8D25",Id:"\u7F16\u53F7",Name:"\u540D\u79F0",General:"\u5E38\u89C4",Details:"\u8BE6\u60C5","Message Name":"\u6D88\u606F\u540D\u79F0",Message:"\u6D88\u606F",Initiator:"\u521B\u5EFA\u8005","Asynchronous Continuations":"\u6301\u7EED\u5F02\u6B65","Asynchronous Before":"\u5F02\u6B65\u524D","Asynchronous After":"\u5F02\u6B65\u540E","Job Configuration":"\u5DE5\u4F5C\u914D\u7F6E",Exclusive:"\u6392\u9664","Job Priority":"\u5DE5\u4F5C\u4F18\u5148\u7EA7","Retry Time Cycle":"\u91CD\u8BD5\u65F6\u95F4\u5468\u671F",Documentation:"\u6587\u6863","Element Documentation":"\u5143\u7D20\u6587\u6863","History Configuration":"\u5386\u53F2\u914D\u7F6E","History Time To Live":"\u5386\u53F2\u7684\u751F\u5B58\u65F6\u95F4",Forms:"\u8868\u5355","Form Key":"\u8868\u5355key","Form Fields":"\u8868\u5355\u5B57\u6BB5","Business Key":"\u4E1A\u52A1key","Form Field":"\u8868\u5355\u5B57\u6BB5",ID:"\u7F16\u53F7",Type:"\u7C7B\u578B",Label:"\u540D\u79F0","Default Value":"\u9ED8\u8BA4\u503C","Default Flow":"\u9ED8\u8BA4\u6D41\u8F6C\u8DEF\u5F84","Conditional Flow":"\u6761\u4EF6\u6D41\u8F6C\u8DEF\u5F84","Sequence Flow":"\u666E\u901A\u6D41\u8F6C\u8DEF\u5F84",Validation:"\u6821\u9A8C","Add Constraint":"\u6DFB\u52A0\u7EA6\u675F",Config:"\u914D\u7F6E",Properties:"\u5C5E\u6027","Add Property":"\u6DFB\u52A0\u5C5E\u6027",Value:"\u503C",Listeners:"\u76D1\u542C\u5668","Execution Listener":"\u6267\u884C\u76D1\u542C","Event Type":"\u4E8B\u4EF6\u7C7B\u578B","Listener Type":"\u76D1\u542C\u5668\u7C7B\u578B","Java Class":"Java\u7C7B",Expression:"\u8868\u8FBE\u5F0F","Must provide a value":"\u5FC5\u987B\u63D0\u4F9B\u4E00\u4E2A\u503C","Delegate Expression":"\u4EE3\u7406\u8868\u8FBE\u5F0F",Script:"\u811A\u672C","Script Format":"\u811A\u672C\u683C\u5F0F","Script Type":"\u811A\u672C\u7C7B\u578B","Inline Script":"\u5185\u8054\u811A\u672C","External Script":"\u5916\u90E8\u811A\u672C",Resource:"\u8D44\u6E90","Field Injection":"\u5B57\u6BB5\u6CE8\u5165",Extensions:"\u6269\u5C55","Input/Output":"\u8F93\u5165/\u8F93\u51FA","Input Parameters":"\u8F93\u5165\u53C2\u6570","Output Parameters":"\u8F93\u51FA\u53C2\u6570",Parameters:"\u53C2\u6570","Output Parameter":"\u8F93\u51FA\u53C2\u6570","Timer Definition Type":"\u5B9A\u65F6\u5668\u5B9A\u4E49\u7C7B\u578B","Timer Definition":"\u5B9A\u65F6\u5668\u5B9A\u4E49",Date:"\u65E5\u671F",Duration:"\u6301\u7EED",Cycle:"\u5FAA\u73AF",Signal:"\u4FE1\u53F7","Signal Name":"\u4FE1\u53F7\u540D\u79F0",Escalation:"\u5347\u7EA7",Error:"\u9519\u8BEF","Link Name":"\u94FE\u63A5\u540D\u79F0",Condition:"\u6761\u4EF6\u540D\u79F0","Variable Name":"\u53D8\u91CF\u540D\u79F0","Variable Event":"\u53D8\u91CF\u4E8B\u4EF6","Specify more than one variable change event as a comma separated list.":"\u591A\u4E2A\u53D8\u91CF\u4E8B\u4EF6\u4EE5\u9017\u53F7\u9694\u5F00","Wait for Completion":"\u7B49\u5F85\u5B8C\u6210","Activity Ref":"\u6D3B\u52A8\u53C2\u8003","Version Tag":"\u7248\u672C\u6807\u7B7E",Executable:"\u53EF\u6267\u884C\u6587\u4EF6","External Task Configuration":"\u6269\u5C55\u4EFB\u52A1\u914D\u7F6E","Task Priority":"\u4EFB\u52A1\u4F18\u5148\u7EA7",External:"\u5916\u90E8",Connector:"\u8FDE\u63A5\u5668","Must configure Connector":"\u5FC5\u987B\u914D\u7F6E\u8FDE\u63A5\u5668","Connector Id":"\u8FDE\u63A5\u5668\u7F16\u53F7",Implementation:"\u5B9E\u73B0\u65B9\u5F0F","Field Injections":"\u5B57\u6BB5\u6CE8\u5165",Fields:"\u5B57\u6BB5","Result Variable":"\u7ED3\u679C\u53D8\u91CF",Topic:"\u4E3B\u9898","Configure Connector":"\u914D\u7F6E\u8FDE\u63A5\u5668","Input Parameter":"\u8F93\u5165\u53C2\u6570",Assignee:"\u4EE3\u7406\u4EBA","Candidate Users":"\u5019\u9009\u7528\u6237","Candidate Groups":"\u5019\u9009\u7EC4","Due Date":"\u5230\u671F\u65F6\u95F4","Follow Up Date":"\u8DDF\u8E2A\u65E5\u671F",Priority:"\u4F18\u5148\u7EA7","The follow up date as an EL expression (e.g. ${someDate} or an ISO date (e.g. 2015-06-26T09:54:00)":"\u8DDF\u8E2A\u65E5\u671F\u5FC5\u987B\u7B26\u5408EL\u8868\u8FBE\u5F0F\uFF0C\u5982\uFF1A ${someDate} ,\u6216\u8005\u4E00\u4E2AISO\u6807\u51C6\u65E5\u671F\uFF0C\u5982\uFF1A2015-06-26T09:54:00","The due date as an EL expression (e.g. ${someDate} or an ISO date (e.g. 2015-06-26T09:54:00)":"\u8DDF\u8E2A\u65E5\u671F\u5FC5\u987B\u7B26\u5408EL\u8868\u8FBE\u5F0F\uFF0C\u5982\uFF1A ${someDate} ,\u6216\u8005\u4E00\u4E2AISO\u6807\u51C6\u65E5\u671F\uFF0C\u5982\uFF1A2015-06-26T09:54:00",Variables:"\u53D8\u91CF","Candidate Starter Configuration":"\u5019\u9009\u4EBA\u8D77\u52A8\u5668\u914D\u7F6E","Candidate Starter Groups":"\u5019\u9009\u4EBA\u8D77\u52A8\u5668\u7EC4","This maps to the process definition key.":"\u8FD9\u6620\u5C04\u5230\u6D41\u7A0B\u5B9A\u4E49\u952E\u3002","Candidate Starter Users":"\u5019\u9009\u4EBA\u8D77\u52A8\u5668\u7684\u7528\u6237","Specify more than one user as a comma separated list.":"\u6307\u5B9A\u591A\u4E2A\u7528\u6237\u4F5C\u4E3A\u9017\u53F7\u5206\u9694\u7684\u5217\u8868\u3002","Tasklist Configuration":"Tasklist\u914D\u7F6E",Startable:"\u542F\u52A8","Specify more than one group as a comma separated list.":"\u6307\u5B9A\u591A\u4E2A\u7EC4\u4F5C\u4E3A\u9017\u53F7\u5206\u9694\u7684\u5217\u8868\u3002"},mr="Camunda",yr="http://camunda.org/schema/1.0/bpmn",vr="camunda",fr={tagAlias:"lowerCase"},br=[],gr=[{name:"Definitions",isAbstract:!0,extends:["bpmn:Definitions"],properties:[{name:"diagramRelationId",isAttr:!0,type:"String"}]},{name:"InOutBinding",superClass:["Element"],isAbstract:!0,properties:[{name:"source",isAttr:!0,type:"String"},{name:"sourceExpression",isAttr:!0,type:"String"},{name:"target",isAttr:!0,type:"String"},{name:"businessKey",isAttr:!0,type:"String"},{name:"local",isAttr:!0,type:"Boolean",default:!1},{name:"variables",isAttr:!0,type:"String"}]},{name:"In",superClass:["InOutBinding"],meta:{allowedIn:["bpmn:CallActivity","bpmn:SignalEventDefinition"]}},{name:"Out",superClass:["InOutBinding"],meta:{allowedIn:["bpmn:CallActivity"]}},{name:"AsyncCapable",isAbstract:!0,extends:["bpmn:Activity","bpmn:Gateway","bpmn:Event"],properties:[{name:"async",isAttr:!0,type:"Boolean",default:!1},{name:"asyncBefore",isAttr:!0,type:"Boolean",default:!1},{name:"asyncAfter",isAttr:!0,type:"Boolean",default:!1},{name:"exclusive",isAttr:!0,type:"Boolean",default:!0}]},{name:"JobPriorized",isAbstract:!0,extends:["bpmn:Process","camunda:AsyncCapable"],properties:[{name:"jobPriority",isAttr:!0,type:"String"}]},{name:"SignalEventDefinition",isAbstract:!0,extends:["bpmn:SignalEventDefinition"],properties:[{name:"async",isAttr:!0,type:"Boolean",default:!1}]},{name:"ErrorEventDefinition",isAbstract:!0,extends:["bpmn:ErrorEventDefinition"],properties:[{name:"errorCodeVariable",isAttr:!0,type:"String"},{name:"errorMessageVariable",isAttr:!0,type:"String"}]},{name:"Error",isAbstract:!0,extends:["bpmn:Error"],properties:[{name:"camunda:errorMessage",isAttr:!0,type:"String"}]},{name:"PotentialStarter",superClass:["Element"],properties:[{name:"resourceAssignmentExpression",type:"bpmn:ResourceAssignmentExpression"}]},{name:"FormSupported",isAbstract:!0,extends:["bpmn:StartEvent","bpmn:UserTask"],properties:[{name:"formHandlerClass",isAttr:!0,type:"String"},{name:"formKey",isAttr:!0,type:"String"}]},{name:"TemplateSupported",isAbstract:!0,extends:["bpmn:Process","bpmn:FlowElement"],properties:[{name:"modelerTemplate",isAttr:!0,type:"String"},{name:"modelerTemplateVersion",isAttr:!0,type:"Integer"}]},{name:"Initiator",isAbstract:!0,extends:["bpmn:StartEvent"],properties:[{name:"initiator",isAttr:!0,type:"String"}]},{name:"ScriptTask",isAbstract:!0,extends:["bpmn:ScriptTask"],properties:[{name:"resultVariable",isAttr:!0,type:"String"},{name:"resource",isAttr:!0,type:"String"}]},{name:"Process",isAbstract:!0,extends:["bpmn:Process"],properties:[{name:"candidateStarterGroups",isAttr:!0,type:"String"},{name:"candidateStarterUsers",isAttr:!0,type:"String"},{name:"versionTag",isAttr:!0,type:"String"},{name:"historyTimeToLive",isAttr:!0,type:"String"},{name:"isStartableInTasklist",isAttr:!0,type:"Boolean",default:!0}]},{name:"EscalationEventDefinition",isAbstract:!0,extends:["bpmn:EscalationEventDefinition"],properties:[{name:"escalationCodeVariable",isAttr:!0,type:"String"}]},{name:"FormalExpression",isAbstract:!0,extends:["bpmn:FormalExpression"],properties:[{name:"resource",isAttr:!0,type:"String"}]},{name:"Assignable",extends:["bpmn:UserTask"],properties:[{name:"assignee",isAttr:!0,type:"String"},{name:"candidateUsers",isAttr:!0,type:"String"},{name:"candidateGroups",isAttr:!0,type:"String"},{name:"dueDate",isAttr:!0,type:"String"},{name:"followUpDate",isAttr:!0,type:"String"},{name:"priority",isAttr:!0,type:"String"},{name:"candidateStrategy",isAttr:!0,type:"String"},{name:"candidateParam",isAttr:!0,type:"String"}]},{name:"CallActivity",extends:["bpmn:CallActivity"],properties:[{name:"calledElementBinding",isAttr:!0,type:"String",default:"latest"},{name:"calledElementVersion",isAttr:!0,type:"String"},{name:"calledElementVersionTag",isAttr:!0,type:"String"},{name:"calledElementTenantId",isAttr:!0,type:"String"},{name:"caseRef",isAttr:!0,type:"String"},{name:"caseBinding",isAttr:!0,type:"String",default:"latest"},{name:"caseVersion",isAttr:!0,type:"String"},{name:"caseTenantId",isAttr:!0,type:"String"},{name:"variableMappingClass",isAttr:!0,type:"String"},{name:"variableMappingDelegateExpression",isAttr:!0,type:"String"}]},{name:"ServiceTaskLike",extends:["bpmn:ServiceTask","bpmn:BusinessRuleTask","bpmn:SendTask","bpmn:MessageEventDefinition"],properties:[{name:"expression",isAttr:!0,type:"String"},{name:"class",isAttr:!0,type:"String"},{name:"delegateExpression",isAttr:!0,type:"String"},{name:"resultVariable",isAttr:!0,type:"String"}]},{name:"DmnCapable",extends:["bpmn:BusinessRuleTask"],properties:[{name:"decisionRef",isAttr:!0,type:"String"},{name:"decisionRefBinding",isAttr:!0,type:"String",default:"latest"},{name:"decisionRefVersion",isAttr:!0,type:"String"},{name:"mapDecisionResult",isAttr:!0,type:"String",default:"resultList"},{name:"decisionRefTenantId",isAttr:!0,type:"String"}]},{name:"ExternalCapable",extends:["camunda:ServiceTaskLike"],properties:[{name:"type",isAttr:!0,type:"String"},{name:"topic",isAttr:!0,type:"String"}]},{name:"TaskPriorized",extends:["bpmn:Process","camunda:ExternalCapable"],properties:[{name:"taskPriority",isAttr:!0,type:"String"}]},{name:"Properties",superClass:["Element"],meta:{allowedIn:["*"]},properties:[{name:"values",type:"Property",isMany:!0}]},{name:"Property",superClass:["Element"],properties:[{name:"id",type:"String",isAttr:!0},{name:"name",type:"String",isAttr:!0},{name:"value",type:"String",isAttr:!0}]},{name:"Connector",superClass:["Element"],meta:{allowedIn:["camunda:ServiceTaskLike"]},properties:[{name:"inputOutput",type:"InputOutput"},{name:"connectorId",type:"String"}]},{name:"InputOutput",superClass:["Element"],meta:{allowedIn:["bpmn:FlowNode","camunda:Connector"]},properties:[{name:"inputOutput",type:"InputOutput"},{name:"connectorId",type:"String"},{name:"inputParameters",isMany:!0,type:"InputParameter"},{name:"outputParameters",isMany:!0,type:"OutputParameter"}]},{name:"InputOutputParameter",properties:[{name:"name",isAttr:!0,type:"String"},{name:"value",isBody:!0,type:"String"},{name:"definition",type:"InputOutputParameterDefinition"}]},{name:"InputOutputParameterDefinition",isAbstract:!0},{name:"List",superClass:["InputOutputParameterDefinition"],properties:[{name:"items",isMany:!0,type:"InputOutputParameterDefinition"}]},{name:"Map",superClass:["InputOutputParameterDefinition"],properties:[{name:"entries",isMany:!0,type:"Entry"}]},{name:"Entry",properties:[{name:"key",isAttr:!0,type:"String"},{name:"value",isBody:!0,type:"String"},{name:"definition",type:"InputOutputParameterDefinition"}]},{name:"Value",superClass:["InputOutputParameterDefinition"],properties:[{name:"id",isAttr:!0,type:"String"},{name:"name",isAttr:!0,type:"String"},{name:"value",isBody:!0,type:"String"}]},{name:"Script",superClass:["InputOutputParameterDefinition"],properties:[{name:"scriptFormat",isAttr:!0,type:"String"},{name:"resource",isAttr:!0,type:"String"},{name:"value",isBody:!0,type:"String"}]},{name:"Field",superClass:["Element"],meta:{allowedIn:["camunda:ServiceTaskLike","camunda:ExecutionListener","camunda:TaskListener"]},properties:[{name:"name",isAttr:!0,type:"String"},{name:"expression",type:"String"},{name:"stringValue",isAttr:!0,type:"String"},{name:"string",type:"String"}]},{name:"InputParameter",superClass:["InputOutputParameter"]},{name:"OutputParameter",superClass:["InputOutputParameter"]},{name:"Collectable",isAbstract:!0,extends:["bpmn:MultiInstanceLoopCharacteristics"],superClass:["camunda:AsyncCapable"],properties:[{name:"collection",isAttr:!0,type:"String"},{name:"elementVariable",isAttr:!0,type:"String"}]},{name:"FailedJobRetryTimeCycle",superClass:["Element"],meta:{allowedIn:["camunda:AsyncCapable","bpmn:MultiInstanceLoopCharacteristics"]},properties:[{name:"body",isBody:!0,type:"String"}]},{name:"ExecutionListener",superClass:["Element"],meta:{allowedIn:["bpmn:Task","bpmn:ServiceTask","bpmn:UserTask","bpmn:BusinessRuleTask","bpmn:ScriptTask","bpmn:ReceiveTask","bpmn:ManualTask","bpmn:ExclusiveGateway","bpmn:SequenceFlow","bpmn:ParallelGateway","bpmn:InclusiveGateway","bpmn:EventBasedGateway","bpmn:StartEvent","bpmn:IntermediateCatchEvent","bpmn:IntermediateThrowEvent","bpmn:EndEvent","bpmn:BoundaryEvent","bpmn:CallActivity","bpmn:SubProcess","bpmn:Process"]},properties:[{name:"expression",isAttr:!0,type:"String"},{name:"class",isAttr:!0,type:"String"},{name:"delegateExpression",isAttr:!0,type:"String"},{name:"event",isAttr:!0,type:"String"},{name:"script",type:"Script"},{name:"fields",type:"Field",isMany:!0}]},{name:"TaskListener",superClass:["Element"],meta:{allowedIn:["bpmn:UserTask"]},properties:[{name:"expression",isAttr:!0,type:"String"},{name:"class",isAttr:!0,type:"String"},{name:"delegateExpression",isAttr:!0,type:"String"},{name:"event",isAttr:!0,type:"String"},{name:"script",type:"Script"},{name:"fields",type:"Field",isMany:!0},{name:"id",type:"String",isAttr:!0},{name:"eventDefinitions",type:"bpmn:TimerEventDefinition",isMany:!0}]},{name:"FormProperty",superClass:["Element"],meta:{allowedIn:["bpmn:StartEvent","bpmn:UserTask"]},properties:[{name:"id",type:"String",isAttr:!0},{name:"name",type:"String",isAttr:!0},{name:"type",type:"String",isAttr:!0},{name:"required",type:"String",isAttr:!0},{name:"readable",type:"String",isAttr:!0},{name:"writable",type:"String",isAttr:!0},{name:"variable",type:"String",isAttr:!0},{name:"expression",type:"String",isAttr:!0},{name:"datePattern",type:"String",isAttr:!0},{name:"default",type:"String",isAttr:!0},{name:"values",type:"Value",isMany:!0}]},{name:"FormData",superClass:["Element"],meta:{allowedIn:["bpmn:StartEvent","bpmn:UserTask"]},properties:[{name:"fields",type:"FormField",isMany:!0},{name:"businessKey",type:"String",isAttr:!0}]},{name:"FormField",superClass:["Element"],properties:[{name:"id",type:"String",isAttr:!0},{name:"label",type:"String",isAttr:!0},{name:"type",type:"String",isAttr:!0},{name:"datePattern",type:"String",isAttr:!0},{name:"defaultValue",type:"String",isAttr:!0},{name:"properties",type:"Properties"},{name:"validation",type:"Validation"},{name:"values",type:"Value",isMany:!0}]},{name:"Validation",superClass:["Element"],properties:[{name:"constraints",type:"Constraint",isMany:!0}]},{name:"Constraint",superClass:["Element"],properties:[{name:"name",type:"String",isAttr:!0},{name:"config",type:"String",isAttr:!0}]},{name:"ConditionalEventDefinition",isAbstract:!0,extends:["bpmn:ConditionalEventDefinition"],properties:[{name:"variableName",isAttr:!0,type:"String"},{name:"variableEvents",isAttr:!0,type:"String"}]}],hr=[],wr={name:mr,uri:yr,prefix:vr,xml:fr,associations:br,types:gr,emumerations:hr},Sr="Activiti",xr="http://activiti.org/bpmn",kr="activiti",Cr={tagAlias:"lowerCase"},_r=[],Pr=[{name:"Definitions",isAbstract:!0,extends:["bpmn:Definitions"],properties:[{name:"diagramRelationId",isAttr:!0,type:"String"}]},{name:"InOutBinding",superClass:["Element"],isAbstract:!0,properties:[{name:"source",isAttr:!0,type:"String"},{name:"sourceExpression",isAttr:!0,type:"String"},{name:"target",isAttr:!0,type:"String"},{name:"businessKey",isAttr:!0,type:"String"},{name:"local",isAttr:!0,type:"Boolean",default:!1},{name:"variables",isAttr:!0,type:"String"}]},{name:"In",superClass:["InOutBinding"],meta:{allowedIn:["bpmn:CallActivity"]}},{name:"Out",superClass:["InOutBinding"],meta:{allowedIn:["bpmn:CallActivity"]}},{name:"AsyncCapable",isAbstract:!0,extends:["bpmn:Activity","bpmn:Gateway","bpmn:Event"],properties:[{name:"async",isAttr:!0,type:"Boolean",default:!1},{name:"asyncBefore",isAttr:!0,type:"Boolean",default:!1},{name:"asyncAfter",isAttr:!0,type:"Boolean",default:!1},{name:"exclusive",isAttr:!0,type:"Boolean",default:!0}]},{name:"JobPriorized",isAbstract:!0,extends:["bpmn:Process","activiti:AsyncCapable"],properties:[{name:"jobPriority",isAttr:!0,type:"String"}]},{name:"SignalEventDefinition",isAbstract:!0,extends:["bpmn:SignalEventDefinition"],properties:[{name:"async",isAttr:!0,type:"Boolean",default:!1}]},{name:"ErrorEventDefinition",isAbstract:!0,extends:["bpmn:ErrorEventDefinition"],properties:[{name:"errorCodeVariable",isAttr:!0,type:"String"},{name:"errorMessageVariable",isAttr:!0,type:"String"}]},{name:"Error",isAbstract:!0,extends:["bpmn:Error"],properties:[{name:"activiti:errorMessage",isAttr:!0,type:"String"}]},{name:"PotentialStarter",superClass:["Element"],properties:[{name:"resourceAssignmentExpression",type:"bpmn:ResourceAssignmentExpression"}]},{name:"FormSupported",isAbstract:!0,extends:["bpmn:StartEvent","bpmn:UserTask"],properties:[{name:"formHandlerClass",isAttr:!0,type:"String"},{name:"formKey",isAttr:!0,type:"String"}]},{name:"TemplateSupported",isAbstract:!0,extends:["bpmn:Process","bpmn:FlowElement"],properties:[{name:"modelerTemplate",isAttr:!0,type:"String"}]},{name:"Initiator",isAbstract:!0,extends:["bpmn:StartEvent"],properties:[{name:"initiator",isAttr:!0,type:"String"}]},{name:"ScriptTask",isAbstract:!0,extends:["bpmn:ScriptTask"],properties:[{name:"resultVariable",isAttr:!0,type:"String"},{name:"resource",isAttr:!0,type:"String"}]},{name:"Process",isAbstract:!0,extends:["bpmn:Process"],properties:[{name:"candidateStarterGroups",isAttr:!0,type:"String"},{name:"candidateStarterUsers",isAttr:!0,type:"String"},{name:"versionTag",isAttr:!0,type:"String"},{name:"historyTimeToLive",isAttr:!0,type:"String"},{name:"isStartableInTasklist",isAttr:!0,type:"Boolean",default:!0},{name:"executionListener",isAbstract:!0,type:"Expression"}]},{name:"EscalationEventDefinition",isAbstract:!0,extends:["bpmn:EscalationEventDefinition"],properties:[{name:"escalationCodeVariable",isAttr:!0,type:"String"}]},{name:"FormalExpression",isAbstract:!0,extends:["bpmn:FormalExpression"],properties:[{name:"resource",isAttr:!0,type:"String"}]},{name:"multiinstance_type",superClass:["Element"]},{name:"multiinstance_condition",superClass:["Element"]},{name:"Assignable",extends:["bpmn:UserTask"],properties:[{name:"assignee",isAttr:!0,type:"String"},{name:"candidateUsers",isAttr:!0,type:"String"},{name:"candidateGroups",isAttr:!0,type:"String"},{name:"dueDate",isAttr:!0,type:"String"},{name:"followUpDate",isAttr:!0,type:"String"},{name:"priority",isAttr:!0,type:"String"},{name:"multiinstance_condition",isAttr:!0,type:"String"},{name:"candidateStrategy",isAttr:!0,type:"String"},{name:"candidateParam",isAttr:!0,type:"String"}]},{name:"CallActivity",extends:["bpmn:CallActivity"],properties:[{name:"calledElementBinding",isAttr:!0,type:"String",default:"latest"},{name:"calledElementVersion",isAttr:!0,type:"String"},{name:"calledElementVersionTag",isAttr:!0,type:"String"},{name:"calledElementTenantId",isAttr:!0,type:"String"},{name:"caseRef",isAttr:!0,type:"String"},{name:"caseBinding",isAttr:!0,type:"String",default:"latest"},{name:"caseVersion",isAttr:!0,type:"String"},{name:"caseTenantId",isAttr:!0,type:"String"},{name:"variableMappingClass",isAttr:!0,type:"String"},{name:"variableMappingDelegateExpression",isAttr:!0,type:"String"}]},{name:"ServiceTaskLike",extends:["bpmn:ServiceTask","bpmn:BusinessRuleTask","bpmn:SendTask","bpmn:MessageEventDefinition"],properties:[{name:"expression",isAttr:!0,type:"String"},{name:"class",isAttr:!0,type:"String"},{name:"delegateExpression",isAttr:!0,type:"String"},{name:"resultVariable",isAttr:!0,type:"String"}]},{name:"DmnCapable",extends:["bpmn:BusinessRuleTask"],properties:[{name:"decisionRef",isAttr:!0,type:"String"},{name:"decisionRefBinding",isAttr:!0,type:"String",default:"latest"},{name:"decisionRefVersion",isAttr:!0,type:"String"},{name:"mapDecisionResult",isAttr:!0,type:"String",default:"resultList"},{name:"decisionRefTenantId",isAttr:!0,type:"String"}]},{name:"ExternalCapable",extends:["activiti:ServiceTaskLike"],properties:[{name:"type",isAttr:!0,type:"String"},{name:"topic",isAttr:!0,type:"String"}]},{name:"TaskPriorized",extends:["bpmn:Process","activiti:ExternalCapable"],properties:[{name:"taskPriority",isAttr:!0,type:"String"}]},{name:"Properties",superClass:["Element"],meta:{allowedIn:["*"]},properties:[{name:"values",type:"Property",isMany:!0}]},{name:"Property",superClass:["Element"],properties:[{name:"id",type:"String",isAttr:!0},{name:"name",type:"String",isAttr:!0},{name:"value",type:"String",isAttr:!0}]},{name:"Connector",superClass:["Element"],meta:{allowedIn:["activiti:ServiceTaskLike"]},properties:[{name:"inputOutput",type:"InputOutput"},{name:"connectorId",type:"String"}]},{name:"InputOutput",superClass:["Element"],meta:{allowedIn:["bpmn:FlowNode","activiti:Connector"]},properties:[{name:"inputOutput",type:"InputOutput"},{name:"connectorId",type:"String"},{name:"inputParameters",isMany:!0,type:"InputParameter"},{name:"outputParameters",isMany:!0,type:"OutputParameter"}]},{name:"InputOutputParameter",properties:[{name:"name",isAttr:!0,type:"String"},{name:"value",isBody:!0,type:"String"},{name:"definition",type:"InputOutputParameterDefinition"}]},{name:"InputOutputParameterDefinition",isAbstract:!0},{name:"List",superClass:["InputOutputParameterDefinition"],properties:[{name:"items",isMany:!0,type:"InputOutputParameterDefinition"}]},{name:"Map",superClass:["InputOutputParameterDefinition"],properties:[{name:"entries",isMany:!0,type:"Entry"}]},{name:"Entry",properties:[{name:"key",isAttr:!0,type:"String"},{name:"value",isBody:!0,type:"String"},{name:"definition",type:"InputOutputParameterDefinition"}]},{name:"Value",superClass:["InputOutputParameterDefinition"],properties:[{name:"id",isAttr:!0,type:"String"},{name:"name",isAttr:!0,type:"String"},{name:"value",isBody:!0,type:"String"}]},{name:"Script",superClass:["InputOutputParameterDefinition"],properties:[{name:"scriptFormat",isAttr:!0,type:"String"},{name:"resource",isAttr:!0,type:"String"},{name:"value",isBody:!0,type:"String"}]},{name:"Field",superClass:["Element"],meta:{allowedIn:["activiti:ServiceTaskLike","activiti:ExecutionListener","activiti:TaskListener"]},properties:[{name:"name",isAttr:!0,type:"String"},{name:"expression",type:"String"},{name:"stringValue",isAttr:!0,type:"String"},{name:"string",type:"String"}]},{name:"InputParameter",superClass:["InputOutputParameter"]},{name:"OutputParameter",superClass:["InputOutputParameter"]},{name:"Collectable",isAbstract:!0,extends:["bpmn:MultiInstanceLoopCharacteristics"],superClass:["activiti:AsyncCapable"],properties:[{name:"collection",isAttr:!0,type:"String"},{name:"elementVariable",isAttr:!0,type:"String"}]},{name:"FailedJobRetryTimeCycle",superClass:["Element"],meta:{allowedIn:["activiti:AsyncCapable","bpmn:MultiInstanceLoopCharacteristics"]},properties:[{name:"body",isBody:!0,type:"String"}]},{name:"ExecutionListener",superClass:["Element"],meta:{allowedIn:["bpmn:Task","bpmn:ServiceTask","bpmn:UserTask","bpmn:BusinessRuleTask","bpmn:ScriptTask","bpmn:ReceiveTask","bpmn:ManualTask","bpmn:ExclusiveGateway","bpmn:SequenceFlow","bpmn:ParallelGateway","bpmn:InclusiveGateway","bpmn:EventBasedGateway","bpmn:StartEvent","bpmn:IntermediateCatchEvent","bpmn:IntermediateThrowEvent","bpmn:EndEvent","bpmn:BoundaryEvent","bpmn:CallActivity","bpmn:SubProcess","bpmn:Process"]},properties:[{name:"expression",isAttr:!0,type:"String"},{name:"class",isAttr:!0,type:"String"},{name:"delegateExpression",isAttr:!0,type:"String"},{name:"event",isAttr:!0,type:"String"},{name:"script",type:"Script"},{name:"fields",type:"Field",isMany:!0}]},{name:"TaskListener",superClass:["Element"],meta:{allowedIn:["bpmn:UserTask"]},properties:[{name:"expression",isAttr:!0,type:"String"},{name:"class",isAttr:!0,type:"String"},{name:"delegateExpression",isAttr:!0,type:"String"},{name:"event",isAttr:!0,type:"String"},{name:"script",type:"Script"},{name:"fields",type:"Field",isMany:!0}]},{name:"FormProperty",superClass:["Element"],meta:{allowedIn:["bpmn:StartEvent","bpmn:UserTask"]},properties:[{name:"id",type:"String",isAttr:!0},{name:"name",type:"String",isAttr:!0},{name:"type",type:"String",isAttr:!0},{name:"required",type:"String",isAttr:!0},{name:"readable",type:"String",isAttr:!0},{name:"writable",type:"String",isAttr:!0},{name:"variable",type:"String",isAttr:!0},{name:"expression",type:"String",isAttr:!0},{name:"datePattern",type:"String",isAttr:!0},{name:"default",type:"String",isAttr:!0},{name:"values",type:"Value",isMany:!0}]},{name:"FormProperty",superClass:["Element"],properties:[{name:"id",type:"String",isAttr:!0},{name:"label",type:"String",isAttr:!0},{name:"type",type:"String",isAttr:!0},{name:"datePattern",type:"String",isAttr:!0},{name:"defaultValue",type:"String",isAttr:!0},{name:"properties",type:"Properties"},{name:"validation",type:"Validation"},{name:"values",type:"Value",isMany:!0}]},{name:"Validation",superClass:["Element"],properties:[{name:"constraints",type:"Constraint",isMany:!0}]},{name:"Constraint",superClass:["Element"],properties:[{name:"name",type:"String",isAttr:!0},{name:"config",type:"String",isAttr:!0}]},{name:"ConditionalEventDefinition",isAbstract:!0,extends:["bpmn:ConditionalEventDefinition"],properties:[{name:"variableName",isAttr:!0,type:"String"},{name:"variableEvent",isAttr:!0,type:"String"}]}],Er=[],Or={name:Sr,uri:xr,prefix:kr,xml:Cr,associations:_r,types:Pr,emumerations:Er},Ar="Flowable",Tr="http://flowable.org/bpmn",Vr="flowable",Ir={tagAlias:"lowerCase"},jr=[],Dr=[{name:"InOutBinding",superClass:["Element"],isAbstract:!0,properties:[{name:"source",isAttr:!0,type:"String"},{name:"sourceExpression",isAttr:!0,type:"String"},{name:"target",isAttr:!0,type:"String"},{name:"businessKey",isAttr:!0,type:"String"},{name:"local",isAttr:!0,type:"Boolean",default:!1},{name:"variables",isAttr:!0,type:"String"}]},{name:"In",superClass:["InOutBinding"],meta:{allowedIn:["bpmn:CallActivity"]}},{name:"Out",superClass:["InOutBinding"],meta:{allowedIn:["bpmn:CallActivity"]}},{name:"AsyncCapable",isAbstract:!0,extends:["bpmn:Activity","bpmn:Gateway","bpmn:Event"],properties:[{name:"async",isAttr:!0,type:"Boolean",default:!1},{name:"asyncBefore",isAttr:!0,type:"Boolean",default:!1},{name:"asyncAfter",isAttr:!0,type:"Boolean",default:!1},{name:"exclusive",isAttr:!0,type:"Boolean",default:!0}]},{name:"JobPriorized",isAbstract:!0,extends:["bpmn:Process","flowable:AsyncCapable"],properties:[{name:"jobPriority",isAttr:!0,type:"String"}]},{name:"SignalEventDefinition",isAbstract:!0,extends:["bpmn:SignalEventDefinition"],properties:[{name:"async",isAttr:!0,type:"Boolean",default:!1}]},{name:"ErrorEventDefinition",isAbstract:!0,extends:["bpmn:ErrorEventDefinition"],properties:[{name:"errorCodeVariable",isAttr:!0,type:"String"},{name:"errorMessageVariable",isAttr:!0,type:"String"}]},{name:"Error",isAbstract:!0,extends:["bpmn:Error"],properties:[{name:"flowable:errorMessage",isAttr:!0,type:"String"}]},{name:"PotentialStarter",superClass:["Element"],properties:[{name:"resourceAssignmentExpression",type:"bpmn:ResourceAssignmentExpression"}]},{name:"FormSupported",isAbstract:!0,extends:["bpmn:StartEvent","bpmn:UserTask"],properties:[{name:"formHandlerClass",isAttr:!0,type:"String"},{name:"formKey",isAttr:!0,type:"String"},{name:"formType",isAttr:!0,type:"String"},{name:"formReadOnly",isAttr:!0,type:"Boolean",default:!1},{name:"formInit",isAttr:!0,type:"Boolean",default:!0}]},{name:"TemplateSupported",isAbstract:!0,extends:["bpmn:Process","bpmn:FlowElement"],properties:[{name:"modelerTemplate",isAttr:!0,type:"String"}]},{name:"Initiator",isAbstract:!0,extends:["bpmn:StartEvent"],properties:[{name:"initiator",isAttr:!0,type:"String"}]},{name:"ScriptTask",isAbstract:!0,extends:["bpmn:ScriptTask"],properties:[{name:"resultVariable",isAttr:!0,type:"String"},{name:"resource",isAttr:!0,type:"String"}]},{name:"Process",isAbstract:!0,extends:["bpmn:Process"],properties:[{name:"candidateStarterGroups",isAttr:!0,type:"String"},{name:"candidateStarterUsers",isAttr:!0,type:"String"},{name:"versionTag",isAttr:!0,type:"String"},{name:"historyTimeToLive",isAttr:!0,type:"String"},{name:"isStartableInTasklist",isAttr:!0,type:"Boolean",default:!0}]},{name:"EscalationEventDefinition",isAbstract:!0,extends:["bpmn:EscalationEventDefinition"],properties:[{name:"escalationCodeVariable",isAttr:!0,type:"String"}]},{name:"FormalExpression",isAbstract:!0,extends:["bpmn:FormalExpression"],properties:[{name:"resource",isAttr:!0,type:"String"}]},{name:"Assignable",extends:["bpmn:UserTask"],properties:[{name:"assignee",isAttr:!0,type:"String"},{name:"candidateUsers",isAttr:!0,type:"String"},{name:"candidateGroups",isAttr:!0,type:"String"},{name:"dueDate",isAttr:!0,type:"String"},{name:"followUpDate",isAttr:!0,type:"String"},{name:"priority",isAttr:!0,type:"String"},{name:"candidateStrategy",isAttr:!0,type:"String"},{name:"candidateParam",isAttr:!0,type:"String"}]},{name:"Assignee",supperClass:"Element",meta:{allowedIn:["*"]},properties:[{name:"label",type:"String",isAttr:!0},{name:"viewId",type:"Number",isAttr:!0}]},{name:"CallActivity",extends:["bpmn:CallActivity"],properties:[{name:"calledElementBinding",isAttr:!0,type:"String",default:"latest"},{name:"calledElementVersion",isAttr:!0,type:"String"},{name:"calledElementVersionTag",isAttr:!0,type:"String"},{name:"calledElementTenantId",isAttr:!0,type:"String"},{name:"caseRef",isAttr:!0,type:"String"},{name:"caseBinding",isAttr:!0,type:"String",default:"latest"},{name:"caseVersion",isAttr:!0,type:"String"},{name:"caseTenantId",isAttr:!0,type:"String"},{name:"variableMappingClass",isAttr:!0,type:"String"},{name:"variableMappingDelegateExpression",isAttr:!0,type:"String"}]},{name:"ServiceTaskLike",extends:["bpmn:ServiceTask","bpmn:BusinessRuleTask","bpmn:SendTask","bpmn:MessageEventDefinition"],properties:[{name:"expression",isAttr:!0,type:"String"},{name:"class",isAttr:!0,type:"String"},{name:"delegateExpression",isAttr:!0,type:"String"},{name:"resultVariable",isAttr:!0,type:"String"}]},{name:"DmnCapable",extends:["bpmn:BusinessRuleTask"],properties:[{name:"decisionRef",isAttr:!0,type:"String"},{name:"decisionRefBinding",isAttr:!0,type:"String",default:"latest"},{name:"decisionRefVersion",isAttr:!0,type:"String"},{name:"mapDecisionResult",isAttr:!0,type:"String",default:"resultList"},{name:"decisionRefTenantId",isAttr:!0,type:"String"}]},{name:"ExternalCapable",extends:["flowable:ServiceTaskLike"],properties:[{name:"type",isAttr:!0,type:"String"},{name:"topic",isAttr:!0,type:"String"}]},{name:"TaskPriorized",extends:["bpmn:Process","flowable:ExternalCapable"],properties:[{name:"taskPriority",isAttr:!0,type:"String"}]},{name:"Properties",superClass:["Element"],meta:{allowedIn:["*"]},properties:[{name:"values",type:"Property",isMany:!0}]},{name:"Property",superClass:["Element"],properties:[{name:"id",type:"String",isAttr:!0},{name:"name",type:"String",isAttr:!0},{name:"value",type:"String",isAttr:!0}]},{name:"Button",superClass:["Element"],meta:{allowedIn:["bpmn:UserTask"]},properties:[{name:"id",type:"String",isAttr:!0},{name:"name",type:"String",isAttr:!0},{name:"code",type:"String",isAttr:!0},{name:"isHide",type:"String",isAttr:!0},{name:"next",type:"String",isAttr:!0},{name:"sort",type:"Integer",isAttr:!0}]},{name:"Assignee",superClass:["Element"],meta:{allowedIn:["bpmn:UserTask"]},properties:[{name:"id",type:"String",isAttr:!0},{name:"type",type:"String",isAttr:!0},{name:"value",type:"String",isAttr:!0},{name:"condition",type:"String",isAttr:!0},{name:"operationType",type:"String",isAttr:!0},{name:"sort",type:"Integer",isAttr:!0}]},{name:"Connector",superClass:["Element"],meta:{allowedIn:["flowable:ServiceTaskLike"]},properties:[{name:"inputOutput",type:"InputOutput"},{name:"connectorId",type:"String"}]},{name:"InputOutput",superClass:["Element"],meta:{allowedIn:["bpmn:FlowNode","flowable:Connector"]},properties:[{name:"inputOutput",type:"InputOutput"},{name:"connectorId",type:"String"},{name:"inputParameters",isMany:!0,type:"InputParameter"},{name:"outputParameters",isMany:!0,type:"OutputParameter"}]},{name:"InputOutputParameter",properties:[{name:"name",isAttr:!0,type:"String"},{name:"value",isBody:!0,type:"String"},{name:"definition",type:"InputOutputParameterDefinition"}]},{name:"InputOutputParameterDefinition",isAbstract:!0},{name:"List",superClass:["InputOutputParameterDefinition"],properties:[{name:"items",isMany:!0,type:"InputOutputParameterDefinition"}]},{name:"Map",superClass:["InputOutputParameterDefinition"],properties:[{name:"entries",isMany:!0,type:"Entry"}]},{name:"Entry",properties:[{name:"key",isAttr:!0,type:"String"},{name:"value",isBody:!0,type:"String"},{name:"definition",type:"InputOutputParameterDefinition"}]},{name:"Value",superClass:["InputOutputParameterDefinition"],properties:[{name:"id",isAttr:!0,type:"String"},{name:"name",isAttr:!0,type:"String"},{name:"value",isBody:!0,type:"String"}]},{name:"Script",superClass:["InputOutputParameterDefinition"],properties:[{name:"scriptFormat",isAttr:!0,type:"String"},{name:"resource",isAttr:!0,type:"String"},{name:"value",isBody:!0,type:"String"}]},{name:"Field",superClass:["Element"],meta:{allowedIn:["flowable:ServiceTaskLike","flowable:ExecutionListener","flowable:TaskListener"]},properties:[{name:"name",isAttr:!0,type:"String"},{name:"expression",type:"String"},{name:"stringValue",isAttr:!0,type:"String"},{name:"string",type:"String"}]},{name:"ChildField",superClass:["Element"],properties:[{name:"id",type:"String",isAttr:!0},{name:"name",type:"String",isAttr:!0},{name:"type",type:"String",isAttr:!0},{name:"required",type:"String",isAttr:!0},{name:"readable",type:"String",isAttr:!0},{name:"writable",type:"String",isAttr:!0},{name:"variable",type:"String",isAttr:!0},{name:"expression",type:"String",isAttr:!0},{name:"datePattern",type:"String",isAttr:!0},{name:"default",type:"String",isAttr:!0},{name:"values",type:"Value",isMany:!0}]},{name:"InputParameter",superClass:["InputOutputParameter"]},{name:"OutputParameter",superClass:["InputOutputParameter"]},{name:"Collectable",isAbstract:!0,extends:["bpmn:MultiInstanceLoopCharacteristics"],superClass:["flowable:AsyncCapable"],properties:[{name:"collection",isAttr:!0,type:"String"},{name:"elementVariable",isAttr:!0,type:"String"}]},{name:"FailedJobRetryTimeCycle",superClass:["Element"],meta:{allowedIn:["flowable:AsyncCapable","bpmn:MultiInstanceLoopCharacteristics"]},properties:[{name:"body",isBody:!0,type:"String"}]},{name:"ExecutionListener",superClass:["Element"],meta:{allowedIn:["bpmn:Task","bpmn:ServiceTask","bpmn:UserTask","bpmn:BusinessRuleTask","bpmn:ScriptTask","bpmn:ReceiveTask","bpmn:ManualTask","bpmn:ExclusiveGateway","bpmn:SequenceFlow","bpmn:ParallelGateway","bpmn:InclusiveGateway","bpmn:EventBasedGateway","bpmn:StartEvent","bpmn:IntermediateCatchEvent","bpmn:IntermediateThrowEvent","bpmn:EndEvent","bpmn:BoundaryEvent","bpmn:CallActivity","bpmn:SubProcess","bpmn:Process"]},properties:[{name:"expression",isAttr:!0,type:"String"},{name:"class",isAttr:!0,type:"String"},{name:"delegateExpression",isAttr:!0,type:"String"},{name:"event",isAttr:!0,type:"String"},{name:"script",type:"Script"},{name:"fields",type:"Field",isMany:!0}]},{name:"TaskListener",superClass:["Element"],meta:{allowedIn:["bpmn:UserTask"]},properties:[{name:"expression",isAttr:!0,type:"String"},{name:"class",isAttr:!0,type:"String"},{name:"delegateExpression",isAttr:!0,type:"String"},{name:"event",isAttr:!0,type:"String"},{name:"script",type:"Script"},{name:"fields",type:"Field",isMany:!0}]},{name:"FormProperty",superClass:["Element"],meta:{allowedIn:["bpmn:StartEvent","bpmn:UserTask"]},properties:[{name:"id",type:"String",isAttr:!0},{name:"name",type:"String",isAttr:!0},{name:"type",type:"String",isAttr:!0},{name:"required",type:"String",isAttr:!0},{name:"readable",type:"String",isAttr:!0},{name:"writable",type:"String",isAttr:!0},{name:"variable",type:"String",isAttr:!0},{name:"expression",type:"String",isAttr:!0},{name:"datePattern",type:"String",isAttr:!0},{name:"default",type:"String",isAttr:!0},{name:"values",type:"Value",isMany:!0},{name:"children",type:"ChildField",isMany:!0},{name:"extensionElements",type:"bpmn:ExtensionElements",isMany:!0}]},{name:"FormData",superClass:["Element"],meta:{allowedIn:["bpmn:StartEvent","bpmn:UserTask"]},properties:[{name:"fields",type:"FormField",isMany:!0},{name:"businessKey",type:"String",isAttr:!0}]},{name:"FormField",superClass:["Element"],properties:[{name:"id",type:"String",isAttr:!0},{name:"label",type:"String",isAttr:!0},{name:"type",type:"String",isAttr:!0},{name:"datePattern",type:"String",isAttr:!0},{name:"defaultValue",type:"String",isAttr:!0},{name:"properties",type:"Properties"},{name:"validation",type:"Validation"},{name:"values",type:"Value",isMany:!0}]},{name:"Validation",superClass:["Element"],properties:[{name:"constraints",type:"Constraint",isMany:!0}]},{name:"Constraint",superClass:["Element"],properties:[{name:"name",type:"String",isAttr:!0},{name:"config",type:"String",isAttr:!0}]},{name:"ConditionalEventDefinition",isAbstract:!0,extends:["bpmn:ConditionalEventDefinition"],properties:[{name:"variableName",isAttr:!0,type:"String"},{name:"variableEvent",isAttr:!0,type:"String"}]},{name:"Condition",superClass:["Element"],meta:{allowedIn:["bpmn:SequenceFlow"]},properties:[{name:"id",type:"String",isAttr:!0},{name:"field",type:"String",isAttr:!0},{name:"compare",type:"String",isAttr:!0},{name:"value",type:"String",isAttr:!0},{name:"logic",type:"String",isAttr:!0},{name:"sort",type:"Integer",isAttr:!0}]}],Ur=[],Mr={name:Ar,uri:Tr,prefix:Vr,xml:Ir,associations:jr,types:Dr,emumerations:Ur},Nr="*";function et(e){const a=this;e.on("moddleCopy.canCopyProperty",function(t){const r=t.property,s=t.parent;return a.canCopyProperty(r,s)})}et.$inject=["eventBus"],et.prototype.canCopyProperty=function(e,a){if(Ci(e)&&!Br(e,a)||tt(e,"camunda:InputOutput")&&!this.canHostInputOutput(a)||Ma(e,["camunda:Connector","camunda:Field"])&&!this.canHostConnector(a)||tt(e,"camunda:In")&&!this.canHostIn(a))return!1},et.prototype.canHostInputOutput=function(e){if(Ve(e,"camunda:Connector"))return!0;const a=Ve(e,"bpmn:FlowNode");return!a||Ma(a,["bpmn:StartEvent","bpmn:Gateway","bpmn:BoundaryEvent"])?!1:!(tt(a,"bpmn:SubProcess")&&a.get("triggeredByEvent"))},et.prototype.canHostConnector=function(e){const a=Ve(e,"camunda:ServiceTaskLike");return tt(a,"bpmn:MessageEventDefinition")?Ve(e,"bpmn:IntermediateThrowEvent")||Ve(e,"bpmn:EndEvent"):!0},et.prototype.canHostIn=function(e){return Ve(e,"bpmn:CallActivity")?!0:Ve(e,"bpmn:SignalEventDefinition")?Ve(e,"bpmn:IntermediateThrowEvent")||Ve(e,"bpmn:EndEvent"):!0};function tt(e,a){return e&&_i(e.$instanceOf)&&e.$instanceOf(a)}function Ma(e,a){return Ze(a,function(t){return tt(e,t)})}function Ve(e,a){if(!a)return e.$parent;if(tt(e,a))return e;if(e.$parent)return Ve(e.$parent,a)}function Br(e,a){const t=e.$type&&e.$model.getTypeDescriptor(e.$type),r=t&&t.meta&&t.meta.allowedIn;return!r||Fr(r)?!0:Ze(r,function(s){return Ve(a,s)})}function Fr(e){return e.indexOf(Nr)!==-1}const Lr={__init__:["camundaModdleExtension"],camundaModdleExtension:["type",et]},$r={FailedJobRetryTimeCycle:["bpmn:StartEvent","bpmn:BoundaryEvent","bpmn:IntermediateCatchEvent","bpmn:Activity"],Connector:["bpmn:EndEvent","bpmn:IntermediateThrowEvent"],Field:["bpmn:EndEvent","bpmn:IntermediateThrowEvent"]};function $t(e,a){return e&&typeof e.$instanceOf=="function"&&e.$instanceOf(a)}function Rr(e){return e&&e.length}function Rt(e,a){return Rr(e)&&Ze(e,function(t){return $t(t,a)})}function zr(e,a){return Ze(a,function(t){return $t(e,t)})}function zt(e,a,t){const r=a.name,s=$r[r.replace(/activiti:/,"")];return r===e&&zr(t,s)}function qt(e){e.on("property.clone",function(a){const t=a.newElement,r=a.propertyDescriptor;this.canCloneProperty(t,r)},this)}qt.$inject=["eventBus"],qt.prototype.canCloneProperty=function(e,a){if(zt("activiti:FailedJobRetryTimeCycle",a,e))return Rt(e.eventDefinitions,"bpmn:TimerEventDefinition")||Rt(e.eventDefinitions,"bpmn:SignalEventDefinition")||$t(e.loopCharacteristics,"bpmn:MultiInstanceLoopCharacteristics");if(zt("activiti:Connector",a,e)||zt("activiti:Field",a,e))return Rt(e.eventDefinitions,"bpmn:MessageEventDefinition")};const qr={__init__:["ActivitiModdleExtension"],ActivitiModdleExtension:["type",qt]},Jr={FailedJobRetryTimeCycle:["bpmn:StartEvent","bpmn:BoundaryEvent","bpmn:IntermediateCatchEvent","bpmn:Activity"],Connector:["bpmn:EndEvent","bpmn:IntermediateThrowEvent"],Field:["bpmn:EndEvent","bpmn:IntermediateThrowEvent"]};function Jt(e,a){return e&&typeof e.$instanceOf=="function"&&e.$instanceOf(a)}function Gr(e){return e&&e.length}function Gt(e,a){return Gr(e)&&Ze(e,function(t){return Jt(t,a)})}function Kr(e,a){return Ze(a,function(t){return Jt(e,t)})}function Kt(e,a,t){const r=a.name,s=Jr[r.replace(/flowable:/,"")];return r===e&&Kr(t,s)}function Yt(e){e.on("property.clone",function(a){const t=a.newElement,r=a.propertyDescriptor;this.canCloneProperty(t,r)},this)}Yt.$inject=["eventBus"],Yt.prototype.canCloneProperty=function(e,a){if(Kt("flowable:FailedJobRetryTimeCycle",a,e))return Gt(e.eventDefinitions,"bpmn:TimerEventDefinition")||Gt(e.eventDefinitions,"bpmn:SignalEventDefinition")||Jt(e.loopCharacteristics,"bpmn:MultiInstanceLoopCharacteristics");if(Kt("flowable:Connector",a,e)||Kt("flowable:Field",a,e))return Gt(e.eventDefinitions,"bpmn:MessageEventDefinition")};const Yr={__init__:["FlowableModdleExtension"],FlowableModdleExtension:["type",Yt]};var Hr=Object.defineProperty,Zr=Object.defineProperties,Qr=Object.getOwnPropertyDescriptors,Na=Object.getOwnPropertySymbols,Wr=Object.prototype.hasOwnProperty,Xr=Object.prototype.propertyIsEnumerable,Ba=(e,a,t)=>a in e?Hr(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,eo=(e,a)=>{for(var t in a||(a={}))Wr.call(a,t)&&Ba(e,t,a[t]);if(Na)for(var t of Na(a))Xr.call(a,t)&&Ba(e,t,a[t]);return e},to=(e,a)=>Zr(e,Qr(a)),xt=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const ao={class:"my-process-designer"},no={class:"my-process-designer__header",style:{"z-index":"10",display:"table-row-group"}},lo={style:{color:"#409eff"}},io=F("br",null,null,-1),ro=F("br",null,null,-1),oo=F("br",null,null,-1),so={class:"my-process-designer__container"},po={class:"hljs"},kt=te(to(eo({},{name:"MyProcessDesigner"}),{__name:"ProcessDesigner",props:{value:String,processId:String,processName:String,formId:Number,translations:{type:Object,default:()=>{}},additionalModel:[Object,Array],moddleExtension:{type:Object,default:()=>{}},onlyCustomizeAddi:{type:Boolean,default:!1},onlyCustomizeModdle:{type:Boolean,default:!1},simulation:{type:Boolean,default:!0},keyboard:{type:Boolean,default:!0},prefix:{type:String,default:"camunda"},events:{type:Array,default:()=>["element.click"]},headerButtonSize:{type:String,default:"small",validator:e=>["default","medium","small","mini"].indexOf(e)!==-1},headerButtonType:{type:String,default:"primary",validator:e=>["default","primary","success","warning","danger","info"].indexOf(e)!==-1}},emits:["destroy","init-finished","save","commandStack-changed","input","change","canvas-viewbox-changed","element-click"],setup(e,{emit:a}){const t=m(),r=m(),s=a,p=e;yt("configGlobal",p);let c=null;const u=m(1),l=m(!1),d=m(!1),g=m(""),b=m("xml"),f=m(!1),w=m(!1),S=De(()=>{const V=[];if(p.onlyCustomizeAddi)return Object.prototype.toString.call(p.additionalModel)=="[object Array]"?p.additionalModel||[]:[p.additionalModel];Object.prototype.toString.call(p.additionalModel)=="[object Array]"?V.push(...p.additionalModel):p.additionalModel&&V.push(p.additionalModel);const A={translate:["value",dr(p.translations||cr)]};return V.push(A),p.simulation&&V.push(Pi),p.prefix==="camunda"&&V.push(Lr),p.prefix==="flowable"&&V.push(Yr),p.prefix==="activiti"&&V.push(qr),V}),v=De(()=>{const V={};if(p.onlyCustomizeModdle)return p.moddleExtension||null;if(p.moddleExtension)for(let A in p.moddleExtension)V[A]=p.moddleExtension[A];return p.prefix==="activiti"&&(V.activiti=Or),p.prefix==="flowable"&&(V.flowable=Mr),p.prefix==="camunda"&&(V.camunda=wr),V}),y=()=>{if(c)return;let V=document.getElementById("bpmnCanvas");c=new Ei({container:V,keyboard:p.keyboard?{bindTo:document}:null,additionalModules:S.value,moddleExtensions:v.value}),s("init-finished",c),x()},x=()=>{const V=c.get("eventBus");p.events.forEach(A=>{V.on(A,function(T){A.replace(/\./g,"-");let M=T?T.element:null;s("element-click",M,T)})}),V.on("commandStack.changed",A=>xt(this,null,function*(){try{f.value=c.get("commandStack").canRedo(),w.value=c.get("commandStack").canUndo();let{xml:T}=yield c.saveXML({format:!0});s("commandStack-changed",A),s("input",T),s("change",T)}catch{}})),c.on("canvas.viewbox.changed",({viewbox:A})=>{s("canvas-viewbox-changed",{viewbox:A});const{scale:T}=A;u.value=Math.floor(T*100)/100})},k=V=>xt(this,null,function*(){let A=p.processId||`Process_${new Date().getTime()}`,T=p.processName||`\u4E1A\u52A1\u6D41\u7A0B_${new Date().getTime()}`,M=V||Ua(A,T,p.prefix);try{let{warnings:z}=yield c.importXML(M);z&&z.length&&z.forEach(X=>{})}catch{}}),C=V=>xt(this,null,function*(){try{if(V==="xml"||V==="bpmn"){const{err:T,xml:M}=yield c.saveXML();let{href:z,filename:X}=E(V.toUpperCase(),M);A(z,X)}else{const{err:T,svg:M}=yield c.saveSVG();if(T)return;let{href:z,filename:X}=E("SVG",M);A(z,X)}}catch{}function A(T,M){if(T&&M){let z=document.createElement("a");z.download=M,z.href=T,z.click(),URL.revokeObjectURL(z.href)}}}),E=(V,A)=>{const T="diagram",M=encodeURIComponent(A);return{filename:`${T}.${V}`,href:`data:application/${V==="svg"?"text/xml":"bpmn20-xml"};charset=UTF-8,${M}`,data:A}},j=()=>{const V=r.value.files[0],A=new FileReader;A.readAsText(V),A.onload=function(){let T=this.result;k(T)}},P=()=>{C("xml")},D=()=>{C("bpmn")},G=()=>{C("svg")},O=()=>{d.value=!d.value,p.simulation&&c.get("toggleMode","strict").toggleMode()},U=()=>{c.get("commandStack").redo()},Y=()=>{c.get("commandStack").undo()},le=(V=.1)=>{let A=Math.floor(u.value*100+V*100)/100;if(A>4)throw new Error("[Process Designer Warn ]: The zoom ratio cannot be greater than 4");u.value=A,c.get("canvas").zoom(u.value)},H=(V=.1)=>{let A=Math.floor(u.value*100-V*100)/100;if(A<.2)throw new Error("[Process Designer Warn ]: The zoom ratio cannot be less than 0.2");u.value=A,c.get("canvas").zoom(u.value)},K=()=>{u.value=1,c.get("canvas").zoom("fit-viewport","auto")},B=()=>{f.value=!1,w.value=!1,k(null)},_=V=>{const A=c.get("alignElements"),T=c.get("selection").get();if(!T||T.length<=1){ri.warning("\u8BF7\u6309\u4F4F Shift \u952E\u9009\u62E9\u591A\u4E2A\u5143\u7D20\u5BF9\u9F50");return}ze.confirm("\u81EA\u52A8\u5BF9\u9F50\u53EF\u80FD\u9020\u6210\u56FE\u5F62\u53D8\u5F62\uFF0C\u662F\u5426\u7EE7\u7EED\uFF1F","\u8B66\u544A",{confirmButtonText:"\u786E\u5B9A",cancelButtonText:"\u53D6\u6D88",type:"warning"}).then(()=>{A.trigger(T,V)})},$=()=>{c.saveXML({format:!0}).then(({xml:V})=>{g.value=V,b.value="xml",l.value=!0})},Z=()=>{c.saveXML({format:!0}).then(({xml:V})=>{var A;const T=new Oi(Ai.Root,Ti(V));g.value=(A=T.parent)==null?void 0:A.toJSON(),b.value="json",l.value=!0})},Q=()=>xt(this,null,function*(){const{err:V,xml:A}=yield c.saveXML();if(V){alert("\u4FDD\u5B58\u6A21\u578B\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5\uFF01");return}s("save",A)});return ei(()=>{}),we(()=>{y(),k(p.value)}),Ae(()=>{c&&c.destroy(),s("destroy",c),c=null}),(V,A)=>{const T=Qe,M=xi,z=oi,X=si,ge=pe,be=Ce,Ee=va("highlight");return h(),R("div",ao,[F("div",no,[ti(V.$slots,"control-header"),V.$slots["control-header"]?J("",!0):(h(),R(W,{key:0},[i(X,{key:"file-control"},{default:o(()=>[i(T,{preIcon:"ep:folder-opened",title:"\u6253\u5F00\u6587\u4EF6",onClick:A[0]||(A[0]=L=>n(r).click())}),i(z,{effect:"light",placement:"bottom"},{content:o(()=>[F("div",lo,[i(M,{title:"\u4E0B\u8F7D\u4E3AXML\u6587\u4EF6",onClick:A[1]||(A[1]=L=>P())}),io,i(M,{title:"\u4E0B\u8F7D\u4E3ASVG\u6587\u4EF6",onClick:A[2]||(A[2]=L=>G())}),ro,i(M,{title:"\u4E0B\u8F7D\u4E3ABPMN\u6587\u4EF6",onClick:A[3]||(A[3]=L=>D())})])]),default:o(()=>[i(T,{title:"\u4E0B\u8F7D\u6587\u4EF6",preIcon:"ep:download"})]),_:1}),i(z,{effect:"light"},{content:o(()=>[i(M,{title:"\u9884\u89C8XML",onClick:$}),oo,i(M,{title:"\u9884\u89C8JSON",onClick:Z})]),default:o(()=>[i(T,{preIcon:"ep:view",title:"\u6D4F\u89C8"})]),_:1}),p.simulation?(h(),I(z,{key:0,effect:"light",content:n(d)?"\u9000\u51FA\u6A21\u62DF":"\u5F00\u542F\u6A21\u62DF"},{default:o(()=>[i(T,{preIcon:"ep:cpu",title:"\u6A21\u62DF",onClick:O})]),_:1},8,["content"])):J("",!0)]),_:1}),i(X,{key:"align-control"},{default:o(()=>[i(z,{effect:"light",content:"\u5411\u5DE6\u5BF9\u9F50"},{default:o(()=>[i(T,{preIcon:"fa:align-left",class:"align align-bottom",onClick:A[4]||(A[4]=L=>_("left"))})]),_:1}),i(z,{effect:"light",content:"\u5411\u53F3\u5BF9\u9F50"},{default:o(()=>[i(T,{preIcon:"fa:align-left",class:"align align-top",onClick:A[5]||(A[5]=L=>_("right"))})]),_:1}),i(z,{effect:"light",content:"\u5411\u4E0A\u5BF9\u9F50"},{default:o(()=>[i(T,{preIcon:"fa:align-left",class:"align align-left",onClick:A[6]||(A[6]=L=>_("top"))})]),_:1}),i(z,{effect:"light",content:"\u5411\u4E0B\u5BF9\u9F50"},{default:o(()=>[i(T,{preIcon:"fa:align-left",class:"align align-right",onClick:A[7]||(A[7]=L=>_("bottom"))})]),_:1}),i(z,{effect:"light",content:"\u6C34\u5E73\u5C45\u4E2D"},{default:o(()=>[i(T,{preIcon:"fa:align-left",class:"align align-center",onClick:A[8]||(A[8]=L=>_("center"))})]),_:1}),i(z,{effect:"light",content:"\u5782\u76F4\u5C45\u4E2D"},{default:o(()=>[i(T,{preIcon:"fa:align-left",class:"align align-middle",onClick:A[9]||(A[9]=L=>_("middle"))})]),_:1})]),_:1}),i(X,{key:"scale-control"},{default:o(()=>[i(z,{effect:"light",content:"\u7F29\u5C0F\u89C6\u56FE"},{default:o(()=>[i(T,{preIcon:"ep:zoom-out",onClick:A[10]||(A[10]=L=>H()),disabled:n(u)<.2},null,8,["disabled"])]),_:1}),i(ge,null,{default:o(()=>[N(ee(Math.floor(n(u)*10*10)+"%"),1)]),_:1}),i(z,{effect:"light",content:"\u653E\u5927\u89C6\u56FE"},{default:o(()=>[i(T,{preIcon:"ep:zoom-in",onClick:A[11]||(A[11]=L=>le()),disabled:n(u)>4},null,8,["disabled"])]),_:1}),i(z,{effect:"light",content:"\u91CD\u7F6E\u89C6\u56FE\u5E76\u5C45\u4E2D"},{default:o(()=>[i(T,{preIcon:"ep:scale-to-original",onClick:A[12]||(A[12]=L=>K())})]),_:1})]),_:1}),i(X,{key:"stack-control"},{default:o(()=>[i(z,{effect:"light",content:"\u64A4\u9500"},{default:o(()=>[i(T,{preIcon:"ep:refresh-left",onClick:A[13]||(A[13]=L=>Y()),disabled:!n(w)},null,8,["disabled"])]),_:1}),i(z,{effect:"light",content:"\u6062\u590D"},{default:o(()=>[i(T,{preIcon:"ep:refresh-right",onClick:A[14]||(A[14]=L=>U()),disabled:!n(f)},null,8,["disabled"])]),_:1}),i(z,{effect:"light",content:"\u91CD\u65B0\u7ED8\u5236"},{default:o(()=>[i(T,{preIcon:"ep:refresh",onClick:A[15]||(A[15]=L=>B())})]),_:1})]),_:1}),i(T,{preIcon:"ep:plus",title:"\u4FDD\u5B58\u6A21\u578B",onClick:Q,type:p.headerButtonType,disabled:n(d)},null,8,["type","disabled"])],64)),F("input",{type:"file",id:"files",ref_key:"refFile",ref:r,style:{display:"none"},accept:".xml, .bpmn",onChange:j},null,544)]),F("div",so,[F("div",{class:"my-process-designer__canvas",ref_key:"bpmnCanvas",ref:t,id:"bpmnCanvas",style:{width:"1680px",height:"800px"}},null,512)]),i(be,{title:"\u9884\u89C8",modelValue:n(l),"onUpdate:modelValue":A[16]||(A[16]=L=>ne(l)?l.value=L:null),width:"80%",scroll:!0,"max-height":"600px"},{default:o(()=>[me((h(),R("div",null,[F("code",po,ee(n(g)),1)])),[[Ee]])]),_:1},8,["modelValue"])])}}}));kt.install=function(e){e.component(kt.name,kt)};var uo=Object.defineProperty,co=Object.defineProperties,mo=Object.getOwnPropertyDescriptors,Fa=Object.getOwnPropertySymbols,yo=Object.prototype.hasOwnProperty,vo=Object.prototype.propertyIsEnumerable,La=(e,a,t)=>a in e?uo(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,fo=(e,a)=>{for(var t in a||(a={}))yo.call(a,t)&&La(e,t,a[t]);if(Fa)for(var t of Fa(a))vo.call(a,t)&&La(e,t,a[t]);return e},bo=(e,a)=>co(e,mo(a)),go=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const ho={class:"panel-tab__content"},wo=te(bo(fo({},{name:"ElementForm"}),{__name:"ElementForm",props:{id:String,type:String},setup(e){const a=e,t=Ne("prefix");Ne("width");const r=m(""),s=m("");m("");const p=m([]);m({}),m({long:"\u957F\u6574\u578B",string:"\u5B57\u7B26\u4E32",boolean:"\u5E03\u5C14\u7C7B",date:"\u65E5\u671F\u7C7B",enum:"\u679A\u4E3E\u7C7B",custom:"\u81EA\u5B9A\u4E49\u7C7B\u578B"}),m(-1),m(-1),m(!1),m(!1),m({}),m(""),m([]),m([]),m([]);const c=m(),u=m(),l=m(),d=m(),g=()=>window==null?void 0:window.bpmnInstances,b=()=>{var v,y;c.value=g().bpmnElement,r.value=c.value.businessObject.formKey,((v=r.value)==null?void 0:v.length)>0&&(r.value=parseInt(r.value)),u.value=c.value.businessObject.get("extensionElements")||g().moddle.create("bpmn:ExtensionElements",{values:[]}),l.value=((y=u.value.values.filter(x=>x.$type===`${t}:FormData`))==null?void 0:y[0])||g().moddle.create(`${t}:FormData`,{fields:[]}),s.value=l.value.businessKey,d.value=u.value.values.filter(x=>x.$type!==`${t}:FormData`),p.value=JSON.parse(JSON.stringify(l.value.fields||[])),w()},f=()=>{g().modeling.updateProperties(ae(c.value),{formKey:r.value})},w=()=>{const v=g().moddle.create("bpmn:ExtensionElements",{values:d.value.concat(l.value)});g().modeling.updateProperties(ae(c.value),{extensionElements:v})},S=m([]);return we(()=>go(this,null,function*(){S.value=yield Wi()})),re(()=>a.id,v=>{v&&v.length&&ce(()=>{b()})},{immediate:!0}),(v,y)=>{const x=Se,k=xe,C=ue,E=ye;return h(),R("div",ho,[i(E,{"label-width":"80px"},{default:o(()=>[i(C,{label:"\u6D41\u7A0B\u8868\u5355"},{default:o(()=>[i(k,{modelValue:n(r),"onUpdate:modelValue":y[0]||(y[0]=j=>ne(r)?r.value=j:null),clearable:"",onChange:f},{default:o(()=>[(h(!0),R(W,null,se(n(S),j=>(h(),I(x,{key:j.id,label:j.name,value:j.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})])}}}));var So=Object.defineProperty,xo=Object.defineProperties,ko=Object.getOwnPropertyDescriptors,$a=Object.getOwnPropertySymbols,Co=Object.prototype.hasOwnProperty,_o=Object.prototype.propertyIsEnumerable,Ra=(e,a,t)=>a in e?So(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,Ht=(e,a)=>{for(var t in a||(a={}))Co.call(a,t)&&Ra(e,t,a[t]);if($a)for(var t of $a(a))_o.call(a,t)&&Ra(e,t,a[t]);return e},Po=(e,a)=>xo(e,ko(a));const Eo={class:"panel-tab__content"},Oo={key:0},Ao={key:1},To=te(Po(Ht({},{name:"ElementBaseInfo"}),{__name:"ElementBaseInfo",props:{businessObject:{type:Object,default:()=>{}},model:{type:Object,default:()=>{}}},setup(e){const a=e,t=m({}),r=m(),s=m({}),p=Re({id:[{required:!0,message:"\u6D41\u7A0B\u6807\u8BC6\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],name:[{required:!0,message:"\u6D41\u7A0B\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),c=()=>window==null?void 0:window.bpmnInstances,u=()=>{var b;r.value=(b=c())==null?void 0:b.bpmnElement,s.value=r.value.businessObject,t.value.type=r.value.businessObject.$type},l=b=>{b&&b.match(/[a-zA-Z_][\-_.0-9a-zA-Z$]*/)&&(s.value.id=b,setTimeout(()=>{g("id")},100))},d=b=>{b&&(s.value.name=b,setTimeout(()=>{g("name")},100))},g=b=>{const f=Object.create(null);f[b]=s.value[b],t.value=Ht(Ht({},s.value),t.value),b==="id"?c().modeling.updateProperties(ae(r.value),{id:s.value[b],di:{id:`${s.value[b]}_di`}}):c().modeling.updateProperties(ae(r.value),f)};return re(()=>a.businessObject,b=>{b&&u()}),re(()=>{var b;return(b=a.model)==null?void 0:b.key},b=>{b&&(l(a.model.key),d(a.model.name))}),Ae(()=>{r.value=null}),(b,f)=>{const w=de,S=ue,v=ye;return h(),R("div",Eo,[i(v,{"label-width":"90px",model:n(t),rules:n(p)},{default:o(()=>[n(t).type=="bpmn:Process"?(h(),R("div",Oo,[i(S,{label:"\u6D41\u7A0B\u6807\u8BC6",prop:"id"},{default:o(()=>[i(w,{modelValue:n(t).id,"onUpdate:modelValue":f[0]||(f[0]=y=>n(t).id=y),placeholder:"\u8BF7\u8F93\u5165\u6D41\u6807\u6807\u8BC6",disabled:n(t).id!==void 0&&n(t).id.length>0,onChange:l},null,8,["modelValue","disabled"])]),_:1}),i(S,{label:"\u6D41\u7A0B\u540D\u79F0",prop:"name"},{default:o(()=>[i(w,{modelValue:n(t).name,"onUpdate:modelValue":f[1]||(f[1]=y=>n(t).name=y),placeholder:"\u8BF7\u8F93\u5165\u6D41\u7A0B\u540D\u79F0",clearable:"",onChange:d},null,8,["modelValue"])]),_:1})])):(h(),R("div",Ao,[i(S,{label:"ID"},{default:o(()=>[i(w,{modelValue:n(s).id,"onUpdate:modelValue":f[2]||(f[2]=y=>n(s).id=y),clearable:"",onChange:f[3]||(f[3]=y=>g("id"))},null,8,["modelValue"])]),_:1}),i(S,{label:"\u540D\u79F0"},{default:o(()=>[i(w,{modelValue:n(s).name,"onUpdate:modelValue":f[4]||(f[4]=y=>n(s).name=y),clearable:"",onChange:f[5]||(f[5]=y=>g("name"))},null,8,["modelValue"])]),_:1})]))]),_:1},8,["model","rules"])])}}}));var Vo=Object.defineProperty,Io=Object.defineProperties,jo=Object.getOwnPropertyDescriptors,za=Object.getOwnPropertySymbols,Do=Object.prototype.hasOwnProperty,Uo=Object.prototype.propertyIsEnumerable,qa=(e,a,t)=>a in e?Vo(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,Mo=(e,a)=>{for(var t in a||(a={}))Do.call(a,t)&&qa(e,t,a[t]);if(za)for(var t of za(a))Uo.call(a,t)&&qa(e,t,a[t]);return e},No=(e,a)=>Io(e,jo(a));const Bo={class:"panel-tab__content"},Fo={class:"element-property input-property"},Lo=F("div",{class:"element-property__label"},"\u5143\u7D20\u6587\u6863\uFF1A",-1),$o={class:"element-property__value"},Ro=te(No(Mo({},{name:"ElementOtherConfig"}),{__name:"ElementOtherConfig",props:{id:String},setup(e){const a=e,t=m(""),r=m(),s=()=>window.bpmnInstances,p=()=>{r.value&&r.value.id===a.id||(r.value=s().elementRegistry.get(a.id));const c=s().bpmnFactory.create("bpmn:Documentation",{text:t.value});s().modeling.updateProperties(ae(r.value),{documentation:[c]})};return Ae(()=>{r.value=null}),re(()=>a.id,c=>{c&&c.length?ce(()=>{var u;const l=(u=s().bpmnElement.businessObject)==null?void 0:u.documentation;t.value=l&&l.length?l[0].text:""}):t.value=""},{immediate:!0}),(c,u)=>{const l=de;return h(),R("div",Bo,[F("div",Fo,[Lo,F("div",$o,[i(l,{type:"textarea",modelValue:n(t),"onUpdate:modelValue":u[0]||(u[0]=d=>ne(t)?t.value=d:null),resize:"vertical",autosize:{minRows:2,maxRows:4},onInput:p,onBlur:p},null,8,["modelValue"])])])])}}}));var zo=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const qo=()=>zo(void 0,null,function*(){return yield ie.get({url:"/system/role/simple-list"})});var Jo=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const Go=()=>Jo(void 0,null,function*(){return yield ie.get({url:"/system/post/simple-list"})});var Ko=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const Yo=()=>Ko(void 0,null,function*(){return yield ie.get({url:"/bpm/user-group/simple-list"})});var at=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const Ho={getProcessExpressionPage:e=>at(void 0,null,function*(){return yield ie.get({url:"/bpm/process-expression/page",params:e})}),getProcessExpression:e=>at(void 0,null,function*(){return yield ie.get({url:"/bpm/process-expression/get?id="+e})}),createProcessExpression:e=>at(void 0,null,function*(){return yield ie.post({url:"/bpm/process-expression/create",data:e})}),updateProcessExpression:e=>at(void 0,null,function*(){return yield ie.put({url:"/bpm/process-expression/update",data:e})}),deleteProcessExpression:e=>at(void 0,null,function*(){return yield ie.delete({url:"/bpm/process-expression/delete?id="+e})}),exportProcessExpression:e=>at(void 0,null,function*(){return yield ie.download({url:"/bpm/process-expression/export-excel",params:e})})};var Zo=Object.defineProperty,Qo=Object.defineProperties,Wo=Object.getOwnPropertyDescriptors,Ja=Object.getOwnPropertySymbols,Xo=Object.prototype.hasOwnProperty,es=Object.prototype.propertyIsEnumerable,Ga=(e,a,t)=>a in e?Zo(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,ts=(e,a)=>{for(var t in a||(a={}))Xo.call(a,t)&&Ga(e,t,a[t]);if(Ja)for(var t of Ja(a))es.call(a,t)&&Ga(e,t,a[t]);return e},as=(e,a)=>Qo(e,Wo(a)),Ka=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const ns=te(as(ts({},{name:"ProcessExpressionDialog"}),{__name:"ProcessExpressionDialog",emits:["success"],setup(e,{expose:a,emit:t}){const r=m(!1),s=m(!0),p=m([]),c=m(0),u=Re({pageNo:1,pageSize:10,type:"",status:Nt.ENABLE});a({open:b=>{u.pageNo=1,u.type=b,l(),r.value=!0}});const l=()=>Ka(this,null,function*(){s.value=!0;try{const b=yield Ho.getProcessExpressionPage(u);p.value=b.list,c.value=b.total}finally{s.value=!1}}),d=t,g=b=>Ka(this,null,function*(){r.value=!1,d("select",b)});return(b,f)=>{const w=Be,S=pe,v=Fe,y=At,x=Le,k=Ce,C=Pe;return h(),I(k,{title:"\u8BF7\u9009\u62E9\u8868\u8FBE\u5F0F",modelValue:n(r),"onUpdate:modelValue":f[2]||(f[2]=E=>ne(r)?r.value=E:null),width:"1024px"},{default:o(()=>[i(x,null,{default:o(()=>[me((h(),I(v,{data:n(p),stripe:!0,"show-overflow-tooltip":!0},{default:o(()=>[i(w,{label:"\u540D\u5B57",align:"center",prop:"name"}),i(w,{label:"\u8868\u8FBE\u5F0F",align:"center",prop:"expression"}),i(w,{label:"\u64CD\u4F5C",align:"center"},{default:o(E=>[i(S,{link:"",type:"primary",onClick:j=>g(E.row)},{default:o(()=>[N(" \u9009\u62E9 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[C,n(s)]]),i(y,{total:n(c),page:n(u).pageNo,"onUpdate:page":f[0]||(f[0]=E=>n(u).pageNo=E),limit:n(u).pageSize,"onUpdate:limit":f[1]||(f[1]=E=>n(u).pageSize=E),onPagination:l},null,8,["total","page","limit"])]),_:1})]),_:1},8,["modelValue"])}}}));var ls=Object.defineProperty,is=Object.defineProperties,rs=Object.getOwnPropertyDescriptors,Ya=Object.getOwnPropertySymbols,os=Object.prototype.hasOwnProperty,ss=Object.prototype.propertyIsEnumerable,Ha=(e,a,t)=>a in e?ls(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,ps=(e,a)=>{for(var t in a||(a={}))os.call(a,t)&&Ha(e,t,a[t]);if(Ya)for(var t of Ya(a))ss.call(a,t)&&Ha(e,t,a[t]);return e},us=(e,a)=>is(e,rs(a)),Za=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const ds=te(us(ps({},{name:"UserTask"}),{__name:"UserTask",props:{id:String,type:String},setup(e){const a=e,t=m({candidateStrategy:void 0,candidateParam:[]}),r=m(),s=()=>window==null?void 0:window.bpmnInstances,p=m([]),c=m(),u=m([]),l=m([]),d=m([]),g=()=>{const y=r.value.businessObject;y&&(y.candidateStrategy!=null?t.value.candidateStrategy=parseInt(y.candidateStrategy):t.value.candidateStrategy=void 0,y.candidateParam&&y.candidateParam.length>0?t.value.candidateStrategy===60?t.value.candidateParam=[y.candidateParam]:t.value.candidateParam=y.candidateParam.split(",").map(x=>+x):t.value.candidateParam=[])},b=()=>{t.value.candidateParam=[],f()},f=()=>{s().modeling.updateProperties(ae(r.value),{candidateStrategy:t.value.candidateStrategy,candidateParam:t.value.candidateParam.join(",")})},w=m(),S=()=>Za(this,null,function*(){w.value.open()}),v=y=>{t.value.candidateParam=[y.expression],f()};return re(()=>a.id,()=>{r.value=s().bpmnElement,ce(()=>{g()})},{immediate:!0}),we(()=>Za(this,null,function*(){p.value=yield qo();const y=yield Bl();c.value=Fl(y,"id"),u.value=yield Go(),l.value=yield He(),d.value=yield Yo()})),Ae(()=>{r.value=null}),(y,x)=>{const k=Se,C=xe,E=ue,j=pi,P=de,D=pe,G=ye;return h(),I(G,{"label-width":"100px"},{default:o(()=>[i(E,{label:"\u89C4\u5219\u7C7B\u578B",prop:"candidateStrategy"},{default:o(()=>[i(C,{modelValue:n(t).candidateStrategy,"onUpdate:modelValue":x[0]||(x[0]=O=>n(t).candidateStrategy=O),clearable:"",style:{width:"100%"},onChange:b},{default:o(()=>[(h(!0),R(W,null,se(n(rt)(n(Oe).BPM_TASK_CANDIDATE_STRATEGY),O=>(h(),I(k,{key:O.value,label:O.label,value:O.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(t).candidateStrategy==10?(h(),I(E,{key:0,label:"\u6307\u5B9A\u89D2\u8272",prop:"candidateParam"},{default:o(()=>[i(C,{modelValue:n(t).candidateParam,"onUpdate:modelValue":x[1]||(x[1]=O=>n(t).candidateParam=O),clearable:"",multiple:"",style:{width:"100%"},onChange:f},{default:o(()=>[(h(!0),R(W,null,se(n(p),O=>(h(),I(k,{key:O.id,label:O.name,value:O.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):J("",!0),n(t).candidateStrategy==20||n(t).candidateStrategy==21?(h(),I(E,{key:1,label:"\u6307\u5B9A\u90E8\u95E8",prop:"candidateParam",span:"24"},{default:o(()=>[i(j,{ref:"treeRef",modelValue:n(t).candidateParam,"onUpdate:modelValue":x[2]||(x[2]=O=>n(t).candidateParam=O),data:n(c),props:n(Nl),"empty-text":"\u52A0\u8F7D\u4E2D\uFF0C\u8BF7\u7A0D\u540E",multiple:"","node-key":"id","show-checkbox":"",onChange:f},null,8,["modelValue","data","props"])]),_:1})):J("",!0),n(t).candidateStrategy==22?(h(),I(E,{key:2,label:"\u6307\u5B9A\u5C97\u4F4D",prop:"candidateParam",span:"24"},{default:o(()=>[i(C,{modelValue:n(t).candidateParam,"onUpdate:modelValue":x[3]||(x[3]=O=>n(t).candidateParam=O),clearable:"",multiple:"",style:{width:"100%"},onChange:f},{default:o(()=>[(h(!0),R(W,null,se(n(u),O=>(h(),I(k,{key:O.id,label:O.name,value:O.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):J("",!0),n(t).candidateStrategy==30?(h(),I(E,{key:3,label:"\u6307\u5B9A\u7528\u6237",prop:"candidateParam",span:"24"},{default:o(()=>[i(C,{modelValue:n(t).candidateParam,"onUpdate:modelValue":x[4]||(x[4]=O=>n(t).candidateParam=O),clearable:"",multiple:"",style:{width:"100%"},onChange:f},{default:o(()=>[(h(!0),R(W,null,se(n(l),O=>(h(),I(k,{key:O.id,label:O.nickname,value:O.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):J("",!0),n(t).candidateStrategy===40?(h(),I(E,{key:4,label:"\u6307\u5B9A\u7528\u6237\u7EC4",prop:"candidateParam"},{default:o(()=>[i(C,{modelValue:n(t).candidateParam,"onUpdate:modelValue":x[5]||(x[5]=O=>n(t).candidateParam=O),clearable:"",multiple:"",style:{width:"100%"},onChange:f},{default:o(()=>[(h(!0),R(W,null,se(n(d),O=>(h(),I(k,{key:O.id,label:O.name,value:O.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):J("",!0),n(t).candidateStrategy===60?(h(),I(E,{key:5,label:"\u6D41\u7A0B\u8868\u8FBE\u5F0F",prop:"candidateParam"},{default:o(()=>[i(P,{type:"textarea",modelValue:n(t).candidateParam[0],"onUpdate:modelValue":x[6]||(x[6]=O=>n(t).candidateParam[0]=O),clearable:"",style:{width:"72%"},onChange:f},null,8,["modelValue"]),i(D,{class:"ml-5px",size:"small",type:"success",onClick:S},{default:o(()=>[N("\u9009\u62E9\u8868\u8FBE\u5F0F")]),_:1}),i(ns,{ref_key:"processExpressionDialogRef",ref:w,onSelect:v},null,512)]),_:1})):J("",!0)]),_:1})}}}));var cs=Object.defineProperty,ms=Object.defineProperties,ys=Object.getOwnPropertyDescriptors,Qa=Object.getOwnPropertySymbols,vs=Object.prototype.hasOwnProperty,fs=Object.prototype.propertyIsEnumerable,Wa=(e,a,t)=>a in e?cs(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,bs=(e,a)=>{for(var t in a||(a={}))vs.call(a,t)&&Wa(e,t,a[t]);if(Qa)for(var t of Qa(a))fs.call(a,t)&&Wa(e,t,a[t]);return e},gs=(e,a)=>ms(e,ys(a));const hs=te(gs(bs({},{name:"ServiceTask"}),{__name:"ServiceTask",props:{id:String,type:String},setup(e){const a=e,t=m({executeType:"",class:"",expression:"",delegateExpression:""}),r=m({}),s=m(),p=()=>window==null?void 0:window.bpmnInstances,c=()=>{var l;for(let d in t.value){let g=((l=s.value)==null?void 0:l.businessObject[d])||t.value[d];r.value[d]=g,g&&(r.value.executeType=d)}},u=()=>{let l=Object.create(null);const d=r.value.executeType;for(let g in r.value)g!=="executeType"&&g!==d&&(l[g]=null);l[d]=r.value[d]||"",p().modeling.updateProperties(ae(s.value),l)};return Ae(()=>{s.value=null}),re(()=>a.id,()=>{s.value=p().bpmnElement,ce(()=>{c()})},{immediate:!0}),(l,d)=>{const g=Se,b=xe,f=ue,w=de;return h(),R("div",null,[i(f,{label:"\u6267\u884C\u7C7B\u578B",key:"executeType"},{default:o(()=>[i(b,{modelValue:n(r).executeType,"onUpdate:modelValue":d[0]||(d[0]=S=>n(r).executeType=S)},{default:o(()=>[i(g,{label:"Java\u7C7B",value:"class"}),i(g,{label:"\u8868\u8FBE\u5F0F",value:"expression"}),i(g,{label:"\u4EE3\u7406\u8868\u8FBE\u5F0F",value:"delegateExpression"})]),_:1},8,["modelValue"])]),_:1}),n(r).executeType==="class"?(h(),I(f,{label:"Java\u7C7B",prop:"class",key:"execute-class"},{default:o(()=>[i(w,{modelValue:n(r).class,"onUpdate:modelValue":d[1]||(d[1]=S=>n(r).class=S),clearable:"",onChange:u},null,8,["modelValue"])]),_:1})):J("",!0),n(r).executeType==="expression"?(h(),I(f,{label:"\u8868\u8FBE\u5F0F",prop:"expression",key:"execute-expression"},{default:o(()=>[i(w,{modelValue:n(r).expression,"onUpdate:modelValue":d[2]||(d[2]=S=>n(r).expression=S),clearable:"",onChange:u},null,8,["modelValue"])]),_:1})):J("",!0),n(r).executeType==="delegateExpression"?(h(),I(f,{label:"\u4EE3\u7406\u8868\u8FBE\u5F0F",prop:"delegateExpression",key:"execute-delegate"},{default:o(()=>[i(w,{modelValue:n(r).delegateExpression,"onUpdate:modelValue":d[3]||(d[3]=S=>n(r).delegateExpression=S),clearable:"",onChange:u},null,8,["modelValue"])]),_:1})):J("",!0)])}}}));var ws=Object.defineProperty,Ss=Object.defineProperties,xs=Object.getOwnPropertyDescriptors,Xa=Object.getOwnPropertySymbols,ks=Object.prototype.hasOwnProperty,Cs=Object.prototype.propertyIsEnumerable,en=(e,a,t)=>a in e?ws(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,_s=(e,a)=>{for(var t in a||(a={}))ks.call(a,t)&&en(e,t,a[t]);if(Xa)for(var t of Xa(a))Cs.call(a,t)&&en(e,t,a[t]);return e},Ps=(e,a)=>Ss(e,xs(a));const Es={style:{"margin-top":"16px"}},Os=te(Ps(_s({},{name:"ScriptTask"}),{__name:"ScriptTask",props:{id:String,type:String},setup(e){const a=e,t=m({scriptFormat:"",script:"",resource:"",resultVariable:""}),r=m({}),s=m(),p=()=>window==null?void 0:window.bpmnInstances,c=()=>{var l;for(let d in t.value){let g=((l=s.value)==null?void 0:l.businessObject[d])||t.value[d];r.value[d]=g}r.value.scriptType=r.value.script?"inline":"external"},u=()=>{let l=Object.create(null);l.scriptFormat=r.value.scriptFormat||null,l.resultVariable=r.value.resultVariable||null,r.value.scriptType==="inline"?(l.script=r.value.script||null,l.resource=null):(l.resource=r.value.resource||null,l.script=null),p().modeling.updateProperties(ae(s.value),l)};return Ae(()=>{s.value=null}),re(()=>a.id,()=>{s.value=p().bpmnElement,ce(()=>{c()})},{immediate:!0}),(l,d)=>{const g=de,b=ue,f=Se,w=xe;return h(),R("div",Es,[i(b,{label:"\u811A\u672C\u683C\u5F0F"},{default:o(()=>[i(g,{modelValue:n(r).scriptFormat,"onUpdate:modelValue":d[0]||(d[0]=S=>n(r).scriptFormat=S),clearable:"",onInput:d[1]||(d[1]=S=>u()),onChange:d[2]||(d[2]=S=>u())},null,8,["modelValue"])]),_:1}),i(b,{label:"\u811A\u672C\u7C7B\u578B"},{default:o(()=>[i(w,{modelValue:n(r).scriptType,"onUpdate:modelValue":d[3]||(d[3]=S=>n(r).scriptType=S)},{default:o(()=>[i(f,{label:"\u5185\u8054\u811A\u672C",value:"inline"}),i(f,{label:"\u5916\u90E8\u8D44\u6E90",value:"external"})]),_:1},8,["modelValue"])]),_:1}),me(i(b,{label:"\u811A\u672C"},{default:o(()=>[i(g,{modelValue:n(r).script,"onUpdate:modelValue":d[4]||(d[4]=S=>n(r).script=S),type:"textarea",resize:"vertical",autosize:{minRows:2,maxRows:4},clearable:"",onInput:d[5]||(d[5]=S=>u()),onChange:d[6]||(d[6]=S=>u())},null,8,["modelValue"])]),_:1},512),[[jt,n(r).scriptType==="inline"]]),me(i(b,{label:"\u8D44\u6E90\u5730\u5740"},{default:o(()=>[i(g,{modelValue:n(r).resource,"onUpdate:modelValue":d[7]||(d[7]=S=>n(r).resource=S),clearable:"",onInput:d[8]||(d[8]=S=>u()),onChange:d[9]||(d[9]=S=>u())},null,8,["modelValue"])]),_:1},512),[[jt,n(r).scriptType==="external"]]),i(b,{label:"\u7ED3\u679C\u53D8\u91CF"},{default:o(()=>[i(g,{modelValue:n(r).resultVariable,"onUpdate:modelValue":d[10]||(d[10]=S=>n(r).resultVariable=S),clearable:"",onInput:d[11]||(d[11]=S=>u()),onChange:d[12]||(d[12]=S=>u())},null,8,["modelValue"])]),_:1})])}}}));var As=Object.defineProperty,Ts=Object.defineProperties,Vs=Object.getOwnPropertyDescriptors,tn=Object.getOwnPropertySymbols,Is=Object.prototype.hasOwnProperty,js=Object.prototype.propertyIsEnumerable,an=(e,a,t)=>a in e?As(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,Ds=(e,a)=>{for(var t in a||(a={}))Is.call(a,t)&&an(e,t,a[t]);if(tn)for(var t of tn(a))js.call(a,t)&&an(e,t,a[t]);return e},Us=(e,a)=>Ts(e,Vs(a));const Ms={style:{"margin-top":"16px"}},Ns={style:{display:"flex","align-items":"center","justify-content":"space-between","flex-wrap":"nowrap"}},Bs=te(Us(Ds({},{name:"ReceiveTask"}),{__name:"ReceiveTask",props:{id:String,type:String},setup(e){const a=e,t=je(),r=m(""),s=m({}),p=m({}),c=m(!1),u=m(),l=m(),d=m(),g=()=>window.bpmnInstances,b=()=>{var v,y;u.value=g().bpmnElement,r.value=((y=(v=u.value.businessObject)==null?void 0:v.messageRef)==null?void 0:y.id)||"-1"},f=()=>{c.value=!0,s.value={}},w=()=>{if(p.value[s.value.id]){t.error("\u8BE5\u6D88\u606F\u5DF2\u5B58\u5728\uFF0C\u8BF7\u4FEE\u6539id\u540E\u91CD\u65B0\u4FDD\u5B58");return}const v=g().moddle.create("bpmn:Message",s.value);d.value.push(v),p.value[s.value.id]=s.value.name,l.value[s.value.id]=v,c.value=!1},S=v=>{v==="-1"?g().modeling.updateProperties(ae(u.value),{messageRef:null}):g().modeling.updateProperties(ae(u.value),{messageRef:l.value[v]})};return we(()=>{l.value=Object.create(null),d.value=g().modeler.getDefinitions().rootElements,d.value.filter(v=>v.$type==="bpmn:Message").forEach(v=>{l.value[v.id]=v,p.value[v.id]=v.name}),p.value[-1]="\u65E0"}),Ae(()=>{u.value=null}),re(()=>a.id,()=>{ce(()=>{b()})},{immediate:!0}),(v,y)=>{const x=Se,k=xe,C=Qe,E=ue,j=de,P=ye,D=pe,G=vt;return h(),R("div",Ms,[i(E,{label:"\u6D88\u606F\u5B9E\u4F8B"},{default:o(()=>[F("div",Ns,[i(k,{modelValue:n(r),"onUpdate:modelValue":y[0]||(y[0]=O=>ne(r)?r.value=O:null),onChange:S},{default:o(()=>[(h(!0),R(W,null,se(Object.keys(n(p)),O=>(h(),I(x,{value:O,label:n(p)[O],key:O},null,8,["value","label"]))),128))]),_:1},8,["modelValue"]),i(C,{type:"primary",preIcon:"ep:plus",style:{"margin-left":"8px"},onClick:f})])]),_:1}),i(G,{modelValue:n(c),"onUpdate:modelValue":y[3]||(y[3]=O=>ne(c)?c.value=O:null),"close-on-click-modal":!1,title:"\u521B\u5EFA\u65B0\u6D88\u606F",width:"400px","append-to-body":"","destroy-on-close":""},{footer:o(()=>[i(D,{size:"small",type:"primary",onClick:w},{default:o(()=>[N("\u786E \u8BA4")]),_:1})]),default:o(()=>[i(P,{model:n(s),size:"small","label-width":"90px"},{default:o(()=>[i(E,{label:"\u6D88\u606FID"},{default:o(()=>[i(j,{modelValue:n(s).id,"onUpdate:modelValue":y[1]||(y[1]=O=>n(s).id=O),clearable:""},null,8,["modelValue"])]),_:1}),i(E,{label:"\u6D88\u606F\u540D\u79F0"},{default:o(()=>[i(j,{modelValue:n(s).name,"onUpdate:modelValue":y[2]||(y[2]=O=>n(s).name=O),clearable:""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}));var Fs=Object.defineProperty,Ls=Object.defineProperties,$s=Object.getOwnPropertyDescriptors,nn=Object.getOwnPropertySymbols,Rs=Object.prototype.hasOwnProperty,zs=Object.prototype.propertyIsEnumerable,ln=(e,a,t)=>a in e?Fs(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,rn=(e,a)=>{for(var t in a||(a={}))Rs.call(a,t)&&ln(e,t,a[t]);if(nn)for(var t of nn(a))zs.call(a,t)&&ln(e,t,a[t]);return e},qs=(e,a)=>Ls(e,$s(a));const Js={class:"panel-tab__content"},Gs=te(qs(rn({},{name:"ElementTaskConfig"}),{__name:"ElementTask",props:{id:String,type:String},setup(e){const a=e,t=m({asyncAfter:!1,asyncBefore:!1,exclusive:!1}),r=m(),s=m({UserTask:"UserTask",ServiceTask:"ServiceTask",ScriptTask:"ScriptTask",ReceiveTask:"ReceiveTask"}),p=m(),c=()=>window.bpmnInstances,u=()=>{!t.value.asyncBefore&&!t.value.asyncAfter&&(t.value.exclusive=!1),c().modeling.updateProperties(c().bpmnElement,rn({},t.value))};return re(()=>a.id,()=>{var l,d,g,b,f,w;p.value=c().bpmnElement,t.value.asyncBefore=(d=(l=p.value)==null?void 0:l.businessObject)==null?void 0:d.asyncBefore,t.value.asyncAfter=(b=(g=p.value)==null?void 0:g.businessObject)==null?void 0:b.asyncAfter,t.value.exclusive=(w=(f=p.value)==null?void 0:f.businessObject)==null?void 0:w.exclusive},{immediate:!0}),re(()=>a.type,()=>{a.type==s.value.UserTask&&(r.value=ds),a.type==s.value.ServiceTask&&(r.value=hs),a.type==s.value.ScriptTask&&(r.value=Os),a.type==s.value.ReceiveTask&&(r.value=Bs)},{immediate:!0}),(l,d)=>{const g=ka,b=ue,f=ye;return h(),R("div",Js,[i(f,{size:"small","label-width":"90px"},{default:o(()=>[i(b,{label:"\u5F02\u6B65\u5EF6\u7EED",style:{display:"none"}},{default:o(()=>[i(g,{modelValue:n(t).asyncBefore,"onUpdate:modelValue":d[0]||(d[0]=w=>n(t).asyncBefore=w),label:"\u5F02\u6B65\u524D",onChange:u},null,8,["modelValue"]),i(g,{modelValue:n(t).asyncAfter,"onUpdate:modelValue":d[1]||(d[1]=w=>n(t).asyncAfter=w),label:"\u5F02\u6B65\u540E",onChange:u},null,8,["modelValue"]),n(t).asyncAfter||n(t).asyncBefore?(h(),I(g,{key:0,modelValue:n(t).exclusive,"onUpdate:modelValue":d[2]||(d[2]=w=>n(t).exclusive=w),label:"\u6392\u9664",onChange:u},null,8,["modelValue"])):J("",!0)]),_:1}),(h(),I(li(n(r)),ai(ni(l.$props)),null,16))]),_:1})])}}}));var Ks=Object.defineProperty,Ys=Object.defineProperties,Hs=Object.getOwnPropertyDescriptors,on=Object.getOwnPropertySymbols,Zs=Object.prototype.hasOwnProperty,Qs=Object.prototype.propertyIsEnumerable,sn=(e,a,t)=>a in e?Ks(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,Zt=(e,a)=>{for(var t in a||(a={}))Zs.call(a,t)&&sn(e,t,a[t]);if(on)for(var t of on(a))Qs.call(a,t)&&sn(e,t,a[t]);return e},pn=(e,a)=>Ys(e,Hs(a));const Ws={class:"panel-tab__content"},Xs=te(pn(Zt({},{name:"ElementMultiInstance"}),{__name:"ElementMultiInstance",props:{businessObject:Object,type:String},setup(e){const a=e,t=Ne("prefix"),r=m(""),s=m({completionCondition:"",loopCardinality:"",extensionElements:[],asyncAfter:!1,asyncBefore:!1,exclusive:!1}),p=m({}),c=m(null),u=m(null),l=()=>window==null?void 0:window.bpmnInstances,d=x=>{var k,C,E,j,P,D;if(!x.loopCharacteristics){r.value="Null",p.value={};return}if(x.loopCharacteristics.$type==="bpmn:StandardLoopCharacteristics"){r.value="StandardLoop",p.value={};return}x.loopCharacteristics.isSequential?r.value="SequentialMultiInstance":r.value="ParallelMultiInstance",p.value=pn(Zt(Zt({},s.value),x.loopCharacteristics),{completionCondition:(E=(C=(k=x.loopCharacteristics)==null?void 0:k.completionCondition)==null?void 0:C.body)!=null?E:"",loopCardinality:(D=(P=(j=x.loopCharacteristics)==null?void 0:j.loopCardinality)==null?void 0:P.body)!=null?D:""}),u.value=l().bpmnElement.businessObject.loopCharacteristics,x.loopCharacteristics.extensionElements&&x.loopCharacteristics.extensionElements.values&&x.loopCharacteristics.extensionElements.values.length&&(p.value.timeCycle=x.loopCharacteristics.extensionElements.values[0].body)},g=x=>{if(x==="Null"){l().modeling.updateProperties(ae(c.value),{loopCharacteristics:null});return}if(x==="StandardLoop"){const k=l().moddle.create("bpmn:StandardLoopCharacteristics");l().modeling.updateProperties(ae(c.value),{loopCharacteristics:k}),u.value=null;return}x==="SequentialMultiInstance"?u.value=l().moddle.create("bpmn:MultiInstanceLoopCharacteristics",{isSequential:!0}):u.value=l().moddle.create("bpmn:MultiInstanceLoopCharacteristics",{collection:"${coll_userList}"}),l().modeling.updateProperties(ae(c.value),{loopCharacteristics:ae(u.value)})},b=x=>{let k=null;x&&x.length&&(k=l().moddle.create("bpmn:FormalExpression",{body:x})),l().modeling.updateModdleProperties(ae(c.value),u.value,{loopCardinality:k})},f=x=>{let k=null;x&&x.length&&(k=l().moddle.create("bpmn:FormalExpression",{body:x})),l().modeling.updateModdleProperties(ae(c.value),u.value,{completionCondition:k})},w=x=>{const k=l().moddle.create("bpmn:ExtensionElements",{values:[l().moddle.create(`${t}:FailedJobRetryTimeCycle`,{body:x})]});l().modeling.updateModdleProperties(ae(c.value),u.value,{extensionElements:k})},S=()=>{l().modeling.updateModdleProperties(ae(c.value),u.value,{collection:p.value.collection||null,elementVariable:p.value.elementVariable||null})},v=x=>{const{asyncBefore:k,asyncAfter:C}=p.value;let E=Object.create(null);!k&&!C?(p.value.exclusive=!1,E={asyncBefore:!1,asyncAfter:!1,exclusive:!1,extensionElements:null}):E[x]=p.value[x],l().modeling.updateModdleProperties(ae(c.value),u.value,E)},y=x=>{x==="\u4F9D\u6B21\u5BA1\u6279"?(g("SequentialMultiInstance"),b("1"),f("${ nrOfCompletedInstances >= nrOfInstances }")):x==="\u4F1A\u7B7E"?(g("ParallelMultiInstance"),f("${ nrOfCompletedInstances >= nrOfInstances }")):x==="\u6216\u7B7E"&&(g("ParallelMultiInstance"),f("${ nrOfCompletedInstances > 0 }"))};return Ae(()=>{u.value=null,c.value=null}),re(()=>a.businessObject,x=>{c.value=l().bpmnElement,d(x)},{immediate:!0}),(x,k)=>{const C=pe,E=ue,j=Se,P=xe,D=de,G=ka,O=ye;return h(),R("div",Ws,[i(O,{"label-width":"90px"},{default:o(()=>[i(E,{label:"\u5FEB\u6377\u914D\u7F6E"},{default:o(()=>[i(C,{size:"small",onClick:k[0]||(k[0]=U=>y("\u4F9D\u6B21\u5BA1\u6279"))},{default:o(()=>[N("\u4F9D\u6B21\u5BA1\u6279")]),_:1}),i(C,{size:"small",onClick:k[1]||(k[1]=U=>y("\u4F1A\u7B7E"))},{default:o(()=>[N("\u4F1A\u7B7E")]),_:1}),i(C,{size:"small",onClick:k[2]||(k[2]=U=>y("\u6216\u7B7E"))},{default:o(()=>[N("\u6216\u7B7E")]),_:1})]),_:1}),i(E,{label:"\u4F1A\u7B7E\u7C7B\u578B"},{default:o(()=>[i(P,{modelValue:n(r),"onUpdate:modelValue":k[3]||(k[3]=U=>ne(r)?r.value=U:null),onChange:g},{default:o(()=>[i(j,{label:"\u5E76\u884C\u591A\u91CD\u4E8B\u4EF6",value:"ParallelMultiInstance"}),i(j,{label:"\u65F6\u5E8F\u591A\u91CD\u4E8B\u4EF6",value:"SequentialMultiInstance"}),i(j,{label:"\u65E0",value:"Null"})]),_:1},8,["modelValue"])]),_:1}),n(r)==="ParallelMultiInstance"||n(r)==="SequentialMultiInstance"?(h(),R(W,{key:0},[i(E,{label:"\u5FAA\u73AF\u6570\u91CF",key:"loopCardinality"},{default:o(()=>[i(D,{modelValue:n(p).loopCardinality,"onUpdate:modelValue":k[4]||(k[4]=U=>n(p).loopCardinality=U),clearable:"",onChange:b},null,8,["modelValue"])]),_:1}),me(i(E,{label:"\u96C6\u5408",key:"collection"},{default:o(()=>[i(D,{modelValue:n(p).collection,"onUpdate:modelValue":k[5]||(k[5]=U=>n(p).collection=U),clearable:"",onChange:S},null,8,["modelValue"])]),_:1},512),[[jt,!1]]),i(E,{label:"\u5143\u7D20\u53D8\u91CF",key:"elementVariable",style:{display:"none"}},{default:o(()=>[i(D,{modelValue:n(p).elementVariable,"onUpdate:modelValue":k[6]||(k[6]=U=>n(p).elementVariable=U),clearable:"",onChange:S},null,8,["modelValue"])]),_:1}),i(E,{label:"\u5B8C\u6210\u6761\u4EF6",key:"completionCondition"},{default:o(()=>[i(D,{modelValue:n(p).completionCondition,"onUpdate:modelValue":k[7]||(k[7]=U=>n(p).completionCondition=U),clearable:"",onChange:f},null,8,["modelValue"])]),_:1}),i(E,{label:"\u5F02\u6B65\u72B6\u6001",key:"async",style:{display:"none"}},{default:o(()=>[i(G,{modelValue:n(p).asyncBefore,"onUpdate:modelValue":k[8]||(k[8]=U=>n(p).asyncBefore=U),label:"\u5F02\u6B65\u524D",onChange:k[9]||(k[9]=U=>v("asyncBefore"))},null,8,["modelValue"]),i(G,{modelValue:n(p).asyncAfter,"onUpdate:modelValue":k[10]||(k[10]=U=>n(p).asyncAfter=U),label:"\u5F02\u6B65\u540E",onChange:k[11]||(k[11]=U=>v("asyncAfter"))},null,8,["modelValue"]),n(p).asyncAfter||n(p).asyncBefore?(h(),I(G,{key:0,modelValue:n(p).exclusive,"onUpdate:modelValue":k[12]||(k[12]=U=>n(p).exclusive=U),label:"\u6392\u9664",onChange:k[13]||(k[13]=U=>v("exclusive"))},null,8,["modelValue"])):J("",!0)]),_:1}),n(p).asyncAfter||n(p).asyncBefore?(h(),I(E,{label:"\u91CD\u8BD5\u5468\u671F",prop:"timeCycle",key:"timeCycle"},{default:o(()=>[i(D,{modelValue:n(p).timeCycle,"onUpdate:modelValue":k[14]||(k[14]=U=>n(p).timeCycle=U),clearable:"",onChange:w},null,8,["modelValue"])]),_:1})):J("",!0)],64)):J("",!0)]),_:1})])}}}));var ep=Object.defineProperty,tp=Object.defineProperties,ap=Object.getOwnPropertyDescriptors,un=Object.getOwnPropertySymbols,np=Object.prototype.hasOwnProperty,lp=Object.prototype.propertyIsEnumerable,dn=(e,a,t)=>a in e?ep(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,cn=(e,a)=>{for(var t in a||(a={}))np.call(a,t)&&dn(e,t,a[t]);if(un)for(var t of un(a))lp.call(a,t)&&dn(e,t,a[t]);return e},mn=(e,a)=>tp(e,ap(a));const ip={class:"panel-tab__content"},rp=te(mn(cn({},{name:"FlowCondition"}),{__name:"FlowCondition",props:{businessObject:Object,type:String},setup(e){const a=e,t=m({}),r=m(),s=m(),p=m(),c=m(),u=()=>window==null?void 0:window.bpmnInstances,l=()=>{if(r.value=u().bpmnElement,s.value=r.value.source,p.value=r.value.businessObject.sourceRef,t.value={type:"default"},p.value&&p.value.default&&p.value.default.id===r.value.id)t.value={type:"default"};else if(!r.value.businessObject.conditionExpression)t.value={type:"normal"};else{const b=r.value.businessObject.conditionExpression;if(t.value=mn(cn({},b),{type:"condition"}),t.value.resource){t.value.conditionType="script",t.value.scriptType="externalScript";return}if(b.language){t.value.conditionType="script",t.value.scriptType="inlineScript";return}t.value.conditionType="expression"}},d=b=>{if(b==="condition"){c.value=u().moddle.create("bpmn:FormalExpression"),u().modeling.updateProperties(ae(r.value),{conditionExpression:c.value});return}if(b==="default"){u().modeling.updateProperties(ae(r.value),{conditionExpression:null}),u().modeling.updateProperties(ae(s.value),{default:ae(r.value)});return}p.value.default&&p.value.default.id===r.value.id&&u().modeling.updateProperties(ae(s.value),{default:null}),u().modeling.updateProperties(ae(r.value),{conditionExpression:null})},g=()=>{let{conditionType:b,scriptType:f,body:w,resource:S,language:v}=t.value,y;b==="expression"?y=u().moddle.create("bpmn:FormalExpression",{body:w}):f==="inlineScript"?(y=u().moddle.create("bpmn:FormalExpression",{body:w,language:v}),t.value.resource=""):(t.value.body="",y=u().moddle.create("bpmn:FormalExpression",{resource:S,language:v})),u().modeling.updateProperties(ae(r.value),{conditionExpression:y})};return Ae(()=>{r.value=null,s.value=null,p.value=null}),re(()=>a.businessObject,b=>{ce(()=>{l()})},{immediate:!0}),(b,f)=>{const w=Se,S=xe,v=ue,y=de,x=ye;return h(),R("div",ip,[i(x,{model:n(t),"label-width":"90px",size:"small"},{default:o(()=>[i(v,{label:"\u6D41\u8F6C\u7C7B\u578B"},{default:o(()=>[i(S,{modelValue:n(t).type,"onUpdate:modelValue":f[0]||(f[0]=k=>n(t).type=k),onChange:d},{default:o(()=>[i(w,{label:"\u666E\u901A\u6D41\u8F6C\u8DEF\u5F84",value:"normal"}),i(w,{label:"\u9ED8\u8BA4\u6D41\u8F6C\u8DEF\u5F84",value:"default"}),i(w,{label:"\u6761\u4EF6\u6D41\u8F6C\u8DEF\u5F84",value:"condition"})]),_:1},8,["modelValue"])]),_:1}),n(t).type==="condition"?(h(),I(v,{label:"\u6761\u4EF6\u683C\u5F0F",key:"condition"},{default:o(()=>[i(S,{modelValue:n(t).conditionType,"onUpdate:modelValue":f[1]||(f[1]=k=>n(t).conditionType=k)},{default:o(()=>[i(w,{label:"\u8868\u8FBE\u5F0F",value:"expression"}),i(w,{label:"\u811A\u672C",value:"script"})]),_:1},8,["modelValue"])]),_:1})):J("",!0),n(t).conditionType&&n(t).conditionType==="expression"?(h(),I(v,{label:"\u8868\u8FBE\u5F0F",key:"express"},{default:o(()=>[i(y,{modelValue:n(t).body,"onUpdate:modelValue":f[2]||(f[2]=k=>n(t).body=k),style:{width:"192px"},clearable:"",onChange:g},null,8,["modelValue"])]),_:1})):J("",!0),n(t).conditionType&&n(t).conditionType==="script"?(h(),R(W,{key:2},[i(v,{label:"\u811A\u672C\u8BED\u8A00",key:"language"},{default:o(()=>[i(y,{modelValue:n(t).language,"onUpdate:modelValue":f[3]||(f[3]=k=>n(t).language=k),clearable:"",onChange:g},null,8,["modelValue"])]),_:1}),i(v,{label:"\u811A\u672C\u7C7B\u578B",key:"scriptType"},{default:o(()=>[i(S,{modelValue:n(t).scriptType,"onUpdate:modelValue":f[4]||(f[4]=k=>n(t).scriptType=k)},{default:o(()=>[i(w,{label:"\u5185\u8054\u811A\u672C",value:"inlineScript"}),i(w,{label:"\u5916\u90E8\u811A\u672C",value:"externalScript"})]),_:1},8,["modelValue"])]),_:1}),n(t).scriptType==="inlineScript"?(h(),I(v,{label:"\u811A\u672C",key:"body"},{default:o(()=>[i(y,{modelValue:n(t).body,"onUpdate:modelValue":f[5]||(f[5]=k=>n(t).body=k),type:"textarea",clearable:"",onChange:g},null,8,["modelValue"])]),_:1})):J("",!0),n(t).scriptType==="externalScript"?(h(),I(v,{label:"\u8D44\u6E90\u5730\u5740",key:"resource"},{default:o(()=>[i(y,{modelValue:n(t).resource,"onUpdate:modelValue":f[6]||(f[6]=k=>n(t).resource=k),clearable:"",onChange:g},null,8,["modelValue"])]),_:1})):J("",!0)],64)):J("",!0)]),_:1},8,["model"])])}}}));var op=Object.defineProperty,sp=Object.defineProperties,pp=Object.getOwnPropertyDescriptors,yn=Object.getOwnPropertySymbols,up=Object.prototype.hasOwnProperty,dp=Object.prototype.propertyIsEnumerable,vn=(e,a,t)=>a in e?op(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,Qt=(e,a)=>{for(var t in a||(a={}))up.call(a,t)&&vn(e,t,a[t]);if(yn)for(var t of yn(a))dp.call(a,t)&&vn(e,t,a[t]);return e},cp=(e,a)=>sp(e,pp(a));const mp={class:"panel-tab__content"},yp={class:"panel-tab__content--title"},vp={class:"panel-tab__content--title",style:{"padding-top":"8px","margin-top":"8px","border-top":"1px solid #eee"}},fp=te(cp(Qt({},{name:"SignalAndMassage"}),{__name:"SignalAndMessage",setup(e){const a=je(),t=m([]),r=m([]),s=m(!1),p=m(""),c=m({}),u=m(),l=m(),d=m(),g=De(()=>p.value==="message"?{title:"\u521B\u5EFA\u6D88\u606F",idLabel:"\u6D88\u606FID",nameLabel:"\u6D88\u606F\u540D\u79F0"}:{title:"\u521B\u5EFA\u4FE1\u53F7",idLabel:"\u4FE1\u53F7ID",nameLabel:"\u4FE1\u53F7\u540D\u79F0"}),b=()=>window==null?void 0:window.bpmnInstances,f=()=>{u.value=b().modeler.getDefinitions().rootElements,l.value={},d.value={},r.value=[],t.value=[],u.value.forEach(v=>{v.$type==="bpmn:Message"&&(l.value[v.id]=!0,r.value.push(Qt({},v))),v.$type==="bpmn:Signal"&&(d.value[v.id]=!0,t.value.push(Qt({},v)))})},w=v=>{p.value=v,c.value={},s.value=!0},S=()=>{if(p.value==="message"){l.value[c.value.id]&&a.error("\u8BE5\u6D88\u606F\u5DF2\u5B58\u5728\uFF0C\u8BF7\u4FEE\u6539id\u540E\u91CD\u65B0\u4FDD\u5B58");const v=b().moddle.create("bpmn:Message",c.value);u.value.push(v)}else{d.value[c.value.id]&&a.error("\u8BE5\u4FE1\u53F7\u5DF2\u5B58\u5728\uFF0C\u8BF7\u4FEE\u6539id\u540E\u91CD\u65B0\u4FDD\u5B58");const v=b().moddle.create("bpmn:Signal",c.value);u.value.push(v)}s.value=!1,f()};return we(()=>{f()}),(v,y)=>{const x=Me,k=Qe,C=Be,E=Fe,j=de,P=ue,D=ye,G=pe,O=vt;return h(),R("div",mp,[F("div",yp,[F("span",null,[i(x,{icon:"ep:menu",style:{"margin-right":"8px",color:"#555"}}),N("\u6D88\u606F\u5217\u8868")]),i(k,{type:"primary",title:"\u521B\u5EFA\u65B0\u6D88\u606F",preIcon:"ep:plus",onClick:y[0]||(y[0]=U=>w("message"))})]),i(E,{data:n(r),border:""},{default:o(()=>[i(C,{type:"index",label:"\u5E8F\u53F7",width:"60px"}),i(C,{label:"\u6D88\u606FID",prop:"id","max-width":"300px","show-overflow-tooltip":""}),i(C,{label:"\u6D88\u606F\u540D\u79F0",prop:"name","max-width":"300px","show-overflow-tooltip":""})]),_:1},8,["data"]),F("div",vp,[F("span",null,[i(x,{icon:"ep:menu",style:{"margin-right":"8px",color:"#555"}}),N("\u4FE1\u53F7\u5217\u8868")]),i(k,{type:"primary",title:"\u521B\u5EFA\u65B0\u4FE1\u53F7",preIcon:"ep:plus",onClick:y[1]||(y[1]=U=>w("signal"))})]),i(E,{data:n(t),border:""},{default:o(()=>[i(C,{type:"index",label:"\u5E8F\u53F7",width:"60px"}),i(C,{label:"\u4FE1\u53F7ID",prop:"id","max-width":"300px","show-overflow-tooltip":""}),i(C,{label:"\u4FE1\u53F7\u540D\u79F0",prop:"name","max-width":"300px","show-overflow-tooltip":""})]),_:1},8,["data"]),i(O,{modelValue:n(s),"onUpdate:modelValue":y[5]||(y[5]=U=>ne(s)?s.value=U:null),title:n(g).title,"close-on-click-modal":!1,width:"400px","append-to-body":"","destroy-on-close":""},{footer:o(()=>[i(G,{onClick:y[4]||(y[4]=U=>s.value=!1)},{default:o(()=>[N("\u53D6 \u6D88")]),_:1}),i(G,{type:"primary",onClick:S},{default:o(()=>[N("\u4FDD \u5B58")]),_:1})]),default:o(()=>[i(D,{model:n(c),"label-width":"90px"},{default:o(()=>[i(P,{label:n(g).idLabel},{default:o(()=>[i(j,{modelValue:n(c).id,"onUpdate:modelValue":y[2]||(y[2]=U=>n(c).id=U),clearable:""},null,8,["modelValue"])]),_:1},8,["label"]),i(P,{label:n(g).nameLabel},{default:o(()=>[i(j,{modelValue:n(c).name,"onUpdate:modelValue":y[3]||(y[3]=U=>n(c).name=U),clearable:""},null,8,["modelValue"])]),_:1},8,["label"])]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}})),qe=()=>window==null?void 0:window.bpmnInstances;function Ct(e,a,t){const r=Object.create(null);switch(r.event=e.event,a&&(r.id=e.id),e.listenerType){case"scriptListener":r.script=gp(e,t);break;case"expressionListener":r.expression=e.expression;break;case"delegateExpressionListener":r.delegateExpression=e.delegateExpression;break;default:r.class=e.class}if(e.fields&&(r.fields=e.fields.map(s=>bp(s,t))),a&&e.event==="timeout"&&e.eventDefinitionType){const s=qe().moddle.create("bpmn:FormalExpression",{body:e.eventTimeDefinitions}),p=qe().moddle.create("bpmn:TimerEventDefinition",{id:`TimerEventDefinition_${hp(8)}`,[`time${e.eventDefinitionType.replace(/^\S/,c=>c.toUpperCase())}`]:s});r.eventDefinitions=[p]}return qe().moddle.create(`${t}:${a?"TaskListener":"ExecutionListener"}`,r)}function bp(e,a){const{name:t,fieldType:r,string:s,expression:p}=e,c=r==="string"?{name:t,string:s}:{name:t,expression:p};return qe().moddle.create(`${a}:Field`,c)}function gp(e,a){const{scriptType:t,scriptFormat:r,value:s,resource:p}=e,c=t==="inlineScript"?{scriptFormat:r,value:s}:{scriptFormat:r,resource:p};return qe().moddle.create(`${a}:Script`,c)}function nt(e,a){const t=qe().moddle.create("bpmn:ExtensionElements",{values:a});qe().modeling.updateProperties(ae(e),{extensionElements:t})}function hp(e=8,a){let t="";const r="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";for(let s=e;s>0;--s)t+=r[Math.floor(Math.random()*r.length)];return t}var wp=Object.defineProperty,Sp=Object.defineProperties,xp=Object.getOwnPropertyDescriptors,fn=Object.getOwnPropertySymbols,kp=Object.prototype.hasOwnProperty,Cp=Object.prototype.propertyIsEnumerable,bn=(e,a,t)=>a in e?wp(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,st=(e,a)=>{for(var t in a||(a={}))kp.call(a,t)&&bn(e,t,a[t]);if(fn)for(var t of fn(a))Cp.call(a,t)&&bn(e,t,a[t]);return e},gn=(e,a)=>Sp(e,xp(a));function hn(e){let a=st({},e);if(e.script&&(a=gn(st(st({},e),e.script),{scriptType:e.script.resource?"externalScript":"inlineScript"})),e.event==="timeout"&&e.eventDefinitions&&e.eventDefinitions.length){let t="";for(const r in e.eventDefinitions[0])r.indexOf("time")!==-1&&(t=r,a.eventDefinitionType=r.replace("time","").toLowerCase());a.eventTimeDefinitions=e.eventDefinitions[0][t].body}return a}function wn(e){var a;let t;return e.class&&(t="classListener"),e.expression&&(t="expressionListener"),e.delegateExpression&&(t="delegateExpressionListener"),e.script&&(t="scriptListener"),gn(st(st({},JSON.parse(JSON.stringify(e))),(a=e.script)!=null?a:{}),{listenerType:t})}function Sn(e){if(e.valueType==="class")return{listenerType:"classListener",class:e.value,event:e.event,fields:[]};if(e.valueType==="expression")return{listenerType:"expressionListener",expression:e.value,event:e.event,fields:[]};if(e.valueType==="delegateExpression")return{listenerType:"delegateExpressionListener",delegateExpression:e.value,event:e.event,fields:[]};throw new Error("\u672A\u77E5\u7684\u76D1\u542C\u5668\u7C7B\u578B")}const xn={classListener:"Java \u7C7B",expressionListener:"\u8868\u8FBE\u5F0F",delegateExpressionListener:"\u4EE3\u7406\u8868\u8FBE\u5F0F",scriptListener:"\u811A\u672C"},_p={create:"\u521B\u5EFA",assignment:"\u6307\u6D3E",complete:"\u5B8C\u6210",delete:"\u5220\u9664",update:"\u66F4\u65B0",timeout:"\u8D85\u65F6"},kn={string:"\u5B57\u7B26\u4E32",expression:"\u8868\u8FBE\u5F0F"};var pt=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const Pp={getProcessListenerPage:e=>pt(void 0,null,function*(){return yield ie.get({url:"/bpm/process-listener/page",params:e})}),getProcessListener:e=>pt(void 0,null,function*(){return yield ie.get({url:"/bpm/process-listener/get?id="+e})}),createProcessListener:e=>pt(void 0,null,function*(){return yield ie.post({url:"/bpm/process-listener/create",data:e})}),updateProcessListener:e=>pt(void 0,null,function*(){return yield ie.put({url:"/bpm/process-listener/update",data:e})}),deleteProcessListener:e=>pt(void 0,null,function*(){return yield ie.delete({url:"/bpm/process-listener/delete?id="+e})})};var Ep=Object.defineProperty,Op=Object.defineProperties,Ap=Object.getOwnPropertyDescriptors,Cn=Object.getOwnPropertySymbols,Tp=Object.prototype.hasOwnProperty,Vp=Object.prototype.propertyIsEnumerable,_n=(e,a,t)=>a in e?Ep(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,Ip=(e,a)=>{for(var t in a||(a={}))Tp.call(a,t)&&_n(e,t,a[t]);if(Cn)for(var t of Cn(a))Vp.call(a,t)&&_n(e,t,a[t]);return e},jp=(e,a)=>Op(e,Ap(a)),Wt=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const Pn=te(jp(Ip({},{name:"ProcessListenerDialog"}),{__name:"ProcessListenerDialog",emits:["success"],setup(e,{expose:a,emit:t}){const r=m(!1),s=m(!0),p=m([]),c=m(0),u=Re({pageNo:1,pageSize:10,type:"",status:Nt.ENABLE});a({open:b=>Wt(this,null,function*(){u.pageNo=1,u.type=b,l(),r.value=!0})});const l=()=>Wt(this,null,function*(){s.value=!0;try{const b=yield Pp.getProcessListenerPage(u);p.value=b.list,c.value=b.total}finally{s.value=!1}}),d=t,g=b=>Wt(this,null,function*(){r.value=!1,d("select",b)});return(b,f)=>{const w=Be,S=ct,v=pe,y=Fe,x=At,k=Le,C=Ce,E=Pe;return h(),I(C,{title:"\u8BF7\u9009\u62E9\u76D1\u542C\u5668",modelValue:n(r),"onUpdate:modelValue":f[2]||(f[2]=j=>ne(r)?r.value=j:null),width:"1024px"},{default:o(()=>[i(k,null,{default:o(()=>[me((h(),I(y,{data:n(p),stripe:!0,"show-overflow-tooltip":!0},{default:o(()=>[i(w,{label:"\u540D\u5B57",align:"center",prop:"name"}),i(w,{label:"\u7C7B\u578B",align:"center",prop:"type"},{default:o(j=>[i(S,{type:n(Oe).BPM_PROCESS_LISTENER_TYPE,value:j.row.type},null,8,["type","value"])]),_:1}),i(w,{label:"\u4E8B\u4EF6",align:"center",prop:"event"}),i(w,{label:"\u503C\u7C7B\u578B",align:"center",prop:"valueType"},{default:o(j=>[i(S,{type:n(Oe).BPM_PROCESS_LISTENER_VALUE_TYPE,value:j.row.valueType},null,8,["type","value"])]),_:1}),i(w,{label:"\u503C",align:"center",prop:"value"}),i(w,{label:"\u64CD\u4F5C",align:"center"},{default:o(j=>[i(v,{link:"",type:"primary",onClick:P=>g(j.row)},{default:o(()=>[N(" \u9009\u62E9 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[E,n(s)]]),i(x,{total:n(c),page:n(u).pageNo,"onUpdate:page":f[0]||(f[0]=j=>n(u).pageNo=j),limit:n(u).pageSize,"onUpdate:limit":f[1]||(f[1]=j=>n(u).pageSize=j),onPagination:l},null,8,["total","page","limit"])]),_:1})]),_:1},8,["modelValue"])}}}));var Dp=Object.defineProperty,Up=Object.defineProperties,Mp=Object.getOwnPropertyDescriptors,En=Object.getOwnPropertySymbols,Np=Object.prototype.hasOwnProperty,Bp=Object.prototype.propertyIsEnumerable,On=(e,a,t)=>a in e?Dp(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,An=(e,a)=>{for(var t in a||(a={}))Np.call(a,t)&&On(e,t,a[t]);if(En)for(var t of En(a))Bp.call(a,t)&&On(e,t,a[t]);return e},Tn=(e,a)=>Up(e,Mp(a)),Xt=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const Fp={class:"panel-tab__content"},Lp={class:"element-drawer__button"},$p={class:"listener-filed__title"},Rp={class:"element-drawer__button"},zp=te(Tn(An({},{name:"ElementListeners"}),{__name:"ElementListeners",props:{id:String,type:String},setup(e){const a=e,t=Ne("prefix"),r=Ne("width"),s=m([]),p=m({}),c=m(!1),u=m([]),l=m({}),d=m(!1),g=m(-1),b=m(-1),f=m(xn),w=m(kn),S=m(),v=m(),y=m(),x=m(),k=m(),C=()=>window==null?void 0:window.bpmnInstances,E=()=>{var K,B,_,$;S.value=C().bpmnElement,v.value=[],y.value=($=(_=(B=(K=S.value.businessObject)==null?void 0:K.extensionElements)==null?void 0:B.values)==null?void 0:_.filter(Z=>Z.$type===`${t}:ExecutionListener`))!=null?$:[],s.value=y.value.map(Z=>wn(Z))},j=(K,B)=>{K?(p.value=hn(K),g.value=B):(p.value={},g.value=-1),K&&K.fields?u.value=K.fields.map(_=>Tn(An({},_),{fieldType:_.string?"string":"expression"})):(u.value=[],p.value.fields=[]),c.value=!0,ce(()=>{x.value&&x.value.clearValidate()})},P=(K,B)=>{l.value=K?JSON.parse(JSON.stringify(K)):{},b.value=K?B:-1,d.value=!0,ce(()=>{k.value&&k.value.clearValidate()})},D=()=>Xt(this,null,function*(){(yield k.value.validate())&&(b.value===-1?(u.value.push(l.value),p.value.fields.push(l.value)):(u.value.splice(b.value,1,l.value),p.value.fields.splice(b.value,1,l.value)),d.value=!1,ce(()=>{l.value={}}))}),G=K=>{ze.confirm("\u786E\u8BA4\u79FB\u9664\u8BE5\u5B57\u6BB5\u5417\uFF1F","\u63D0\u793A",{confirmButtonText:"\u786E \u8BA4",cancelButtonText:"\u53D6 \u6D88"}).then(()=>{u.value.splice(K,1),p.value.fields.splice(K,1)}).catch(()=>{})},O=K=>{ze.confirm("\u786E\u8BA4\u79FB\u9664\u8BE5\u76D1\u542C\u5668\u5417\uFF1F","\u63D0\u793A",{confirmButtonText:"\u786E \u8BA4",cancelButtonText:"\u53D6 \u6D88"}).then(()=>{y.value.splice(K,1),s.value.splice(K,1),nt(S.value,v.value.concat(y.value))}).catch(()=>{})},U=()=>Xt(this,null,function*(){var K,B,_,$;if(!(yield x.value.validate()))return;const Z=Ct(p.value,!1,t);g.value===-1?(y.value.push(Z),s.value.push(p.value)):(y.value.splice(g.value,1,Z),s.value.splice(g.value,1,p.value)),v.value=($=(_=(B=(K=S.value.businessObject)==null?void 0:K.extensionElements)==null?void 0:B.values)==null?void 0:_.filter(Q=>Q.$type!==`${t}:ExecutionListener`))!=null?$:[],nt(S.value,v.value.concat(y.value)),c.value=!1,p.value={}}),Y=m(),le=()=>Xt(this,null,function*(){Y.value.open("execution")}),H=K=>{var B,_,$,Z;const Q=Sn(K),V=Ct(Q,!1,t);y.value.push(V),s.value.push(Q),v.value=(Z=($=(_=(B=S.value.businessObject)==null?void 0:B.extensionElements)==null?void 0:_.values)==null?void 0:$.filter(A=>A.$type!==`${t}:ExecutionListener`))!=null?Z:[],nt(S.value,v.value.concat(y.value))};return re(()=>a.id,K=>{K&&K.length&&ce(()=>{E()})},{immediate:!0}),(K,B)=>{const _=Be,$=pe,Z=Dt,Q=Fe,V=Qe,A=Se,T=xe,M=ue,z=de,X=ye,ge=Me,be=Ut,Ee=vt;return h(),R(W,null,[F("div",Fp,[i(Q,{data:n(s),size:"small",border:""},{default:o(()=>[i(_,{label:"\u5E8F\u53F7",width:"50px",type:"index"}),i(_,{label:"\u4E8B\u4EF6\u7C7B\u578B","min-width":"100px",prop:"event"}),i(_,{label:"\u76D1\u542C\u5668\u7C7B\u578B","min-width":"100px","show-overflow-tooltip":"",formatter:L=>n(f)[L.listenerType]},null,8,["formatter"]),i(_,{label:"\u64CD\u4F5C",width:"100px"},{default:o(L=>[i($,{size:"small",link:"",onClick:q=>j(L.row,L.$index)},{default:o(()=>[N("\u7F16\u8F91")]),_:2},1032,["onClick"]),i(Z,{direction:"vertical"}),i($,{size:"small",link:"",style:{color:"#ff4d4f"},onClick:q=>O(L.$index)},{default:o(()=>[N("\u79FB\u9664")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),F("div",Lp,[i(V,{type:"primary",preIcon:"ep:plus",title:"\u6DFB\u52A0\u76D1\u542C\u5668",size:"small",onClick:B[0]||(B[0]=L=>j(null))}),i(V,{type:"success",preIcon:"ep:select",title:"\u9009\u62E9\u76D1\u542C\u5668",size:"small",onClick:le})]),i(be,{modelValue:n(c),"onUpdate:modelValue":B[12]||(B[12]=L=>ne(c)?c.value=L:null),title:"\u6267\u884C\u76D1\u542C\u5668",size:`${n(r)}px`,"append-to-body":"","destroy-on-close":""},{default:o(()=>[i(X,{model:n(p),"label-width":"96px",ref_key:"listenerFormRef",ref:x},{default:o(()=>[i(M,{label:"\u4E8B\u4EF6\u7C7B\u578B",prop:"event",rules:{required:!0,trigger:["blur","change"]}},{default:o(()=>[i(T,{modelValue:n(p).event,"onUpdate:modelValue":B[1]||(B[1]=L=>n(p).event=L)},{default:o(()=>[i(A,{label:"start",value:"start"}),i(A,{label:"end",value:"end"})]),_:1},8,["modelValue"])]),_:1}),i(M,{label:"\u76D1\u542C\u5668\u7C7B\u578B",prop:"listenerType",rules:{required:!0,trigger:["blur","change"]}},{default:o(()=>[i(T,{modelValue:n(p).listenerType,"onUpdate:modelValue":B[2]||(B[2]=L=>n(p).listenerType=L)},{default:o(()=>[(h(!0),R(W,null,se(Object.keys(n(f)),L=>(h(),I(A,{key:L,label:n(f)[L],value:L},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(p).listenerType==="classListener"?(h(),I(M,{label:"Java\u7C7B",prop:"class",key:"listener-class",rules:{required:!0,trigger:["blur","change"]}},{default:o(()=>[i(z,{modelValue:n(p).class,"onUpdate:modelValue":B[3]||(B[3]=L=>n(p).class=L),clearable:""},null,8,["modelValue"])]),_:1})):J("",!0),n(p).listenerType==="expressionListener"?(h(),I(M,{label:"\u8868\u8FBE\u5F0F",prop:"expression",key:"listener-expression",rules:{required:!0,trigger:["blur","change"]}},{default:o(()=>[i(z,{modelValue:n(p).expression,"onUpdate:modelValue":B[4]||(B[4]=L=>n(p).expression=L),clearable:""},null,8,["modelValue"])]),_:1})):J("",!0),n(p).listenerType==="delegateExpressionListener"?(h(),I(M,{label:"\u4EE3\u7406\u8868\u8FBE\u5F0F",prop:"delegateExpression",key:"listener-delegate",rules:{required:!0,trigger:["blur","change"]}},{default:o(()=>[i(z,{modelValue:n(p).delegateExpression,"onUpdate:modelValue":B[5]||(B[5]=L=>n(p).delegateExpression=L),clearable:""},null,8,["modelValue"])]),_:1})):J("",!0),n(p).listenerType==="scriptListener"?(h(),R(W,{key:3},[i(M,{label:"\u811A\u672C\u683C\u5F0F",prop:"scriptFormat",key:"listener-script-format",rules:{required:!0,trigger:["blur","change"],message:"\u8BF7\u586B\u5199\u811A\u672C\u683C\u5F0F"}},{default:o(()=>[i(z,{modelValue:n(p).scriptFormat,"onUpdate:modelValue":B[6]||(B[6]=L=>n(p).scriptFormat=L),clearable:""},null,8,["modelValue"])]),_:1}),i(M,{label:"\u811A\u672C\u7C7B\u578B",prop:"scriptType",key:"listener-script-type",rules:{required:!0,trigger:["blur","change"],message:"\u8BF7\u9009\u62E9\u811A\u672C\u7C7B\u578B"}},{default:o(()=>[i(T,{modelValue:n(p).scriptType,"onUpdate:modelValue":B[7]||(B[7]=L=>n(p).scriptType=L)},{default:o(()=>[i(A,{label:"\u5185\u8054\u811A\u672C",value:"inlineScript"}),i(A,{label:"\u5916\u90E8\u811A\u672C",value:"externalScript"})]),_:1},8,["modelValue"])]),_:1}),n(p).scriptType==="inlineScript"?(h(),I(M,{label:"\u811A\u672C\u5185\u5BB9",prop:"value",key:"listener-script",rules:{required:!0,trigger:["blur","change"],message:"\u8BF7\u586B\u5199\u811A\u672C\u5185\u5BB9"}},{default:o(()=>[i(z,{modelValue:n(p).value,"onUpdate:modelValue":B[8]||(B[8]=L=>n(p).value=L),clearable:""},null,8,["modelValue"])]),_:1})):J("",!0),n(p).scriptType==="externalScript"?(h(),I(M,{label:"\u8D44\u6E90\u5730\u5740",prop:"resource",key:"listener-resource",rules:{required:!0,trigger:["blur","change"],message:"\u8BF7\u586B\u5199\u8D44\u6E90\u5730\u5740"}},{default:o(()=>[i(z,{modelValue:n(p).resource,"onUpdate:modelValue":B[9]||(B[9]=L=>n(p).resource=L),clearable:""},null,8,["modelValue"])]),_:1})):J("",!0)],64)):J("",!0)]),_:1},8,["model"]),i(Z),F("p",$p,[F("span",null,[i(ge,{icon:"ep:menu"}),N("\u6CE8\u5165\u5B57\u6BB5\uFF1A")]),i(V,{type:"primary",onClick:B[10]||(B[10]=L=>P(null)),title:"\u6DFB\u52A0\u5B57\u6BB5"})]),i(Q,{data:n(u),size:"small","max-height":"240",fit:"",border:"",style:{flex:"none"}},{default:o(()=>[i(_,{label:"\u5E8F\u53F7",width:"50px",type:"index"}),i(_,{label:"\u5B57\u6BB5\u540D\u79F0","min-width":"100px",prop:"name"}),i(_,{label:"\u5B57\u6BB5\u7C7B\u578B","min-width":"80px","show-overflow-tooltip":"",formatter:L=>n(w)[L.fieldType]},null,8,["formatter"]),i(_,{label:"\u5B57\u6BB5\u503C/\u8868\u8FBE\u5F0F","min-width":"100px","show-overflow-tooltip":"",formatter:L=>L.string||L.expression},null,8,["formatter"]),i(_,{label:"\u64CD\u4F5C",width:"130px"},{default:o(L=>[i($,{size:"small",link:"",onClick:q=>P(L.row,L.$index)},{default:o(()=>[N("\u7F16\u8F91")]),_:2},1032,["onClick"]),i(Z,{direction:"vertical"}),i($,{size:"small",link:"",style:{color:"#ff4d4f"},onClick:q=>G(L.$index)},{default:o(()=>[N("\u79FB\u9664")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),F("div",Rp,[i($,{onClick:B[11]||(B[11]=L=>c.value=!1)},{default:o(()=>[N("\u53D6 \u6D88")]),_:1}),i($,{type:"primary",onClick:U},{default:o(()=>[N("\u4FDD \u5B58")]),_:1})])]),_:1},8,["modelValue","size"]),i(Ee,{title:"\u5B57\u6BB5\u914D\u7F6E",modelValue:n(d),"onUpdate:modelValue":B[18]||(B[18]=L=>ne(d)?d.value=L:null),width:"600px","append-to-body":"","destroy-on-close":""},{footer:o(()=>[i($,{size:"small",onClick:B[17]||(B[17]=L=>d.value=!1)},{default:o(()=>[N("\u53D6 \u6D88")]),_:1}),i($,{size:"small",type:"primary",onClick:D},{default:o(()=>[N("\u786E \u5B9A")]),_:1})]),default:o(()=>[i(X,{model:n(l),"label-width":"96spx",ref_key:"listenerFieldFormRef",ref:k,style:{height:"136px"}},{default:o(()=>[i(M,{label:"\u5B57\u6BB5\u540D\u79F0\uFF1A",prop:"name",rules:{required:!0,trigger:["blur","change"]}},{default:o(()=>[i(z,{modelValue:n(l).name,"onUpdate:modelValue":B[13]||(B[13]=L=>n(l).name=L),clearable:""},null,8,["modelValue"])]),_:1}),i(M,{label:"\u5B57\u6BB5\u7C7B\u578B\uFF1A",prop:"fieldType",rules:{required:!0,trigger:["blur","change"]}},{default:o(()=>[i(T,{modelValue:n(l).fieldType,"onUpdate:modelValue":B[14]||(B[14]=L=>n(l).fieldType=L)},{default:o(()=>[(h(!0),R(W,null,se(Object.keys(n(w)),L=>(h(),I(A,{key:L,label:n(w)[L],value:L},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(l).fieldType==="string"?(h(),I(M,{label:"\u5B57\u6BB5\u503C\uFF1A",prop:"string",key:"field-string",rules:{required:!0,trigger:["blur","change"]}},{default:o(()=>[i(z,{modelValue:n(l).string,"onUpdate:modelValue":B[15]||(B[15]=L=>n(l).string=L),clearable:""},null,8,["modelValue"])]),_:1})):J("",!0),n(l).fieldType==="expression"?(h(),I(M,{label:"\u8868\u8FBE\u5F0F\uFF1A",prop:"expression",key:"field-expression",rules:{required:!0,trigger:["blur","change"]}},{default:o(()=>[i(z,{modelValue:n(l).expression,"onUpdate:modelValue":B[16]||(B[16]=L=>n(l).expression=L),clearable:""},null,8,["modelValue"])]),_:1})):J("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue"])]),i(Pn,{ref_key:"processListenerDialogRef",ref:Y,onSelect:H},null,512)],64)}}}));var qp=Object.defineProperty,Jp=Object.defineProperties,Gp=Object.getOwnPropertyDescriptors,Vn=Object.getOwnPropertySymbols,Kp=Object.prototype.hasOwnProperty,Yp=Object.prototype.propertyIsEnumerable,In=(e,a,t)=>a in e?qp(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,Hp=(e,a)=>{for(var t in a||(a={}))Kp.call(a,t)&&In(e,t,a[t]);if(Vn)for(var t of Vn(a))Yp.call(a,t)&&In(e,t,a[t]);return e},Zp=(e,a)=>Jp(e,Gp(a));const Qp={class:"panel-tab__content"},Wp={class:"element-drawer__button"},Xp=te(Zp(Hp({},{name:"ElementProperties"}),{__name:"ElementProperties",props:{id:String,type:String},setup(e){const a=e,t=Ne("prefix"),r=m([]),s=m({}),p=m(-1),c=m(!1),u=m(),l=m(),d=m(),g=m(),b=m(),f=()=>window==null?void 0:window.bpmnInstances,w=()=>{var k,C,E,j,P;u.value=f().bpmnElement,l.value=[],d.value=(j=(E=(C=(k=u.value.businessObject)==null?void 0:k.extensionElements)==null?void 0:C.values)==null?void 0:E.filter(D=>(D.$type!==`${t}:Properties`&&l.value.push(D),D.$type===`${t}:Properties`)))!=null?j:[],g.value=d.value.reduce((D,G)=>D.concat(G.values),[]),r.value=JSON.parse(JSON.stringify((P=g.value)!=null?P:[]))},S=(k,C)=>{p.value=C,s.value=C===-1?{}:JSON.parse(JSON.stringify(k)),c.value=!0,ce(()=>{b.value&&b.value.clearValidate()})},v=(k,C)=>{ze.confirm("\u786E\u8BA4\u79FB\u9664\u8BE5\u5C5E\u6027\u5417\uFF1F","\u63D0\u793A",{confirmButtonText:"\u786E \u8BA4",cancelButtonText:"\u53D6 \u6D88"}).then(()=>{r.value.splice(C,1),g.value.splice(C,1);const E=f().moddle.create(`${t}:Properties`,{values:g.value});x(E),w()}).catch(()=>{})},y=()=>{const{name:k,value:C}=s.value;if(p.value!==-1)f().modeling.updateModdleProperties(ae(u.value),ae(g.value)[ae(p.value)],{name:k,value:C});else{const E=f().moddle.create(`${t}:Property`,{name:k,value:C}),j=f().moddle.create(`${t}:Properties`,{values:g.value.concat([E])});x(j)}c.value=!1,w()},x=k=>{const C=f().moddle.create("bpmn:ExtensionElements",{values:l.value.concat([k])});f().modeling.updateProperties(ae(u.value),{extensionElements:C})};return re(()=>a.id,k=>{k&&k&&k.length&&w()},{immediate:!0}),(k,C)=>{const E=Be,j=pe,P=Dt,D=Fe,G=Qe,O=de,U=ue,Y=ye,le=Ce;return h(),R("div",Qp,[i(D,{data:n(r),"max-height":"240",fit:"",border:""},{default:o(()=>[i(E,{label:"\u5E8F\u53F7",width:"50px",type:"index"}),i(E,{label:"\u5C5E\u6027\u540D",prop:"name","min-width":"100px","show-overflow-tooltip":""}),i(E,{label:"\u5C5E\u6027\u503C",prop:"value","min-width":"100px","show-overflow-tooltip":""}),i(E,{label:"\u64CD\u4F5C",width:"110px"},{default:o(H=>[i(j,{link:"",onClick:K=>S(H.row,H.$index),size:"small"},{default:o(()=>[N(" \u7F16\u8F91 ")]),_:2},1032,["onClick"]),i(P,{direction:"vertical"}),i(j,{link:"",size:"small",style:{color:"#ff4d4f"},onClick:K=>v(H.row,H.$index)},{default:o(()=>[N(" \u79FB\u9664 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),F("div",Wp,[i(G,{type:"primary",preIcon:"ep:plus",title:"\u6DFB\u52A0\u5C5E\u6027",onClick:C[0]||(C[0]=H=>S(null,-1))})]),i(le,{modelValue:n(c),"onUpdate:modelValue":C[4]||(C[4]=H=>ne(c)?c.value=H:null),title:"\u5C5E\u6027\u914D\u7F6E",width:"600px","append-to-body":"","destroy-on-close":""},{footer:o(()=>[i(j,{onClick:C[3]||(C[3]=H=>c.value=!1)},{default:o(()=>[N("\u53D6 \u6D88")]),_:1}),i(j,{type:"primary",onClick:y},{default:o(()=>[N("\u786E \u5B9A")]),_:1})]),default:o(()=>[i(Y,{model:n(s),"label-width":"80px",ref_key:"attributeFormRef",ref:b},{default:o(()=>[i(U,{label:"\u5C5E\u6027\u540D\uFF1A",prop:"name"},{default:o(()=>[i(O,{modelValue:n(s).name,"onUpdate:modelValue":C[1]||(C[1]=H=>n(s).name=H),clearable:""},null,8,["modelValue"])]),_:1}),i(U,{label:"\u5C5E\u6027\u503C\uFF1A",prop:"value"},{default:o(()=>[i(O,{modelValue:n(s).value,"onUpdate:modelValue":C[2]||(C[2]=H=>n(s).value=H),clearable:""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}));var eu=Object.defineProperty,tu=Object.defineProperties,au=Object.getOwnPropertyDescriptors,jn=Object.getOwnPropertySymbols,nu=Object.prototype.hasOwnProperty,lu=Object.prototype.propertyIsEnumerable,Dn=(e,a,t)=>a in e?eu(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,Un=(e,a)=>{for(var t in a||(a={}))nu.call(a,t)&&Dn(e,t,a[t]);if(jn)for(var t of jn(a))lu.call(a,t)&&Dn(e,t,a[t]);return e},Mn=(e,a)=>tu(e,au(a)),ea=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const iu={class:"panel-tab__content"},ru={class:"element-drawer__button"},ou={class:"listener-filed__title"},su={class:"element-drawer__button"},pu=te(Mn(Un({},{name:"UserTaskListeners"}),{__name:"UserTaskListeners",props:{id:String,type:String},setup(e){const a=e,t=Ne("prefix"),r=Ne("width"),s=m([]),p=m(_p),c=m(xn),u=m(!1),l=m({}),d=m(kn),g=m([]),b=m(!1),f=m(-1),w=m(-1),S=m({}),v=m(),y=m(),x=m(),k=m(),C=m(),E=()=>window==null?void 0:window.bpmnInstances,j=()=>{var B,_,$;v.value=E().bpmnElement,x.value=[],y.value=($=(_=(B=v.value.businessObject)==null?void 0:B.extensionElements)==null?void 0:_.values.filter(Z=>Z.$type===`${t}:TaskListener`))!=null?$:[],s.value=y.value.map(Z=>wn(Z))},P=(B,_)=>{B?(l.value=hn(B),f.value=_):(l.value={},f.value=-1),B&&B.fields?g.value=B.fields.map($=>Mn(Un({},$),{fieldType:$.string?"string":"expression"})):(g.value=[],l.value.fields=[]),u.value=!0,ce(()=>{k.value&&k.value.clearValidate()})},D=(B,_)=>{ze.confirm("\u786E\u8BA4\u79FB\u9664\u8BE5\u76D1\u542C\u5668\u5417\uFF1F","\u63D0\u793A",{confirmButtonText:"\u786E \u8BA4",cancelButtonText:"\u53D6 \u6D88"}).then(()=>{y.value.splice(_,1),s.value.splice(_,1),nt(v.value,x.value.concat(y.value))}).catch(()=>{})},G=()=>ea(this,null,function*(){var B,_,$,Z;if(!(yield k.value.validate()))return;const Q=Ct(l.value,!0,t);f.value===-1?(y.value.push(Q),s.value.push(l.value)):(y.value.splice(f.value,1,Q),s.value.splice(f.value,1,l.value)),x.value=(Z=($=(_=(B=v.value.businessObject)==null?void 0:B.extensionElements)==null?void 0:_.values)==null?void 0:$.filter(V=>V.$type!==`${t}:TaskListener`))!=null?Z:[],nt(v.value,x.value.concat(y.value)),u.value=!1,l.value={}}),O=(B,_)=>{S.value=B?JSON.parse(JSON.stringify(B)):{},w.value=B?_:-1,b.value=!0,ce(()=>{C.value&&C.value.clearValidate()})},U=()=>ea(this,null,function*(){(yield C.value.validate())&&(w.value===-1?(g.value.push(S.value),l.value.fields.push(S.value)):(g.value.splice(w.value,1,S.value),l.value.fields.splice(w.value,1,S.value)),b.value=!1,ce(()=>{S.value={}}))}),Y=(B,_)=>{ze.confirm("\u786E\u8BA4\u79FB\u9664\u8BE5\u5B57\u6BB5\u5417\uFF1F","\u63D0\u793A",{confirmButtonText:"\u786E \u8BA4",cancelButtonText:"\u53D6 \u6D88"}).then(()=>{g.value.splice(_,1),l.value.fields.splice(_,1)}).catch(()=>{})},le=m(),H=()=>ea(this,null,function*(){le.value.open("task")}),K=B=>{var _,$,Z,Q;const V=Sn(B),A=Ct(V,!0,t);y.value.push(A),s.value.push(V),x.value=(Q=(Z=($=(_=v.value.businessObject)==null?void 0:_.extensionElements)==null?void 0:$.values)==null?void 0:Z.filter(T=>T.$type!==`${t}:TaskListener`))!=null?Q:[],nt(v.value,x.value.concat(y.value))};return re(()=>a.id,B=>{B&&B.length&&ce(()=>{j()})},{immediate:!0}),(B,_)=>{const $=Be,Z=pe,Q=Dt,V=Fe,A=Qe,T=Se,M=xe,z=ue,X=de,ge=ye,be=Me,Ee=Ut,L=vt;return h(),R(W,null,[F("div",iu,[i(V,{data:n(s),size:"small",border:""},{default:o(()=>[i($,{label:"\u5E8F\u53F7",width:"50px",type:"index"}),i($,{label:"\u4E8B\u4EF6\u7C7B\u578B","min-width":"80px","show-overflow-tooltip":"",formatter:q=>n(p)[q.event]},null,8,["formatter"]),i($,{label:"\u4E8B\u4EF6id","min-width":"80px",prop:"id","show-overflow-tooltip":""}),i($,{label:"\u76D1\u542C\u5668\u7C7B\u578B","min-width":"80px","show-overflow-tooltip":"",formatter:q=>n(c)[q.listenerType]},null,8,["formatter"]),i($,{label:"\u64CD\u4F5C",width:"90px"},{default:o(q=>[i(Z,{size:"small",link:"",onClick:lt=>P(q.row,q.$index)},{default:o(()=>[N("\u7F16\u8F91")]),_:2},1032,["onClick"]),i(Q,{direction:"vertical"}),i(Z,{size:"small",link:"",style:{color:"#ff4d4f"},onClick:lt=>D(q.row,q.$index)},{default:o(()=>[N("\u79FB\u9664")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),F("div",ru,[i(A,{size:"small",type:"primary",preIcon:"ep:plus",title:"\u6DFB\u52A0\u76D1\u542C\u5668",onClick:_[0]||(_[0]=q=>P(null))}),i(A,{type:"success",preIcon:"ep:select",title:"\u9009\u62E9\u76D1\u542C\u5668",size:"small",onClick:H})]),i(Ee,{modelValue:n(u),"onUpdate:modelValue":_[15]||(_[15]=q=>ne(u)?u.value=q:null),title:"\u4EFB\u52A1\u76D1\u542C\u5668",size:`${n(r)}px`,"append-to-body":"","destroy-on-close":""},{default:o(()=>[i(ge,{size:"small",model:n(l),"label-width":"96px",ref_key:"listenerFormRef",ref:k},{default:o(()=>[i(z,{label:"\u4E8B\u4EF6\u7C7B\u578B",prop:"event",rules:{required:!0,trigger:["blur","change"]}},{default:o(()=>[i(M,{modelValue:n(l).event,"onUpdate:modelValue":_[1]||(_[1]=q=>n(l).event=q)},{default:o(()=>[(h(!0),R(W,null,se(Object.keys(n(p)),q=>(h(),I(T,{key:q,label:n(p)[q],value:q},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),i(z,{label:"\u76D1\u542C\u5668ID",prop:"id",rules:{required:!0,trigger:["blur","change"]}},{default:o(()=>[i(X,{modelValue:n(l).id,"onUpdate:modelValue":_[2]||(_[2]=q=>n(l).id=q),clearable:""},null,8,["modelValue"])]),_:1}),i(z,{label:"\u76D1\u542C\u5668\u7C7B\u578B",prop:"listenerType",rules:{required:!0,trigger:["blur","change"]}},{default:o(()=>[i(M,{modelValue:n(l).listenerType,"onUpdate:modelValue":_[3]||(_[3]=q=>n(l).listenerType=q)},{default:o(()=>[(h(!0),R(W,null,se(Object.keys(n(c)),q=>(h(),I(T,{key:q,label:n(c)[q],value:q},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(l).listenerType==="classListener"?(h(),I(z,{label:"Java\u7C7B",prop:"class",key:"listener-class",rules:{required:!0,trigger:["blur","change"]}},{default:o(()=>[i(X,{modelValue:n(l).class,"onUpdate:modelValue":_[4]||(_[4]=q=>n(l).class=q),clearable:""},null,8,["modelValue"])]),_:1})):J("",!0),n(l).listenerType==="expressionListener"?(h(),I(z,{label:"\u8868\u8FBE\u5F0F",prop:"expression",key:"listener-expression",rules:{required:!0,trigger:["blur","change"]}},{default:o(()=>[i(X,{modelValue:n(l).expression,"onUpdate:modelValue":_[5]||(_[5]=q=>n(l).expression=q),clearable:""},null,8,["modelValue"])]),_:1})):J("",!0),n(l).listenerType==="delegateExpressionListener"?(h(),I(z,{label:"\u4EE3\u7406\u8868\u8FBE\u5F0F",prop:"delegateExpression",key:"listener-delegate",rules:{required:!0,trigger:["blur","change"]}},{default:o(()=>[i(X,{modelValue:n(l).delegateExpression,"onUpdate:modelValue":_[6]||(_[6]=q=>n(l).delegateExpression=q),clearable:""},null,8,["modelValue"])]),_:1})):J("",!0),n(l).listenerType==="scriptListener"?(h(),R(W,{key:3},[i(z,{label:"\u811A\u672C\u683C\u5F0F",prop:"scriptFormat",key:"listener-script-format",rules:{required:!0,trigger:["blur","change"],message:"\u8BF7\u586B\u5199\u811A\u672C\u683C\u5F0F"}},{default:o(()=>[i(X,{modelValue:n(l).scriptFormat,"onUpdate:modelValue":_[7]||(_[7]=q=>n(l).scriptFormat=q),clearable:""},null,8,["modelValue"])]),_:1}),i(z,{label:"\u811A\u672C\u7C7B\u578B",prop:"scriptType",key:"listener-script-type",rules:{required:!0,trigger:["blur","change"],message:"\u8BF7\u9009\u62E9\u811A\u672C\u7C7B\u578B"}},{default:o(()=>[i(M,{modelValue:n(l).scriptType,"onUpdate:modelValue":_[8]||(_[8]=q=>n(l).scriptType=q)},{default:o(()=>[i(T,{label:"\u5185\u8054\u811A\u672C",value:"inlineScript"}),i(T,{label:"\u5916\u90E8\u811A\u672C",value:"externalScript"})]),_:1},8,["modelValue"])]),_:1}),n(l).scriptType==="inlineScript"?(h(),I(z,{label:"\u811A\u672C\u5185\u5BB9",prop:"value",key:"listener-script",rules:{required:!0,trigger:["blur","change"],message:"\u8BF7\u586B\u5199\u811A\u672C\u5185\u5BB9"}},{default:o(()=>[i(X,{modelValue:n(l).value,"onUpdate:modelValue":_[9]||(_[9]=q=>n(l).value=q),clearable:""},null,8,["modelValue"])]),_:1})):J("",!0),n(l).scriptType==="externalScript"?(h(),I(z,{label:"\u8D44\u6E90\u5730\u5740",prop:"resource",key:"listener-resource",rules:{required:!0,trigger:["blur","change"],message:"\u8BF7\u586B\u5199\u8D44\u6E90\u5730\u5740"}},{default:o(()=>[i(X,{modelValue:n(l).resource,"onUpdate:modelValue":_[10]||(_[10]=q=>n(l).resource=q),clearable:""},null,8,["modelValue"])]),_:1})):J("",!0)],64)):J("",!0),n(l).event==="timeout"?(h(),R(W,{key:4},[i(z,{label:"\u5B9A\u65F6\u5668\u7C7B\u578B",prop:"eventDefinitionType",key:"eventDefinitionType"},{default:o(()=>[i(M,{modelValue:n(l).eventDefinitionType,"onUpdate:modelValue":_[11]||(_[11]=q=>n(l).eventDefinitionType=q)},{default:o(()=>[i(T,{label:"\u65E5\u671F",value:"date"}),i(T,{label:"\u6301\u7EED\u65F6\u957F",value:"duration"}),i(T,{label:"\u5FAA\u73AF",value:"cycle"}),i(T,{label:"\u65E0",value:"null"})]),_:1},8,["modelValue"])]),_:1}),n(l).eventDefinitionType&&n(l).eventDefinitionType!=="null"?(h(),I(z,{label:"\u5B9A\u65F6\u5668",prop:"eventTimeDefinitions",key:"eventTimeDefinitions",rules:{required:!0,trigger:["blur","change"],message:"\u8BF7\u586B\u5199\u5B9A\u65F6\u5668\u914D\u7F6E"}},{default:o(()=>[i(X,{modelValue:n(l).eventTimeDefinitions,"onUpdate:modelValue":_[12]||(_[12]=q=>n(l).eventTimeDefinitions=q),clearable:""},null,8,["modelValue"])]),_:1})):J("",!0)],64)):J("",!0)]),_:1},8,["model"]),i(Q),F("p",ou,[F("span",null,[i(be,{icon:"ep:menu"}),N("\u6CE8\u5165\u5B57\u6BB5\uFF1A")]),i(Z,{size:"small",type:"primary",onClick:_[13]||(_[13]=q=>O(null))},{default:o(()=>[N("\u6DFB\u52A0\u5B57\u6BB5")]),_:1})]),i(V,{data:n(g),size:"small","max-height":"240",fit:"",border:"",style:{flex:"none"}},{default:o(()=>[i($,{label:"\u5E8F\u53F7",width:"50px",type:"index"}),i($,{label:"\u5B57\u6BB5\u540D\u79F0","min-width":"100px",prop:"name"}),i($,{label:"\u5B57\u6BB5\u7C7B\u578B","min-width":"80px","show-overflow-tooltip":"",formatter:q=>n(d)[q.fieldType]},null,8,["formatter"]),i($,{label:"\u5B57\u6BB5\u503C/\u8868\u8FBE\u5F0F","min-width":"100px","show-overflow-tooltip":"",formatter:q=>q.string||q.expression},null,8,["formatter"]),i($,{label:"\u64CD\u4F5C",width:"100px"},{default:o(q=>[i(Z,{size:"small",link:"",onClick:lt=>O(q.row,q.$index)},{default:o(()=>[N("\u7F16\u8F91")]),_:2},1032,["onClick"]),i(Q,{direction:"vertical"}),i(Z,{size:"small",link:"",style:{color:"#ff4d4f"},onClick:lt=>Y(q.row,q.$index)},{default:o(()=>[N("\u79FB\u9664")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),F("div",su,[i(Z,{size:"small",onClick:_[14]||(_[14]=q=>u.value=!1)},{default:o(()=>[N("\u53D6 \u6D88")]),_:1}),i(Z,{size:"small",type:"primary",onClick:G},{default:o(()=>[N("\u4FDD \u5B58")]),_:1})])]),_:1},8,["modelValue","size"]),i(L,{title:"\u5B57\u6BB5\u914D\u7F6E",modelValue:n(b),"onUpdate:modelValue":_[21]||(_[21]=q=>ne(b)?b.value=q:null),width:"600px","append-to-body":"","destroy-on-close":""},{footer:o(()=>[i(Z,{size:"small",onClick:_[20]||(_[20]=q=>b.value=!1)},{default:o(()=>[N("\u53D6 \u6D88")]),_:1}),i(Z,{size:"small",type:"primary",onClick:U},{default:o(()=>[N("\u786E \u5B9A")]),_:1})]),default:o(()=>[i(ge,{model:n(S),size:"small","label-width":"96px",ref_key:"listenerFieldFormRef",ref:C,style:{height:"136px"}},{default:o(()=>[i(z,{label:"\u5B57\u6BB5\u540D\u79F0\uFF1A",prop:"name",rules:{required:!0,trigger:["blur","change"]}},{default:o(()=>[i(X,{modelValue:n(S).name,"onUpdate:modelValue":_[16]||(_[16]=q=>n(S).name=q),clearable:""},null,8,["modelValue"])]),_:1}),i(z,{label:"\u5B57\u6BB5\u7C7B\u578B\uFF1A",prop:"fieldType",rules:{required:!0,trigger:["blur","change"]}},{default:o(()=>[i(M,{modelValue:n(S).fieldType,"onUpdate:modelValue":_[17]||(_[17]=q=>n(S).fieldType=q)},{default:o(()=>[(h(!0),R(W,null,se(Object.keys(n(d)),q=>(h(),I(T,{key:q,label:n(d)[q],value:q},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(S).fieldType==="string"?(h(),I(z,{label:"\u5B57\u6BB5\u503C\uFF1A",prop:"string",key:"field-string",rules:{required:!0,trigger:["blur","change"]}},{default:o(()=>[i(X,{modelValue:n(S).string,"onUpdate:modelValue":_[18]||(_[18]=q=>n(S).string=q),clearable:""},null,8,["modelValue"])]),_:1})):J("",!0),n(S).fieldType==="expression"?(h(),I(z,{label:"\u8868\u8FBE\u5F0F\uFF1A",prop:"expression",key:"field-expression",rules:{required:!0,trigger:["blur","change"]}},{default:o(()=>[i(X,{modelValue:n(S).expression,"onUpdate:modelValue":_[19]||(_[19]=q=>n(S).expression=q),clearable:""},null,8,["modelValue"])]),_:1})):J("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue"])]),i(Pn,{ref_key:"processListenerDialogRef",ref:le,onSelect:K},null,512)],64)}}}));var uu=Object.defineProperty,du=Object.defineProperties,cu=Object.getOwnPropertyDescriptors,Nn=Object.getOwnPropertySymbols,mu=Object.prototype.hasOwnProperty,yu=Object.prototype.propertyIsEnumerable,Bn=(e,a,t)=>a in e?uu(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,vu=(e,a)=>{for(var t in a||(a={}))mu.call(a,t)&&Bn(e,t,a[t]);if(Nn)for(var t of Nn(a))yu.call(a,t)&&Bn(e,t,a[t]);return e},fu=(e,a)=>du(e,cu(a));const _t=te(fu(vu({},{name:"MyPropertiesPanel"}),{__name:"PropertiesPanel",props:{bpmnModeler:{type:Object,default:()=>{}},prefix:{type:String,default:"camunda"},width:{type:Number,default:480},idEditDisabled:{type:Boolean,default:!1},model:Object},setup(e){const a=e,t=m("base"),r=m(""),s=m(""),p=m({}),c=m(!1),u=m(!1),l=m();yt("prefix",a.prefix),yt("width",a.width);const d=()=>window==null?void 0:window.bpmnInstances,g=re(()=>a.bpmnModeler,()=>{if(!a.bpmnModeler)return;const w=window;w.bpmnInstances={modeler:a.bpmnModeler,modeling:a.bpmnModeler.get("modeling"),moddle:a.bpmnModeler.get("moddle"),eventBus:a.bpmnModeler.get("eventBus"),bpmnFactory:a.bpmnModeler.get("bpmnFactory"),elementFactory:a.bpmnModeler.get("elementFactory"),elementRegistry:a.bpmnModeler.get("elementRegistry"),replace:a.bpmnModeler.get("replace"),selection:a.bpmnModeler.get("selection")},b(),g()},{immediate:!0}),b=()=>{f(null),a.bpmnModeler.on("import.done",w=>{f(null)}),a.bpmnModeler.on("selection.changed",({newSelection:w})=>{f(w[0]||null)}),a.bpmnModeler.on("element.changed",({element:w})=>{w&&w.id===r.value&&f(w)})},f=w=>{var S;let v=w;v||(v=(S=d().elementRegistry.find(y=>y.type==="bpmn:Process"))!=null?S:d().elementRegistry.find(y=>y.type==="bpmn:Collaboration")),v&&(d().bpmnElement=v,l.value=v,r.value=v.id,s.value=v.type.split(":")[1]||"",p.value=JSON.parse(JSON.stringify(v.businessObject)),c.value=!!(s.value==="SequenceFlow"&&v.source&&v.source.type.indexOf("StartEvent")===-1),u.value=s.value==="UserTask"||s.value==="StartEvent")};return Ae(()=>{const w=window;w.bpmnInstances=null}),re(()=>r.value,()=>{t.value="base"}),(w,S)=>{const v=Me,y=ui,x=wo,k=di;return h(),R("div",{class:"process-panel__container",style:fa({width:`${e.width}px`})},[i(k,{modelValue:n(t),"onUpdate:modelValue":S[0]||(S[0]=C=>ne(t)?t.value=C:null)},{default:o(()=>[i(y,{name:"base"},{title:o(()=>[i(v,{icon:"ep:info-filled"}),N(" \u5E38\u89C4")]),default:o(()=>[i(To,{"id-edit-disabled":e.idEditDisabled,"business-object":n(p),type:n(s),model:e.model},null,8,["id-edit-disabled","business-object","type","model"])]),_:1}),n(s)==="Process"?(h(),I(y,{name:"condition",key:"message"},{title:o(()=>[i(v,{icon:"ep:comment"}),N("\u6D88\u606F\u4E0E\u4FE1\u53F7")]),default:o(()=>[i(fp)]),_:1})):J("",!0),n(c)?(h(),I(y,{name:"condition",key:"condition"},{title:o(()=>[i(v,{icon:"ep:promotion"}),N("\u6D41\u8F6C\u6761\u4EF6")]),default:o(()=>[i(rp,{"business-object":n(p),type:n(s)},null,8,["business-object","type"])]),_:1})):J("",!0),n(u)?(h(),I(y,{name:"condition",key:"form"},{title:o(()=>[i(v,{icon:"ep:list"}),N("\u8868\u5355")]),default:o(()=>[i(x,{id:n(r),type:n(s)},null,8,["id","type"])]),_:1})):J("",!0),n(s).indexOf("Task")!==-1?(h(),I(y,{name:"task",key:"task"},{title:o(()=>[i(v,{icon:"ep:checked"}),N("\u4EFB\u52A1")]),default:o(()=>[i(Gs,{id:n(r),type:n(s)},null,8,["id","type"])]),_:1})):J("",!0),n(s).indexOf("Task")!==-1&&n(s)!=="ServiceTask"?(h(),I(y,{name:"multiInstance",key:"multiInstance"},{title:o(()=>[i(v,{icon:"ep:help-filled"}),N("\u591A\u5B9E\u4F8B\uFF08\u4F1A\u7B7E\u914D\u7F6E\uFF09")]),default:o(()=>[i(Xs,{"business-object":n(p),type:n(s)},null,8,["business-object","type"])]),_:1})):J("",!0),i(y,{name:"listeners",key:"listeners"},{title:o(()=>[i(v,{icon:"ep:bell-filled"}),N("\u6267\u884C\u76D1\u542C\u5668")]),default:o(()=>[i(zp,{id:n(r),type:n(s)},null,8,["id","type"])]),_:1}),n(s)==="UserTask"?(h(),I(y,{name:"taskListeners",key:"taskListeners"},{title:o(()=>[i(v,{icon:"ep:bell-filled"}),N("\u4EFB\u52A1\u76D1\u542C\u5668")]),default:o(()=>[i(pu,{id:n(r),type:n(s)},null,8,["id","type"])]),_:1})):J("",!0),i(y,{name:"extensions",key:"extensions"},{title:o(()=>[i(v,{icon:"ep:circle-plus-filled"}),N("\u6269\u5C55\u5C5E\u6027")]),default:o(()=>[i(Xp,{id:n(r),type:n(s)},null,8,["id","type"])]),_:1}),i(y,{name:"other",key:"other"},{title:o(()=>[i(v,{icon:"ep:promotion"}),N("\u5176\u4ED6")]),default:o(()=>[i(Ro,{id:n(r)},null,8,["id"])]),_:1})]),_:1},8,["modelValue"])],4)}}}));_t.install=function(e){e.component(_t.name,_t)};const bu=e=>ie.get({url:"/bpm/activity/list?processInstanceId="+e});var gu=Object.defineProperty,hu=Object.defineProperties,wu=Object.getOwnPropertyDescriptors,Fn=Object.getOwnPropertySymbols,Su=Object.prototype.hasOwnProperty,xu=Object.prototype.propertyIsEnumerable,Ln=(e,a,t)=>a in e?gu(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,ku=(e,a)=>{for(var t in a||(a={}))Su.call(a,t)&&Ln(e,t,a[t]);if(Fn)for(var t of Fn(a))xu.call(a,t)&&Ln(e,t,a[t]);return e},Cu=(e,a)=>hu(e,wu(a)),ta=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const _u={class:"my-process-designer"},Pu={class:"my-process-designer__container"},ut=te(Cu(ku({},{name:"MyProcessViewer"}),{__name:"ProcessViewer",props:{value:{type:String,default:""},prefix:{type:String,default:"flowable"},activityData:{type:Array,default:()=>[]},processInstanceData:{type:Object,default:()=>{}},taskData:{type:Array,default:()=>[]}},emits:["destroy"],setup(e,{expose:a,emit:t}){const r=e;yt("configGlobal",r);const s=t;let p;const c=m(""),u=m([]),l=m(void 0),d=m([]),g=m(),b=m(null),f=m(null),w=()=>{p||(p=new Vi({container:g.value,bpmnRenderer:{}}))},S=P=>ta(this,null,function*(){let D=`Process_${new Date().getTime()}`,G=`\u4E1A\u52A1\u6D41\u7A0B_${new Date().getTime()}`,O=P||Ua(D,G,r.prefix);if(!(!O||!p))try{let{warnings:U}=yield p.importXML(O);U&&U.length&&U.forEach(Y=>{}),yield v(),p.get("canvas").zoom("fit-viewport","auto")}catch{}}),v=()=>ta(this,null,function*(){var P,D;u.value=yield bu((P=r.processInstanceData)==null?void 0:P.id);const G=u.value;if(G.length===0)return;let O=p.get("canvas"),U=G.find(K=>!K.endTime),Y=G[G.length-1],le=!1,H=[];(D=p.getDefinitions().rootElements[0].flowElements)==null||D.forEach(K=>{var B,_,$;let Z=G.find(Q=>Q.key===K.id);if(Z){if(K.$type==="bpmn:UserTask"){const Q=d.value.find(A=>A.id===Z.taskId);if(!Q)return;if(le){H.push(K.id);return}if(O.addMarker(K.id,x(Q.status)),Q.status===1&&(le=!0),Q.status!==2)return;const V=k(Z);V==null||V.forEach(A=>{let T=G.find(M=>M.key===A.targetRef.id);T?O.addMarker(A.id,T.endTime?"highlight":"highlight-todo"):A.targetRef.$type==="bpmn:ExclusiveGateway"?(O.addMarker(A.id,Z.endTime?"highlight":"highlight-todo"),O.addMarker(A.targetRef.id,Z.endTime?"highlight":"highlight-todo")):A.targetRef.$type==="bpmn:EndEvent"&&(!U&&Y.key===K.id&&(O.addMarker(A.id,"highlight"),O.addMarker(A.targetRef.id,"highlight")),Z.endTime||(O.addMarker(A.id,"highlight-todo"),O.addMarker(A.targetRef.id,"highlight-todo")))})}else if(K.$type==="bpmn:ExclusiveGateway"){O.addMarker(K.id,y(Z));let Q,V;(B=K.outgoing)==null||B.forEach(A=>{let T=G.find(M=>M.key===A.targetRef.id);T&&(!V||V.type==="endEvent")&&(Q=A,V=T)}),Q&&V&&O.addMarker(Q.id,y(V))}else if(K.$type==="bpmn:ParallelGateway")O.addMarker(K.id,y(Z)),(_=K.outgoing)==null||_.forEach(Q=>{const V=G.find(A=>A.key===Q.targetRef.id);V&&(O.addMarker(Q.id,y(V)),O.addMarker(Q.targetRef.id,y(V)))});else if(K.$type==="bpmn:StartEvent")O.addMarker(K.id,"highlight"),($=K.outgoing)==null||$.forEach(Q=>{G.find(V=>V.key===Q.targetRef.id)&&(O.addMarker(Q.id,"highlight"),O.addMarker(K.id,"highlight"))});else if(K.$type==="bpmn:EndEvent"){if(!l.value||l.value.status===1)return;O.addMarker(K.id,x(l.value.status))}else if(K.$type==="bpmn:ServiceTask"){if(Z.startTime>0&&Z.endTime===0&&O.addMarker(K.id,x(1)),Z.endTime>0){O.addMarker(K.id,x(2));const Q=k(Z);Q==null||Q.forEach(V=>{O.addMarker(V.id,x(2))})}}else if(K.$type==="bpmn:SequenceFlow"){let Q=G.find(V=>V.key===K.targetRef.id);Q&&O.addMarker(K.id,y(Q))}}}),Ie(H)||(d.value=d.value.filter(K=>!H.includes(K.taskDefinitionKey)))}),y=P=>P.endTime?"highlight":"highlight-todo",x=P=>P===1?"highlight-todo":P===2?"highlight":P===3?"highlight-reject":P===4?"highlight-cancel":P===5?"highlight-return":P===6||P===7||P===0?"highlight-todo":"",k=P=>{if(P.outgoing&&P.outgoing.length>0)return P.outgoing;const D=p.getDefinitions().rootElements[0].flowElements,G=[];return D.forEach(O=>{O.$type==="bpmn:SequenceFlow"&&O.sourceRef.id===P.key&&G.push(O)}),G},C=()=>{const P=p.get("eventBus");P.on("element.hover",function(D){let G=D?D.element:null;E(G)}),P.on("element.out",function(D){let G=D?D.element:null;j(G)})},E=P=>{var D;P.value=P,!b.value&&(b.value={}),!f.value&&(f.value=p.get("overlays"));const G=u.value.find(O=>O.key===P.value.id);if(G&&!b.value[P.value.id]&&P.value.type!=="bpmn:Process"){let O=`<div class="element-overlays">
            <p>Elemet id: ${P.value.id}</p>
            <p>Elemet type: ${P.value.type}</p>
          </div>`;if(P.value.type==="bpmn:StartEvent"&&l.value)O=`<p>\u53D1\u8D77\u4EBA\uFF1A${l.value.startUser.nickname}</p>
                  <p>\u90E8\u95E8\uFF1A${l.value.startUser.deptName}</p>
                  <p>\u521B\u5EFA\u65F6\u95F4\uFF1A${ke(l.value.createTime)}`;else if(P.value.type==="bpmn:UserTask"){let U=d.value.find(H=>H.id===G.taskId);if(!U)return;let Y=rt(Oe.BPM_TASK_STATUS),le="";Y.forEach(H=>{H.value==U.status&&(le=H.label)}),O=`<p>\u5BA1\u6279\u4EBA\uFF1A${U.assigneeUser.nickname}</p>
                  <p>\u90E8\u95E8\uFF1A${U.assigneeUser.deptName}</p>
                  <p>\u7ED3\u679C\uFF1A${le}</p>
                  <p>\u521B\u5EFA\u65F6\u95F4\uFF1A${ke(U.createTime)}</p>`,U.endTime&&(O+=`<p>\u7ED3\u675F\u65F6\u95F4\uFF1A${ke(U.endTime)}</p>`),U.reason&&(O+=`<p>\u5BA1\u6279\u5EFA\u8BAE\uFF1A${U.reason}</p>`)}else if(P.value.type==="bpmn:ServiceTask"&&l.value)G.startTime>0&&(O=`<p>\u521B\u5EFA\u65F6\u95F4\uFF1A${ke(G.startTime)}</p>`),G.endTime>0&&(O+=`<p>\u7ED3\u675F\u65F6\u95F4\uFF1A${ke(G.endTime)}</p>`);else if(P.value.type==="bpmn:EndEvent"&&l.value){let U=rt(Oe.BPM_TASK_STATUS),Y="";U.forEach(le=>{le.value==l.value.status&&(Y=le.label)}),O=`<p>\u7ED3\u679C\uFF1A${Y}</p>`,l.value.endTime&&(O+=`<p>\u7ED3\u675F\u65F6\u95F4\uFF1A${ke(l.value.endTime)}</p>`)}b.value[P.value.id]=(D=ae(f.value))==null?void 0:D.add(P.value,{position:{left:0,bottom:0},html:`<div class="element-overlays">${O}</div>`})}},j=P=>{ae(f.value).remove({element:P}),b.value[P.id]=null};return a({refreshBpm:()=>ta(this,null,function*(){c.value=r.value,w(),S(c.value),C()})}),Ae(()=>{p&&p.destroy(),s("destroy",p),p=null}),re(()=>r.value,P=>{c.value=P,S(c.value)}),re(()=>r.activityData,P=>{u.value=P,S(c.value)}),re(()=>r.processInstanceData,P=>{l.value=P,S(c.value)}),re(()=>r.taskData,P=>{d.value=P,S(c.value)}),(P,D)=>(h(),R("div",_u,[F("div",Pu,[F("div",{class:"my-process-designer__canvas",style:{height:"760px"},ref_key:"bpmnCanvas",ref:g},null,512)])]))}}));ut.install=function(e){e.component(ut.name,ut)};function aa(e,a,t,r,s,p,c,u,l,d,g,b){e=e||{},r.registerProvider(this),this._contextPad=r,this._modeling=s,this._elementFactory=p,this._connect=c,this._create=u,this._popupMenu=l,this._canvas=d,this._rules=g,this._translate=b,e.autoPlace!==!1&&(this._autoPlace=a.get("autoPlace",!1)),t.on("create.end",250,function(f){const w=f.context,S=w.shape;if(!Ui(f)||!r.isOpen(S))return;const v=r.getEntries(S);v.replace&&v.replace.action.click(f,S)})}aa.$inject=["config.contextPad","injector","eventBus","contextPad","modeling","elementFactory","connect","create","popupMenu","canvas","rules","translate","elementRegistry"],aa.prototype.getContextPadEntries=function(e){const a=this._contextPad,t=this._modeling,r=this._elementFactory,s=this._connect,p=this._create,c=this._popupMenu,u=this._canvas,l=this._rules,d=this._autoPlace,g=this._translate,b={};if(e.type==="label")return b;const f=e.businessObject;function w(C,E){s.start(C,E)}function S(){t.removeElements([e])}function v(C){const E=u.getContainer(),j=a.getPad(C).html,P=E.getBoundingClientRect(),D=j.getBoundingClientRect(),G=D.top-P.top;return{x:D.left-P.left,y:G+D.height+5}}function y(C,E,j,P){typeof j!="string"&&(P=j,j=g("Append {type}",{type:C.replace(/^bpmn:/,"")}));function D(G,O){const U=r.createShape(fe({type:C},P));p.start(G,U,{source:O})}return{group:"model",className:E,title:j,action:{dragstart:D,click:d?function(G,O){const U=r.createShape(fe({type:C},P));d.append(O,U)}:D}}}function x(C){return function(E,j){t.splitLane(j,C),a.open(j,!0)}}if(Bt(f,["bpmn:Lane","bpmn:Participant"])&&Mi(f)){const C=Ni(e);fe(b,{"lane-insert-above":{group:"lane-insert-above",className:"bpmn-icon-lane-insert-above",title:g("Add Lane above"),action:{click:function(E,j){t.addLane(j,"top")}}}}),C.length<2&&(e.height>=120&&fe(b,{"lane-divide-two":{group:"lane-divide",className:"bpmn-icon-lane-divide-two",title:g("Divide into two Lanes"),action:{click:x(2)}}}),e.height>=180&&fe(b,{"lane-divide-three":{group:"lane-divide",className:"bpmn-icon-lane-divide-three",title:g("Divide into three Lanes"),action:{click:x(3)}}})),fe(b,{"lane-insert-below":{group:"lane-insert-below",className:"bpmn-icon-lane-insert-below",title:g("Add Lane below"),action:{click:function(E,j){t.addLane(j,"bottom")}}}})}bt(f,"bpmn:FlowNode")&&(bt(f,"bpmn:EventBasedGateway")?fe(b,{"append.receive-task":y("bpmn:ReceiveTask","bpmn-icon-receive-task",g("Append ReceiveTask")),"append.message-intermediate-event":y("bpmn:IntermediateCatchEvent","bpmn-icon-intermediate-event-catch-message",g("Append MessageIntermediateCatchEvent"),{eventDefinitionType:"bpmn:MessageEventDefinition"}),"append.timer-intermediate-event":y("bpmn:IntermediateCatchEvent","bpmn-icon-intermediate-event-catch-timer",g("Append TimerIntermediateCatchEvent"),{eventDefinitionType:"bpmn:TimerEventDefinition"}),"append.condition-intermediate-event":y("bpmn:IntermediateCatchEvent","bpmn-icon-intermediate-event-catch-condition",g("Append ConditionIntermediateCatchEvent"),{eventDefinitionType:"bpmn:ConditionalEventDefinition"}),"append.signal-intermediate-event":y("bpmn:IntermediateCatchEvent","bpmn-icon-intermediate-event-catch-signal",g("Append SignalIntermediateCatchEvent"),{eventDefinitionType:"bpmn:SignalEventDefinition"})}):$n(f,"bpmn:BoundaryEvent","bpmn:CompensateEventDefinition")?fe(b,{"append.compensation-activity":y("bpmn:Task","bpmn-icon-task",g("Append compensation activity"),{isForCompensation:!0})}):!bt(f,"bpmn:EndEvent")&&!f.isForCompensation&&!$n(f,"bpmn:IntermediateThrowEvent","bpmn:LinkEventDefinition")&&!Ii(f)&&fe(b,{"append.end-event":y("bpmn:EndEvent","bpmn-icon-end-event-none",g("Append EndEvent")),"append.gateway":y("bpmn:ExclusiveGateway","bpmn-icon-gateway-none",g("Append Gateway")),"append.append-task":y("bpmn:UserTask","bpmn-icon-user-task",g("Append Task")),"append.intermediate-event":y("bpmn:IntermediateThrowEvent","bpmn-icon-intermediate-event-none",g("Append Intermediate/Boundary Event"))})),c.isEmpty(e,"bpmn-replace")||fe(b,{replace:{group:"edit",className:"bpmn-icon-screw-wrench",title:"\u4FEE\u6539\u7C7B\u578B",action:{click:function(C,E){const j=fe(v(E),{cursor:{x:C.x,y:C.y}});c.open(E,"bpmn-replace",j)}}}}),Bt(f,["bpmn:FlowNode","bpmn:InteractionNode","bpmn:DataObjectReference","bpmn:DataStoreReference"])&&fe(b,{"append.text-annotation":y("bpmn:TextAnnotation","bpmn-icon-text-annotation"),connect:{group:"connect",className:"bpmn-icon-connection-multi",title:g("Connect using "+(f.isForCompensation?"":"Sequence/MessageFlow or ")+"Association"),action:{click:w,dragstart:w}}}),Bt(f,["bpmn:DataObjectReference","bpmn:DataStoreReference"])&&fe(b,{connect:{group:"connect",className:"bpmn-icon-connection-multi",title:g("Connect using DataInputAssociation"),action:{click:w,dragstart:w}}}),bt(f,"bpmn:Group")&&fe(b,{"append.text-annotation":y("bpmn:TextAnnotation","bpmn-icon-text-annotation")});let k=l.allowed("elements.delete",{elements:[e]});return ji(k)&&(k=k[0]===e),k&&fe(b,{delete:{group:"edit",className:"bpmn-icon-trash",title:g("Remove"),action:{click:S}}}),b};function $n(e,a,t){const r=e.$instanceOf(a);let s=!1;const p=e.eventDefinitions||[];return Di(p,function(c){c.$type===t&&(s=!0)}),r&&s}const Eu={__init__:["contextPadProvider"],contextPadProvider:["type",aa]};function dt(e,a,t,r,s,p,c,u){Pa.call(this,e,a,t,r,s,p,c,u,2e3)}const na=function(){};na.prototype=Pa.prototype,na.prototype.getPaletteEntries=function(){const e={},a=this._create,t=this._elementFactory,r=this._spaceTool,s=this._lassoTool,p=this._handTool,c=this._globalConnect,u=this._translate;function l(b,f,w,S,v){function y(k){const C=t.createShape(fe({type:b},v));a.start(k,C)}const x=b.replace(/^bpmn:/,"");return{group:f,className:w,title:S||u("Create {type}",{type:x}),action:{dragstart:y,click:y}}}function d(b){const f=t.createShape({type:"bpmn:SubProcess",x:0,y:0,isExpanded:!0}),w=t.createShape({type:"bpmn:StartEvent",x:40,y:82,parent:f});a.start(b,[f,w],{hints:{autoSelect:[w]}})}function g(b){a.start(b,t.createParticipantShape())}return fe(e,{"hand-tool":{group:"tools",className:"bpmn-icon-hand-tool",title:"\u6FC0\u6D3B\u6293\u624B\u5DE5\u5177",action:{click:function(b){p.activateHand(b)}}},"lasso-tool":{group:"tools",className:"bpmn-icon-lasso-tool",title:u("Activate the lasso tool"),action:{click:function(b){s.activateSelection(b)}}},"space-tool":{group:"tools",className:"bpmn-icon-space-tool",title:u("Activate the create/remove space tool"),action:{click:function(b){r.activateSelection(b)}}},"global-connect-tool":{group:"tools",className:"bpmn-icon-connection-multi",title:u("Activate the global connect tool"),action:{click:function(b){c.toggle(b)}}},"tool-separator":{group:"tools",separator:!0},"create.start-event":l("bpmn:StartEvent","event","bpmn-icon-start-event-none",u("Create StartEvent")),"create.intermediate-event":l("bpmn:IntermediateThrowEvent","event","bpmn-icon-intermediate-event-none",u("Create Intermediate/Boundary Event")),"create.end-event":l("bpmn:EndEvent","event","bpmn-icon-end-event-none",u("Create EndEvent")),"create.exclusive-gateway":l("bpmn:ExclusiveGateway","gateway","bpmn-icon-gateway-none",u("Create Gateway")),"create.user-task":l("bpmn:UserTask","activity","bpmn-icon-user-task",u("Create User Task")),"create.service-task":l("bpmn:ServiceTask","activity","bpmn-icon-service",u("Create Service Task")),"create.data-object":l("bpmn:DataObjectReference","data-object","bpmn-icon-data-object",u("Create DataObjectReference")),"create.data-store":l("bpmn:DataStoreReference","data-store","bpmn-icon-data-store",u("Create DataStoreReference")),"create.subprocess-expanded":{group:"activity",className:"bpmn-icon-subprocess-expanded",title:u("Create expanded SubProcess"),action:{dragstart:d,click:d}},"create.participant-expanded":{group:"collaboration",className:"bpmn-icon-participant",title:u("Create Pool/Participant"),action:{dragstart:g,click:g}},"create.group":l("bpmn:Group","artifact","bpmn-icon-group",u("Create Group"))}),e},dt.$inject=["palette","create","elementFactory","spaceTool","lassoTool","handTool","globalConnect","translate"],dt.prototype=new na,dt.prototype.constructor=dt;const Ou={__init__:["paletteProvider"],paletteProvider:["type",dt]};var la=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const Au=e=>la(void 0,null,function*(){return yield ie.get({url:"/bpm/model/get?id="+e})}),Tu=e=>la(void 0,null,function*(){return yield ie.put({url:"/bpm/model/update",data:e})}),Vu=e=>la(void 0,null,function*(){return yield ie.post({url:"/bpm/model/create",data:e})});var Iu=Object.defineProperty,ju=Object.defineProperties,Du=Object.getOwnPropertyDescriptors,Rn=Object.getOwnPropertySymbols,Uu=Object.prototype.hasOwnProperty,Mu=Object.prototype.propertyIsEnumerable,zn=(e,a,t)=>a in e?Iu(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,ia=(e,a)=>{for(var t in a||(a={}))Uu.call(a,t)&&zn(e,t,a[t]);if(Rn)for(var t of Rn(a))Mu.call(a,t)&&zn(e,t,a[t]);return e},ra=(e,a)=>ju(e,Du(a)),qn=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const Nu=te(ra(ia({},{name:"BpmModelEditor"}),{__name:"index",setup(e){const a=mt(),{query:t}=ot(),r=je(),s=m(void 0),p=m(null),c=m({simulation:!0,labelEditing:!1,labelVisible:!1,prefix:"flowable",headerButtonSize:"mini",additionalModel:[Eu,Ou]}),u=m(),l=b=>{setTimeout(()=>{p.value=b},10)},d=b=>qn(this,null,function*(){const f=ra(ia({},u.value),{bpmnXml:b});f.id?(yield Tu(f),r.success("\u4FEE\u6539\u6210\u529F")):(yield Vu(f),r.success("\u65B0\u589E\u6210\u529F")),g()}),g=()=>{a.push({name:"BpmModel"})};return we(()=>qn(this,null,function*(){const b=t.modelId;if(!b){r.error("\u7F3A\u5C11\u6A21\u578B modelId \u7F16\u53F7");return}const f=yield Au(b);f.bpmnXml||(f.bpmnXml=` <?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.activiti.org/processdef">
  <process id="${f.key}" name="${f.name}" isExecutable="true" />
  <bpmndi:BPMNDiagram id="BPMNDiagram">
    <bpmndi:BPMNPlane id="${f.key}_di" bpmnElement="${f.key}" />
  </bpmndi:BPMNDiagram>
</definitions>`),u.value=ra(ia({},f),{bpmnXml:void 0}),s.value=f.bpmnXml})),(b,f)=>{const w=Le;return h(),I(w,null,{default:o(()=>[n(s)!==void 0?(h(),I(n(kt),$e({key:"designer",modelValue:n(s),"onUpdate:modelValue":f[0]||(f[0]=S=>ne(s)?s.value=S:null),value:n(s)},n(c),{keyboard:"",ref:"processDesigner",onInitFinished:l,additionalModel:n(c).additionalModel,onSave:d}),null,16,["modelValue","value","additionalModel"])):J("",!0),i(n(_t),{key:"penal",bpmnModeler:n(p),prefix:n(c).prefix,class:"process-panel",model:n(u)},null,8,["bpmnModeler","prefix","model"])]),_:1})}}})),Bu=Object.freeze(Object.defineProperty({__proto__:null,default:Nu},Symbol.toStringTag,{value:"Module"})),Je=e=>(ba("data-v-df1f3d16"),e=e(),ga(),e),Fu={class:"add-node-btn-box"},Lu={class:"add-node-btn"},$u={class:"add-node-popover-body"},Ru=Je(()=>F("div",{class:"item-wrapper"},[F("span",{class:"iconfont"},"\uE8EF")],-1)),zu=Je(()=>F("p",null,"\u5BA1\u6279\u4EBA",-1)),qu=[Ru,zu],Ju=Je(()=>F("div",{class:"item-wrapper"},[F("span",{class:"iconfont"},"\uE93B")],-1)),Gu=Je(()=>F("p",null,"\u6284\u9001\u4EBA",-1)),Ku=[Ju,Gu],Yu=Je(()=>F("div",{class:"item-wrapper"},[F("span",{class:"iconfont"},"\uE9BE")],-1)),Hu=Je(()=>F("p",null,"\u6761\u4EF6\u5206\u652F",-1)),Zu=[Yu,Hu],Qu=Je(()=>F("button",{class:"btn",type:"button"},[F("span",{class:"iconfont"},"\uE95B")],-1)),Wu={__name:"addNode",props:{childNodeP:{type:Object,default:()=>({})}},emits:["update:childNodeP"],setup(e,{emit:a}){let t=e,r=a,s=m(!1);const p=c=>{if(s.value=!1,c!=4){var u;c==1?u={nodeName:"\u5BA1\u6838\u4EBA",error:!0,type:1,settype:1,selectMode:0,selectRange:0,directorLevel:1,examineMode:1,noHanderAction:1,examineEndDirectorLevel:0,childNode:t.childNodeP,nodeUserList:[]}:c==2&&(u={nodeName:"\u6284\u9001\u4EBA",type:2,ccSelfSelectFlag:1,childNode:t.childNodeP,nodeUserList:[]}),r("update:childNodeP",u)}else r("update:childNodeP",{nodeName:"\u8DEF\u7531",type:4,childNode:null,conditionNodes:[{nodeName:"\u6761\u4EF61",error:!0,type:3,priorityLevel:1,conditionList:[],nodeUserList:[],childNode:t.childNodeP},{nodeName:"\u6761\u4EF62",type:3,priorityLevel:2,conditionList:[],nodeUserList:[],childNode:null}]})};return(c,u)=>{const l=ci;return h(),R("div",Fu,[F("div",Lu,[i(l,{placement:"right-start",modelValue:n(s),"onUpdate:modelValue":u[3]||(u[3]=d=>ne(s)?s.value=d:s=d),width:"auto"},{reference:o(()=>[Qu]),default:o(()=>[F("div",$u,[F("a",{class:"add-node-popover-item approver",onClick:u[0]||(u[0]=d=>p(1))},qu),F("a",{class:"add-node-popover-item notifier",onClick:u[1]||(u[1]=d=>p(2))},Ku),F("a",{class:"add-node-popover-item condition",onClick:u[2]||(u[2]=d=>p(4))},Zu)])]),_:1},8,["modelValue"])])])}}},oa=Tt(Wu,[["__scopeId","data-v-df1f3d16"]]),sa=e=>{if(e)return e.map(a=>a.name).toString()},Jn=e=>{if(e.settype==1){if(e.nodeUserList.length==1)return e.nodeUserList[0].name;if(e.nodeUserList.length>1){if(e.examineMode==1)return sa(e.nodeUserList);if(e.examineMode==2)return e.nodeUserList.length+"\u4EBA\u4F1A\u7B7E"}}else if(e.settype==2){const a=e.directorLevel==1?"\u76F4\u63A5\u4E3B\u7BA1":"\u7B2C"+e.directorLevel+"\u7EA7\u4E3B\u7BA1";if(e.examineMode==1)return a;if(e.examineMode==2)return a+"\u4F1A\u7B7E"}else{if(e.settype==4)return e.selectRange==1?"\u53D1\u8D77\u4EBA\u81EA\u9009":e.nodeUserList.length>0?e.selectRange==2?"\u53D1\u8D77\u4EBA\u81EA\u9009":"\u53D1\u8D77\u4EBA\u4ECE"+e.nodeUserList[0].name+"\u4E2D\u81EA\u9009":"";if(e.settype==5)return"\u53D1\u8D77\u4EBA\u81EA\u5DF1";if(e.settype==7)return"\u4ECE\u76F4\u63A5\u4E3B\u7BA1\u5230\u901A\u8BAF\u5F55\u4E2D\u7EA7\u522B\u6700\u9AD8\u7684\u7B2C"+e.examineEndDirectorLevel+"\u4E2A\u5C42\u7EA7\u4E3B\u7BA1"}},Gn=e=>{if(e.nodeUserList.length!=0)return sa(e.nodeUserList);if(e.ccSelfSelectFlag==1)return"\u53D1\u8D77\u4EBA\u81EA\u9009"},Kn=(e,a)=>{const{conditionList:t,nodeUserList:r}=e.conditionNodes[a];if(t.length==0)return a==e.conditionNodes.length-1&&e.conditionNodes[0].conditionList.length!=0?"\u5176\u4ED6\u6761\u4EF6\u8FDB\u5165\u6B64\u6D41\u7A0B":"\u8BF7\u8BBE\u7F6E\u6761\u4EF6";{let s="";for(let p=0;p<t.length;p++){const{columnId:c,columnType:u,showType:l,showName:d,optType:g,zdy1:b,opt1:f,zdy2:w,opt2:S,fixedDownBoxValue:v}=t[p];if(c==0&&r.length!=0&&(s+="\u53D1\u8D77\u4EBA\u5C5E\u4E8E\uFF1A",s+=r.map(y=>y.name).join("\u6216")+" \u5E76\u4E14 "),u=="String"&&l=="3"&&b&&(s+=d+"\u5C5E\u4E8E\uFF1A"+Xu(b,JSON.parse(v))+" \u5E76\u4E14 "),u=="Double")if(g!=6&&b){const y=["","<",">","\u2264","=","\u2265"][g];s+=`${d} ${y} ${b} \u5E76\u4E14 `}else g==6&&b&&w&&(s+=`${b} ${f} ${d} ${S} ${w} \u5E76\u4E14 `)}return s?s.substring(0,s.length-4):"\u8BF7\u8BBE\u7F6E\u6761\u4EF6"}},Xu=(e,a)=>{const t=[],r=e.split(",");for(const s in a)r.map(p=>{p==s&&t.push(a[s].value)});return t.join("\u6216")},ed=["87, 106, 149","255, 148, 62","50, 150, 250"],td=["\u53D1\u8D77\u4EBA","\u5BA1\u6838\u4EBA","\u6284\u9001\u4EBA"],ad=_a("simpleWorkflow",{state:()=>({tableId:"",isTried:!1,promoterDrawer:!1,flowPermission1:{},approverDrawer:!1,approverConfig1:{},copyerDrawer:!1,copyerConfig1:{},conditionDrawer:!1,conditionsConfig1:{conditionNodes:[]}}),actions:{setTableId(e){this.tableId=e},setIsTried(e){this.isTried=e},setPromoter(e){this.promoterDrawer=e},setFlowPermission(e){this.flowPermission1=e},setApprover(e){this.approverDrawer=e},setApproverConfig(e){this.approverConfig1=e},setCopyer(e){this.copyerDrawer=e},setCopyerConfig(e){this.copyerConfig1=e},setCondition(e){this.conditionDrawer=e},setConditionsConfig(e){this.conditionsConfig1=e}}}),nd=()=>ad(Fi),ld={key:0,class:"node-wrap"},id={key:0},rd={class:"iconfont"},od=["placeholder"],sd={class:"text"},pd={key:0,class:"placeholder"},ud=F("i",{class:"anticon anticon-right arrow"},null,-1),dd={key:0,class:"error_tip"},cd=F("i",{class:"anticon anticon-exclamation-circle"},null,-1),md=[cd],yd={key:1,class:"branch-wrap"},vd={class:"branch-box-wrap"},fd={class:"branch-box"},bd={class:"condition-node"},gd={class:"condition-node-box"},hd=["onClick"],wd={class:"title-wrapper"},Sd=["onBlur","onUpdate:modelValue"],xd=["onClick"],kd=["onClick"],Cd=["onClick"],_d=["onClick"],Pd=["onClick"],Ed={key:2,class:"error_tip"},Od=F("i",{class:"anticon anticon-exclamation-circle"},null,-1),Ad=[Od],Td=F("div",{class:"top-left-cover-line"},null,-1),Vd=F("div",{class:"bottom-left-cover-line"},null,-1),Id=F("div",{class:"top-right-cover-line"},null,-1),jd=F("div",{class:"bottom-right-cover-line"},null,-1),Dd={__name:"nodeWrap",props:{nodeConfig:{type:Object,default:()=>({})},flowPermission:{type:Object,default:()=>[]}},emits:["update:flowPermission","update:nodeConfig"],setup(e,{emit:a}){let t=ha().uid,r=e,s=De(()=>td[r.nodeConfig.type]),p=De(()=>r.nodeConfig.type==0?sa(r.flowPermission)||"\u6240\u6709\u4EBA":r.nodeConfig.type==1?Jn(r.nodeConfig):Gn(r.nodeConfig)),c=m([]),u=m(!1);const l=()=>{for(var _=0;_<r.nodeConfig.conditionNodes.length;_++)r.nodeConfig.conditionNodes[_].error=Kn(r.nodeConfig,_)=="\u8BF7\u8BBE\u7F6E\u6761\u4EF6"&&_!=r.nodeConfig.conditionNodes.length-1};we(()=>{r.nodeConfig.type==1?r.nodeConfig.error=!Jn(r.nodeConfig):r.nodeConfig.type==2?r.nodeConfig.error=!Gn(r.nodeConfig):r.nodeConfig.type==4&&l()});let d=a,g=nd(),{setPromoter:b,setApprover:f,setCopyer:w,setCondition:S,setFlowPermission:v,setApproverConfig:y,setCopyerConfig:x,setConditionsConfig:k}=g,C=De(()=>g.isTried),E=De(()=>g.flowPermission1),j=De(()=>g.approverConfig1),P=De(()=>g.copyerConfig1),D=De(()=>g.conditionsConfig1);re(E,_=>{_.flag&&_.id===t&&d("update:flowPermission",_.value)}),re(j,_=>{_.flag&&_.id===t&&d("update:nodeConfig",_.value)}),re(P,_=>{_.flag&&_.id===t&&d("update:nodeConfig",_.value)}),re(D,_=>{_.flag&&_.id===t&&d("update:nodeConfig",_.value)});const G=_=>{_||_===0?c.value[_]=!0:u.value=!0},O=_=>{_||_===0?(c.value[_]=!1,r.nodeConfig.conditionNodes[_].nodeName=r.nodeConfig.conditionNodes[_].nodeName||"\u6761\u4EF6"):(u.value=!1,r.nodeConfig.nodeName=r.nodeConfig.nodeName||s)},U=()=>{d("update:nodeConfig",r.nodeConfig.childNode)},Y=()=>{let _=r.nodeConfig.conditionNodes.length+1;r.nodeConfig.conditionNodes.push({nodeName:"\u6761\u4EF6"+_,type:3,priorityLevel:_,conditionList:[],nodeUserList:[],childNode:null}),l(),d("update:nodeConfig",r.nodeConfig)},le=_=>{r.nodeConfig.conditionNodes.splice(_,1),r.nodeConfig.conditionNodes.map(($,Z)=>{$.priorityLevel=Z+1,$.nodeName=`\u6761\u4EF6${Z+1}`}),l(),d("update:nodeConfig",r.nodeConfig),r.nodeConfig.conditionNodes.length==1&&(r.nodeConfig.childNode&&(r.nodeConfig.conditionNodes[0].childNode?H(r.nodeConfig.conditionNodes[0].childNode,r.nodeConfig.childNode):r.nodeConfig.conditionNodes[0].childNode=r.nodeConfig.childNode),d("update:nodeConfig",r.nodeConfig.conditionNodes[0].childNode))},H=(_,$)=>{_.childNode?H(_.childNode,$):_.childNode=$},K=_=>{var{type:$}=r.nodeConfig;$==0?(b(!0),v({value:r.flowPermission,flag:!1,id:t})):$==1?(f(!0),y({value:{...JSON.parse(JSON.stringify(r.nodeConfig)),settype:r.nodeConfig.settype?r.nodeConfig.settype:1},flag:!1,id:t})):$==2?(w(!0),x({value:JSON.parse(JSON.stringify(r.nodeConfig)),flag:!1,id:t})):(S(!0),k({value:JSON.parse(JSON.stringify(r.nodeConfig)),priorityLevel:_,flag:!1,id:t}))},B=(_,$=1)=>{r.nodeConfig.conditionNodes[_]=r.nodeConfig.conditionNodes.splice(_+$,1,r.nodeConfig.conditionNodes[_])[0],r.nodeConfig.conditionNodes.map((Z,Q)=>{Z.priorityLevel=Q+1}),l(),d("update:nodeConfig",r.nodeConfig)};return(_,$)=>{const Z=_e("nodeWrap",!0),Q=va("focus");return h(),R(W,null,[e.nodeConfig.type<3?(h(),R("div",ld,[F("div",{class:Sa(["node-wrap-box",(e.nodeConfig.type==0?"start-node ":"")+(n(C)&&e.nodeConfig.error?"active error":"")])},[F("div",{class:"title",style:fa(`background: rgb(${n(ed)[e.nodeConfig.type]});`)},[e.nodeConfig.type==0?(h(),R("span",id,ee(e.nodeConfig.nodeName),1)):(h(),R(W,{key:1},[F("span",rd,ee(e.nodeConfig.type==1?"\uE8EF":"\uE93B"),1),n(u)?me((h(),R("input",{key:0,type:"text",class:"ant-input editable-title-input",onBlur:$[0]||($[0]=V=>O()),onFocus:$[1]||($[1]=V=>V.currentTarget.select()),"onUpdate:modelValue":$[2]||($[2]=V=>e.nodeConfig.nodeName=V),placeholder:n(s)},null,40,od)),[[Q],[wa,e.nodeConfig.nodeName]]):(h(),R("span",{key:1,class:"editable-title",onClick:$[3]||($[3]=V=>G())},ee(e.nodeConfig.nodeName),1)),F("i",{class:"anticon anticon-close close",onClick:U})],64))],4),F("div",{class:"content",onClick:K},[F("div",sd,[n(p)?J("",!0):(h(),R("span",pd,"\u8BF7\u9009\u62E9"+ee(n(s)),1)),N(" "+ee(n(p)),1)]),ud]),n(C)&&e.nodeConfig.error?(h(),R("div",dd,md)):J("",!0)],2),i(oa,{childNodeP:e.nodeConfig.childNode,"onUpdate:childNodeP":$[4]||($[4]=V=>e.nodeConfig.childNode=V)},null,8,["childNodeP"])])):J("",!0),e.nodeConfig.type==4?(h(),R("div",yd,[F("div",vd,[F("div",fd,[F("button",{class:"add-branch",onClick:Y},"\u6DFB\u52A0\u6761\u4EF6"),(h(!0),R(W,null,se(e.nodeConfig.conditionNodes,(V,A)=>(h(),R("div",{class:"col-box",key:A},[F("div",bd,[F("div",gd,[F("div",{class:Sa(["auto-judge",n(C)&&V.error?"error active":""])},[A!=0?(h(),R("div",{key:0,class:"sort-left",onClick:T=>B(A,-1)},"<",8,hd)):J("",!0),F("div",wd,[n(c)[A]?me((h(),R("input",{key:0,type:"text",class:"ant-input editable-title-input",onBlur:T=>O(A),onFocus:$[5]||($[5]=T=>T.currentTarget.select()),"onUpdate:modelValue":T=>V.nodeName=T},null,40,Sd)),[[wa,V.nodeName]]):(h(),R("span",{key:1,class:"editable-title",onClick:T=>G(A)},ee(V.nodeName),9,xd)),F("span",{class:"priority-title",onClick:T=>K(V.priorityLevel)},"\u4F18\u5148\u7EA7"+ee(V.priorityLevel),9,kd),F("i",{class:"anticon anticon-close close",onClick:T=>le(A)},null,8,Cd)]),A!=e.nodeConfig.conditionNodes.length-1?(h(),R("div",{key:1,class:"sort-right",onClick:T=>B(A)},">",8,_d)):J("",!0),F("div",{class:"content",onClick:T=>K(V.priorityLevel)},ee(n(Kn)(e.nodeConfig,A)),9,Pd),n(C)&&V.error?(h(),R("div",Ed,Ad)):J("",!0)],2),i(oa,{childNodeP:V.childNode,"onUpdate:childNodeP":T=>V.childNode=T},null,8,["childNodeP","onUpdate:childNodeP"])])]),V.childNode?(h(),I(Z,{key:0,nodeConfig:V.childNode,"onUpdate:nodeConfig":T=>V.childNode=T},null,8,["nodeConfig","onUpdate:nodeConfig"])):J("",!0),A==0?(h(),R(W,{key:1},[Td,Vd],64)):J("",!0),A==e.nodeConfig.conditionNodes.length-1?(h(),R(W,{key:2},[Id,jd],64)):J("",!0)]))),128))]),i(oa,{childNodeP:e.nodeConfig.childNode,"onUpdate:childNodeP":$[6]||($[6]=V=>e.nodeConfig.childNode=V)},null,8,["childNodeP"])])])):J("",!0),e.nodeConfig.childNode?(h(),I(Z,{key:2,nodeConfig:e.nodeConfig.childNode,"onUpdate:nodeConfig":$[7]||($[7]=V=>e.nodeConfig.childNode=V)},null,8,["nodeConfig"])):J("",!0)],64)}}};var Ud=Object.defineProperty,Md=Object.defineProperties,Nd=Object.getOwnPropertyDescriptors,Yn=Object.getOwnPropertySymbols,Bd=Object.prototype.hasOwnProperty,Fd=Object.prototype.propertyIsEnumerable,Hn=(e,a,t)=>a in e?Ud(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,Ld=(e,a)=>{for(var t in a||(a={}))Bd.call(a,t)&&Hn(e,t,a[t]);if(Yn)for(var t of Yn(a))Fd.call(a,t)&&Hn(e,t,a[t]);return e},$d=(e,a)=>Md(e,Nd(a));const Rd={class:"dingflow-design"},zd={class:"box-scale"},qd=F("div",{class:"end-node"},[F("div",{class:"end-node-circle"}),F("div",{class:"end-node-text"},"\u6D41\u7A0B\u7ED3\u675F")],-1),Jd=te($d(Ld({},{name:"SimpleWorkflowDesignEditor"}),{__name:"index",setup(e){let a=m({nodeName:"\u53D1\u8D77\u4EBA",type:0,id:"root",formPerms:{},nodeUserList:[],childNode:{}});return(t,r)=>(h(),R("div",null,[F("section",Rd,[F("div",zd,[i(Dd,{nodeConfig:n(a),"onUpdate:nodeConfig":r[0]||(r[0]=s=>ne(a)?a.value=s:a=s)},null,8,["nodeConfig"]),qd])])]))}})),Gd=Object.freeze(Object.defineProperty({__proto__:null,default:Jd},Symbol.toStringTag,{value:"Module"}));var Zn=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const pa=(e,a)=>Zn(void 0,null,function*(){return yield ie.get({url:"/bpm/process-definition/get",params:{id:e,key:a}})}),Kd=e=>Zn(void 0,null,function*(){return yield ie.get({url:"/bpm/process-definition/page",params:e})});var Yd=Object.defineProperty,Hd=Object.defineProperties,Zd=Object.getOwnPropertyDescriptors,Qn=Object.getOwnPropertySymbols,Qd=Object.prototype.hasOwnProperty,Wd=Object.prototype.propertyIsEnumerable,Wn=(e,a,t)=>a in e?Yd(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,Xd=(e,a)=>{for(var t in a||(a={}))Qd.call(a,t)&&Wn(e,t,a[t]);if(Qn)for(var t of Qn(a))Wd.call(a,t)&&Wn(e,t,a[t]);return e},ec=(e,a)=>Hd(e,Zd(a)),ua=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const tc=te(ec(Xd({},{name:"BpmProcessDefinition"}),{__name:"index",setup(e){const{push:a}=mt(),{query:t}=ot(),r=m(!0),s=m(0),p=m([]),c=Re({pageNo:1,pageSize:10,key:t.key}),u=()=>ua(this,null,function*(){r.value=!0;try{const v=yield Kd(c);p.value=v.list,s.value=v.total}finally{r.value=!1}}),l=m(!1),d=m({rule:[],option:{}}),g=v=>ua(this,null,function*(){v.formType==10?(St(d,v.formConf,v.formFields),l.value=!0):yield a({path:v.formCustomCreatePath})}),b=m(!1),f=m(null),w=m({prefix:"flowable"}),S=v=>ua(this,null,function*(){var y;f.value=(y=yield pa(v.id))==null?void 0:y.bpmnXml,b.value=!0});return we(()=>{u()}),(v,y)=>{const x=Be,k=pe,C=Ca,E=Fe,j=At,P=Le,D=_e("form-create"),G=Ce,O=Pe;return h(),R(W,null,[i(P,null,{default:o(()=>[me((h(),I(E,{data:n(p)},{default:o(()=>[i(x,{label:"\u5B9A\u4E49\u7F16\u53F7",align:"center",prop:"id",width:"400"}),i(x,{label:"\u6D41\u7A0B\u540D\u79F0",align:"center",prop:"name",width:"200"},{default:o(U=>[i(k,{type:"primary",link:"",onClick:Y=>S(U.row)},{default:o(()=>[F("span",null,ee(U.row.name),1)]),_:2},1032,["onClick"])]),_:1}),i(x,{label:"\u5B9A\u4E49\u5206\u7C7B",align:"center",prop:"categoryName",width:"100"}),i(x,{label:"\u8868\u5355\u4FE1\u606F",align:"center",prop:"formType",width:"200"},{default:o(U=>[U.row.formType===10?(h(),I(k,{key:0,type:"primary",link:"",onClick:Y=>g(U.row)},{default:o(()=>[F("span",null,ee(U.row.formName),1)]),_:2},1032,["onClick"])):(h(),I(k,{key:1,type:"primary",link:"",onClick:Y=>g(U.row)},{default:o(()=>[F("span",null,ee(U.row.formCustomCreatePath),1)]),_:2},1032,["onClick"]))]),_:1}),i(x,{label:"\u6D41\u7A0B\u7248\u672C",align:"center",prop:"processDefinition.version",width:"80"},{default:o(U=>[U.row?(h(),I(C,{key:0},{default:o(()=>[N("v"+ee(U.row.version),1)]),_:2},1024)):(h(),I(C,{key:1,type:"warning"},{default:o(()=>[N("\u672A\u90E8\u7F72")]),_:1}))]),_:1}),i(x,{label:"\u72B6\u6001",align:"center",prop:"version",width:"80"},{default:o(U=>[U.row.suspensionState===1?(h(),I(C,{key:0,type:"success"},{default:o(()=>[N("\u6FC0\u6D3B")]),_:1})):J("",!0),U.row.suspensionState===2?(h(),I(C,{key:1,type:"warning"},{default:o(()=>[N("\u6302\u8D77")]),_:1})):J("",!0)]),_:1}),i(x,{label:"\u90E8\u7F72\u65F6\u95F4",align:"center",prop:"deploymentTime",width:"180",formatter:n(Vt)},null,8,["formatter"]),i(x,{label:"\u5B9A\u4E49\u63CF\u8FF0",align:"center",prop:"description",width:"300","show-overflow-tooltip":""})]),_:1},8,["data"])),[[O,n(r)]]),i(j,{total:n(s),page:n(c).pageNo,"onUpdate:page":y[0]||(y[0]=U=>n(c).pageNo=U),limit:n(c).pageSize,"onUpdate:limit":y[1]||(y[1]=U=>n(c).pageSize=U),onPagination:u},null,8,["total","page","limit"])]),_:1}),i(G,{title:"\u8868\u5355\u8BE6\u60C5",modelValue:n(l),"onUpdate:modelValue":y[2]||(y[2]=U=>ne(l)?l.value=U:null),width:"800"},{default:o(()=>[i(D,{rule:n(d).rule,option:n(d).option},null,8,["rule","option"])]),_:1},8,["modelValue"]),i(G,{title:"\u6D41\u7A0B\u56FE",modelValue:n(b),"onUpdate:modelValue":y[4]||(y[4]=U=>ne(b)?b.value=U:null),width:"800"},{default:o(()=>[i(n(ut),$e({key:"designer",modelValue:n(f),"onUpdate:modelValue":y[3]||(y[3]=U=>ne(f)?f.value=U:null),value:n(f)},n(w),{prefix:n(w).prefix}),null,16,["modelValue","value","prefix"])]),_:1},8,["modelValue"])],64)}}})),ac=Object.freeze(Object.defineProperty({__proto__:null,default:tc},Symbol.toStringTag,{value:"Module"}));var nc=Object.defineProperty,lc=Object.defineProperties,ic=Object.getOwnPropertyDescriptors,Xn=Object.getOwnPropertySymbols,rc=Object.prototype.hasOwnProperty,oc=Object.prototype.propertyIsEnumerable,el=(e,a,t)=>a in e?nc(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,sc=(e,a)=>{for(var t in a||(a={}))rc.call(a,t)&&el(e,t,a[t]);if(Xn)for(var t of Xn(a))oc.call(a,t)&&el(e,t,a[t]);return e},pc=(e,a)=>lc(e,ic(a));const uc=F("span",{class:"el-icon-picture-outline"},"\u6D41\u7A0B\u56FE",-1),dc=te(pc(sc({},{name:"BpmProcessInstanceBpmnViewer"}),{__name:"ProcessInstanceBpmnViewer",props:{loading:Te.bool,id:Te.string,processInstance:Te.any,tasks:Te.array,bpmnXml:Te.string},setup(e,{expose:a}){const t=m(),r=m({prefix:"flowable"});return a({refreshBpm:()=>{var s;(s=n(t))==null||s.refreshBpm()}}),(s,p)=>{const c=ft,u=Pe;return me((h(),I(c,{class:"box-card"},{header:o(()=>[uc]),default:o(()=>[i(n(ut),$e({key:"designer",processInstanceData:e.processInstance,taskData:e.tasks,value:e.bpmnXml},n(r),{ref_key:"processViewerRef",ref:t}),null,16,["processInstanceData","taskData","value"])]),_:1})),[[u,e.loading]])}}}));var cc=Object.defineProperty,mc=Object.defineProperties,yc=Object.getOwnPropertyDescriptors,tl=Object.getOwnPropertySymbols,vc=Object.prototype.hasOwnProperty,fc=Object.prototype.propertyIsEnumerable,al=(e,a,t)=>a in e?cc(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,bc=(e,a)=>{for(var t in a||(a={}))vc.call(a,t)&&al(e,t,a[t]);if(tl)for(var t of tl(a))fc.call(a,t)&&al(e,t,a[t]);return e},gc=(e,a)=>mc(e,yc(a)),nl=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const hc=te(gc(bc({},{name:"TaskSignDeleteForm"}),{__name:"TaskSignDeleteForm",emits:["success"],setup(e,{expose:a,emit:t}){const r=je(),s=m(!1),p=m(!1),c=m({id:"",reason:""}),u=m({id:[{required:!0,message:"\u5FC5\u987B\u9009\u62E9\u51CF\u7B7E\u4EFB\u52A1",trigger:"change"}],reason:[{required:!0,message:"\u51CF\u7B7E\u7406\u7531\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),l=m(),d=m([]);a({open:w=>nl(this,null,function*(){if(d.value=yield Ll(w),Ie(d.value))return r.warning("\u5F53\u524D\u6CA1\u6709\u53EF\u51CF\u7B7E\u7684\u4EFB\u52A1"),!1;s.value=!0,f()})});const g=t,b=()=>nl(this,null,function*(){if(!(!l||!(yield l.value.validate()))){p.value=!0;try{yield $l(c.value),r.success("\u51CF\u7B7E\u6210\u529F"),s.value=!1,g("success")}finally{p.value=!1}}}),f=()=>{var w;c.value={id:"",reason:""},(w=l.value)==null||w.resetFields()};return(w,S)=>{const v=mi,y=xa,x=ue,k=de,C=ye,E=pe,j=Ce,P=Pe;return h(),I(j,{modelValue:n(s),"onUpdate:modelValue":S[3]||(S[3]=D=>ne(s)?s.value=D:null),title:"\u51CF\u7B7E",width:"500"},{footer:o(()=>[i(E,{disabled:n(p),type:"primary",onClick:b},{default:o(()=>[N("\u786E \u5B9A")]),_:1},8,["disabled"]),i(E,{onClick:S[2]||(S[2]=D=>s.value=!1)},{default:o(()=>[N("\u53D6 \u6D88")]),_:1})]),default:o(()=>[me((h(),I(C,{ref_key:"formRef",ref:l,model:n(c),rules:n(u),"label-width":"110px"},{default:o(()=>[i(x,{label:"\u51CF\u7B7E\u4EFB\u52A1",prop:"id"},{default:o(()=>[i(y,{modelValue:n(c).id,"onUpdate:modelValue":S[0]||(S[0]=D=>n(c).id=D)},{default:o(()=>[(h(!0),R(W,null,se(n(d),D=>(h(),I(v,{key:D.id,label:D.id},{default:o(()=>{var G,O,U,Y;return[N(ee(D.name)+" ("+ee(((G=D.assigneeUser)==null?void 0:G.deptName)||((O=D.ownerUser)==null?void 0:O.deptName))+" - "+ee(((U=D.assigneeUser)==null?void 0:U.nickname)||((Y=D.ownerUser)==null?void 0:Y.nickname))+") ",1)]}),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),i(x,{label:"\u51CF\u7B7E\u7406\u7531",prop:"reason"},{default:o(()=>[i(k,{modelValue:n(c).reason,"onUpdate:modelValue":S[1]||(S[1]=D=>n(c).reason=D),clearable:"",placeholder:"\u8BF7\u8F93\u5165\u51CF\u7B7E\u7406\u7531"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[P,n(p)]])]),_:1},8,["modelValue"])}}}));var wc=Object.defineProperty,Sc=Object.defineProperties,xc=Object.getOwnPropertyDescriptors,ll=Object.getOwnPropertySymbols,kc=Object.prototype.hasOwnProperty,Cc=Object.prototype.propertyIsEnumerable,il=(e,a,t)=>a in e?wc(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,_c=(e,a)=>{for(var t in a||(a={}))kc.call(a,t)&&il(e,t,a[t]);if(ll)for(var t of ll(a))Cc.call(a,t)&&il(e,t,a[t]);return e},Pc=(e,a)=>Sc(e,xc(a)),Ec=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const Oc=te(Pc(_c({},{name:"TaskSignList"}),{__name:"TaskSignList",emits:["success"],setup(e,{expose:a,emit:t}){const r=je(),s=m(!1),p=m({});a({open:b=>Ec(this,null,function*(){if(Ie(b.children)){r.warning("\u8BE5\u4EFB\u52A1\u6CA1\u6709\u5B50\u4EFB\u52A1");return}p.value=b,s.value=!0})});const c=m(),u=t,l=b=>{c.value.open(b.id)},d=()=>{u("success"),s.value=!1},g=b=>b&&b.children&&!Ie(b.children);return(b,f)=>{const w=Me,S=pe,v=Be,y=ct,x=Fe,k=Ut;return h(),I(k,{modelValue:n(s),"onUpdate:modelValue":f[1]||(f[1]=C=>ne(s)?s.value=C:null),title:"\u5B50\u4EFB\u52A1",size:"880px"},{header:o(()=>{var C,E;return[F("h4",null,"\u3010"+ee(n(p).name)+" \u3011\u5BA1\u6279\u4EBA\uFF1A"+ee((E=(C=n(p))==null?void 0:C.assigneeUser)==null?void 0:E.nickname),1),g(n(p))?(h(),I(S,{key:0,style:{"margin-left":"5px"},type:"danger",plain:"",onClick:f[0]||(f[0]=j=>l(n(p)))},{default:o(()=>[i(w,{icon:"ep:remove"}),N(" \u51CF\u7B7E ")]),_:1})):J("",!0)]}),default:o(()=>[i(x,{data:n(p).children,style:{width:"100%"},"row-key":"id",border:""},{default:o(()=>[i(v,{prop:"assigneeUser.nickname",label:"\u5BA1\u6279\u4EBA","min-width":"100"},{default:o(C=>{var E,j;return[N(ee(((E=C.row.assigneeUser)==null?void 0:E.nickname)||((j=C.row.ownerUser)==null?void 0:j.nickname)),1)]}),_:1}),i(v,{prop:"assigneeUser.deptName",label:"\u6240\u5728\u90E8\u95E8","min-width":"100"},{default:o(C=>{var E,j;return[N(ee(((E=C.row.assigneeUser)==null?void 0:E.deptName)||((j=C.row.ownerUser)==null?void 0:j.deptName)),1)]}),_:1}),i(v,{label:"\u5BA1\u6279\u72B6\u6001",prop:"status",width:"120"},{default:o(C=>[i(y,{type:n(Oe).BPM_TASK_STATUS,value:C.row.status},null,8,["type","value"])]),_:1}),i(v,{label:"\u63D0\u4EA4\u65F6\u95F4",align:"center",prop:"createTime",width:"180",formatter:n(Vt)},null,8,["formatter"]),i(v,{label:"\u7ED3\u675F\u65F6\u95F4",align:"center",prop:"endTime",width:"180",formatter:n(Vt)},null,8,["formatter"]),i(v,{label:"\u64CD\u4F5C",prop:"operation",width:"90"},{default:o(C=>[g(C.row)?(h(),I(S,{key:0,type:"danger",plain:"",size:"small",onClick:E=>l(C.row)},{default:o(()=>[i(w,{icon:"ep:remove"}),N(" \u51CF\u7B7E ")]),_:2},1032,["onClick"])):J("",!0)]),_:1})]),_:1},8,["data"]),i(hc,{ref_key:"taskSignDeleteFormRef",ref:c,onSuccess:d},null,512)]),_:1},8,["modelValue"])}}}));var Ac=Object.defineProperty,Tc=Object.defineProperties,Vc=Object.getOwnPropertyDescriptors,rl=Object.getOwnPropertySymbols,Ic=Object.prototype.hasOwnProperty,jc=Object.prototype.propertyIsEnumerable,ol=(e,a,t)=>a in e?Ac(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,Dc=(e,a)=>{for(var t in a||(a={}))Ic.call(a,t)&&ol(e,t,a[t]);if(rl)for(var t of rl(a))jc.call(a,t)&&ol(e,t,a[t]);return e},Uc=(e,a)=>Tc(e,Vc(a)),sl=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const pl=e=>(ba("data-v-930931ca"),e=e(),ga(),e),Mc=pl(()=>F("span",{class:"el-icon-picture-outline"},"\u5BA1\u6279\u8BB0\u5F55",-1)),Nc={class:"block"},Bc=pl(()=>F("div",{class:"p-5px bg-#eaeef1 rounded-3px color-#565656"}," \u6D41\u7A0B\u7ED3\u675F ",-1)),Fc={key:0,class:"p-5px bg-#97b2d1 rounded-3px color-#fff"},Lc={key:1,class:"p-5px bg-#eaeef1 rounded-3px color-#3271ae m-t-5px"},$c={key:2,class:"p-5px bg-#eaeef1 rounded-3px color-#565656 m-t-5px"},Rc={key:3,class:"p-5px bg-#eaeef1 rounded-3px color-#565656 m-t-5px"},zc={class:"p-5px bg-#eaeef1 rounded-3px color-#565656"},qc=te(Uc(Dc({},{name:"BpmProcessInstanceTaskList"}),{__name:"ProcessInstanceTaskList",props:{loading:Te.bool,processInstance:Te.object,tasks:Te.arrayOf(Te.object)},emits:["refresh","skip"],setup(e,{emit:a}){const t=e,{getUser:r}=It(),s=v=>v.status===2?"success":v.status===3?"danger":v.status===4?"warning":"",p=v=>[0,1,6,7].includes(v.status)?"primary":v.status===2?"success":v.status===3?"danger":v.status===4?"info":v.status===5?"warning":"",c=m(),u=v=>{c.value.open(v)},l=m(),d=m({rule:[],option:{},values:{}}),g=m(!1),b=v=>sl(this,null,function*(){var y,x,k,C,E,j;St(d,v.formConf,v.formFields,v.formVariables),g.value=!0,yield ce(),(x=(y=l.value)==null?void 0:y.fapi)==null||x.btn.show(!1),(C=(k=l.value)==null?void 0:k.fapi)==null||C.resetBtn.show(!1),(j=(E=l.value)==null?void 0:E.fapi)==null||j.disabled(!0)}),f=a,w=v=>sl(this,null,function*(){const{value:y}=yield ze.prompt("\u8BF7\u8F93\u5165\u8DF3\u8FC7\u6D41\u7A0B\u8282\u70B9\u539F\u56E0","\u8DF3\u8FC7\u6D41\u7A0B\u8282\u70B9",{confirmButtonText:"\u786E\u8BA4",cancelButtonText:"\u53D6\u6D88",inputPattern:/^[\s\S]*.*\S[\s\S]*$/,inputErrorMessage:"\u8DF3\u8FC7\u539F\u56E0\u4E0D\u80FD\u4E3A\u7A7A"});f("skip",v,y)}),S=()=>{f("refresh")};return(v,y)=>{const x=ct,k=ft,C=yi,E=Me,j=pe,P=vi,D=Mt,G=_e("form-create"),O=Ce,U=Pe;return h(),R(W,null,[me((h(),I(k,{class:"box-card"},{header:o(()=>[Mc]),default:o(()=>[i(D,{offset:3,span:18},{default:o(()=>[F("div",Nc,[i(P,null,{default:o(()=>{var Y,le;return[e.processInstance.endTime?(h(),I(C,{key:0,type:s(e.processInstance),timestamp:n(ke)((Y=e.processInstance)==null?void 0:Y.endTime),placement:"top"},{default:o(()=>[i(k,{"header-style":{padding:"5px"},"body-style":{padding:"5px"},shadow:"never"},{header:o(()=>[N(" \u6D41\u7A0B\u7ED3\u675F "),i(x,{type:n(Oe).BPM_PROCESS_INSTANCE_STATUS,value:e.processInstance.status},null,8,["type","value"])]),default:o(()=>[Bc]),_:1})]),_:1},8,["type","timestamp"])):J("",!0),(h(!0),R(W,null,se(e.tasks,(H,K)=>(h(),I(C,{key:K,type:p(H),timestamp:n(ke)(H==null?void 0:H.endTime),placement:"top"},{default:o(()=>[i(k,{"header-style":{padding:"5px"},"body-style":{padding:"5px"},shadow:"never"},{header:o(()=>{var B,_,$;return[N(ee(H.name)+" ",1),i(x,{type:n(Oe).BPM_TASK_STATUS,value:H.status},null,8,["type","value"]),n(Ie)(H.children)?J("",!0):(h(),I(j,{key:0,class:"ml-10px",onClick:Z=>u(H),size:"small"},{default:o(()=>[i(E,{icon:"ep:memo"}),N(" \u5B50\u4EFB\u52A1 ")]),_:2},1032,["onClick"])),H.formId>0?(h(),I(j,{key:1,type:"primary",class:"ml-10px",size:"small",onClick:Z=>b(H),link:""},{default:o(()=>[i(E,{icon:"ep:memo"}),N(" \u67E5\u770B\u8868\u5355 ")]),_:2},1032,["onClick"])):J("",!0),[0,1,6].includes(H.status)&&n(gt)().diff(n(gt)(n(ke)(H==null?void 0:H.createTime)),"days")>=2&&($=(_=(B=t.processInstance)==null?void 0:B.formVariables)==null?void 0:_.manager)!=null&&$.includes(n(r).id)?(h(),I(j,{key:2,class:"ml-10px",type:"danger",size:"small",plain:"",onClick:Z=>w(H)},{default:o(()=>[N(" \u8DF3\u8FC7 ")]),_:2},1032,["onClick"])):J("",!0)]}),default:o(()=>[H.assigneeUser&&H.assigneeUser.id!=1?(h(),R("div",Fc,[i(E,{icon:"ep:user"}),N(" "+ee(H.assigneeUser.nickname),1)])):J("",!0),H.reason?(h(),R("div",Lc,ee(H.reason),1)):J("",!0),H.durationInMillis?(h(),R("div",$c," \u8017\u65F6:"+ee(n(Rl)(H==null?void 0:H.durationInMillis)),1)):(h(),R("div",Rc," \u5DF2\u7B49\u5F85:"+ee(n(gt)().diff(n(gt)(n(ke)(H==null?void 0:H.createTime)),"days"))+"\u5929 ",1))]),_:2},1024)]),_:2},1032,["type","timestamp"]))),128)),i(C,{type:"success",timestamp:n(ke)((le=e.processInstance)==null?void 0:le.startTime),placement:"top"},{default:o(()=>[i(k,{"header-style":{padding:"5px"},"body-style":{padding:"5px"},shadow:"never"},{header:o(()=>[N("\u63D0\u4EA4\u5355\u636E")]),default:o(()=>{var H;return[F("div",zc,[i(E,{icon:"ep:user"}),N(" "+ee((H=e.processInstance.startUser)==null?void 0:H.nickname)+" \u63D0\u4EA4\u9001\u5BA1 ",1)])]}),_:1})]),_:1},8,["timestamp"])]}),_:1})])]),_:1})]),_:1})),[[U,e.loading]]),i(Oc,{ref_key:"taskSignListRef",ref:c,onSuccess:S},null,512),i(O,{title:"\u8868\u5355\u8BE6\u60C5",modelValue:n(g),"onUpdate:modelValue":y[1]||(y[1]=Y=>ne(g)?g.value=Y:null),width:"600"},{default:o(()=>[i(G,{ref_key:"fApi",ref:l,modelValue:n(d).values,"onUpdate:modelValue":y[0]||(y[0]=Y=>n(d).values=Y),option:n(d).option,rule:n(d).rule},null,8,["modelValue","option","rule"])]),_:1},8,["modelValue"])],64)}}})),Jc=Tt(qc,[["__scopeId","data-v-930931ca"]]);var ul=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const Gc=te({__name:"TaskReturnForm",emits:["success"],setup(e,{expose:a,emit:t}){const r=je(),s=m(!1),p=m(!1),c=m({id:"",targetTaskDefinitionKey:void 0,reason:""}),u=m({targetTaskDefinitionKey:[{required:!0,message:"\u5FC5\u987B\u9009\u62E9\u56DE\u9000\u8282\u70B9",trigger:"change"}],reason:[{required:!0,message:"\u56DE\u9000\u7406\u7531\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),l=m(),d=m([]);a({open:w=>ul(this,null,function*(){if(d.value=yield zl(w),d.value.length===0)return r.warning("\u5F53\u524D\u6CA1\u6709\u53EF\u56DE\u9000\u7684\u8282\u70B9"),!1;s.value=!0,f(),c.value.id=w})});const g=t,b=()=>ul(this,null,function*(){if(!(!l||!(yield l.value.validate()))){p.value=!0;try{yield ql(c.value),r.success("\u56DE\u9000\u6210\u529F"),s.value=!1,g("success")}finally{p.value=!1}}}),f=()=>{var w;c.value={id:"",targetTaskDefinitionKey:void 0,reason:""},(w=l.value)==null||w.resetFields()};return(w,S)=>{const v=Se,y=xe,x=ue,k=de,C=ye,E=pe,j=Ce,P=Pe;return h(),I(j,{modelValue:n(s),"onUpdate:modelValue":S[3]||(S[3]=D=>ne(s)?s.value=D:null),title:"\u56DE\u9000\u4EFB\u52A1",width:"500"},{footer:o(()=>[i(E,{disabled:n(p),type:"primary",onClick:b},{default:o(()=>[N("\u786E \u5B9A")]),_:1},8,["disabled"]),i(E,{onClick:S[2]||(S[2]=D=>s.value=!1)},{default:o(()=>[N("\u53D6 \u6D88")]),_:1})]),default:o(()=>[me((h(),I(C,{ref_key:"formRef",ref:l,model:n(c),rules:n(u),"label-width":"110px"},{default:o(()=>[i(x,{label:"\u9000\u56DE\u8282\u70B9",prop:"targetTaskDefinitionKey"},{default:o(()=>[i(y,{modelValue:n(c).targetTaskDefinitionKey,"onUpdate:modelValue":S[0]||(S[0]=D=>n(c).targetTaskDefinitionKey=D),clearable:"",style:{width:"100%"}},{default:o(()=>[(h(!0),R(W,null,se(n(d),D=>(h(),I(v,{key:D.taskDefinitionKey,label:D.name,value:D.taskDefinitionKey},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),i(x,{label:"\u56DE\u9000\u7406\u7531",prop:"reason"},{default:o(()=>[i(k,{modelValue:n(c).reason,"onUpdate:modelValue":S[1]||(S[1]=D=>n(c).reason=D),clearable:"",placeholder:"\u8BF7\u8F93\u5165\u56DE\u9000\u7406\u7531"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[P,n(p)]])]),_:1},8,["modelValue"])}}});var Kc=Object.defineProperty,Yc=Object.defineProperties,Hc=Object.getOwnPropertyDescriptors,dl=Object.getOwnPropertySymbols,Zc=Object.prototype.hasOwnProperty,Qc=Object.prototype.propertyIsEnumerable,cl=(e,a,t)=>a in e?Kc(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,Wc=(e,a)=>{for(var t in a||(a={}))Zc.call(a,t)&&cl(e,t,a[t]);if(dl)for(var t of dl(a))Qc.call(a,t)&&cl(e,t,a[t]);return e},Xc=(e,a)=>Yc(e,Hc(a)),ml=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const em=te(Xc(Wc({},{name:"BpmTaskDelegateForm"}),{__name:"TaskDelegateForm",emits:["success"],setup(e,{expose:a,emit:t}){const r=m(!1),s=m(!1),p=m({id:"",delegateUserId:void 0,reason:""}),c=m({delegateUserId:[{required:!0,message:"\u63A5\u6536\u4EBA\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],reason:[{required:!0,message:"\u59D4\u6D3E\u7406\u7531\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),u=m(),l=m([]);a({open:f=>ml(this,null,function*(){r.value=!0,b(),p.value.id=f,l.value=yield He()})});const d=t,g=()=>ml(this,null,function*(){if(!(!u||!(yield u.value.validate()))){s.value=!0;try{yield Jl(p.value),r.value=!1,d("success")}finally{s.value=!1}}}),b=()=>{var f;p.value={id:"",delegateUserId:void 0,reason:""},(f=u.value)==null||f.resetFields()};return(f,w)=>{const S=Se,v=xe,y=ue,x=de,k=ye,C=pe,E=Ce,j=Pe;return h(),I(E,{modelValue:n(r),"onUpdate:modelValue":w[3]||(w[3]=P=>ne(r)?r.value=P:null),title:"\u59D4\u6D3E\u4EFB\u52A1",width:"500"},{footer:o(()=>[i(C,{disabled:n(s),type:"primary",onClick:g},{default:o(()=>[N("\u786E \u5B9A")]),_:1},8,["disabled"]),i(C,{onClick:w[2]||(w[2]=P=>r.value=!1)},{default:o(()=>[N("\u53D6 \u6D88")]),_:1})]),default:o(()=>[me((h(),I(k,{ref_key:"formRef",ref:u,model:n(p),rules:n(c),"label-width":"110px"},{default:o(()=>[i(y,{label:"\u63A5\u6536\u4EBA",prop:"delegateUserId"},{default:o(()=>[i(v,{modelValue:n(p).delegateUserId,"onUpdate:modelValue":w[0]||(w[0]=P=>n(p).delegateUserId=P),clearable:"",style:{width:"100%"}},{default:o(()=>[(h(!0),R(W,null,se(n(l),P=>(h(),I(S,{key:P.id,label:P.nickname,value:P.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),i(y,{label:"\u59D4\u6D3E\u7406\u7531",prop:"reason"},{default:o(()=>[i(x,{modelValue:n(p).reason,"onUpdate:modelValue":w[1]||(w[1]=P=>n(p).reason=P),clearable:"",placeholder:"\u8BF7\u8F93\u5165\u59D4\u6D3E\u7406\u7531"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[j,n(s)]])]),_:1},8,["modelValue"])}}}));var tm=Object.defineProperty,am=Object.defineProperties,nm=Object.getOwnPropertyDescriptors,yl=Object.getOwnPropertySymbols,lm=Object.prototype.hasOwnProperty,im=Object.prototype.propertyIsEnumerable,vl=(e,a,t)=>a in e?tm(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,rm=(e,a)=>{for(var t in a||(a={}))lm.call(a,t)&&vl(e,t,a[t]);if(yl)for(var t of yl(a))im.call(a,t)&&vl(e,t,a[t]);return e},om=(e,a)=>am(e,nm(a)),fl=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const sm=te(om(rm({},{name:"TaskTransferForm"}),{__name:"TaskTransferForm",emits:["success"],setup(e,{expose:a,emit:t}){const r=m(!1),s=m(!1),p=m({id:"",assigneeUserId:void 0,reason:""}),c=m({assigneeUserId:[{required:!0,message:"\u65B0\u5BA1\u6279\u4EBA\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],reason:[{required:!0,message:"\u8F6C\u6D3E\u7406\u7531\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),u=m(),l=m([]);a({open:f=>fl(this,null,function*(){r.value=!0,b(),p.value.id=f,l.value=yield He()})});const d=t,g=()=>fl(this,null,function*(){if(!(!u||!(yield u.value.validate()))){s.value=!0;try{yield Gl(p.value),r.value=!1,d("success")}finally{s.value=!1}}}),b=()=>{var f;p.value={id:"",assigneeUserId:void 0,reason:""},(f=u.value)==null||f.resetFields()};return(f,w)=>{const S=Se,v=xe,y=ue,x=de,k=ye,C=pe,E=Ce,j=Pe;return h(),I(E,{modelValue:n(r),"onUpdate:modelValue":w[3]||(w[3]=P=>ne(r)?r.value=P:null),title:"\u8F6C\u6D3E\u4EFB\u52A1",width:"500"},{footer:o(()=>[i(C,{disabled:n(s),type:"primary",onClick:g},{default:o(()=>[N("\u786E \u5B9A")]),_:1},8,["disabled"]),i(C,{onClick:w[2]||(w[2]=P=>r.value=!1)},{default:o(()=>[N("\u53D6 \u6D88")]),_:1})]),default:o(()=>[me((h(),I(k,{ref_key:"formRef",ref:u,model:n(p),rules:n(c),"label-width":"110px"},{default:o(()=>[i(y,{label:"\u65B0\u5BA1\u6279\u4EBA",prop:"assigneeUserId"},{default:o(()=>[i(v,{modelValue:n(p).assigneeUserId,"onUpdate:modelValue":w[0]||(w[0]=P=>n(p).assigneeUserId=P),clearable:"",style:{width:"100%"},filterable:""},{default:o(()=>[(h(!0),R(W,null,se(n(l),P=>(h(),I(S,{key:P.id,label:P.nickname,value:P.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),i(y,{label:"\u8F6C\u6D3E\u7406\u7531",prop:"reason"},{default:o(()=>[i(x,{modelValue:n(p).reason,"onUpdate:modelValue":w[1]||(w[1]=P=>n(p).reason=P),clearable:"",placeholder:"\u8BF7\u8F93\u5165\u8F6C\u6D3E\u7406\u7531"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[j,n(s)]])]),_:1},8,["modelValue"])}}}));var pm=Object.defineProperty,um=Object.defineProperties,dm=Object.getOwnPropertyDescriptors,bl=Object.getOwnPropertySymbols,cm=Object.prototype.hasOwnProperty,mm=Object.prototype.propertyIsEnumerable,gl=(e,a,t)=>a in e?pm(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,ym=(e,a)=>{for(var t in a||(a={}))cm.call(a,t)&&gl(e,t,a[t]);if(bl)for(var t of bl(a))mm.call(a,t)&&gl(e,t,a[t]);return e},vm=(e,a)=>um(e,dm(a)),hl=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const fm=te(vm(ym({},{name:"TaskSignCreateForm"}),{__name:"TaskSignCreateForm",emits:["success"],setup(e,{expose:a,emit:t}){const r=je(),s=m(!1),p=m(!1),c=m({id:"",userIds:[],type:"",reason:""}),u=m({userIds:[{required:!0,message:"\u52A0\u7B7E\u5904\u7406\u4EBA\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],reason:[{required:!0,message:"\u52A0\u7B7E\u7406\u7531\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}]}),l=m(),d=m([]);a({open:w=>hl(this,null,function*(){s.value=!0,f(),c.value.id=w,d.value=yield He()})});const g=t,b=w=>hl(this,null,function*(){if(!(!l||!(yield l.value.validate()))){p.value=!0,c.value.type=w;try{yield Kl(c.value),r.success("\u52A0\u7B7E\u6210\u529F"),s.value=!1,g("success")}finally{p.value=!1}}}),f=()=>{var w;c.value={id:"",userIds:[],type:"",reason:""},(w=l.value)==null||w.resetFields()};return(w,S)=>{const v=Se,y=xe,x=ue,k=de,C=ye,E=pe,j=Ce,P=Pe;return h(),I(j,{modelValue:n(s),"onUpdate:modelValue":S[5]||(S[5]=D=>ne(s)?s.value=D:null),title:"\u52A0\u7B7E",width:"500"},{footer:o(()=>[i(E,{disabled:n(p),type:"primary",onClick:S[2]||(S[2]=D=>b("before"))},{default:o(()=>[N(" \u5411\u524D\u52A0\u7B7E ")]),_:1},8,["disabled"]),i(E,{disabled:n(p),type:"primary",onClick:S[3]||(S[3]=D=>b("after"))},{default:o(()=>[N(" \u5411\u540E\u52A0\u7B7E ")]),_:1},8,["disabled"]),i(E,{onClick:S[4]||(S[4]=D=>s.value=!1)},{default:o(()=>[N("\u53D6 \u6D88")]),_:1})]),default:o(()=>[me((h(),I(C,{ref_key:"formRef",ref:l,model:n(c),rules:n(u),"label-width":"110px"},{default:o(()=>[i(x,{label:"\u52A0\u7B7E\u5904\u7406\u4EBA",prop:"userIds"},{default:o(()=>[i(y,{modelValue:n(c).userIds,"onUpdate:modelValue":S[0]||(S[0]=D=>n(c).userIds=D),multiple:"",clearable:"",style:{width:"100%"}},{default:o(()=>[(h(!0),R(W,null,se(n(d),D=>(h(),I(v,{key:D.id,label:D.nickname,value:D.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),i(x,{label:"\u52A0\u7B7E\u7406\u7531",prop:"reason"},{default:o(()=>[i(k,{modelValue:n(c).reason,"onUpdate:modelValue":S[1]||(S[1]=D=>n(c).reason=D),clearable:"",placeholder:"\u8BF7\u8F93\u5165\u52A0\u7B7E\u7406\u7531"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[P,n(p)]])]),_:1},8,["modelValue"])}}}));var bm=Object.defineProperty,gm=Object.defineProperties,hm=Object.getOwnPropertyDescriptors,wl=Object.getOwnPropertySymbols,wm=Object.prototype.hasOwnProperty,Sm=Object.prototype.propertyIsEnumerable,Sl=(e,a,t)=>a in e?bm(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,xl=(e,a)=>{for(var t in a||(a={}))wm.call(a,t)&&Sl(e,t,a[t]);if(wl)for(var t of wl(a))Sm.call(a,t)&&Sl(e,t,a[t]);return e},xm=(e,a)=>gm(e,hm(a)),Ue=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const km={class:"el-icon-document"},Cm={key:1},_m={class:"el-icon-picture-outline"},Pm={class:"el-icon-picture-outline"},Em={style:{"margin-bottom":"20px","margin-left":"10%","font-size":"14px"},class:"audit-btns"},Om=te(xm(xl({},{name:"BpmProcessInstanceDetail"}),{__name:"index",props:{processInstanceId:Te.string.def("")},setup(e){const a=m("info"),t=m(),{query:r}=ot(),s=je(),{proxy:p}=ha(),c=It().getUser.id,u=m(""),l=e,d=m(!1),g=m({}),b=m(""),f=m(!0),w=m([]),S=m([]),v=m([]),y=Re({reason:[{required:!0,message:"\u5BA1\u6279\u5EFA\u8BAE\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),x=m([]),k=m([]),C=m(),E=m(),j=m({rule:[],option:{},values:{}});re(()=>k.value,T=>{T==null||T.forEach(M=>{M.btn.show(!1),M.resetBtn.show(!1)})},{deep:!0}),re(()=>a.value,T=>{var M;T==="bpmn"&&((M=n(t))==null||M.refreshBpm())});const P=(T,M)=>Ue(this,null,function*(){const z={id:T.id,reason:It().getUser.nickname+"\u8DF3\u8FC7\u5F53\u524D\u8282\u70B9\uFF0C\u8DF3\u8FC7\u539F\u56E0\uFF1A"+M,copyUserIds:[]};yield ya(z),s.success("\u5BA1\u6279\u8DF3\u8FC7\u6210\u529F"),_()}),D=(T,M)=>Ue(this,null,function*(){var z;if(C.value&&C.value.updateForm&&!(yield(z=n(C))==null?void 0:z.updateForm()))return;const X=S.value.indexOf(T),ge=p.$refs["form"+X][0],be=n(ge);if(!be||!(yield be.validate()))return;const Ee={id:T.id,reason:v.value[X].reason,copyUserIds:v.value[X].copyUserIds};if(M){const L=k.value[X];L&&(yield L.validate(),Ee.variables=x.value[X].value),yield ya(Ee),s.success("\u5BA1\u6279\u901A\u8FC7\u6210\u529F")}else yield Ql(Ee),s.success("\u5BA1\u6279\u4E0D\u901A\u8FC7\u6210\u529F");_()}),G=m(),O=T=>Ue(this,null,function*(){var M;C.value&&C.value.updateForm&&!(yield(M=n(C))==null?void 0:M.updateForm())||G.value.open(T)}),U=m(),Y=T=>Ue(this,null,function*(){var M;C.value&&C.value.updateForm&&!(yield(M=n(C))==null?void 0:M.updateForm())||U.value.open(T.id)}),le=m(),H=T=>Ue(this,null,function*(){le.value.open(T.id)}),K=m(),B=T=>Ue(this,null,function*(){var M;C.value&&C.value.updateForm&&!(yield(M=n(C))==null?void 0:M.updateForm())||K.value.open(T.id)}),_=()=>{Z(),Q()},$=m(null),Z=()=>Ue(this,null,function*(){var T;try{d.value=!0;const M=yield Yl(u.value);if(!M){s.error("\u67E5\u8BE2\u4E0D\u5230\u6D41\u7A0B\u4FE1\u606F\uFF01");return}g.value=M;const z=M.processDefinition;z.formType===10?(St(j,z.formConf,z.formFields,M.formVariables),ce().then(()=>{var X,ge,be;(X=E.value)==null||X.btn.show(!1),(ge=E.value)==null||ge.resetBtn.show(!1),(be=E.value)==null||be.disabled(!0)})):$.value=Hl(M.processDefinition.formCustomViewPath),b.value=(T=yield pa(z.id))==null?void 0:T.bpmnXml}finally{d.value=!1}}),Q=()=>Ue(this,null,function*(){S.value=[],v.value=[],x.value=[],k.value=[];try{f.value=!0;const T=yield Zl(u.value);w.value=[],T.forEach(M=>{M.status!==4&&w.value.push(M)}),w.value.sort((M,z)=>M.endTime&&z.endTime?z.endTime-M.endTime:M.endTime?1:z.endTime?-1:z.createTime-M.createTime),V(w.value)}finally{f.value=!1}}),V=T=>{T.forEach(M=>{if(Ie(M.children)||V(M.children),!(M.status!==1&&M.status!==6)&&!(c!==1&&(!M.assigneeUser||M.assigneeUser.id!==c)))if(S.value.push(xl({},M)),v.value.push({reason:"",copyUserIds:[]}),M.formId&&M.formConf){const z={};St(z,M.formConf,M.formFields,M.formVariables),x.value.push(z)}else x.value.push({})}),S.value.length==0&&(a.value="audit")},A=m([]);return we(()=>Ue(this,null,function*(){l.processInstanceId?u.value=l.processInstanceId:u.value=r.id,_(),A.value=yield He()})),(T,M)=>{const z=_e("form-create"),X=Mt,ge=ft,be=ue,Ee=Ca,L=de,q=Se,lt=xe,Tl=ye,Ge=Me,Ke=pe,Pt=fi,Vl=bi,Il=gi,jl=Le,da=Pe;return h(),I(jl,null,{default:o(()=>[i(Il,{gutter:20},{default:o(()=>[i(X,{xs:24,sm:24,md:24,lg:17,xl:17},{default:o(()=>[me((h(),I(ge,{class:"box-card"},{header:o(()=>{var he;return[F("span",km,"\u7533\u8BF7\u4FE1\u606F\u3010"+ee(((he=n(g).formVariables)==null?void 0:he.approve_title)||n(g).name)+"\u3011",1)]}),default:o(()=>{var he,ve,oe,it,ca,ma;return[((ve=(he=n(g))==null?void 0:he.processDefinition)==null?void 0:ve.formType)===10?(h(),I(X,{key:0,offset:6,span:16},{default:o(()=>[i(z,{modelValue:n(j).values,"onUpdate:modelValue":M[0]||(M[0]=Et=>n(j).values=Et),api:n(E),"onUpdate:api":M[1]||(M[1]=Et=>ne(E)?E.value=Et:null),option:n(j).option,rule:n(j).rule},null,8,["modelValue","api","option","rule"])]),_:1})):J("",!0),((it=(oe=n(g))==null?void 0:oe.processDefinition)==null?void 0:it.formType)===20?(h(),R("div",Cm,[i(n($),{id:n(g).businessKey,processInstanceId:n(g).id,modifyForm:(ma=(ca=n(S))==null?void 0:ca[0])==null?void 0:ma.modifyForm,ref_key:"businessFormRef",ref:C},null,8,["id","processInstanceId","modifyForm"])])):J("",!0)]}),_:1})),[[da,n(d)]])]),_:1}),i(X,{xs:24,sm:24,md:24,lg:7,xl:7},{default:o(()=>[i(Vl,{modelValue:n(a),"onUpdate:modelValue":M[2]||(M[2]=he=>ne(a)?a.value=he:null)},{default:o(()=>[n(S).length>0?(h(),I(Pt,{key:0,label:"\u5BA1\u6279\u4FE1\u606F",name:"info"},{default:o(()=>[(h(!0),R(W,null,se(n(S),(he,ve)=>me((h(),I(ge,{key:ve,class:"box-card"},{header:o(()=>[F("span",_m,"\u5BA1\u6279\u4EFB\u52A1\u3010"+ee(he.name)+"\u3011",1)]),default:o(()=>[i(Tl,{ref_for:!0,ref:"form"+ve,model:n(v)[ve],rules:n(y),"label-width":"110px"},{default:o(()=>[n(g)&&n(g).name?(h(),I(be,{key:0,label:"\u6D41\u7A0B\u540D"},{default:o(()=>[N(ee(n(g).name),1)]),_:1})):J("",!0),n(g)&&n(g).startUser?(h(),I(be,{key:1,label:"\u6D41\u7A0B\u53D1\u8D77\u4EBA"},{default:o(()=>{var oe;return[N(ee((oe=n(g))==null?void 0:oe.startUser.nickname)+" ",1),i(Ee,{size:"small",type:"info"},{default:o(()=>{var it;return[N(ee((it=n(g))==null?void 0:it.startUser.deptName),1)]}),_:1})]}),_:1})):J("",!0),n(S)[ve].formId>0?(h(),I(ge,{key:2,class:"mb-15px !-mt-10px",shadow:"never"},{header:o(()=>{var oe;return[F("span",Pm," \u586B\u5199\u8868\u5355\u3010"+ee((oe=n(S)[ve])==null?void 0:oe.formName)+"\u3011 ",1)]}),default:o(()=>[i(z,{modelValue:n(x)[ve].value,"onUpdate:modelValue":oe=>n(x)[ve].value=oe,api:n(k)[ve],"onUpdate:api":oe=>n(k)[ve]=oe,option:n(x)[ve].option,rule:n(x)[ve].rule},null,8,["modelValue","onUpdate:modelValue","api","onUpdate:api","option","rule"])]),_:2},1024)):J("",!0),i(be,{label:"\u5BA1\u6279\u5EFA\u8BAE",prop:"reason"},{default:o(()=>[i(L,{modelValue:n(v)[ve].reason,"onUpdate:modelValue":oe=>n(v)[ve].reason=oe,placeholder:"\u8BF7\u8F93\u5165\u5BA1\u6279\u5EFA\u8BAE",type:"textarea"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),i(be,{label:"\u6284\u9001\u4EBA",prop:"copyUserIds"},{default:o(()=>[i(lt,{modelValue:n(v)[ve].copyUserIds,"onUpdate:modelValue":oe=>n(v)[ve].copyUserIds=oe,multiple:"",placeholder:"\u8BF7\u9009\u62E9\u6284\u9001\u4EBA",filterable:""},{default:o(()=>[(h(!0),R(W,null,se(n(A),oe=>(h(),I(q,{key:oe.id,label:oe.nickname,value:oe.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1032,["model","rules"]),F("div",Em,[i(Ke,{type:"success",onClick:oe=>D(he,!0),size:"small"},{default:o(()=>[i(Ge,{icon:"ep:select"}),N(" \u901A\u8FC7 ")]),_:2},1032,["onClick"]),i(Ke,{type:"danger",onClick:oe=>D(he,!1),size:"small"},{default:o(()=>[i(Ge,{icon:"ep:close"}),N(" \u4E0D\u901A\u8FC7 ")]),_:2},1032,["onClick"]),i(Ke,{type:"primary",onClick:oe=>O(he.id),size:"small"},{default:o(()=>[i(Ge,{icon:"ep:edit"}),N(" \u8F6C\u529E ")]),_:2},1032,["onClick"]),i(Ke,{type:"primary",onClick:oe=>Y(he),size:"small"},{default:o(()=>[i(Ge,{icon:"ep:position"}),N(" \u59D4\u6D3E ")]),_:2},1032,["onClick"]),i(Ke,{type:"primary",onClick:oe=>B(he),size:"small"},{default:o(()=>[i(Ge,{icon:"ep:plus"}),N(" \u52A0\u7B7E ")]),_:2},1032,["onClick"]),i(Ke,{type:"warning",onClick:oe=>H(he),size:"small"},{default:o(()=>[i(Ge,{icon:"ep:back"}),N(" \u56DE\u9000 ")]),_:2},1032,["onClick"])])]),_:2},1024)),[[da,n(d)]])),128))]),_:1})):J("",!0),i(Pt,{label:"\u5BA1\u6279\u8BB0\u5F55",name:"audit"},{default:o(()=>[i(Jc,{loading:n(f),"process-instance":n(g),tasks:n(w),onRefresh:Q,onSkip:P},null,8,["loading","process-instance","tasks"])]),_:1}),i(Pt,{label:"\u6D41\u7A0B\u56FE",name:"bpmn"},{default:o(()=>[i(dc,{id:`${n(u)}`,"bpmn-xml":n(b),loading:n(d),"process-instance":n(g),tasks:n(w),ref_key:"processViewerRef",ref:t},null,8,["id","bpmn-xml","loading","process-instance","tasks"])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),i(sm,{ref_key:"taskTransferFormRef",ref:G,onSuccess:_},null,512),i(Gc,{ref_key:"taskReturnFormRef",ref:le,onSuccess:_},null,512),i(em,{ref_key:"taskDelegateForm",ref:U,onSuccess:_},null,512),i(fm,{ref_key:"taskSignCreateFormRef",ref:K,onSuccess:_},null,512)]),_:1})}}})),Am=Tt(Om,[["__scopeId","data-v-10a64152"]]),Tm=Object.freeze(Object.defineProperty({__proto__:null,default:Am},Symbol.toStringTag,{value:"Module"}));var kl=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const Vm=e=>kl(void 0,null,function*(){return yield ie.post({url:"/bpm/oa/leave/create",data:e})}),Im=e=>kl(void 0,null,function*(){return yield ie.get({url:"/bpm/oa/leave/get?id="+e})});var jm=Object.defineProperty,Dm=Object.defineProperties,Um=Object.getOwnPropertyDescriptors,Cl=Object.getOwnPropertySymbols,Mm=Object.prototype.hasOwnProperty,Nm=Object.prototype.propertyIsEnumerable,_l=(e,a,t)=>a in e?jm(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,Pl=(e,a)=>{for(var t in a||(a={}))Mm.call(a,t)&&_l(e,t,a[t]);if(Cl)for(var t of Cl(a))Nm.call(a,t)&&_l(e,t,a[t]);return e},Bm=(e,a)=>Dm(e,Um(a)),El=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const Fm="oa_leave",Lm=te(Bm(Pl({},{name:"BpmOALeaveCreate"}),{__name:"create",setup(e){const a=je(),{delView:t}=Lt(),{push:r,currentRoute:s}=mt(),p=m(!1),c=m({type:void 0,reason:void 0,startTime:void 0,endTime:void 0}),u=Re({type:[{required:!0,message:"\u8BF7\u5047\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],reason:[{required:!0,message:"\u8BF7\u5047\u539F\u56E0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],startTime:[{required:!0,message:"\u8BF7\u5047\u5F00\u59CB\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],endTime:[{required:!0,message:"\u8BF7\u5047\u7ED3\u675F\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}]}),l=m(),d=m([]),g=m({}),b=m(),f=m({}),w=m([]),S=()=>El(this,null,function*(){var v,y;if(!(!l||!(yield l.value.validate()))){((v=d.value)==null?void 0:v.length)>0&&(yield b.value.validate()),p.value=!0;try{const x=Pl({},c.value);((y=d.value)==null?void 0:y.length)>0&&(x.startUserSelectAssignees=g.value),yield Vm(x),a.success("\u53D1\u8D77\u6210\u529F"),t(n(s)),yield r({name:"BpmOALeave"})}finally{p.value=!1}}});return we(()=>El(this,null,function*(){var v;const y=yield pa(void 0,Fm);if(!y){a.error("OA \u8BF7\u5047\u7684\u6D41\u7A0B\u6A21\u578B\u672A\u914D\u7F6E\uFF0C\u8BF7\u68C0\u67E5\uFF01");return}if(d.value=y.startUserSelectTasks,((v=d.value)==null?void 0:v.length)>0){for(const x of d.value)g.value[x.id]=[],f.value[x.id]=[{required:!0,message:"\u8BF7\u9009\u62E9\u5BA1\u6279\u4EBA",trigger:"blur"}];w.value=yield He()}})),(v,y)=>{const x=Se,k=xe,C=ue,E=hi,j=de,P=ye,D=ft,G=Mt,O=pe,U=Pe;return me((h(),I(P,{ref_key:"formRef",ref:l,model:n(c),rules:n(u),"label-width":"80px"},{default:o(()=>[i(C,{label:"\u8BF7\u5047\u7C7B\u578B",prop:"type"},{default:o(()=>[i(k,{modelValue:n(c).type,"onUpdate:modelValue":y[0]||(y[0]=Y=>n(c).type=Y),clearable:"",placeholder:"\u8BF7\u9009\u62E9\u8BF7\u5047\u7C7B\u578B"},{default:o(()=>[(h(!0),R(W,null,se(n(rt)(n(Oe).BPM_OA_LEAVE_TYPE),Y=>(h(),I(x,{key:Y.value,label:Y.label,value:Y.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),i(C,{label:"\u5F00\u59CB\u65F6\u95F4",prop:"startTime"},{default:o(()=>[i(E,{modelValue:n(c).startTime,"onUpdate:modelValue":y[1]||(y[1]=Y=>n(c).startTime=Y),clearable:"",placeholder:"\u8BF7\u9009\u62E9\u5F00\u59CB\u65F6\u95F4",type:"datetime","value-format":"x"},null,8,["modelValue"])]),_:1}),i(C,{label:"\u7ED3\u675F\u65F6\u95F4",prop:"endTime"},{default:o(()=>[i(E,{modelValue:n(c).endTime,"onUpdate:modelValue":y[2]||(y[2]=Y=>n(c).endTime=Y),clearable:"",placeholder:"\u8BF7\u9009\u62E9\u7ED3\u675F\u65F6\u95F4",type:"datetime","value-format":"x"},null,8,["modelValue"])]),_:1}),i(C,{label:"\u539F\u56E0",prop:"reason"},{default:o(()=>[i(j,{modelValue:n(c).reason,"onUpdate:modelValue":y[3]||(y[3]=Y=>n(c).reason=Y),placeholder:"\u8BF7\u8F93\u8BF7\u5047\u539F\u56E0",type:"textarea"},null,8,["modelValue"])]),_:1}),n(d).length>0?(h(),I(G,{key:0},{default:o(()=>[i(D,{class:"mb-10px"},{header:o(()=>[N("\u6307\u5B9A\u5BA1\u6279\u4EBA")]),default:o(()=>[i(P,{model:n(g),rules:n(f),ref_key:"startUserSelectAssigneesFormRef",ref:b},{default:o(()=>[(h(!0),R(W,null,se(n(d),Y=>(h(),I(C,{key:Y.id,label:`\u4EFB\u52A1\u3010${Y.name}\u3011`,prop:Y.id},{default:o(()=>[i(k,{modelValue:n(g)[Y.id],"onUpdate:modelValue":le=>n(g)[Y.id]=le,multiple:"",placeholder:"\u8BF7\u9009\u62E9\u5BA1\u6279\u4EBA"},{default:o(()=>[(h(!0),R(W,null,se(n(w),le=>(h(),I(x,{key:le.id,label:le.nickname,value:le.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["label","prop"]))),128))]),_:1},8,["model","rules"])]),_:1})]),_:1})):J("",!0),i(C,null,{default:o(()=>[i(O,{disabled:n(p),type:"primary",onClick:S},{default:o(()=>[N("\u786E \u5B9A")]),_:1},8,["disabled"])]),_:1})]),_:1},8,["model","rules"])),[[U,n(p)]])}}})),$m=Object.freeze(Object.defineProperty({__proto__:null,default:Lm},Symbol.toStringTag,{value:"Module"}));var Rm=Object.defineProperty,zm=Object.defineProperties,qm=Object.getOwnPropertyDescriptors,Ol=Object.getOwnPropertySymbols,Jm=Object.prototype.hasOwnProperty,Gm=Object.prototype.propertyIsEnumerable,Al=(e,a,t)=>a in e?Rm(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,Km=(e,a)=>{for(var t in a||(a={}))Jm.call(a,t)&&Al(e,t,a[t]);if(Ol)for(var t of Ol(a))Gm.call(a,t)&&Al(e,t,a[t]);return e},Ym=(e,a)=>zm(e,qm(a)),Hm=(e,a,t)=>new Promise((r,s)=>{var p=l=>{try{u(t.next(l))}catch(d){s(d)}},c=l=>{try{u(t.throw(l))}catch(d){s(d)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(p,c);u((t=t.apply(e,a)).next())});const Zm=te(Ym(Km({},{name:"BpmOALeaveDetail"}),{__name:"detail",props:{id:Te.number.def(void 0)},setup(e,{expose:a}){const{query:t}=ot(),r=e,s=m(!1),p=m({}),c=t.id,u=()=>Hm(this,null,function*(){s.value=!0;try{p.value=yield Im(r.id||c)}finally{s.value=!1}});return a({open:u}),we(()=>{u()}),(l,d)=>{const g=ct,b=wi,f=Si,w=Le;return h(),I(w,null,{default:o(()=>[i(f,{column:1,border:""},{default:o(()=>[i(b,{label:"\u8BF7\u5047\u7C7B\u578B"},{default:o(()=>[i(g,{type:n(Oe).BPM_OA_LEAVE_TYPE,value:n(p).type},null,8,["type","value"])]),_:1}),i(b,{label:"\u5F00\u59CB\u65F6\u95F4"},{default:o(()=>[N(ee(n(ke)(n(p).startTime,"YYYY-MM-DD")),1)]),_:1}),i(b,{label:"\u7ED3\u675F\u65F6\u95F4"},{default:o(()=>[N(ee(n(ke)(n(p).endTime,"YYYY-MM-DD")),1)]),_:1}),i(b,{label:"\u539F\u56E0"},{default:o(()=>[N(ee(n(p).reason),1)]),_:1})]),_:1})]),_:1})}}})),Qm=Object.freeze(Object.defineProperty({__proto__:null,default:Zm},Symbol.toStringTag,{value:"Module"}));export{Yi as a,Bu as b,Gd as c,ac as d,Tm as e,$m as f,Oa as g,Qm as h,ur as i,Lt as u};
