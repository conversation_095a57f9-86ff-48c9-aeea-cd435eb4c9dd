<template>
  <vxe-split-pane width="300px" min-width="300px">
    <div class="p-10px">
      <el-form class="custom-form" label-width="110px">
        <el-divider>项目基础信息</el-divider>
        <el-form-item label="项目名称">
          <el-input placeholder="请输入项目名称" v-model="formData.name" />
        </el-form-item>
        <el-form-item label="项目状态">
          <el-select v-model="formData.status" multiple>
            <el-option
              v-for="dict in getIntDictOptions('project_status')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="项目等级">
          <el-select v-model="formData.level" multiple>
            <el-option
              v-for="dict in getIntDictOptions('project_level')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="项目平台">
          <el-select v-model="formData.platform" multiple>
            <el-option
              v-for="dict in getIntDictOptions('project_platform')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="项目类型">
          <el-select v-model="formData.mold" multiple>
            <el-option
              v-for="dict in getIntDictOptions('project_type')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-divider>费用分摊数据年份</el-divider>
        <el-form-item label="年份">
          <el-date-picker
            type="year"
            v-model="formData.year"
            value-format="YYYY"
            :clearable="false"
          />
        </el-form-item>
      </el-form>
    </div>
  </vxe-split-pane>
  <vxe-split-pane>
    <div class="p-10px h-full">
      <vxe-toolbar size="mini" ref="toolbarRef">
        <template #buttons>
          <el-button type="primary" size="small" @click="onList"> 查询 </el-button>
        </template>
        <template #tools>
          <el-button type="warning" size="small" @click="handleExport">导出</el-button>
        </template>
      </vxe-toolbar>
      <vxe-table
        height="86%"
        :header-cell-style="{ padding: '0', height: '34px' }"
        :cell-style="{ padding: '0', height: '34px', cursor: 'pointer' }"
        :header-cell-config="{ height: '34px' }"
        :cell-config="{ height: '34px' }"
        :row-config="{ isCurrent: true }"
        show-overflow
        align="center"
        border
        ref="tableRef"
        :data="list"
        :loading="loading"
        :export-config="{
          remote: true
        }"
        :virtual-y-config="{ enabled: true, gt: 0, immediate: true }"
        :virtual-x-config="{ enabled: true, gt: 0, immediate: true }"
        @cell-click="handleCellClick"
      >
        <vxe-column title="项目名称" field="basicsName" align="left" width="200px" />
        <vxe-column title="项目等级" field="basicsLevel" width="100px">
          <template #default="{ row }">
            {{ getDictLabel('project_level', row.basicsLevel) }}
          </template>
        </vxe-column>
        <vxe-column title="项目平台" field="basicsPlatform" width="100px">
          <template #default="{ row }">
            {{ getDictLabel('project_platform', row.basicsPlatform) }}
          </template>
        </vxe-column>
        <vxe-column title="项目类型" field="basicsType" width="100px">
          <template #default="{ row }">
            {{ getDictLabel('project_form', row.basicsType) }}
          </template>
        </vxe-column>
        <vxe-column title="开发类型" field="basicsMold" width="100px">
          <template #default="{ row }">
            {{ getDictLabel('project_type', row.basicsMold) }}
          </template>
        </vxe-column>
        <vxe-column title="状态" field="basicsStatus" width="100px">
          <template #default="{ row }">
            {{ getDictLabel('project_status', row.basicsStatus) }}
          </template>
        </vxe-column>
        <vxe-column title="进度" field="basicsProgress" width="100px" />
        <vxe-column title="系统编号" field="basicsNumber" width="100px" />
        <vxe-column title="ERP编码" field="basicsErpCode" width="100px" />
        <vxe-column title="项目计划下单时间" field="basicsPlanOrderDate" width="100px" />
        <vxe-column title="项目实际下单时间" field="basicsActualOrderDate" width="100px" />
        <vxe-column title="立项时间" field="basicsCreateDate" width="100px" />
        <vxe-column title="完成时间" field="basicsActualCompletedDate" width="100px" />
        <vxe-column title="计划成本" field="planCost" width="100px" />
        <vxe-column title="实际成本" field="actualCost" width="100px" />
        <vxe-column title="成本达成率" field="costRate" width="100px">
          <template #default="{ row }">
            <span v-if="row.costRate">{{ row.costRate }}%</span>
          </template>
        </vxe-column>
        <vxe-column title="OA流程编码" field="flowNumber" width="100px" />
        <vxe-column title="OA流程" field="flowName" width="100px" />
        <vxe-column
          v-for="dict in getStrDictOptions('project_team_role')"
          :key="dict.value"
          :title="dict.label"
          :field="dict.value"
          width="100px"
        >
          <template #default="{ row }">
            {{ getUserNickName(row.teamMembers?.[dict.value]) }}
          </template>
        </vxe-column>
        <vxe-column
          v-for="node in [
            { label: '签发任务书', value: 'start' },
            { label: 'TR1', value: 'TR1' },
            { label: 'CDCP', value: 'CDCP' },
            { label: 'TR2', value: 'TR2' },
            { label: 'TR3', value: 'TR3' },
            { label: 'PDCP', value: 'PDCP' },
            { label: 'TR4', value: 'TR4' },
            { label: 'TR4A', value: 'TR4A' },
            { label: 'TR5', value: 'TR5' },
            { label: 'TR6', value: 'TR6' },
            { label: 'ADCP', value: 'ADCP' }
          ]"
          :key="node.value"
          :title="node.label"
          :field="`activitiesDates.${node.value}`"
          width="100px"
        />
        <vxe-colgroup
          v-for="month in [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]"
          :key="month"
          :title="month === 13 ? '全年统计' : month == 14 ? '累计数据' : `${month}月数据`"
        >
          <vxe-column
            title="报销费用"
            :field="`monthlyShareExpenses.${month - 1}.reimbursement`"
            width="80"
          />
          <vxe-column
            title="物料领用费用"
            :field="`monthlyShareExpenses.${month - 1}.receive`"
            width="80"
          />
          <vxe-column
            title="备注"
            :field="`monthlyShareExpenses.${month - 1}.remark`"
            width="80"
            v-if="![13, 14].includes(month)"
          />
        </vxe-colgroup>
      </vxe-table>
    </div>
  </vxe-split-pane>
  <vxe-split-pane width="400px" min-width="400px">
    <vxe-table
      height="100%"
      :header-cell-style="{ padding: '0', height: '34px' }"
      :cell-style="{ padding: '0', height: '34px', cursor: 'pointer' }"
      :cell-config="{ height: '34px' }"
      :row-config="{ isCurrent: true }"
      show-overflow
      align="center"
      border
      :data="workingHoursList"
      show-footer
      :footerMethod="footerMethodSum"
      empty-text="请选择左侧项目"
    >
      <vxe-column title="团队" field="role" />
      <vxe-column title="人员" field="manager" />
      <vxe-column title="实际工时" field="actualHours" />
    </vxe-table>
  </vxe-split-pane>
</template>

<script lang="ts" setup>
import { ActivitiesApi } from '@/api/project/activities'
import { ExportApi } from '@/api/project/export'
import { getAllUser } from '@/api/system/user'
import { getDictLabel, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import download from '@/utils/download'
import moment from 'moment'

const formData = ref<any>({
  name: undefined,
  status: undefined,
  level: undefined,
  platform: undefined,
  mold: undefined,
  year: moment().format('YYYY')
})
const list = ref<any[]>([])
const userList = ref<any[]>([])
const loading = ref(false)
const workingHoursList = ref<any[]>([])

const onList = async () => {
  loading.value = true
  try {
    const res = await ExportApi.getBasicsExportList(formData.value)
    list.value = res
  } finally {
    loading.value = false
  }
}
const message = useMessage()
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    const data = await ExportApi.exportBasicsExportList(formData.value)
    download.excel(data, '项目工时信息.xlsx')
  } catch {
  } finally {
  }
}
/** 获取用户名 */
const getUserNickName = (ids: number[]) => {
  if (!ids || ids.length === 0) return ''
  return userList.value
    .filter((item) => ids.includes(item.id))
    .map((item) => item.nickname + (item.status == 2 ? '(离职)' : ''))
    .join(',')
}
const onListUser = async () => {
  const res = await getAllUser()
  userList.value = res
}

/** 表尾合计函数 */
function footerMethodSum({ columns, data }) {
  const footerData = [
    columns.map((column, _columnIndex) => {
      if (_columnIndex === 0) {
        return '合计'
      }
      if (_columnIndex === 1) return
      return sumNum(data, column.field)
    })
  ]
  return footerData
}

// 进行合计
function sumNum(costForm, type) {
  let total = 0
  for (let i = 0; i < costForm.length; i++) {
    total += costForm[i][type]
  }
  return total
}

const handleCellClick = async ({ row }) => {
  const res = await ActivitiesApi.getWorkingHoursTotal(row.basicsId)
  workingHoursList.value = res
}

onMounted(() => {
  onList()
  onListUser()
})
</script>
