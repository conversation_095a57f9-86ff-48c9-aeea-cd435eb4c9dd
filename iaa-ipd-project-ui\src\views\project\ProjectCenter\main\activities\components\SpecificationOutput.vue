<template>
  <div
    class="flex mb-10px"
    v-if="allowPermission && data.status !== 10 && data.progress !== 100 && allowTheOutput"
  >
    <el-button
      type="success"
      plain
      size="small"
      class="!w-33%"
      @click="
        () => {
          specEdit = false
          openSpecSelectedFrom()
        }
      "
    >
      引用已有规格书
    </el-button>
    <el-popover trigger="click" placement="bottom-start" width="300">
      <template #reference>
        <el-button type="primary" plain size="small" class="!w-33%">创建新规格书</el-button>
      </template>
      <el-input v-model="templateName">
        <template #append>
          <el-button :icon="Search" @click="onTemplateList" />
        </template>
      </el-input>
      <div class="max-h-200px w-full border-1px border-solid border-#f1f1f1 rounded mt-5px">
        <div
          class="flex p-5px justify-between border-b-1px border-b-solid border-b-#f1f1f1"
          v-for="(item, index) in specDocTemplateList"
          :key="index"
        >
          <el-button type="primary" link @click="openCreateSpec(item)">
            {{ item.name }}
          </el-button>
        </div>
      </div>
    </el-popover>

    <el-button
      type="warning"
      plain
      size="small"
      class="!w-33%"
      @click="
        () => {
          specEdit = true
          openSpecSelectedFrom()
        }
      "
    >
      根据已有规格书修改
    </el-button>
  </div>
  <vxe-table
    :data="dockingList"
    :header-cell-config="{ height: 30 }"
    :cell-config="{ height: 30 }"
    :cell-style="{ padding: '0 5px' }"
    :header-cell-style="{ padding: '0 5px' }"
    show-overflow
    border
    stripe
  >
    <vxe-column title="规格书" field="name" />
    <vxe-column title="审批状态" field="approvalStatus" width="200" align="center">
      <template #default="{ row }">
        <DictTag type="specification_status" :value="row.approvalStatus" />
        <el-button
          type="primary"
          link
          v-if="row.approvalStatus != 0"
          @click="toBpm(row.processInstanceId)"
        >
          跳转流程
        </el-button>
      </template>
    </vxe-column>
    <vxe-column title="操作" field="opereation" width="200" align="center">
      <template #default="{ row }">
        <el-button
          type="warning"
          link
          size="small"
          v-if="
            row.approvalStatus == 0 &&
            allowPermission &&
            data.status !== 10 &&
            data.progress !== 100 &&
            allowTheOutput
          "
          @click="onPreviewSpec(row, false)"
        >
          修改
        </el-button>
        <el-button type="primary" link size="small" @click="onPreviewSpec(row, true)">
          查看详情
        </el-button>
        <el-button
          type="danger"
          link
          size="small"
          v-if="allowPermission && data.status !== 10 && data.progress !== 100 && allowTheOutput"
          @click="deleteDocking(row.id)"
        >
          删除
        </el-button>
      </template>
    </vxe-column>
  </vxe-table>

  <!-- 规格书选择对话框 -->
  <Dialog title="选择规格书作为输出对象" v-model="specSelectedVisible">
    <el-input
      v-model="specName"
      size="small"
      placeholder="输入内容发起规格书查询"
      @keydown.enter="onListSpec"
    >
      <template #append>
        <el-button type="primary" :icon="Search" size="small" @click="onListSpec" />
      </template>
    </el-input>
    <vxe-table
      class="mt-10px"
      :data="specList"
      :header-cell-config="{ height: 30 }"
      :cell-config="{ height: 30 }"
      :cell-style="{ padding: '0 5px' }"
      :header-cell-style="{ padding: '0 5px' }"
      height="250px"
      border
      stripe
      show-overflow
    >
      <vxe-column title="规格书" field="name" />
      <vxe-column title="状态" field="status" width="100">
        <template #default="{ row }">
          <DictTag type="specification_status" :value="row.status" />
        </template>
      </vxe-column>
      <vxe-column title="版本" field="version" width="100px" align="center">
        <template #default="{ row }">
          {{ `V${row.version}` }}
        </template>
      </vxe-column>
      <vxe-column title="操作" align="center" width="100px">
        <template #default="{ row }">
          <el-button type="primary" link size="small" @click="onSpecSelect(row)" v-if="!specEdit">
            选择
          </el-button>
          <el-button
            type="warning"
            link
            size="small"
            v-if="specEdit"
            @click="onPreviewSpec({ code: row.id }, false)"
          >
            修改
          </el-button>
        </template>
      </vxe-column>
    </vxe-table>
    <template v-if="!specEdit">
      <el-divider>已选规格书</el-divider>
      <vxe-table
        :data="specSelectedList"
        :header-cell-config="{ height: 30 }"
        :cell-config="{ height: 30 }"
        :cell-style="{ padding: '0 5px' }"
        :header-cell-style="{ padding: '0 5px' }"
        height="250px"
        border
        stripe
      >
        <vxe-column title="规格书" field="name" />
        <vxe-column title="状态" field="status" width="100">
          <template #default="{ row }">
            <DictTag type="specification_status" :value="row.status" />
          </template>
        </vxe-column>
        <vxe-column title="版本" field="version" width="100px" align="center">
          <template #default="{ row }">
            {{ `V${row.version}` }}
          </template>
        </vxe-column>
        <vxe-column title="操作" align="center" width="100px">
          <template #default="{ row }">
            <el-button type="danger" link size="small" @click="onSepcUnSelected(row)">
              取消选择
            </el-button>
          </template>
        </vxe-column>
      </vxe-table>
    </template>
    <template #footer v-if="!specEdit">
      <el-button type="primary" @click="onSubmitSpecList" :loading="bomSelectedLoading">
        保存
      </el-button>
    </template>
  </Dialog>

  <!-- 规格书预览对话框 -->
  <Dialog title="规格书查看" v-model="specPreviewVisible" width="80%" class="file-viewer-dialog">
    <template #title>
      <div class="flex justify-between items-center">
        <div class="text-white leading-40px">
          {{ specData?.name || '创建' + specDocTemplate?.name }}
        </div>
        <el-button link v-if="specData?.id" @click="specificationRef?.onExport()" class="ml-50px">
          <Icon icon="ep:download" />
          <span class="text-white">下载为word文件</span>
        </el-button>
      </div>
    </template>
    <SpecificationForm
      ref="specificationRef"
      @success="onSubmitSpec"
      @cancel="specPreviewVisible = false"
    />
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { ActivitiesVO } from '@/api/project/activities'
import { DockingApi } from '@/api/project/activitiestargetdocking'
import { SpecificationApi } from '@/api/project/specification'
import { useMessage } from '@/hooks/web/useMessage'
import SpecificationForm from '@/views/project/ArchiveCenter/specification/SpecificationForm.vue'

const props = defineProps<{
  data: ActivitiesVO
  formData: ActivitiesVO
  allowPermission: boolean
  allowTheOutput: boolean
}>()

const emits = defineEmits(['completed', 'refresh', 'docking-updated'])

const message = useMessage()

// 响应式数据
const dockingList = ref<any[]>([])
const specSelectedVisible = ref(false)
const specList = ref<any[]>([])
const specName = ref('')
const specSelectedList = ref<any[]>([])
const specEdit = ref(false)
const specPreviewVisible = ref(false)
const specData = ref<any>({})
const specificationRef = ref()
const specDocTemplateList = ref<any[]>([])
const specDocTemplate = ref<any>({})
const templateName = ref('')
const bomSelectedLoading = ref(false)

// 方法
const onListDocking = async () => {
  const res = await DockingApi.getDockingList(props.formData.id!)
  dockingList.value = res
  emits('docking-updated')
}

const onListSpec = async () => {
  const res = await SpecificationApi.listSpecification(specName.value)
  specList.value = res
}

const openSpecSelectedFrom = () => {
  specSelectedVisible.value = true
  onListSpec()
}

const onSpecSelect = (row: any) => {
  const exists = specSelectedList.value.some((item) => item.id === row.id)
  if (exists) {
    message.alertError('当前规格书已选择')
    return
  }
  specSelectedList.value.push(row)
}

const onSepcUnSelected = (row: any) => {
  specSelectedList.value = specSelectedList.value.filter((item) => item.id != row.id)
}

const onSubmitSpecList = async () => {
  if (!(specSelectedList.value && specSelectedList.value.length > 0)) {
    message.alertError('未选择任何规格书')
    return
  }
  bomSelectedLoading.value = true
  try {
    const data = specSelectedList.value.map((item) => {
      return {
        activitiesId: props.formData.id,
        targetType: props.formData.targetType,
        targetDockingId: props.formData.targetDockingId,
        code: item.id,
        name: item.name + '-V' + item.version,
        approvalStatus: item.status,
        processInstanceId: item.processInstanceId
      }
    })
    await DockingApi.createBatch(data)
    specName.value = ''
    specSelectedList.value = []
    specList.value = []
    specSelectedVisible.value = false
    await onListDocking()
    if (!specSelectedList.value.some((item) => item.status != 3)) {
      await message.confirm('保存成功，请确认是否发起活动完成流程')
      message
        .confirm('保存成功，请确认是否发起活动完成流程')
        .then(() => {
          emits('completed')
        })
        .catch(() => {
          emits('refresh')
        })
    }
  } finally {
    bomSelectedLoading.value = false
  }
}

const onPreviewSpec = async (row: any, edit: boolean) => {
  const res = await SpecificationApi.getSpecification(row.code)
  specData.value = res
  specPreviewVisible.value = true
  await nextTick()
  specificationRef.value?.openForm({}, edit, specData.value)
}

const onSubmitSpec = async (row: any) => {
  bomSelectedLoading.value = true
  try {
    const dockingData = dockingList.value.find((item) => item.code == row.id)
    await DockingApi.createBatch([
      {
        id: dockingData?.id,
        activitiesId: props.formData.id,
        targetType: props.formData.targetType,
        targetDockingId: props.formData.targetDockingId,
        code: row.id,
        name: row.name + '-V' + row.version,
        approvalStatus: row.status,
        processInstanceId: row.processInstanceId
      }
    ])
    specPreviewVisible.value = false
    await onListDocking()
    if (!specSelectedList.value.some((item) => item.status != 3)) {
      message
        .confirm('保存成功，请确认是否发起活动完成流程')
        .then(() => {
          emits('completed')
        })
        .catch(() => {
          emits('refresh')
        })
    }
  } finally {
    bomSelectedLoading.value = false
  }
}

const openCreateSpec = async (template: any) => {
  specPreviewVisible.value = true
  specDocTemplate.value = template
  await nextTick()
  specificationRef.value?.openForm(template, false)
}

const onTemplateList = async () => {
  const res = await SpecificationApi.listDocTemplate(templateName.value)
  specDocTemplateList.value = res
}

const deleteDocking = async (id: number) => {
  await message.confirm('确定删除该输出对象？')
  await DockingApi.delDocking(id)
  message.success('删除成功')
  onListDocking()
}

const toBpm = (processInstanceId: string) => {
  window.open(`/bpm/process-instance/detail?id=${processInstanceId}`, '_blank')
}

// 初始化方法，由父组件调用
const init = async () => {
  await onListDocking()
  await onTemplateList()
}

// 暴露方法给父组件
defineExpose({
  init,
  onListDocking,
  onTemplateList,
  openSpecSelectedFrom,
  onPreviewSpec
})
</script>
