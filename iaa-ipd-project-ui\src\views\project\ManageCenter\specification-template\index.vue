<template>
  <ContentWrap>
    <el-form inline>
      <el-form-item label="模板名称">
        <el-input v-model="queryParams.name" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary">查询</el-button>
        <el-button type="success" @click="specificationFormRef?.openForm()">新增</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <ContentWrap>
    <vxe-table
      :data="list"
      :header-cell-config="{ height: 30 }"
      :cell-config="{ height: 30 }"
      :loading="loading"
      border
      stripe
      align="center"
      show-overflow
    >
      <vxe-column title="模板编号" field="templateCode" />
      <vxe-column title="模板名称" field="name" />
      <vxe-column title="版本" field="version">
        <template #default="{ row }">
          <el-button type="primary" link size="small" @click="openListHistory(row.templateCode)">{{
            `V${row.version}`
          }}</el-button>
        </template>
      </vxe-column>
      <vxe-column title="操作" field="operation">
        <template #default="{ row }">
          <el-button type="primary" link size="small" @click="specificationFormRef?.openForm(row)">
            编辑
          </el-button>
          <el-button type="warning" link size="small" @click="openPreviewForm(row.content)">
            预览表单
          </el-button>
        </template>
      </vxe-column>
    </vxe-table>
    <Pagination
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      :total="total"
    />
  </ContentWrap>
  <SpecificationTemplateForm ref="specificationFormRef" @success="handleList" />
  <NoModalDrawer title="预览表单" v-model="previewFormVisible" size="60%">
    <AmisRenderer :formid="previewForm?.id" :formjson="previewForm" />
  </NoModalDrawer>
  <Dialog title="历史版本" v-model="historyVisible">
    <vxe-table
      :data="historyList"
      :loading="loading"
      :header-cell-config="{ height: 30 }"
      :cell-config="{ height: 30 }"
      border
      stripe
      align="center"
      show-overflow
    >
      <vxe-column title="模板编号" field="templateCode" />
      <vxe-column title="模板名称" field="name" />
      <vxe-column title="版本" field="version">
        <template #default="{ row }">
          {{ `V${row.version}` }}
        </template>
      </vxe-column>
      <vxe-column title="操作" field="operation">
        <template #default="{ row }">
          <el-button type="warning" link size="small" @click="openPreviewForm(row.content)">
            预览表单
          </el-button>
        </template>
      </vxe-column>
    </vxe-table>
  </Dialog>
</template>

<script lang="ts" setup>
import SpecificationTemplateForm from './SpecificationTemplateForm.vue'
import { SpecificationApi } from '@/api/project/specification'

const queryParams = reactive({
  pageNo: 1,
  pageSize: 30,
  name: ''
})
const total = ref(0)
const list = ref<any[]>([])
const loading = ref(false)
const specificationFormRef = ref()
const previewFormVisible = ref(false)
const previewForm = ref<any>({})
const historyList = ref<any[]>([])
const historyVisible = ref(false)
const onList = async () => {
  loading.value = true
  try {
    const res = await SpecificationApi.pageDocTemplate(queryParams)
    list.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const handleList = () => {
  queryParams.pageNo = 1
  onList()
}

const openPreviewForm = (content: string) => {
  previewFormVisible.value = true
  previewForm.value = JSON.parse(content)
}

const openListHistory = async (templateCode: string) => {
  historyVisible.value = true
  historyList.value = await SpecificationApi.getTemplateListByCode(templateCode)
}

onMounted(() => {
  onList()
})
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 10px !important;
}
</style>
