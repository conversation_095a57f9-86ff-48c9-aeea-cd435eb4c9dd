<template>
  <div class="flex w-full">
    <div class="flex-1">
      <el-select v-model="parentValue" filterable clearable @change="onTopChange">
        <el-option
          v-for="dict in treeData"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>
    </div>
    <div class="flex-1" v-if="topData?.children?.length > 0">
      <el-select v-model="childValue" filterable clearable @change="onChildChange">
        <el-option
          v-for="dict in topData?.children"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'
import { handleTree } from '@/utils/tree'

const parentValue = ref<any>(undefined)
const childValue = ref<any>(undefined)
const topData = ref<any>({})
const treeData = ref<any[]>([])

// 定义 emits 用于向父组件传递值
const emit = defineEmits(['update:modelValue'])

const props = defineProps({
  data: propTypes.oneOf<any[]>([]).isRequired,
  modelValue: propTypes.string
})

const onTopChange = (value: any) => {
  childValue.value = undefined
  topData.value = treeData.value?.find((item) => item.value == value)
  console.log(topData.value?.children?.length)
  if (!topData.value?.children || topData.value?.children?.length == 0) {
    emit('update:modelValue', value)
  }
}

const onChildChange = (value: any) => {
  emit('update:modelValue', value)
}

watch(
  () => props.modelValue,
  () => {
    if (!props.modelValue) return
    parentValue.value = undefined
    childValue.value = undefined

    const itemData = props.data.find((item) => item.value == props.modelValue)
    if (!itemData.parentId) {
      parentValue.value = itemData.value
    } else {
      parentValue.value = props.data.find((item) => item.id == itemData.parentId).value
      childValue.value = itemData.value
    }
  },
  { immediate: true }
)

watch(
  () => props.data,
  () => {
    treeData.value = handleTree(props.data)
  },
  { immediate: true }
)
</script>
