import { resolve } from 'path'
import { loadEnv } from 'vite'
import type { UserConfig, ConfigEnv } from 'vite'
import { createVitePlugins } from './build/vite'

// 当前执行node命令时文件夹的地址(工作目录)
const root = process.cwd()

// 路径查找
function pathResolve(dir: string) {
  return resolve(root, '.', dir)
}

// 极低内存构建配置 - 牺牲一些性能换取内存
export default ({ command, mode }: ConfigEnv): UserConfig => {
  let env = {} as any
  const isBuild = command === 'build'
  if (!isBuild) {
    env = loadEnv((process.argv[3] === '--mode' ? process.argv[4] : process.argv[3]), root)
  } else {
    env = loadEnv(mode, root)
  }
  
  return {
    base: env.VITE_BASE_PATH,
    root: root,
    server: {
      port: env.VITE_PORT,
      host: "0.0.0.0",
      open: env.VITE_OPEN === 'true',
    },
    plugins: createVitePlugins(),
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@import "./src/styles/variables.scss";',
          javascriptEnabled: true
        }
      }
    },
    resolve: {
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.scss', '.css'],
      alias: [
        {
          find: 'vue-i18n',
          replacement: 'vue-i18n/dist/vue-i18n.cjs.js'
        },
        {
          find: /\@\//,
          replacement: `${pathResolve('src')}/`
        }
      ]
    },
    build: {
      minify: 'esbuild', // 使用更快的 esbuild
      outDir: env.VITE_OUT_DIR || 'dist',
      sourcemap: false, // 禁用 sourcemap
      reportCompressedSize: false, // 禁用压缩大小报告
      chunkSizeWarningLimit: 500, // 更严格的chunk大小限制
      rollupOptions: {
        maxParallelFileOps: 1, // 严格限制并行文件操作
        output: {
          // 更激进的代码分割
          manualChunks: (id) => {
            // 将每个大型库都单独分割
            if (id.includes('node_modules')) {
              if (id.includes('vue') && !id.includes('vue-router')) return 'vue'
              if (id.includes('vue-router')) return 'vue-router'
              if (id.includes('pinia')) return 'pinia'
              if (id.includes('element-plus')) return 'element-plus'
              if (id.includes('echarts')) return 'echarts'
              if (id.includes('amis-editor')) return 'amis-editor'
              if (id.includes('amis-ui')) return 'amis-ui'
              if (id.includes('amis-core')) return 'amis-core'
              if (id.includes('amis')) return 'amis'
              if (id.includes('markmap')) return 'markmap'
              if (id.includes('lodash')) return 'lodash'
              if (id.includes('axios')) return 'axios'
              if (id.includes('dayjs')) return 'dayjs'
              // 其他小型库
              return 'vendor'
            }
          },
          chunkFileNames: 'js/[name]-[hash].js',
          entryFileNames: 'js/[name]-[hash].js',
          assetFileNames: 'assets/[name]-[hash].[ext]'
        },
      },
    },
    optimizeDeps: {
      // 禁用预构建以节省内存（会影响开发体验）
      disabled: isBuild,
      include: isBuild ? [] : [
        'vue',
        'vue-router',
        'element-plus'
      ],
      exclude: [
        '@iconify/json'
      ],
      esbuildOptions: {
        target: 'es2015',
        logLevel: 'error'
      }
    },
    esbuild: {
      drop: ['console', 'debugger'],
      target: 'es2015',
      legalComments: 'none',
      minifyIdentifiers: true,
      minifySyntax: true,
      minifyWhitespace: true
    }
  }
}
