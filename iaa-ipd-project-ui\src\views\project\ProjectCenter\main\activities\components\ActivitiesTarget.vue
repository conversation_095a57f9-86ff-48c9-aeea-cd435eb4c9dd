<template>
  <template v-if="formData.targetType === 3">
    <el-empty description="无需输出" />
  </template>
  <!-- 附件输出 -->
  <template v-else-if="[0, 4].includes(formData.targetType!)">
    <el-collapse v-model="activeNames" ref="collapseRef" class="custom-collapse">
      <AttachmentOutput
        ref="attachmentOutputRef"
        :data="props.data"
        :form-data="formData"
        :file-template-list="props.fileTemplateList"
        :allow-permission="allowPermission"
        :allow-the-output="allowTheOutput"
        @attachment-updated="(res) => (attachmentList = res)"
      />
      <WorkHoursConfirm
        ref="workHoursConfirmRef"
        :data="props.data"
        :form-data="formData"
        :attachment-list="attachmentList"
        @audit-work-hours="onAduitWork"
      />
    </el-collapse>
  </template>

  <template v-else-if="formData.targetType == 1">
    <BomOutput
      ref="bomOutputRef"
      :data="props.data"
      :form-data="formData"
      :allow-permission="allowPermission"
      :allow-the-output="allowTheOutput"
      @completed="onCompleted"
      @refresh="emits('refresh')"
    />
  </template>
  <template v-else-if="formData.targetType == 5">
    <SpecificationOutput
      ref="specificationOutputRef"
      :data="props.data"
      :form-data="formData"
      :allow-permission="allowPermission"
      :allow-the-output="allowTheOutput"
      @completed="onCompleted"
      @refresh="emits('refresh')"
    />
  </template>
  <template v-else-if="formData.targetType == 6">
    <ActivityOutput
      ref="activityOutputRef"
      :data="props.data"
      :form-data="formData"
      :allow-permission="allowPermission"
      :allow-the-output="allowTheOutput"
      @completed="onCompleted"
      @refresh="emits('refresh')"
    />
    <!-- @docking-updated="onDockingUpdated" -->
  </template>
  <template v-else>
    <el-empty description="开发中，暂不支持" />
  </template>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { ActivitiesVO } from '@/api/project/activities'
import { propTypes } from '@/utils/propTypes'
import { useUserStore } from '@/store/modules/user'
import AttachmentOutput from './AttachmentOutput.vue'
import BomOutput from './BomOutput.vue'
import SpecificationOutput from './SpecificationOutput.vue'
import ActivityOutput from './ActivityOutput.vue'
import WorkHoursConfirm from './WorkHoursConfirm.vue'

const props = defineProps({
  data: propTypes.oneOf<ActivitiesVO>([]).isRequired,
  fileTemplateList: propTypes.arrayOf<any>([]).def([]),
  edit: propTypes.bool.def(false)
})

const emits = defineEmits(['refresh', 'completed'])

const formData = ref<ActivitiesVO>({})
const { getUser } = useUserStore()

// 响应式数据
const activeNames = ref(['1', '2', '3'])
const attachmentList = ref<any[]>([])

// 子组件引用
const attachmentOutputRef = ref()
const bomOutputRef = ref()
const specificationOutputRef = ref()
const activityOutputRef = ref()
const workHoursConfirmRef = ref()

const allowPermission = computed(() => {
  return (
    props.data.director?.includes(getUser.id) ||
    props.data.coordinate?.includes(getUser.id) ||
    false
  )
})

const allowTheOutput = computed(() => {
  if (!props.data.dependencies || props.data.dependencies.length == 0) {
    return true
  }
  let allow = true
  props.data.dependencies.forEach((item) => {
    if (item.dependencyType == 'fs' && item.progress != 100) {
      allow = false
    }
  })
  return allow
})

const onCompleted = () => {
  emits('completed')
}

const onAduitWork = async () => {
  try {
    await workHoursConfirmRef.value?.auditWorkHours()
    emits('refresh') // 刷新数据
  } catch (error) {
    console.error('工时确认失败:', error)
  }
}

// 初始化子组件数据
const initChildComponents = async () => {
  // 等待下一个tick确保子组件已挂载
  await nextTick()

  // 根据targetType初始化对应的子组件
  if (formData.value.targetType === 0 || formData.value.targetType === 4) {
    // 附件输出
    await attachmentOutputRef.value?.init()
    await workHoursConfirmRef.value?.init()
  } else if (formData.value.targetType === 1) {
    // BOM输出
    await bomOutputRef.value?.init()
  } else if (formData.value.targetType === 5) {
    // 规格书输出
    await specificationOutputRef.value?.init()
  } else if (formData.value.targetType === 6) {
    // 活动输出
    await activityOutputRef.value?.init()
  }
}

// 初始化
const init = () => {
  formData.value = { ...props.data }
  initChildComponents()
}

// 生命周期
onMounted(() => {
  init()
})

// 监听数据变化
watch(
  () => props.data,
  (newData) => {
    if (newData) {
      formData.value = { ...newData }
      // 当数据变化时，重新初始化子组件
      initChildComponents()
    }
  },
  { immediate: true, deep: true }
)

// 暴露方法给父组件
defineExpose({
  init,
  formData
})
</script>

<style lang="scss" scoped>
:deep(.el-collapse-item__header) {
  background-color: #fafafa !important;
  border: 0.3px dashed var(--el-color-info-light-5) !important;
  border-left: 5px solid var(--el-color-primary) !important;
  font-size: 1rem;
  height: 1.8rem;
}
:deep(.vxe-cell) {
  padding: 0px !important;
}
</style>
