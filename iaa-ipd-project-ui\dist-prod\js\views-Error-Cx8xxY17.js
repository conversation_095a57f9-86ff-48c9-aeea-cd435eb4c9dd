import{aB as Fe,aC as Ke,aD as Ve,l as d,m,p as Ye,q as g,R as re,S as Qe,K as Xe,Q as Ze,aE as h,J as C,u as w}from"./vue-vendor-BbSoq9WN.js";import{C as er,D as rr,b as tr}from"./element-plus-DgaixBsQ.js";import{by as or,bz as ar,bA as nr,bB as lr}from"./vendor-DLCNhz7G.js";let u,O,T,L,R,te,S,k,oe,ae,p,ne,le,se,x,b,v,sr=(async()=>{let I,A,E;I="modulepreload",A=function(e){return"/"+e},E={},O=function(e,r,t){let o=Promise.resolve();return r&&r.length>0&&(document.getElementsByTagName("link"),o=Promise.all(r.map(a=>{if(a=A(a),a in E)return;E[a]=!0;const n=a.endsWith(".css"),s=n?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${a}"]${s}`))return;const l=document.createElement("link");if(l.rel=n?"stylesheet":I,n||(l.as="script",l.crossOrigin=""),l.href=a,document.head.appendChild(l),n)return new Promise((c,i)=>{l.addEventListener("load",c),l.addEventListener("error",()=>i(new Error(`Unable to preload CSS for ${a}`)))})}))),o.then(()=>e()).catch(a=>{const n=new Event("vite:preloadError",{cancelable:!0});if(n.payload=a,window.dispatchEvent(n),!n.defaultPrevented)throw a})},R=(e,r)=>{const t=e[r];return t?typeof t=="function"?t():Promise.resolve(t):new Promise((o,a)=>{(typeof queueMicrotask=="function"?queueMicrotask:setTimeout)(a.bind(null,new Error("Unknown variable dynamic import: "+r)))})},b=or(),b.use(ar);let f,j;ae=e=>{e.use(b)},u={ROLE_ROUTERS:"roleRouters",USER:"user",IS_DARK:"isDark",LANG:"lang",THEME:"theme",LAYOUT:"layout",DICT_CACHE:"dictCache",LoginForm:"loginForm",TenantId:"tenantId",PROJECT_CURRENT_ID:"projectCurrentId",PROJECT_CURRENT_CATEGORY:"projectCurrentCategory",PROJECT_CURRENT_TAB:"projectCurrentTab"},v=(e="localStorage")=>({wsCache:new nr({storage:e})}),te=()=>{const{wsCache:e}=v();e.delete(u.USER),e.delete(u.ROLE_ROUTERS)},{wsCache:f}=v(),j={"zh-CN":er,en:rr},L=lr("locales",{state:()=>({currentLocale:{lang:f.get(u.LANG)||"zh-CN",elLocale:j[f.get(u.LANG)||"zh-CN"]},localeMap:[{lang:"zh-CN",name:"\u7B80\u4F53\u4E2D\u6587"},{lang:"en",name:"English"}]}),getters:{getCurrentLocale(){return this.currentLocale},getLocaleMap(){return this.localeMap}},actions:{setCurrentLocale(e){this.currentLocale.lang=e==null?void 0:e.lang,this.currentLocale.elLocale=j[e==null?void 0:e.lang],f.set(u.LANG,e==null?void 0:e.lang)}}}),S=()=>L(b),k=e=>{var r;(r=document.querySelector("html"))==null||r.setAttribute("lang",e)};var D=(e,r,t)=>new Promise((o,a)=>{var n=c=>{try{l(t.next(c))}catch(i){a(i)}},s=c=>{try{l(t.throw(c))}catch(i){a(i)}},l=c=>c.done?o(c.value):Promise.resolve(c.value).then(n,s);l((t=t.apply(e,r)).next())});let N,M;N=()=>D(void 0,null,function*(){var e;const r=S(),t=r.getCurrentLocale,o=r.getLocaleMap,a=(e=(yield R(Object.assign({"../../locales/en.ts":()=>O(()=>import("./en-Dc3Fg5U0.js"),__vite__mapDeps([])),"../../locales/zh-CN.ts":()=>O(()=>import("./zh-CN-wGqerCQf.js"),__vite__mapDeps([]))}),`../../locales/${t.lang}.ts`)).default)!=null?e:{};return k(t.lang),r.setCurrentLocale({lang:t.lang}),{legacy:!1,locale:t.lang,fallbackLocale:t.lang,messages:{[t.lang]:a},availableLocales:o.map(n=>n.lang),sync:!0,silentTranslationWarn:!0,missingWarn:!1,silentFallbackWarn:!0}}),oe=e=>D(void 0,null,function*(){const r=yield N();p=Fe.createI18n(r),e.use(p)}),M=Ke({func:void 0,bool:void 0,string:void 0,number:void 0,object:void 0,integer:void 0}),x=class extends M{static get style(){return Ve("style",{type:[String,Object]})}};var ce=Object.defineProperty,ie=Object.defineProperties,ue=Object.getOwnPropertyDescriptors,y=Object.getOwnPropertySymbols,U=Object.prototype.hasOwnProperty,z=Object.prototype.propertyIsEnumerable,q=(e,r,t)=>r in e?ce(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,pe=(e,r)=>{for(var t in r||(r={}))U.call(r,t)&&q(e,t,r[t]);if(y)for(var t of y(r))z.call(r,t)&&q(e,t,r[t]);return e},be=(e,r)=>ie(e,ue(r)),fe=(e,r)=>{var t={};for(var o in e)U.call(e,o)&&r.indexOf(o)<0&&(t[o]=e[o]);if(e!=null&&y)for(var o of y(e))r.indexOf(o)<0&&z.call(e,o)&&(t[o]=e[o]);return t};let _,B,G,H;_=(e,r)=>r,T=e=>{const r={t:s=>_(e,s)};if(!p)return r;const t=p.global,{t:o}=t,a=fe(t,["t"]),n=(s,...l)=>s?!s.includes(".")&&!e?s:o(_(e,s),...l):"";return be(pe({},a),{t:n})},B="/assets/404-B3JyPfEa.svg",G="/assets/500-BGu8fdSB.svg",H="/assets/403-RqeqO19C.svg";var ye=Object.defineProperty,de=Object.defineProperties,me=Object.getOwnPropertyDescriptors,J=Object.getOwnPropertySymbols,ge=Object.prototype.hasOwnProperty,Oe=Object.prototype.propertyIsEnumerable,W=(e,r,t)=>r in e?ye(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,ve=(e,r)=>{for(var t in r||(r={}))ge.call(r,t)&&W(e,t,r[t]);if(J)for(var t of J(r))Oe.call(r,t)&&W(e,t,r[t]);return e},Ee=(e,r)=>de(e,me(r));const je={class:"flex justify-center"},_e={class:"text-center"},Pe=["src"],he={class:"text-14px text-[var(--el-color-info)]"},Ce={class:"mt-20px"},P=d(Ee(ve({},{name:"Error"}),{__name:"Error",props:{type:x.string.validate(e=>["404","500","403"].includes(e)).def("404")},emits:["errorClick"],setup(e,{emit:r}){const{t}=T(),o={404:{url:B,message:t("error.pageError"),buttonText:t("error.returnToHome")},500:{url:G,message:t("error.networkError"),buttonText:t("error.returnToHome")},403:{url:H,message:t("error.noPermission"),buttonText:t("error.returnToHome")}},a=e,n=r,s=()=>{n("errorClick",a.type)};return(l,c)=>{const i=tr;return m(),Ye("div",je,[g("div",_e,[g("img",{src:o[e.type].url,alt:"",width:"350"},null,8,Pe),g("div",he,re(o[e.type].message),1),g("div",Ce,[Qe(i,{type:"primary",onClick:s},{default:Xe(()=>[Ze(re(o[e.type].buttonText),1)]),_:1})])])])}}}));var we=Object.defineProperty,Te=Object.defineProperties,Le=Object.getOwnPropertyDescriptors,$=Object.getOwnPropertySymbols,Re=Object.prototype.hasOwnProperty,Se=Object.prototype.propertyIsEnumerable,F=(e,r,t)=>r in e?we(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,ke=(e,r)=>{for(var t in r||(r={}))Re.call(r,t)&&F(e,t,r[t]);if($)for(var t of $(r))Se.call(r,t)&&F(e,t,r[t]);return e},xe=(e,r)=>Te(e,Le(r));let K;K=d(xe(ke({},{name:"Error403"}),{__name:"403",setup(e){const{push:r}=h();return(t,o)=>{const a=P;return m(),C(a,{type:"403",onErrorClick:o[0]||(o[0]=n=>w(r)("/"))})}}})),ne=Object.freeze(Object.defineProperty({__proto__:null,default:K},Symbol.toStringTag,{value:"Module"}));var Ie=Object.defineProperty,Ae=Object.defineProperties,De=Object.getOwnPropertyDescriptors,V=Object.getOwnPropertySymbols,Ne=Object.prototype.hasOwnProperty,Me=Object.prototype.propertyIsEnumerable,Y=(e,r,t)=>r in e?Ie(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,Ue=(e,r)=>{for(var t in r||(r={}))Ne.call(r,t)&&Y(e,t,r[t]);if(V)for(var t of V(r))Me.call(r,t)&&Y(e,t,r[t]);return e},ze=(e,r)=>Ae(e,De(r));let Q;Q=d(ze(Ue({},{name:"Error404"}),{__name:"404",setup(e){const{push:r}=h();return(t,o)=>{const a=P;return m(),C(a,{onErrorClick:o[0]||(o[0]=n=>w(r)("/"))})}}})),le=Object.freeze(Object.defineProperty({__proto__:null,default:Q},Symbol.toStringTag,{value:"Module"}));var qe=Object.defineProperty,Be=Object.defineProperties,Ge=Object.getOwnPropertyDescriptors,X=Object.getOwnPropertySymbols,He=Object.prototype.hasOwnProperty,Je=Object.prototype.propertyIsEnumerable,Z=(e,r,t)=>r in e?qe(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,We=(e,r)=>{for(var t in r||(r={}))He.call(r,t)&&Z(e,t,r[t]);if(X)for(var t of X(r))Je.call(r,t)&&Z(e,t,r[t]);return e},$e=(e,r)=>Be(e,Ge(r));let ee;ee=d($e(We({},{name:"Error500"}),{__name:"500",setup(e){const{push:r}=h();return(t,o)=>{const a=P;return m(),C(a,{type:"500",onErrorClick:o[0]||(o[0]=n=>w(r)("/"))})}}})),se=Object.freeze(Object.defineProperty({__proto__:null,default:ee},Symbol.toStringTag,{value:"Module"}))})();export{u as C,O as _,sr as __tla,T as a,L as b,R as c,te as d,S as e,k as f,oe as g,ae as h,p as i,ne as j,le as k,se as l,x as p,b as s,v as u};
function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = []
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
